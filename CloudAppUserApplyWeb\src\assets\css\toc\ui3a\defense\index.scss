.defense-list-wrapper  .defense-item-wrapper {
    background-color: $UI3A-light-background-color;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-title {
    color: $UI3A-white-color;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-text {
    color: $UI3A-font-color;
}

.add-defense-wrapper .add-defense-head {
    background-color: $UI3A-light-background-color;
}

.add-defense-wrapper .add-defense-head .add-defense-title {
    color: $UI3A-white-color;
}

.add-defense-wrapper .add-defense-head .add-defense-text {
    color: $UI3A-font-color;
}

.add-defense-wrapper .add-defense-device {
    color: $UI3A-font-color;
}

.add-defense-wrapper .device-content .device-list-wrapper {
    background-color: $UI3A-light-background-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-title {
    color: $UI3A-white-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-text {
    color: $UI3A-font-color;
}

.device-content .device-list-wrapper .device-item-wrapper {
    border-bottom: 1px solid $UI3A-border-color;
}

.ipc-setting-wrapper .ipc-setting-box {
    background-color: $UI3A-light-background-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-title {
    color: $UI3A-white-color;
}

.ipc-setting-wrapper .ipc-setting-desc {
    color: $UI3A-font-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-text {
    color: $UI3A-font-color;
}

.ipc-linkage-wrapper .ipc-configure {
    color: $UI3A-font-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list {
    background-color: $UI3A-light-background-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list .ipc-linkage-item {
    color: $UI3A-white-color;
    border-bottom: 1px solid $UI3A-border-color;
}

.channel-wrapper .channel-content .channel-list {
    background-color: $UI3A-light-background-color;
}

.area-name-wrapper .common-input {
    background: transparent!important;
}