@import './checkUserPwd.scss';
@import './upgrade.scss';
@import './navBar.scss';
@import './trusteeship/myInstaller.scss';
@import './trusteeship/deviceDetails.scss';
@import './trusteeship/check.scss';
@import './trusteeship/permission.scss';
@import './trusteeship/validity.scss';
@import './ipc-upgrade/index.scss';
@import './transfer/transferRequest.scss';
@import './share/shareManage.scss';
@import './defense/index.scss';
@import './line-bind/index.scss';
@import './household-management/householdManagement.scss';

html,
body,
#app {
  background-color: $light-gray-color;
  color: $black-color;
}

// 根据主题 全局 修改vant样式
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $UI1K-color-primary;
}

.van-button--primary {
  color: $white-color;
  background-color: $UI1K-color-primary;
  border: 1px solid $UI1K-color-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $black-color;
}


// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 40px;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .footer-btn {
    width: 345px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: $UI1K-button-background-color;
    border-radius: 4px;
    color: $white-color;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $UI1K-color-primary;

  .van-tabs__content {
    background-color: $UI1K-light-background-color;
  }
}


// 弹窗按钮
.van-dialog__cancel, .van-dialog__confirm {
  color: $UI1K-color-primary;
  background-color: $UI1K-light-background-color;
}

// Tab背景色
.van-tabs__nav {
  background-color: $UI1K-light-background-color;
}
.van-tab {
  color: $UI1K-font-color;
}
.van-tab--active {
  color: $UI1K-color-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $UI1K-color-primary;
  border-color: $UI1K-color-primary;
}

// switch背景色
.van-switch {
  background-color: $UI1K-font-color;
}
.van-switch--on {
  background-color: $UI1K-color-primary;
}