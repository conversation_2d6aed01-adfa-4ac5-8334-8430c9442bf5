<template>
  <div class="defense-list-wrapper">
    <van-swipe-cell v-for="(item, index) in dataList" :key="'item' + index" :name="`device-${index}`">
      <div class="defense-item-wrapper">
        <div class="defense-item-content">
          <!-- 阻止侧滑返回误触点击事件，把click改成muouseup -->
          <div class="defense-item-left" @mouseup="handleEdit(item, index)">
            <div class="defense-item-title defense-ellipsis-text">{{ item.groupName }}</div>
            <div
              :class="[
                'defense-item-text',
                'defense-ellipsis-text',
                item.status > 0 ? 'defense-status' : 'canecl-defense-status'
              ]"
            >
              {{ statusNameObj[item.status || 0] }}
            </div>
          </div>
          <div class="defense-item-right">
            <div class="defense-status-img" @click.stop="handleClick(item, index)">
              <theme-image
                alt="defense"
                class="defense-img"
                :imageName="statusImgObj[item.status] || 'cancel_defense.png'"
              />
            </div>
          </div>
        </div>
      </div>
      <template #right>
        <van-button square type="danger" class="swipe-right-btn" @click="() => handleDelete(item, index)">
          <theme-image alt="delete" class="refuse-img" imageName="delete.png" />
        </van-button>
      </template>
    </van-swipe-cell>
  </div>
</template>
<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'DefenseList',
  components: {
    ThemeImage
  },
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      //   dataList: [{}, {}, {}]
      statusImgObj: {
        0: 'cancel_defense.png',
        1: 'out_defense.png',
        2: 'home_defense.png'
      },
      statusNameObj: {
        0: this.$t('cancelDefense'),
        1: this.$t('outDefense'),
        2: this.$t('homeDefense')
      }
    }
  },
  computed: {},
  methods: {
    handleClick(item, index) {
      this.$emit('handleClick', item, index)
    },
    handleEdit(item, index) {
      this.$emit('handleEdit', item, index)
    },
    handleDelete(item, index) {
      this.$emit('handleDelete', item, index)
    }
  }
}
</script>
<style lang="scss" scoped>
.defense-list-wrapper {
  width: 100%;
  padding-top: 10px;
  box-sizing: border-box;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
}
.defense-item-wrapper {
  width: 100%;
  height: 72px;
  box-sizing: border-box;
  margin-bottom: 6px;
  .defense-item-content {
    width: 100%;
    height: 100%;
    padding: 15px 12px 15px 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .defense-item-left {
      width: calc(100% - 36px);
      height: 100%;
      box-sizing: border-box;
      padding-left: 8px;
      .defense-item-title {
        width: 100%;
        height: 24px;
        font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .defense-item-text {
        font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .defense-item-right {
      width: 36px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .defense-status-img .theme-image-container {
        width: 36px;
        height: 36px;
      }
    }
  }
}
::v-deep .swipe-right-btn {
  width: 72px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
