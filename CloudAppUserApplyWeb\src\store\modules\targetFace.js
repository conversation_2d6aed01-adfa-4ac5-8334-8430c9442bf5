import {
  add<PERSON>erson,
  edit<PERSON><PERSON>,
  del<PERSON><PERSON>,
  query<PERSON><PERSON>,
  getPersonFields,
  getAccessControlConfig
} from '@/api/targetFace.js'
import { formatXmlData, strToDate, dateFormat } from '@/utils/common.js'

function initiatePersonFileds(display = true) {
  return {
    listType: ['strangerList', 'whiteList', 'blackList', 'admin'],
    accessType: ['FaceMatch', 'Password', 'SwipingCard', 'MatchAndPass', 'MatchAndCard', 'MatchorCard', 'AllType'],
    sexType: ['male', 'female'],
    name: {
      display,
      maxLen: 127
    },
    sex: {
      display
    },
    age: {
      display,
      max: 120,
      min: 0
    },
    jobNumber: {
      display,
      maxLen: 31
    },
    identifyNumber: {
      display,
      maxCardLen: 20,
      maxCount: 5
    },
    telephone: {
      display,
      maxLen: 18
    },
    comment: {
      display,
      maxLen: 63
    },
    password: {
      display,
      minPassLen: 6,
      maxPassLen: 8,
      input: 'digit'
    },
    floor: {
      display,
      max: 99,
      min: 1
    },
    room: {
      display,
      max: 9999,
      min: 1
    }
  }
}

function formatTargetFaceDetail(body) {
  const {
    config: { personInfo, personID, faceImgs }
  } = body

  personInfo.personID = personID

  if (faceImgs && faceImgs.item) {
    const imgData = faceImgs.item.pictureData['#cdata-section']
    personInfo.faceImg = `data:image/jpg;base64,${imgData}`
  } else {
    personInfo.faceImg = ''
  }

  const keys = Object.keys(personInfo)
  const newInfo = {}
  keys.forEach(key => {
    const value = formatXmlData(personInfo[key])
    if (typeof value === 'object') {
      newInfo[key] = ''
    } else {
      newInfo[key] = value
    }
  })

  newInfo.cardIds = newInfo.identifyNumber.split('#')
  newInfo.lockIdArr = newInfo.lockIDs.split(',')

  if (newInfo.startTime) {
    newInfo.startTime = strToDate(newInfo.startTime)
    newInfo.endTime = strToDate(newInfo.endTime)
    newInfo.termOfValidity = 'custom'
  } else {
    newInfo.termOfValidity = 'foreverValid'
  }
  // console.log('target face detail', newInfo)
  return newInfo
}

function getBase64ImgInfo(base64Str) {
  const body = base64Str.split(',')[1]
  const size = body && body.length ? parseInt(body.length * (3 / 4)) : 0
  let width = 0
  let height = 0

  const img = document.createElement('img')
  img.src = base64Str

  return new Promise(resolve => {
    img.onload = () => {
      width = img.width
      height = img.height

      resolve({
        size,
        width,
        height,
        pictureData: body
      })
    }
    img.onerror = () => {
      console.error('resolve base64 img failed')

      resolve({
        size,
        width,
        height,
        pictureData: body
      })
    }
  })
}

function formatDelRequest({ personID }) {
  return `<?xml version="1.0" encoding="utf-8"?>
<config version="1.0" xmlns="http://www.ipc.com/ver10">
  <types>
    <deleteType>
      <enum>byPersonID</enum>
      <enum>byListType</enum>
      <enum>byName</enum>
      <enum>byIdentifyNumber</enum>
    </deleteType>
    <listType>
      <enum>strangerList</enum>
      <enum>whiteList</enum>
      <enum>blackList</enum>
      <enum>admin</enum>
    </listType>
  </types>
  <deleteAction>
    <deleteType type="deleteType">byPersonID</deleteType>
    <personID type="uint32">${personID}</personID>
  </deleteAction>
</config>`
}

// 只能用从门禁复制过来的加密算法
function aesEncrypt(str, key) {
  let secret = key + '0000000000000000'
  let aesKey = window.CryptoJS.enc.Utf8.parse(secret.slice(0, 16))
  let encryptedData = window.CryptoJS.AES.encrypt(str, aesKey, {
    mode: window.CryptoJS.mode.ECB,
    padding: window.CryptoJS.pad.ZeroPadding
  })
  let encryptedHexStr = encryptedData.ciphertext.toString()
  return encryptedHexStr
}

async function formatTargetAddRequest({
  personID,
  listType,
  name = '',
  sex = '',
  age = '',
  jobNumber = '',
  accessType,
  cardIds = '',
  telephone = '',
  lockIdArr,
  startTime = '',
  endTime = '',
  floor = '',
  room = '',
  comment = '',
  faceImg = '',
  password = '',
  termOfValidity = 'custom'
}) {
  const personIDStr = `<personID type="uint32">${personID}</personID>`
  const identifyNumber = cardIds.join('#')
  const lockIDs = lockIdArr.join(',')

  const sTime = startTime && termOfValidity === 'custom' ? dateFormat(startTime) : ''
  const eTime = endTime && termOfValidity === 'custom' ? dateFormat(endTime) : ''

  const { size, width, height, pictureData } = await getBase64ImgInfo(faceImg)
  const imgStr = `<faceImgs type="list" maxCount="5" count="1">
    <item>
      <pictureData type="string" maxLen="273080"><![CDATA[${pictureData}]]></pictureData>
      <pictureNum type="uint32">1</pictureNum>
      <width type="uint32">${width}</width>
      <height type="uint32">${height}</height>
      <format type="formatType">jpg</format>
      <size type="uint32" base64="true">${size}</size>
    </item>
  </faceImgs>`

  let pwdStr = ''

  if (password) {
    pwdStr = `<password type="string" encryptType="aes"><![CDATA[${aesEncrypt(password, jobNumber)}]]></password>`
  }

  return `<?xml version="1.0" encoding="utf-8"?>
<config version="1.0" xmlns="http://www.ipc.com/ver10">
  <types>
    <listType>
      <enum>strangerList</enum>
      <enum>whiteList</enum>
      <enum>blackList</enum>
      <enum>admin</enum>
    </listType>
    <sexType>
      <enum>male</enum>
      <enum>female</enum>
    </sexType>
    <formatType>
      <enum>jpg</enum>
    </formatType>
  </types>
  ${personID === '-1' ? '' : personIDStr}
  <personInfo>
    <listType type="listType">${listType}</listType>
    <name type="string" maxLen="127" dataType="sensitive"><![CDATA[${name}]]></name>
    <sex type="sexType">${sex}</sex>
    <age type="uint32">${age}</age>
    <jobNumber type="string" maxLen="31" dataType="sensitive"><![CDATA[${jobNumber}]]></jobNumber>
    ${pwdStr}
    <accessType type="accessType">${accessType}</accessType>
    <identifyNumber type="string" maxLen="127" dataType="sensitive"><![CDATA[${identifyNumber}]]></identifyNumber>
    <telephone type="string" maxLen="63" dataType="sensitive"><![CDATA[${telephone}]]></telephone>
    <lockIDs type="string"><![CDATA[${lockIDs}]]></lockIDs>
    <startTime type="string"><![CDATA[${sTime}]]></startTime>
    <endTime type="string"><![CDATA[${eTime}]]></endTime>
    <floor type="uint32">${floor}</floor>
    <room type="uint32">${room}</room>
    <comment type="string" maxLen="63"><![CDATA[${comment}]]></comment>
  </personInfo>
  ${faceImg ? imgStr : ''}
</config>`
}

function formatQueryRequset({ pageNum, pageSize, listType, name, personID }) {
  const pageStr = `<queryType type="queryType">byPageNum</queryType>
    <PageNum type="uint32">${pageNum}</PageNum>
    <PageSize type="uint32">${pageSize}</PageSize>`

  const listTypeStr = `<queryType type="queryType">byListType</queryType>
    <listType type="listType">${listType}</listType>`

  const nameStr = `<queryType type="queryType">byName</queryType>
    <name type="string"><![CDATA[${name}]]></name>`

  const personIdStr = `<queryType type="queryType">byPersonID</queryType>
    <personID type="uint32">${personID}</personID>`

  return `<?xml version="1.0" encoding="utf-8"?>
<config version="1.0" xmlns="http://www.ipc.com/ver10">
  <types>
    <queryType>
      <enum>bySex</enum>
      <enum>byListType</enum>
      <enum>byName</enum>
      <enum>byIdentifyNumber</enum>
    </queryType>
    <listType>
      <enum>strangerList</enum>
      <enum>whiteList</enum>
      <enum>blackList</enum>
      <enum>admin</enum>
    </listType>
  </types>
  <queryAction>
    ${personID ? personIdStr : ''}
    ${listType ? listTypeStr : ''}
    ${name ? nameStr : ''}
    ${pageNum === undefined ? '' : pageStr}
  </queryAction>
</config>`
}

export default {
  namespaced: true,
  state: () => ({
    targetFaceList: [],
    doorLockList: [],
    queryDetail: true,
    devId: '',
    targetFaceDetail: {
      listType: 'strangerList',
      jobNumber: '',
      name: '',
      sex: 'male',
      age: '',
      telephone: '',
      floor: '',
      room: '',
      accessType: 'FaceMatch',
      cardIds: [],
      password: '',
      lockIdArr: [],
      startTime: '',
      endTime: '',
      termOfValidity: 'foreverValid',
      comment: '',
      faceImg: '',
      personID: '-1'
    },
    personFileds: initiatePersonFileds(),
    displayEncrytPwd: false
  }),
  mutations: {
    INIT_TARGET_FACE_DETAIL(state, params) {
      let detail = {
        listType: 'strangerList',
        jobNumber: '',
        name: '',
        sex: 'male',
        age: '',
        telephone: '',
        floor: '',
        room: '',
        accessType: 'FaceMatch',
        cardIds: [],
        password: '',
        lockIdArr: [],
        startTime: '',
        endTime: '',
        termOfValidity: 'foreverValid',
        comment: '',
        faceImg: '',
        personID: '-1'
      }
      if (params) {
        detail = params
      }

      state.targetFaceDetail = detail
    },
    SET_TARGET_FACE_LIST(state, data) {
      state.targetFaceList = data
    },
    SET_PERSON_FIELDS(state, data) {
      state.personFileds = data
    },
    SET_QUERY_DETAIL(state, data) {
      state.queryDetail = data
    },
    SET_DEV_ID(state, data) {
      state.devId = data
    },
    SET_DOOR_LOCK_LIST(state, data) {
      state.doorLockList = data
    },
    SET_DISPLAY_ENCRYPT_PWD(state, data) {
      state.displayEncrytPwd = data
    }
  },
  actions: {
    async queryTargetFaceList({ state }, params) {
      const requestXml = formatQueryRequset(params)

      let {
        config: {
          face: {
            personID: { _count: total = 0, item: pageInfo = [] }
          }
        }
      } = await queryPerson({
        devId: state.devId,
        xml: requestXml
      })

      if (!Array.isArray(pageInfo)) {
        pageInfo = [pageInfo]
      }

      pageInfo = pageInfo.map(item => formatXmlData(item))

      const reqArr = []
      pageInfo.forEach(id => {
        const detailParams = formatQueryRequset({ personID: id })

        reqArr.push(queryPerson({ xml: detailParams, devId: state.devId }))
      })

      let records = []

      await Promise.all(reqArr).then(values => {
        records = values.map(value => {
          return formatTargetFaceDetail(value)
        })
      })
      // console.log(records)

      return {
        total: parseInt(total),
        records
      }
    },
    async queryTargetFace({ commit, state }, params) {
      if (params.personID == '-1') {
        commit('SET_DISPLAY_ENCRYPT_PWD', false)
        commit('INIT_TARGET_FACE_DETAIL')
      } else {
        const detailParams = formatQueryRequset(params)

        const body = await queryPerson({ xml: detailParams, devId: state.devId })

        const obj = formatTargetFaceDetail(body)

        if (obj.password && obj.password.length > 0) {
          obj.password = ''
          commit('SET_DISPLAY_ENCRYPT_PWD', true)
        } else {
          commit('SET_DISPLAY_ENCRYPT_PWD', false)
        }

        commit('INIT_TARGET_FACE_DETAIL', obj)
      }
    },
    async queryAccessConfig({ commit, state }) {
      const {
        config: {
          AccessControl: {
            doorLock: { item }
          }
        }
      } = await getAccessControlConfig({ xml: '', devId: state.devId })

      let lockInfo = []
      if (Array.isArray(item)) {
        lockInfo = item
      } else {
        lockInfo = [item]
      }

      lockInfo = lockInfo.map(info => {
        let { doorName, id } = info

        return {
          doorName: formatXmlData(doorName),
          id: formatXmlData(id)
        }
      })

      commit('SET_DOOR_LOCK_LIST', lockInfo)
    },
    async queryPersonFields({ commit, state }) {
      const {
        config: { types, personInfo }
      } = await getPersonFields({ xml: '', devId: state.devId })

      const keys = Object.keys(types)
      const newInfo = initiatePersonFileds(false)
      keys.forEach(key => {
        newInfo[key] = formatXmlData(types[key])
      })
      // console.log('person inf', personInfo)

      if (personInfo.name) {
        newInfo.name.display = true
        newInfo.name.maxLen = parseInt(personInfo.name._maxLen)
      }

      if (personInfo.sex) {
        newInfo.sex.display = true
      }

      if (personInfo.age) {
        newInfo.age.display = true
      }

      if (personInfo.jobNumber) {
        newInfo.jobNumber.display = true
        newInfo.jobNumber.maxLen = parseInt(personInfo.jobNumber._maxLen)
      }

      if (personInfo.identifyNumber) {
        newInfo.identifyNumber.display = true
        newInfo.identifyNumber.maxCardLen = parseInt(personInfo.identifyNumber._maxCardLen)
        newInfo.identifyNumber.maxCount = parseInt(personInfo.identifyNumber._maxCount)
      }

      if (personInfo.telephone) {
        newInfo.telephone.display = true
        newInfo.telephone.maxLen = parseInt(personInfo.telephone._maxLen)
      }

      if (personInfo.comment) {
        newInfo.comment.display = true
        newInfo.comment.maxLen = parseInt(personInfo.comment._maxLen)
      }

      if (personInfo.password) {
        newInfo.password.display = true
        newInfo.password.minPassLen = parseInt(personInfo.password._minPassLen)
        newInfo.password.maxPassLen = parseInt(personInfo.password._maxPassLen)
        newInfo.password.input = personInfo.password._input
      }

      if (personInfo.floor) {
        newInfo.floor.display = true
        newInfo.floor.max = parseInt(personInfo.floor._max)
      }

      if (personInfo.room) {
        newInfo.room.display = true
        newInfo.room.max = parseInt(personInfo.room._max)
        newInfo.room.min = parseInt(personInfo.room._min)
      }
      // console.log('person fields', newInfo)

      commit('SET_PERSON_FIELDS', newInfo)
    },
    async addTargetFace({ state }, params) {
      const requestXml = await formatTargetAddRequest(params)

      const body = await addPerson({
        devId: state.devId,
        xml: requestXml
      })

      return body
    },
    async editTargetFace({ state }, params) {
      const requestXml = await formatTargetAddRequest(params)

      const body = await editPerson({
        devId: state.devId,
        xml: requestXml
      })

      return body
    },
    async delTargetFace({ state }, params) {
      const requestXml = formatDelRequest(params)

      const body = await delPerson({
        devId: state.devId,
        xml: requestXml
      })

      return body
    }
  }
}
