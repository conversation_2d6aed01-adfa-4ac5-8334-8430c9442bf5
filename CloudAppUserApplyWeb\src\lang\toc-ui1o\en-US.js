export default {
  upgrade: 'Cloud Upgrade',
  cancel: 'Cancel',
  confirm: 'Confirm',
  deviceUpdate: 'Device',
  cameraUpdate: 'Camera',
  allUpdate: 'All Upgrade',
  updateNow: 'Upgrade',
  currentVersion: 'Current Version',
  latestVersion: 'Latest Version',
  updateContent: 'Update Content',
  hasLatestVersion: 'Already the latest version',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Waiting download',
  inprogress: 'Downloading',
  downloadFail: 'Download failed',
  downloadFinished: 'Download complete',
  inupgrade: 'Upgrading',
  upgradeFail: 'Update failed',
  upgradeSuccess: 'Upgrade successfully',
  deviceUpgradeInfo:
    'During the upgrade the device will be disconnected and automatically restarted. Are you sure to upgrade?',
  upgradeTip: 'During the upgrade the device will be disconnected and automatically restarted.',
  cameraUpgradeInfo:
    'During the upgrade the camera will be disconnected and automatically restarted. Are you sure to upgrade?',
  pwdUserNameError: 'Username or password error',
  permissionAuth: 'Super administrator authority authentication',
  pleaseEnterUser: 'Please enter username',
  pleaseEnterPwd: 'Please enter password',
  noCameraUpgrade: 'No upgradeable camera detected',
  handleCheck: 'Update Detection',
  paySuccess: 'Payment succeeded',
  payFail: 'Payment failed',
  done: 'Complete',
  rePurchase: 'Repurchase',
  cloudStorage: 'Cloud Storage',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  myInstaller: 'My installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Add Hosting',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  delete: 'Delete',
  operationSuccess: 'Operation succeeded',
  operationFail: 'Operation failed',
  cancelTrusteeship: 'Cancel Hosting',
  chooseDevice: 'Select device',
  noAvaiableDevice:
    'The device that supports the hosting function and is bind with your P2P account has the ability to grant access to installers.',
  leastChoose: 'Select at least one device',
  details: 'Details',
  live: 'Live',
  rec: 'Playback',
  config: 'Configuration',
  confirmTrusteeshipTip:
    'The hosting request has been sent to the installer please wait for the installer to process it',
  cancelTrusteeshipTip:
    'After canceling hosting the installer is unable to provide you with remote maintenance services. Are you sure to cancel?',
  unBindTrusteeship: 'After unbinding all device hosting will be cancelled. Are you sure to unbind?',
  trusteeshipPermissions: 'Hosting permissions',
  trusteeshipTime: 'Hosting time',
  unBind: 'Unbinding',
  serviceException: 'Service exception',
  pullingText: 'Pull down to loading...',
  loosingText: 'Swipe to refresh…',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more',
  checkSuccess: 'Check successfully',
  checkFail: 'Check failed',
  viewUpdateContent: 'View update content',
  deviceDisconnected: 'Failed to connect device',
  updateNote: 'Update note',
  noData: 'No Data',
  tips: 'Tips',
  password: 'Password',
  pwdError: 'Password error, you can try {0} more times',
  pwdErrorLock: 'Login is blocked due to too many login attempts with an incorrect account or password.',
  noPermissions: 'No permissions',
  permission: 'Permission',
  validity: 'Validity',
  permissionValidity: 'Permission validity',
  isSaveModify: 'Do you want to save the changes?',
  manyMinutes: '{0}min',
  manyHours: '{0}h',
  manyDays: '{0}d',
  manyMinutesEn: '{0} minutes',
  manyHoursEn: '{0} hours',
  manyDaysEn: '{0} days',
  oneWeek: '1 week',
  forever: 'Forever',
  expired: 'Expired',
  residue: 'Residue',
  transferRequest: 'Transfer request',
  acceptTransfer: 'Accept',
  refuseTransferConfirm: 'Are you sure to decline the transfer?',
  bindInstallerText: 'You can host devices after binding the Installer({account}). Bind now?',
  bindSuccess: 'Binding successful',
  acceptSuccess: 'Accept successfully',
  from: 'From',
  shareManage: 'Sharing Management',
  shareDetail: 'Sharing Detail',
  acceptShare: 'Accept sharing',
  permissionText: 'The permissions you have obtained',
  livePreview: 'Live preview',
  playback: 'Playback',
  alarm: 'Alarm',
  intercom: 'Intercom',
  gimbal: 'PTZ',
  refuseShareConfirm: 'Confirm refusal of share?',
  acceptAll: 'Accept All',
  exitShare: 'Exit sharing',
  cancelDefense: 'Disarm',
  homeDefense: 'Stay',
  outDefense: 'Away',
  defenseDeployment: 'Arm/Disarm',
  oneClickDeployment: 'Arm',
  oneClickDisarm: 'Disarm',
  oneClickRemoval: 'Clear Alarm',
  deploySuccess: 'Arming successful',
  disarmSuccess: 'Disarm successfully',
  add: 'Add',
  edit: 'Edit',
  setting: 'Setting',
  all: 'All',
  name: 'Name',
  cameraSensor: 'Link to Camera',
  deleteDeviceConfirm: 'Confirm delete the device?',
  onlySameDevice: 'Only cameras belonging to the same Video Recorder can be added to the same group',
  pleaseChooseChannel: 'Please choose channel',
  pleaseAddCameraSensor: 'Please add camera/sensor',
  defensiveLinkageItem: 'Disarm Linkage Item',
  defensiveDesc: 'The selected Disarm Linkage Item is not effective in the Disarm status.',
  bypassHome: 'Set up a Stay Arm Bypass',
  bypassHomeDesc: 'If enabled, the zone will be auto-bypassed during stay-arming',
  ipcSound: 'IPC Sound',
  ipcLight: 'IPC Light',
  pleaseChooseLinkage: 'Please choose disarm linkage item',
  deleteConfirm: 'Confirm delete?',
  pleaseAddGroup: 'Please add group first',
  groupChannelEmpty: 'No channel has been added to the group',
  removalSuccess: 'The clearing alarm command is sent',
  removalFail: 'Alert removal sent fail',
  reqSuccess: 'Success',
  reqFail: 'Fail',
  groupLimit: 'Supports adding up to {limit} groups',
  areaGroup: 'Area group',
  pleaseEnter: 'Please enter',
  groupNoDevice: 'No device added to the group',
  addPoint: 'Add',
  addSuccess: 'Add successfully',
  addFail: 'Add fail',
  editSuccess: 'Edit successfully',
  editFail: 'Edit fail',
  deleteSuccess: 'Delete successfully',
  deleteFail: 'Delete fail',
  pointNameExist: 'Preset Point Name Exist',
  noPointSelect: 'No preset points to choose, Please create a preset point first',
  choosePoint: 'Choose',
  presetPoint: 'Preset Point',
  presetPointName: 'Preset Point Name',
  cruiseLineName: 'Cruise Line Name',
  pleaseEnterPoint: 'Please Enter Preset Point Name',
  pleaseEnterLine: 'Please Enter Cruise Line Name',
  presetPointEmpty: 'The preset point list cannot be empty!',
  lineNameExist: 'Cruise Line Name Exist',
  presetPointLimit: 'The number of preset points cannot exceed {0}!',
  manySecond: '{0} seconds',
  oneMinute: '1 minute',
  speed: 'Speed',
  holdTime: 'Duration',
  deletePointConfirm: 'Confirm delete the preset point?',
  pleaseChoosePoint: 'Please Choose Preset Point',
  pleaseChooseSpeed: 'Please Choose Speed',
  pleaseChooseHoldTime: 'Please Choose Duration',
  lineAuthBind: 'Bind with Line APP account',
  bindFail: 'Binding fail',
  binding: 'Binding',
  householdManagement: 'Buildings & Residents',
  addBuilding: 'Add building',
  buildingName: 'Building Name',
  enterBuildingName: 'Please enter the building name',
  buildingNum: 'Building Number',
  enterBuildingNum: 'Please enter the building number',
  relateDevice: 'Associate Devices',
  roomNum: 'Room quantity',
  room: 'Room',
  addRoom: 'Add Room',
  roomName: 'Room Number',
  enterRoomName: 'Please enter the room number',
  household: 'Residents',
  addRoomMember: 'Add Residents',
  changeSuccessfully: 'Successfully modified',
  email: 'Email',
  enterMemberEmail: 'Please enter email address',
  mobile: 'Phone',
  enterMemberMobile: 'Please enter phone number',
  emailNameError: 'Incorrect email format',
  mobileError: 'Incorrect phone number format',
  emailNameNotEmpty: 'The email cannot be empty',
  mobileNotEmpty: 'The phone number cannot be empty',
  memberInMax: 'The quantity of the residents in this room has reached the upper limit.',
  memberMobileRepeate: 'This phone number already exists in this room.',
  emailRepeate: 'This email already exists in this room.',
  supportDash: 'Only support -, _, and blank spaces as special characters.',
  errorCode: {
    400: 'Parameter error',
    404: 'The requested resource (web page etc.) does not exist',
    500: 'System exception!',
    502: 'Server request failed',
    503: 'Server exception',
    504: 'Server request timeout',
    550: 'Request timeout',
    1000: 'Parameter error',
    1005: 'Image verification code error',
    1007: 'Picture verification code is required',
    1008: 'The verification code has expired',
    1009: 'Verification code error',
    1011: 'The parameter is not filled in correctly!',
    1012: 'API not recognized',
    1013: 'The verification code failed to send',
    1015: 'The user already exists',
    1027: 'Please enter the correct device serial number/security code',
    1028: 'The channel has been enabled or disabled',
    4500: 'Parameter error',
    5000: 'Sorry, you do not have permission to perform this operation',
    5001: 'The current user has no permission',
    6000: 'The current business status does not support this operation',
    6001: 'Too frequent operation',
    7000: 'Parameter error',
    7001: 'The user does not exist',
    7002: 'Old password error!',
    7003: 'Token is error!',
    7004: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7005: 'Invalid signature',
    7006: 'Mobile number already exists',
    7007: 'The user is locked, please contact the administrator to unlock',
    7009: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7010: 'The administrator account is not activated',
    7011: 'Account not activated',
    7019: 'The username already exists',
    7021: 'Deletion failed! Please clear all hosts under this host group first',
    7023: 'The mailbox has been bound',
    7028: 'The template has been used in the project and cannot be deleted!',
    7029: 'The template name already exists!',
    7030: 'The data already exists!',
    7032: 'The firmware package already exists!',
    7034: 'The firmware package has been released and cannot be deleted!',
    7040: 'Device does not exist or not online',
    7042: 'There are other tasks in the startup state',
    7043: 'The task has not been approved!',
    7044: 'Operation failed. There are no devices eligible for upgrade!',
    7045: 'The task is not approved!',
    7056: 'This version has been included in the supporting compatibility management and cannot be deleted!',
    7057: 'Issuing document cannot be blank!',
    7061: 'Correction failed, cannot create correction again!',
    7065: 'Channel has already been shared',
    7066: 'The customer code already exists!',
    7068: 'The customer code does not exist!',
    7069: 'Too much data, please narrow the scope and search again!',
    7072: 'The device already exists',
    7081: 'Import failed!',
    7082: 'Export failed!',
    7084: 'The customer country code already exists',
    7086: 'The operation is refused due to system exception',
    7087: 'The product already exists!',
    7088: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7090: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7093: 'Image and text information is not configured!',
    7094: 'The service terms information does not exist!',
    9000: 'System exception!',
    9001: 'The protocol version is too low. The old version is no longer compatible and needs to be upgraded',
    9002: 'Protocol version error, unrecognized version field or error message',
    9003: 'Failed to send verification code',
    9004: 'Database operation failed',
    9005: 'The data does not exist',
    9006: 'The data already exists',
    9007: 'The data to be viewed does not exist',
    9008: 'The data does not exist',
    9009: 'Data exception',
    9500: 'System exception!',
    10000: 'Failed to connect device',
    10001: 'System exception!',
    12344: 'Network connection failed',
    12345: 'Network connection timeout',
    20021: 'This email has been used',
    20024: 'The account has been activated',
    20030: 'The link has expired',
    20070: 'Failed to invite this user because you are in different data centers.',
    20071: 'Failed to invite this user because you belong to different group.',
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    32018: 'The data doesnt exist.',
    32019: 'Operation failed',
    32021: 'Data does not exist',
    32022: 'Hosting service is not supported because {0} device and installer are not in the same country/region.',
    33001: 'No permission to operate this device',
    33002: 'No permission to operate this site',
    33003: 'The site does not exist',
    33004: 'The length of the device name must be between 0 and 32',
    33010: 'The device already exists',
    33601: 'Repetitive device!',
    33602: 'limit max arming group num!',
    33603: 'Operation failed!',
    34001: 'This account has been bound.',
    34003: 'The state information is incorrect',
    34004: 'Authorization failed.',
    34005: 'Operation Failed: The authorization has expired. Please obtain it again.',
    34006: 'Device transfer does not exist',
    34007: 'Can only accept transfers from same user',
    34021: 'The quantity of the building has reached the upper limit.',
    34022: 'The building name already exists',
    34023: 'The Building number already exists',
    34024: 'Delete failed. Please first delete the rooms and devices in this building.',
    34025: 'Associate failed. This device has been associated to another building.',
    34026: 'The quantity of the devices in this building has reached the upper limit.',
    34027: 'Operation failed. This building has been deleted.',
    34028: 'This room number already exists.',
    34029: 'The quantity of the room in this building has reached the upper limit.',
    34030: 'The quantity of the residents in this room has reached the upper limit.',
    34031: 'Operation failed. This room has been deleted.',
    34033: 'Delete failed. Please first delete the residents in this room.',
    34035: 'The phone number already exists in this room.',
    34036: 'This email already exists in this room.',
    34037: 'Operation failed. This resident has been deleted.',
    536870934: 'Operation failure, please check the device status',
    536870940: 'Operation failure, please check the device status',
    536870943: 'Invalid parameter',
    536870945: 'Operation failure, please check the device status',
    536870947: 'Username does not exist',
    536870948: 'Username or password error',
    536871017: 'Operation failure, please check the device status',
    536871039: 'Invalid parameter',
    536871060: 'Operation failure please check the device status',
    536871082: 'Operation failure, please check the device status',
    536871083: 'Operation failure, please check the device status',
    ipc: {
      499: 'Unknown error',
      612: 'Operation failure, please check the device status',
      730: 'Operation failure, please check the device status',
      731: 'Operation failure, please check the device status',
      732: 'Operation failure, please check the device status',
      735: 'Operation failure, please check the device status'
    }
  }
}
