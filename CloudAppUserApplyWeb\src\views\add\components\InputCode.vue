<template>
  <div class="add-info-box">
    <div class="info-tile">
      <div @click="closeCodePop">{{ $t('cancel') }}</div>
      <div>{{ $t('enterVerificationCode') }}</div>
      <div @click="confirm">{{ $t('confirm') }}</div>
    </div>
    <van-field v-model="securityCode" maxlength="50" clearable :placeholder="$t('enterDevVerificationCode')" />
    <div class="text-1 text-top">
      {{ $t('codeTips1') }}
    </div>
    <div class="text-1">
      {{ $t('codeTips2') }}
    </div>
    <div class="text-2">
      {{ $t('codeTips3') }}
    </div>
    <div class="text-2">
      {{ $t('codeTips4') }}
    </div>
    <div class="text-1 text-bottom">
      {{ $t('codeTips5') }}
    </div>
    <van-image :src="require('@/assets/img/common/add-device/security_code_guide.png')" />
  </div>
</template>
<script>
export default {
  name: 'inputCode',
  components: {},
  props: {
    code: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      securityCode: ''
    }
  },
  beforeMount() {
    this.securityCode = this.code
  },
  methods: {
    closeCodePop() {
      this.$emit('closeCodePop')
    },
    confirm() {
      if (!this.securityCode) {
        this.$toast(this.$t('enterDevVerificationCode'))
        return
      }
      this.$emit('setSecurityCode', this.securityCode)
    }
  }
}
</script>
<style lang="scss" scoped>
.add-info-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 24px;
  .info-tile {
    width: 100%;
    height: 60px;
    line-height: 60px;
    color: var(--text-color-primary, #1a1a1a);
    display: flex;
    justify-content: space-between;
  }
  .text-1 {
    width: 100%;
    font-size: var(--font-size-text-size, 12px);
    font-weight: 400;
    line-height: 20px;
    color: var(--icon-color-secondary, #666666);
    margin-top: 5px;
    text-align: left;
  }
  .text-2 {
    width: 100%;
    font-size: var(--font-size-text-size, 12px);
    font-weight: 500;
    line-height: 20px;
    color: var(--text-color-primary, #1a1a1a);
    margin-top: 5px;
    text-align: left;
  }
  .text-top {
    margin-top: 15px;
  }
  .text-bottom {
    margin-bottom: 15px;
  }
  .van-image {
    width: 303px;
    height: 168px;
  }
}
</style>
