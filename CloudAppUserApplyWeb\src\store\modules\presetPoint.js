export default {
  namespaced: true,
  state: () => ({
    initStatus: false, // 是否第一次进入
    devType: '2', // 设备类型,1：IPC 2：NVR 3：DVR 4：依图套装 5：TVT套装 7：依图NVR 8：NVMS 9：卡片机 10：门铃 11：太阳能IPC
    chlIndex: null, // 通道号
    pointRecord: {
      name: 'preset1',
      index: 1
    }, // 记录新增/编辑的预置点信息
    pointList: [], // 已有的预置点
    pointOptions: [] // 预置点的选项
  }),
  getters: {},
  mutations: {
    SET_INIT_STATUS(state, data) {
      state.initStatus = data
    },
    SET_DEV_TYPE(state, data) {
      state.devType = data
    },
    SET_CHL_INDEX(state, data) {
      state.chlIndex = data
    },
    SET_POINT_RECORD(state, data) {
      state.pointRecord = data
    },
    SET_POINT_LIST(state, data) {
      state.pointList = data
    },
    SET_POINT_OPTIONS(state, data) {
      state.pointOptions = data
    }
  },
  actions: {}
}
