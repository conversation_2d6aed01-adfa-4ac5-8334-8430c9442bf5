<template>
  <div class="panel-outputs">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- 循环渲染DSC区域 -->
      <template v-if="dscGroups.length">
        <dsc-group
          v-for="(dscGroup, groupIndex) in dscGroups"
          :key="`dsc-group-${groupIndex}`"
          :dsc-group="dscGroup"
          :group-index="groupIndex"
          @output-action="handleOutputActionEvent"
        />
      </template>
      <!-- 空状态 -->
      <empty-state v-else />
    </div>
    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="2" />

    <!-- 编辑输出名称弹窗 -->
    <output-edit-dialog :show.sync="showEditDialog" :current-output="currentEditOutput" @confirm="handleEditConfirm" />
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import EmptyState from './components/EmptyState.vue'
import DscGroup from './components/DscGroup.vue'
import OutputEditDialog from './components/OutputEditDialog.vue'
import { mapGetters } from 'vuex'
import { handleCommonError } from '@/utils/alarmSystem'
import { setPimaOutputsName, setPimaOutputs, getPimaOutputs } from '@/api/alarmSystem'

// 开发环境模拟数据
const MOCK_DSC_GROUPS = [
  {
    id: 1,
    outputs: [
      {
        id: 1,
        name: 'output 1001',
        status: 'Off',
        isOn: false,
        showPopover: false
      },
      {
        id: 2,
        name: 'output 1002',
        status: 'Custom Status', // 测试自定义状态显示
        isOn: false,
        showPopover: false
      },
      {
        id: 3,
        name: 'output 1003',
        // 测试statusText计算属性的回退逻辑 - 没有status字段
        isOn: true,
        showPopover: false
      }
    ]
  },
  {
    id: 2,
    outputs: [
      {
        id: 4,
        name: 'output 1004',
        status: 'Off',
        isOn: false,
        showPopover: false
      },
      {
        id: 5,
        name: 'output 1005',
        status: 'Off',
        isOn: false,
        showPopover: false
      }
    ]
  }
]

export default {
  name: 'PanelOutputs',
  components: {
    AlarmBottomNavigation,
    EmptyState,
    DscGroup,
    OutputEditDialog
  },
  data() {
    return {
      showEditDialog: false,
      currentEditOutput: null,
      // 组件内数据管理
      outputs: [], // 输出设备列表
      loading: false, // 加载状态
      lastUpdated: null, // 最后更新时间
      error: null // 错误信息
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['systemType', 'canFetchPanelState', 'isPimaSystem', 'isRiscoSystem']),
    // 将outputs数据转换为DSC分组格式
    dscGroups() {
      // 开发环境下使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        return MOCK_DSC_GROUPS
      }

      if (!this.outputs.length) return []

      return this.groupOutputsBySize(this.outputs)
    }
  },
  created() {
    this.loadOutputs()
  },
  beforeDestroy() {
    // 清理组件数据和缓存状态
    this.outputs = []
    this.lastUpdated = null
    this.error = null
    this.loading = false
  },
  methods: {
    // 公共的loading处理方法
    async withLoading(asyncFn) {
      try {
        this.$loading.show()
        return await asyncFn()
      } finally {
        this.$loading.hide()
      }
    },
    // 处理来自子组件的输出操作事件
    handleOutputActionEvent(payload) {
      const { output, action } = payload
      if (action === 'rename') {
        this.handleRename(output)
      } else {
        this.handleOutputAction(output, action)
      }
    },
    // 加载outputs数据
    async loadOutputs(force = false) {
      // 智能缓存检查
      if (!force && this.shouldUseCache()) {
        return
      }

      // 权限检查
      if (!this.canFetchPanelState) {
        return
      }

      // 数据获取流程
      this.loading = true
      this.error = null

      try {
        const data = await this.fetchOutputsData()
        this.outputs = data
        this.lastUpdated = Date.now()
      } catch (error) {
        this.error = error
        this.handleLoadError(error)
      } finally {
        this.loading = false
      }
    },
    // 数据获取方法
    async fetchOutputsData() {
      if (this.isPimaSystem) {
        const response = await getPimaOutputs({ data: null })
        return this.transformPimaOutputs(response.data)
      } else if (this.isRiscoSystem) {
        // Risco系统：预留接口
        throw new Error('Risco system output control not implemented yet')
      } else {
        throw new Error(`Unsupported system type: ${this.systemType}`)
      }
    },
    // Pima数据转换方法
    transformPimaOutputs(pimaOutputs) {
      if (!Array.isArray(pimaOutputs)) {
        console.warn('Invalid Pima outputs data:', pimaOutputs)
        return []
      }

      return pimaOutputs.map((output, index) => ({
        id: output.Number !== undefined ? output.Number : index,
        name: output.Name || `Output ${output.Number !== undefined ? output.Number : index + 1}`,
        status: output.IsOpen ? 'On' : 'Off',
        isOn: output.IsOpen || false,
        isFiltered: output.IsFiltered || false,
        showPopover: false, // UI状态：是否显示下拉菜单
        systemType: 'Pima',
        rawData: output // 保留原始数据
      }))
    },
    // 智能缓存检查
    shouldUseCache() {
      if (!this.lastUpdated || !this.outputs.length) {
        return false
      }

      const now = Date.now()
      const cacheTimeout = 5 * 60 * 1000 // 5分钟
      return now - this.lastUpdated < cacheTimeout
    },
    // 处理加载错误
    handleLoadError(error) {
      console.error('Failed to load outputs:', error)
      handleCommonError(error)
    },
    // 将outputs数据按大小分组
    groupOutputsBySize(outputs) {
      if (!outputs.length) return []
      const groupSize = 6
      const groups = []
      for (let i = 0; i < outputs.length; i += groupSize) {
        groups.push({
          id: Math.floor(i / groupSize) + 1,
          outputs: outputs.slice(i, i + groupSize)
        })
      }
      return groups
    },
    // 处理输出操作（从下拉菜单）
    async handleOutputAction(output, action) {
      this.closePopover(output)
      const isActive = action === 'on'
      const actionText = isActive ? this.$t('on') : this.$t('off')

      try {
        await this.withLoading(async () => {
          await this.setOutputStatus(output, isActive)
        })
        this.$toast.success(`${output.name} ${actionText}`)
      } catch (error) {
        handleCommonError(error)
      }
    },

    // 设置输出设备状态
    async setOutputStatus(output, isActive) {
      if (this.isPimaSystem) {
        await setPimaOutputs({
          data: [
            {
              number: output.id,
              active: isActive
            }
          ]
        })
        // 直接更新本地数据
        output.isOn = isActive
        output.status = isActive ? 'On' : 'Off'
      } else if (this.isRiscoSystem) {
        // Risco系统：预留接口
        throw new Error('Risco system output control not implemented yet')
      } else {
        throw new Error(`Unsupported system type: ${this.systemType}`)
      }
    },
    // 关闭popover的公共方法
    closePopover(output) {
      output.showPopover = false
    },
    // 重命名输出
    handleRename(output) {
      this.closePopover(output)
      this.currentEditOutput = output
      this.showEditDialog = true
    },
    // 处理编辑确认事件
    async handleEditConfirm(payload) {
      const { output, newName, done } = payload
      try {
        await this.performUpdateOutput(output, newName)
        done(true)
        this.clearEditDialogState()
      } catch (error) {
        done(false)
      }
    },
    // 清除编辑弹窗状态
    clearEditDialogState() {
      this.currentEditOutput = null
    },
    // 更新输出设备名称
    async performUpdateOutput(output, newName) {
      try {
        await this.withLoading(async () => {
          await this.updateOutputName(output, newName)
        })
        this.$toast.success(this.$t('operationSuccess'))
      } catch (error) {
        handleCommonError(error)
        throw error
      }
    },
    // 更新输出设备名称
    async updateOutputName(output, newName) {
      if (this.isPimaSystem) {
        await setPimaOutputsName({
          data: [
            {
              number: output.id,
              name: newName
            }
          ]
        })
        // 直接更新本地数据
        output.name = newName
      } else if (this.isRiscoSystem) {
        // Risco系统：预留接口
        throw new Error('Risco system output name update not implemented yet')
      } else {
        throw new Error(`Unsupported system type: ${this.systemType}`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-outputs {
  height: 100%;
  display: flex;
  flex-direction: column;
  .panel-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 0;
    gap: 6px;
  }
}
</style>
