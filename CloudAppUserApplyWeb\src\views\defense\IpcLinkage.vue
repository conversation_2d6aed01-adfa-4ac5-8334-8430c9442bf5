<template>
  <div class="ipc-linkage-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="ipc-linkage-content">
      <div class="ipc-configure">
        <div class="ipc-configure-label">{{ $t('defensiveLinkageItem') }}</div>
      </div>
      <div class="ipc-linkage-list-wrapper">
        <div class="ipc-linkage-list" v-if="ipcLinkageList && ipcLinkageList.length">
          <div class="ipc-linkage-item" v-for="(item, index) in ipcLinkageList" :key="'item.value' + index">
            <div class="ipc-linkage-name text-over-ellipsis">{{ item.label }}</div>
            <van-checkbox v-model="item.flag">
              <template #icon="props">
                <img
                  class="check-img"
                  :src="
                    props.checked
                      ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check.png')
                      : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_no.png')
                  "
                />
              </template>
            </van-checkbox>
          </div>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')" />
          </div>
        </div>
      </div>
      <div class="ipc-configure ipc-desc">
        <div class="ipc-desc-label">{{ $t('defensiveDesc') }}</div>
      </div>
    </div>
    <div class="footer" v-if="ipcLinkageList && ipcLinkageList.length">
      <div class="footer-btn" @click="handleConfirm">
        {{ $t('confirm') }}
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import { mapState, mapMutations } from 'vuex'
import { transformXml } from '@/utils/common'
import { appRequestDevice } from '@/utils/appbridge'
import { addDefenseGroup, getDefenseGroupList, urlDefenseSwitchNodes } from '@/api/defense'
export default {
  name: 'IpcLinkage',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      ipcLinkageList: IPC_LINKAGE_LIST_FULLNAME(),
      linkageList: []
    }
  },
  created() {
    if (this.channelRecord && this.channelRecord.chlName) {
      this.$route.meta.title = this.channelRecord.chlName
    }
  },
  mounted() {
    if (this.channelRecord) {
      const { sn, chlIndex, extra } = this.channelRecord
      const { linkageList = [] } = extra
      this.linkageList = linkageList
      // console.log('linkageList', linkageList)
      const newIpcLinkageList = JSON.parse(JSON.stringify(this.ipcLinkageList))
      // 找到设备支持的能力集，过滤出支持的能力集选项
      const supportFun = this.capabilityObj[`${sn}~${chlIndex}`] || []
      // 根据能力集找到可以支持联动项
      const filterLinkageList = newIpcLinkageList.filter(item => supportFun.includes(item.value))
      // 遍历ipcLinkageList给flag属性赋值
      filterLinkageList.forEach(item => {
        if (linkageList.includes(item.value)) {
          item.flag = 1
        } else {
          item.flag = 0
        }
      })
      this.ipcLinkageList = filterLinkageList
    }
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('defense', ['channelRecord', 'defenseRecord', 'capabilityObj']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('defense', ['SET_CHANNEL_RECORD', 'SET_DEFENSE_RECORD', 'SET_DEFENSE_GROUP_LIST']),
    back() {
      this.$router.go(-1)
    },
    // 查询布防撤防列表
    getDefenseList() {
      getDefenseGroupList({})
        .then(({ data }) => {
          this.$loading.hide()
          const resData = data || []
          // that.dataList = resData
          this.SET_DEFENSE_GROUP_LIST(resData)
        })
        .catch(error => {
          console.error(error)
        })
    },
    async handleConfirm() {
      // 筛选出勾选和未勾选的项保存下来
      const checkIPCList = this.ipcLinkageList.filter(item => item.flag).map(item2 => item2.value)
      // 允许不勾选
      // if (checkIPCList.length === 0) {
      //   this.$toast(this.$t('pleaseChooseLinkage'))
      //   return
      // }
      const { sn, chlIndex, extra } = this.channelRecord
      // 编辑布防组
      const { id, groupName, channelList, status } = this.defenseRecord
      const newChannelList = JSON.parse(JSON.stringify(channelList))
      // 找到channelList中对应的channelRecord
      const channelItem = newChannelList.find(item => item.sn === sn && item.chlIndex === chlIndex)
      if (channelItem) {
        channelItem.extra = {
          ...extra,
          linkageList: checkIPCList
        }
      }
      // 编辑时发送请求
      if (id) {
        // 构造传参
        const params = {
          id,
          groupName,
          status,
          details: newChannelList.map(item => {
            const { sn, chlIndex, extra } = item
            return {
              sn,
              chlIndex,
              extra: JSON.stringify(extra)
            }
          })
        }
        this.$loading.show()
        try {
          const curDefenseRecord = {
            ...this.defenseRecord,
            channelList: newChannelList
          }
          const that = this
          const callback = async msg => {
            if (msg === 'SUCCESS') {
              await addDefenseGroup(params)
              // 更新defenseRecord
              this.SET_DEFENSE_RECORD(curDefenseRecord)
              // 更新当前的channelRecord
              const newChannelRecord = {
                ...this.channelRecord,
                extra: {
                  ...this.channelRecord.extra,
                  linkageList: checkIPCList
                }
              }
              this.SET_CHANNEL_RECORD(newChannelRecord)
              await this.getDefenseList()
              this.back()
            } else {
              that.$toast(this.$t('editFail'))
            }
          }
          // 发送协议到对应的设备
          this.updateDeviceStatus(curDefenseRecord, callback)
        } catch (err) {
          console.error(err)
        } finally {
          this.$loading.hide()
        }
      } else {
        // 新增时不用发送请求直接更新记录
        // 更新defenseRecord
        this.SET_DEFENSE_RECORD({
          ...this.defenseRecord,
          channelList: newChannelList
        })
        // 更新当前的channelRecord
        const newChannelRecord = {
          ...this.channelRecord,
          extra: {
            ...this.channelRecord.extra,
            linkageList: checkIPCList
          }
        }
        this.SET_CHANNEL_RECORD(newChannelRecord)
        this.back()
      }
    },
    // 更新设备布撤防状态
    async updateDeviceStatus(item, callback) {
      const { status, channelList } = item
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }
      // 勾选的联动项和未勾选的联动项发送相反的指令
      // 遍历channelList，将每个channel拆分成两份：勾选的联动项和未勾选的联动项
      const checkChannelList = []
      const noChannelList = []
      channelList.forEach(item => {
        let { extra } = item
        if (typeof extra === 'string') {
          extra = JSON.parse(extra)
        }
        const { bypassSwitch = 0, linkageList = [] } = extra
        // 根据当前设备能力集支持的所有联动项目找到未被勾选的联动项
        const noLinkageList = this.ipcLinkageList
          .filter(item => linkageList.indexOf(item.value) === -1)
          .map(item => item.value)
        // 有已勾选的联动项
        if (linkageList.length) {
          checkChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: linkageList.slice()
            }
          })
        }
        // 有未勾选的联动项
        if (noLinkageList.length) {
          noChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: noLinkageList.slice()
            }
          })
        }
      })
      const reqStatus = [0, 0] // 请求状态：0 pending 1 success 2 fail
      const reqArr = [checkChannelList, noChannelList]
      reqArr.forEach((item, index) => {
        if (item.length) {
          const { sn } = item[0]
          const req = {
            devId: sn,
            url: 'editNodeDefenseStatus',
            params: urlDefenseSwitchNodes(status, item, index) // 用index区分是勾选还是未勾选，对应的布撤防状态是相反的
          }
          const that = this
          appRequestDevice(req, function (res) {
            let resData = res.replace(/\\t|\\n/g, '')
            resData = JSON.parse(resData)
            // const errorCode = resData.code
            if (resData.code == 200) {
              const xmlObject = transformXml(resData.body)
              if (xmlObject.response.status == 'success') {
                // 处理结果
                reqStatus[index] = 1
              } else {
                reqStatus[index] = 2
              }
            } else {
              reqStatus[index] = 2
            }
            // 检查是否请求都发送完
            that.checkReqStatus(reqStatus, callback)
          })
        } else {
          // 没有联动项默认不发送请求，直接默认成功
          reqStatus[index] = 1
          // 检查是否请求都发送完
          this.checkReqStatus(reqStatus, callback)
        }
      })
    },
    // 检查请求状态 0 未全部请求完 1 全部请求成功 2 有请求失败
    checkReqStatus(reqStatus, callback) {
      if (reqStatus.indexOf(2) > -1) {
        // 有请求失败则回调失败
        callback && callback('ERROR')
      } else if (reqStatus.every(val => val === 1)) {
        // 全部请求成功才回调成功
        callback && callback('SUCCESS')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ipc-linkage-wrapper {
  height: 100%;
  overflow: hidden;
  .ipc-linkage-content {
    height: calc(100% - 120px);
    overflow: auto;
  }
  .ipc-configure,
  .ipc-desc {
    height: 32px;
    line-height: 32px;
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
  }
  .ipc-linkage-item {
    height: 40px;
    padding: 0px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:last-child {
      border: 0;
    }
    .check-img {
      width: 20px;
      height: 20px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      img {
        width: 120px;
        height: 123px;
      }
    }
  }
  .footer-btn {
    width: 343px;
    height: 46px;
    border-radius: 10px;
  }
}
</style>
