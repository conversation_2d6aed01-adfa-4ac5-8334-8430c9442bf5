import i18n from '@/lang'
import { Toast } from 'vant'
/**
 * 报警系统工具函数
 * 包含多个组件共用的状态映射、图标映射等公共功能
 */

/**
 * 获取状态对应的图标名称
 * @param {string} status - 状态字符串
 * @returns {string} - 图标路径
 */
export function getStatusIconName(status) {
  const normalizedStatus = status.toUpperCase()
  const iconMap = {
    // 基本状态
    DISARMED: 'alarm-system/disarm.png',
    ARMED_HOME: 'alarm-system/home.png',
    ARMED_AWAY: 'alarm-system/away.png',
    MIXED: 'alarm-system/mixed.png',
    // 兼容简化状态
    DISARM: 'alarm-system/disarm.png',
    HOME: 'alarm-system/home.png',
    ARM: 'alarm-system/away.png',
    ARMED: 'alarm-system/away.png',
    AWAY: 'alarm-system/away.png',
    // 其他状态
    TROUBLE: 'alarm-system/trouble.png',
    BYPASS: 'alarm-system/bypass.png',
    NOT_READY: 'alarm-system/not_ready.png',
    UNKNOWN: 'alarm-system/unknown.png'
  }
  return iconMap[normalizedStatus] || 'alarm-system/home.png'
}

/**
 * 验证面板状态数据结构
 * @param {Object} data - 面板状态数据
 * @returns {boolean} - 数据是否有效
 */
export function isValidPanelStateData(data) {
  // Pima系统：data.state.status是数组
  if (data?.state?.status && Array.isArray(data.state.status)) {
    return data.state.status.length > 0
  }
  // Risco系统：data.state.status是对象，包含systemStatus
  if (data?.state?.status && typeof data.state.status === 'object') {
    return data.state.status.systemStatus !== undefined
  }
  return false
}

/**
 * Pima系统状态码映射
 * @param {number} statusCode - Pima系统状态码
 * @returns {string} - 统一状态字符串
 */
export function mapPimaStatus(statusCode) {
  const statusMap = {
    0: 'MIXED', // Unknown/Mix Partitions
    1: 'DISARMED', // Disarm
    2: 'ARMED_AWAY', // FullArm
    3: 'ARMED_HOME', // Home1
    4: 'ARMED_HOME', // Home2
    5: 'ARMED_HOME', // Home3
    6: 'ARMED_HOME', // Home4
    7: 'ARMED_HOME', // ShabatON
    8: 'DISARMED' // ShabatOFF
  }
  return statusMap[statusCode] || 'MIXED'
}

/**
 * Risco系统状态码映射
 * 基于实际API返回数据调整状态映射
 * @param {number} statusCode - Risco系统状态码
 * @returns {string} - 统一状态字符串
 */
export function mapRiscoStatus(statusCode) {
  const statusMap = {
    0: 'DISARMED', // 撤防
    1: 'ARMED_AWAY', // 离家布防
    2: 'ARMED_HOME', // 在家布防
    3: 'ARMED_AWAY', // 全布防
    4: 'ARMED_AWAY', // 离家布防变体
    5: 'ARMED_AWAY', // 离家布防变体
    6: 'ARMED_HOME', // 在家布防变体
    8: 'DISARMED', // 撤防变体
    9: 'ARMED_AWAY', // 离家布防变体
    10: 'ARMED_HOME', // 在家布防变体
    11: 'MIXED', // 混合状态
    12: 'ARMED_HOME', // 在家布防变体
    13: 'MIXED', // 混合状态
    14: 'ARMED_HOME', // 在家布防变体
    15: 'ARMED_AWAY', // 离家布防变体
    16: 'DISARMED' // 撤防变体
  }
  return statusMap[statusCode] || 'UNKNOWN'
}

/**
 * 分析Risco系统分区状态
 * @param {Array} partitions - 分区状态数组
 * @returns {string} - 统一状态字符串
 */
export function analyzeRiscoPartitionStatus(partitions) {
  if (!Array.isArray(partitions) || partitions.length === 0) {
    return 'UNKNOWN'
  }

  // 获取所有分区的布防状态
  const armedStates = partitions.map(partition => partition.armedState)
  const uniqueStates = [...new Set(armedStates)]

  // 如果所有分区状态相同
  if (uniqueStates.length === 1) {
    return mapRiscoPartitionState(uniqueStates[0])
  }

  // 如果分区状态不同，返回混合状态
  return 'MIXED'
}

/**
 * Risco分区状态码映射
 * @param {number} armedState - 分区布防状态
 * @returns {string} - 统一状态字符串
 */
export function mapRiscoPartitionState(armedState) {
  const stateMap = {
    0: 'DISARMED', // 撤防
    1: 'ARMED_AWAY', // 离家布防
    2: 'ARMED_HOME', // 在家布防
    3: 'ARMED_AWAY' // 全布防
  }
  return stateMap[armedState] || 'UNKNOWN'
}

/**
 * 解析面板状态数据
 * 根据不同系统类型映射为统一的基本状态
 * @param {Object} data - 面板状态数据
 * @param {string} systemType - 系统类型 ('Pima' | 'Risco' | 'Tyco')
 * @returns {string|null} - 统一状态字符串或null
 */
export function parsePanelStatus(data, systemType) {
  // 验证数据结构
  if (!isValidPanelStateData(data)) {
    console.warn('Invalid panel state data structure')
    return null
  }

  // 根据系统类型进行不同的状态映射
  if (systemType === 'Pima') {
    // Pima系统：data.state.status是数组
    const statusCode = data.state.status[0]
    return mapPimaStatus(statusCode)
  } else if (systemType === 'Risco') {
    // Risco系统：直接使用系统状态
    const statusObj = data.state.status
    console.log('statusObj', statusObj)

    if (statusObj.systemStatus !== undefined) {
      return mapRiscoStatus(statusObj.systemStatus)
    }

    return 'UNKNOWN'
  } else {
    // Tyco系统：假设使用类似Risco的结构
    if (Array.isArray(data.state.status)) {
      const statusCode = data.state.status[0]
      return mapRiscoStatus(statusCode)
    } else if (data.state.status.systemStatus !== undefined) {
      return mapRiscoStatus(data.state.status.systemStatus)
    }

    return 'UNKNOWN'
  }
}

/**
 * 解析面板在线状态
 * @param {Object} data - 面板状态数据
 * @param {string} systemType - 系统类型
 * @returns {boolean} - 在线状态
 */
export function parsePanelOnlineStatus(data, systemType) {
  if (!data?.state) {
    return false
  }
  if (systemType === 'Risco') {
    // Risco系统都使用isOnline字段
    if (data.state.isOnline !== undefined) {
      return Boolean(data.state.isOnline)
    }
  } else {
    // 其余系统暂时使用isOnline字段--根据实际情况调整
    if (data.state.isOnline !== undefined) {
      return Boolean(data.state.isOnline)
    }
  }

  // 默认返回false
  return false
}

/**
 * 获取状态的国际化文本
 * @param {string} status - 状态字符串
 * @returns {string} - 国际化key
 */
export function getStatusI18nKey(status) {
  const statusMap = {
    DISARMED: 'disarm',
    ARMED_HOME: 'home',
    ARMED_AWAY: 'away',
    MIXED: 'mixed',
    UNKNOWN: 'unknownEvent',
    TROUBLE: 'faults'
  }
  return statusMap[status] || 'unknownEvent'
}

/**
 * 将Pima系统状态数据转换为统一格式
 * @param {Object} pimaData - Pima系统原始状态数据
 * @returns {Object} 转换后的统一格式数据
 */
export function transformPimaStatusData(pimaData) {
  if (!pimaData) {
    return { state: { isOnline: false } }
  }

  return {
    // 使用面板整体状态而非分区状态
    state: {
      status: [pimaData.status], // 转换为数组格式以保持一致性
      isOnline: true, // Pima系统能获取到数据说明在线
      media: 1 // 假设有连接
    },
    partitions: pimaData.partitions || [],
    // 保留原始数据
    originalData: pimaData,
    systemType: 'Pima'
  }
}

// 统一的错误处理
export function handleCommonError(error) {
  const errorMessage = error.code ? i18n.t(error.code) : error.message
  Toast.fail(errorMessage)
  // 记录详细错误信息用于调试
  console.error('API Error Details:', error)
}
