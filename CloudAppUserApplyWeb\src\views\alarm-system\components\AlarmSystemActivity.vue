<template>
  <div class="alarm-box-wrapper alarm-system-activity">
    <div class="activity-header">
      <div class="activity-title">{{ $t('latestActivity') }}</div>
      <theme-image
        class="expand-icon"
        :class="{ expanded: isExpanded }"
        imageName="arrow_right.png"
        alt="expand"
        @click="handleExpandClick"
      />
    </div>
    <div class="activity-content" :class="{ expanded: isExpanded }">
      <div v-if="!fetchedActivities.length && !activities.length" class="no-data-box">
        <span class="no-data-text">{{ $t('noData') }}</span>
      </div>
      <!-- 活动列表 -->
      <template v-else>
        <div
          v-for="activity in displayActivities"
          :key="activity.id"
          class="alarm-bottom-box activity-item"
          @click="handleActivityClick(activity)"
        >
          <div class="activity-icon">
            <theme-image class="activity-indicator" :imageName="getActivityIcon(activity.type)" alt="activity" />
          </div>
          <div class="activity-details">
            <div class="activity-description">{{ activity.description }}</div>
            <div class="alarm-sub-text activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getEventLog, getPimaLog } from '@/api/alarmSystem'

// 事件类型映射表常量
const EVENT_TYPE_MAPPINGS = [
  { keywords: ['disarm'], type: 'disarm' },
  { keywords: ['arm'], type: 'arm' },
  { keywords: ['door'], type: 'door' },
  { keywords: ['motion'], type: 'motion' },
  { keywords: ['window'], type: 'window' },
  { keywords: ['fire'], type: 'fire' },
  { keywords: ['panic'], type: 'panic' }
]

// 活动图标映射表常量
const ACTIVITY_ICON_MAP = {
  disarm: 'alarm-system/disarm.png',
  arm: 'alarm-system/away.png',
  door: 'alarm-system/door.png',
  motion: 'alarm-system/motion.png',
  window: 'alarm-system/window.png',
  fire: 'alarm-system/fire.png',
  panic: 'alarm-system/panic.png',
  activity: 'alarm-system/activity.png'
}

export default {
  name: 'AlarmSystemActivity',
  props: {
    // 手动传入的活动数据（向后兼容）
    activities: {
      type: Array,
      default: () => []
    },
    // 是否自动获取数据
    autoFetch: {
      type: Boolean,
      default: true
    },
    // 显示的活动数量限制
    minItems: {
      type: Number,
      default: 0
    },
    // 显示的活动数量限制
    maxItems: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      fetchedActivities: [],
      isExpanded: true
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['siteId', 'sessionId', 'systemType', 'isPimaSystem']),
    // 判断是否有足够的数据进行API调用
    canFetchActivities() {
      return this.siteId && this.sessionId
    },
    // 显示的活动列表
    displayActivities() {
      let activities = []
      // 优先使用手动传入的数据
      if (this.activities.length) {
        activities = this.activities
      } else if (this.canFetchActivities) {
        // 使用获取的真实数据
        activities = this.fetchedActivities
      }
      // 根据展开状态决定显示数量
      if (this.isExpanded) {
        // 展开时显示所有活动（最多maxItems个）
        return activities.slice(0, this.maxItems)
      } else {
        // 收缩时显示三条
        return activities.slice(0, this.minItems)
      }
    }
  },
  created() {
    if (this.autoFetch && this.canFetchActivities) {
      this.loadActivities()
    }
  },
  methods: {
    // 获取Activity数据
    async loadActivities() {
      try {
        await this.fetchSystemActivities()
      } catch (error) {
        console.error('Failed to load activities:', error)
        this.fetchedActivities = []
      }
    },
    // 根据系统类型分发到具体执行器
    async fetchSystemActivities() {
      if (!this.canFetchActivities) {
        console.warn('Missing siteId or sessionToken for activities fetch')
        return
      }
      if (this.isPimaSystem) {
        await this.fetchPimaActivities()
      } else {
        await this.fetchRiscoActivities()
      }
    },
    // Risco系统API调用
    async fetchRiscoActivities() {
      try {
        const response = await getEventLog(this.siteId, {
          offset: 0,
          count: this.maxItems
        })

        // 增强类型检查
        const eventsList = response?.response?.controlPanelEventsList
        if (Array.isArray(eventsList)) {
          this.fetchedActivities = this.transformRiscoEventData(eventsList)
        } else {
          console.warn('fetchRiscoActivities: Invalid response format', response)
          this.fetchedActivities = []
        }
      } catch (error) {
        console.error('fetchRiscoActivities failed:', error)
        this.fetchedActivities = []
      }
    },
    // Pima系统API调用
    async fetchPimaActivities() {
      try {
        const response = await getPimaLog({ data: null })
        this.fetchedActivities = this.transformPimaData(response.data || [])
      } catch (error) {
        console.error('fetchPimaActivities failed:', error)
        this.fetchedActivities = []
      }
    },
    // 转换Risco日志数据
    transformRiscoEventData(events) {
      return events.map((event, index) => ({
        id: event.lineNumber || index,
        description: this.formatEventDescription(event),
        time: this.formatEventTime(event.logTime),
        type: this.getEventTypeFromText(event.eventName || event.eventText),
        eventName: event.eventName,
        zone: this.extractZone(event.eventText),
        rawData: event
      }))
    },
    // 转换Pima日志数据
    transformPimaData(logLines) {
      // 确保logLines是数组
      if (!Array.isArray(logLines)) {
        console.warn('transformPimaData: logLines is not an array', logLines)
        return []
      }

      return logLines.map((line, index) => {
        // 确保line是字符串
        const logLine = typeof line === 'string' ? line : String(line || '')
        const timeMatch = logLine.match(/(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2})/)
        const timestamp = timeMatch?.[1] || new Date().toISOString()

        return {
          id: index,
          description: this.extractPimaDescription(logLine) || this.$t('activity'),
          time: this.formatEventTime(timestamp),
          type: this.getEventTypeFromText(logLine),
          rawData: { logLine }
        }
      })
    },
    // 提取Pima描述信息
    extractPimaDescription(logLine) {
      if (!logLine) return this.$t('unknownActivity')
      // 根据Pima日志格式提取有意义的描述
      return logLine.trim()
    },
    // 事件类型识别方法
    getEventTypeFromText(text) {
      if (!text) return 'activity'
      const normalizedText = text.toLowerCase()
      for (const mapping of EVENT_TYPE_MAPPINGS) {
        if (mapping.keywords.some(keyword => normalizedText.includes(keyword))) {
          return mapping.type
        }
      }
      return 'activity'
    },
    // 格式化事件描述
    formatEventDescription(event) {
      const eventText = event.eventText || event.eventName || this.$t('unknownEvent')
      const eventType = this.getEventTypeFromText(eventText)

      // 根据识别的事件类型生成友好的描述
      switch (eventType) {
        case 'disarm':
          return this.$t('systemDisarmed')
        case 'arm':
          return this.$t('systemArmed')
        case 'door':
          return `${this.$t('doorActivity')} - ${this.extractZone(eventText)}`
        case 'motion':
          return `${this.$t('motionDetected')} - ${this.extractZone(eventText)}`
        case 'window':
          return `${this.$t('windowActivity')} - ${this.extractZone(eventText)}`
        case 'fire':
          return `${this.$t('fire')} - ${this.extractZone(eventText)}`
        case 'panic':
          return `${this.$t('panic')} - ${this.extractZone(eventText)}`
        default:
          return eventText
      }
    },
    // 格式化事件时间
    formatEventTime(logTime) {
      if (!logTime) return ''

      try {
        // 确保logTime是有效的日期格式
        const date = new Date(logTime)
        if (isNaN(date.getTime())) {
          console.warn('formatEventTime: Invalid date format', logTime)
          return String(logTime)
        }

        return this.$moment ? this.$moment(date).format('YYYY-MM-DD HH:mm:ss') : date.toLocaleString()
      } catch (error) {
        console.error('formatEventTime error:', error)
        return String(logTime)
      }
    },

    // 提取区域信息
    extractZone(eventText) {
      if (!eventText) return this.$t('unknownZone')
      // 尝试提取位置信息
      const match = eventText.match(/([A-Za-z\s]+Door|[A-Za-z\s]+Window|[A-Za-z\s]+Room|Zone\s*\d+)/i)
      return match ? match[1].trim() : 'System'
    },
    // 获取活动图标
    getActivityIcon(type) {
      return ACTIVITY_ICON_MAP[type] || ACTIVITY_ICON_MAP.activity
    },
    // 处理活动点击
    handleActivityClick(activity) {
      this.$emit('activity-click', activity)
    },
    // 处理展开按钮点击
    handleExpandClick() {
      // 切换展开状态
      this.isExpanded = !this.isExpanded
      // 如果展开且需要更多数据，则获取更多活动
      if (this.isExpanded && this.autoFetch && this.canFetchActivities) {
        this.loadActivities()
      }
      // 发送展开状态变化事件给父组件
      this.$emit('expand-click', this.isExpanded)
    },
    // 手动刷新活动数据
    refresh() {
      return this.loadActivities()
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-activity {
  padding: 0 16px;
  box-sizing: border-box;
  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    .activity-title {
      font-size: 16px;
      font-weight: 500;
    }
    .expand-icon {
      width: 24px;
      height: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      &.expanded {
        transform: rotate(90deg);
      }
    }
  }
  .activity-content {
    .no-data-box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
      .no-data-text {
        font-size: 12px;
      }
    }
    .activity-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      &:last-child {
        border-bottom: none;
      }
      .activity-icon {
        margin-right: 12px;
        .activity-indicator {
          width: 32px;
          height: 32px;
        }
      }
      .activity-details {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        .activity-description {
          min-height: 18px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 5px;
        }
        .activity-time {
          height: 18px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
