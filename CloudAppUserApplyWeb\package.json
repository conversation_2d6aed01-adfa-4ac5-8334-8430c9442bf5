{"name": "superlive-app-web", "version": "1.0.0", "description": "superlive-app-web with <PERSON><PERSON> Vant UI", "author": "<EMAIL>", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "vue-cli-service serve", "build": "vue-cli-service build", "stage": "vue-cli-service build --mode staging", "lint": "vue-cli-service lint", "deps": "yarn upgrade-interactive --latest", "prepare": "cd .. && husky install .husky", "lint-staged": "lint-staged"}, "lint-staged": {"*.(js|vue)": ["eslint"]}, "dependencies": {"@vue-office/docx": "^0.2.4", "@vue-office/excel": "^0.2.10", "@vue-office/pdf": "^0.2.5", "ali-oss": "^6.1.1", "alloyfinger": "^0.1.16", "amfe-flexible": "^2.2.1", "aws-sdk": "^2.1012.0", "axios": "^1.3.4", "better-scroll": "^2.4.2", "core-js": "^3.23.3", "crypto-js": "^4.2.0", "emoji-regex": "^10.4.0", "file-loader": "^6.2.0", "js-md5": "^0.7.3", "node-polyfill-webpack-plugin": "^2.0.1", "regenerator-runtime": "^0.13.5", "sha1": "^1.1.1", "tvtcloudcountrycode": "^0.0.4", "url-loader": "^4.1.1", "vant": "^2.12.48", "vconsole": "^3.15.0", "vue": "^2.7.8", "vue-demi": "^0.13.11", "vue-i18n": "^8.25.1", "vue-router": "^3.5.4", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.18.10", "@babel/eslint-parser": "^7.18.2", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.5", "babel-plugin-transform-remove-console": "^6.9.4", "browserslist": "^4.22.2", "caniuse-lite": "^1.0.30001572", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.2.0", "husky": "^8.0.3", "lint-staged": "13.1.0", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.7.1", "sass": "^1.54.4", "sass-loader": "^13.0.2", "webpack-bundle-analyzer": "^4.5.0"}}