<template>
  <div class="device-camera-wrapper">
    <div class="choose-device-content">
      <van-collapse v-model="activeNames">
        <!-- 一级：设备 -->
        <van-collapse-item
          v-for="(item, index) of deviceChannelList"
          :title="item.devName"
          :name="item.sn"
          :key="`${item.sn}~${index}`"
        >
          <!-- 一级：设备左侧勾选 -->
          <template #title>
            <div class="device-collapse-box">
              <van-checkbox
                v-if="['add', 'check'].includes(type)"
                :name="`${item.sn}~device`"
                :ref="`${item.sn}~device`"
                v-model="deviceChecked[`${item.sn}`]"
                label-disabled
                @click.native="e => toggleDevice(e, `${item.sn}`, item)"
              ></van-checkbox>
              <div class="device-icon">
                <theme-image alt="nvr" imageName="nvr.png" />
              </div>
              <div :class="[['add', 'check'].includes(type) ? 'device-collapse-body-check' : 'device-collapse-body']">
                <div class="device-collapse-title">{{ item.deviceName }}</div>
                <div
                  v-if="
                    ['permission', 'detail', 'edit'].includes(type) && devSupportFunObj[item.sn]?.includes('shareDevOp')
                  "
                  class="device-collapse-capability"
                >
                  {{ devOperationObj[item.sn] ? $t('deviceOperation') : $t('none') }}
                </div>
              </div>
              <div class="device-share-over-tooltip" v-if="shareOverTen">
                {{ $t('shareOverTenDesc') }}
              </div>
            </div>
          </template>
          <template v-if="['permission', 'detail', 'edit'].includes(type)" #right-icon>
            <div
              v-if="devSupportFunObj[item.sn]?.includes('shareDevOp')"
              class="channel-more-box"
              @click.stop="e => updateOperation(e, item)"
            >
              <theme-image class="more-icon" alt="more" imageName="more.png" />
            </div>
            <div v-else></div>
          </template>
          <!-- 二级：通道勾选 -->
          <van-checkbox-group v-model="result[`${item.sn}`]" :ref="`${item.sn}~checkboxGroup`">
            <van-cell-group>
              <van-cell
                v-for="(item2, index2) of item.children"
                :clickable="['add', 'check', 'apply'].includes(type)"
                :key="`${item.sn}~${item2.chlIndex}`"
                :title="`${item2.chlName}`"
                :class="channelCheckedSet.has[`${item.sn}~${item2.chlIndex}}`] ? 'van-cell-checked' : 'van-cell-normal'"
                @click.stop.native="e => toggle(e, `${item.sn}~${item2.chlIndex}~${index2}`, item, item2)"
              >
                <template #title>
                  <div :class="['channel-collapse-box', channelDisabled ? 'channel-disabeld-box' : '']">
                    <van-checkbox
                      v-if="['add', 'check', 'apply'].includes(type)"
                      :name="`${item.sn}~${item2.chlIndex}`"
                      :ref="`${item.sn}~${item2.chlIndex}~${index2}`"
                      :disabled="channelDisabled"
                    />
                    <div class="channel-icon">
                      <theme-image alt="camera" imageName="camera.png" />
                    </div>
                    <div class="channel-collapse-title">
                      <div class="channel-title">{{ item2.chlName }}</div>
                      <div
                        class="channel-capability"
                        v-if="
                          ['detail', 'permission', 'edit', 'check'].includes(type) &&
                          channelObj[`${item.sn}~${item2.chlIndex}`] &&
                          channelObj[`${item.sn}~${item2.chlIndex}`].checkCapability
                        "
                      >
                        <span
                          v-for="(item3, index3) of channelObj[`${item.sn}~${item2.chlIndex}`].checkCapability"
                          :key="item3.value"
                          class="channel-capability-item"
                        >
                          <span>{{ item3.label }}</span>
                          <span
                            class="separator-box"
                            v-if="index3 < channelObj[`${item.sn}~${item2.chlIndex}`].checkCapability.length - 1"
                          ></span>
                        </span>
                      </div>
                    </div>
                  </div>
                </template>
                <template #right-icon v-if="['permission', 'edit', 'apply'].includes(type)">
                  <div class="channel-more-box" @click="e => handleMore(e, item2)">
                    <theme-image class="more-icon" alt="more" imageName="more.png" />
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </van-collapse-item>
      </van-collapse>
    </div>
    <van-popup v-model="showCapabilityOptions" round position="bottom">
      <choose-capability
        v-model="capabilitys"
        :options="capabilityOptions"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
    </van-popup>
    <van-popup v-model="showOperaton" round position="bottom">
      <choose-operation v-model="operationChecked" @cancel="handleOperationCancel" @confirm="handleOperationConfirm" />
    </van-popup>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ChooseCapability from './ChooseCapability.vue'
import ChooseOperation from './ChooseOperation.vue'
import { CHANNEL_CAPABILITY_LIST } from '@/utils/options'
import { getChannelDetail } from '@/api/share'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'ChooseChannel',
  components: {
    ChooseCapability,
    ThemeImage,
    ChooseOperation
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      // add表示新增通道（勾选和没有权限编辑）permission表示编辑权限 detail表示查看 edit表示编辑权限 check表示勾选  apply是分享申请（设备不勾选，通道可勾选，权限可编辑）
      default: () => 'add'
    },
    deviceChannelList: {
      type: Array,
      default: () => []
    },
    // 通道是否禁用--设备分享申请时使用
    channelDisabled: {
      type: Boolean,
      default: false
    },
    // 设备是否分享超过10个用户--设备分享申请时使用
    shareOverTen: {
      type: Boolean,
      default: false
    },
    // 设备支持的能力集
    devSupportFunObj: {
      type: Object,
      default: () => {}
    },
    // 设备勾选操作权限
    devOperationObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      chooseCameras: [], // 选中的通道
      activeNames: [], // 打开的折叠面板
      deviceChecked: {}, // 设备是否勾选
      channelCheckedSet: new Set(), // 通道勾选集合
      channelObj: {}, // 通道的对象--包含勾选的能力集等信息
      result: {}, // 通道选择结果
      showCapabilityOptions: false, // 是否展示权限勾选弹窗
      record: {}, // 当前操作权限的通道
      capabilityOptions: [], // 某个通道的所有权限列表选项
      capabilitys: [], // 某个通道勾选的权限列表
      showOperaton: false, // 是否展示操作权限
      operationChecked: false,
      currentDevice: {} // 当前操作的设备
    }
  },
  created() {
    // 首次进入清除选中的设备
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  watch: {
    value: {
      handler(val, oldVal) {
        if (val && JSON.stringify(val) !== JSON.stringify(oldVal)) {
          // 根据val的值变化更新选中结果
          const deviceChecked = { ...this.deviceChecked }
          let result = { ...this.result }
          const channelCheckedSet = new Set(Array.from(this.channelCheckedSet))
          val.forEach(item => {
            const { sn, chlIndex } = item
            deviceChecked[sn] = true
            // 更新result
            const checkKeyArr = result[`${sn}`] || []
            const checkArr = checkKeyArr.filter(item => item !== `${sn}~${chlIndex}`)
            result = {
              ...this.result,
              [`${sn}`]: [...checkArr, `${sn}~${chlIndex}`]
            }
            channelCheckedSet.add(`${item.sn}~${item.chlIndex}`)
          })
          this.deviceChecked = deviceChecked
          this.result = result
          console.log('result', result)
          this.channelCheckedSet = channelCheckedSet
        }
      },
      immediate: true
    },
    deviceChannelList: {
      handler(val) {
        if (val && val.length) {
          // 遍历设备通道树，找到每个通道的勾选的能力集
          const channelObj = {}
          val.forEach(item => {
            const { sn, children = [] } = item
            children.forEach(item2 => {
              const { chlIndex, checkCapability = [] } = item2
              channelObj[`${sn}~${chlIndex}`] = {
                ...item2,
                checkCapability: checkCapability.slice()
              }
            })
          })
          this.channelObj = channelObj

          if (['permission', 'edit'].includes(this.type)) {
            this.activeNames = val.map(item => item.sn)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    // 全部勾选
    selectAll(flag) {
      // 勾选所有的设备
      console.log('进入这里', flag)
      const deviceChecked = {}
      const result = {}
      this.deviceChannelList.forEach(item => {
        const { sn } = item
        const key = `${item.sn}~device`
        console.log('item', item, this.$refs[key])
        // 触发设备勾选
        deviceChecked[sn] = flag
        // 触发通道勾选
        result[sn] = flag
          ? item.children.map(item2 => {
              const { chlIndex } = item2
              return `${sn}~${chlIndex}`
            })
          : []
      })
      this.deviceChecked = deviceChecked
      this.result = result
      this.$nextTick(() => {
        // 将选中的通道传递出去
        this.emitCheckChannel()
      })
    },
    // 设备勾选
    toggleDevice(e, key, deviceRecord) {
      // 如果设备下的checkboxGroup已经展开并渲染了，则阻止默认事件
      if (this.$refs[`${key}~checkboxGroup`] && this.$refs[`${key}~checkboxGroup`][0]) {
        e.stopPropagation()
      } else {
        // 否则往上传递
      }
      // 禁止变化设备勾选状态
      if (['permission'].includes(this.type)) {
        return
      }
      // console.log('flag', flag, 'key', key)
      const { children = [] } = deviceRecord
      const channelCheckedSet = new Set(Array.from(this.channelCheckedSet))
      // 判断是否全选
      if (this.deviceChecked[key]) {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: false
        }
        // 直接清空勾选
        this.result = {
          ...this.result,
          [key]: []
        }
        children.forEach(item => channelCheckedSet.delete(`${item.sn}~${item.chlIndex}`))
      } else {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: true
        }
        // 直接把所有都勾选上
        this.result = {
          ...this.result,
          [key]: children.map(item => `${item.sn}~${item.chlIndex}`)
        }
        children.forEach(item => channelCheckedSet.add(`${item.sn}~${item.chlIndex}`))
      }
      this.channelCheckedSet = channelCheckedSet
      this.$nextTick(() => {
        // 将选中的通道传递出去
        this.emitCheckChannel()
      })
    },
    // 通道勾选
    toggle(e, refKey, deviceRecord, channelRecord) {
      // 阻止冒泡事件
      e.stopPropagation()
      // 禁止变化设备勾选状态
      if (['permission', 'detail'].includes(this.type)) {
        return
      }
      if (this.type === 'apply' && this.channelDisabled) {
        return
      }

      // console.log('refKey', refKey, 'this.$refs', this.$refs, 'this.$refs[refKey]', this.$refs[refKey])
      const { children = [] } = deviceRecord
      const { sn, chlIndex } = channelRecord
      const checkSet = new Set(this.result[`${sn}`])
      const channelCheckedSet = new Set(Array.from(this.channelCheckedSet))
      if (checkSet.has(`${sn}~${chlIndex}`)) {
        // 说明是去勾选
        this.$refs[refKey][0].toggle(false)
        checkSet.delete(`${sn}~${chlIndex}`)
        channelCheckedSet.delete(`${sn}~${chlIndex}`)
        // 更新result
        const checkKeyArr = this.result[`${sn}`] || []
        const checkArr = checkKeyArr.filter(item => item !== `${sn}~${chlIndex}`)
        this.result = {
          ...this.result,
          [`${sn}`]: [...checkArr]
        }
      } else {
        // 表示是勾选
        this.$refs[refKey][0].toggle(true)
        checkSet.add(`${sn}~${chlIndex}`)
        channelCheckedSet.add(`${sn}~${chlIndex}`)
        // 更新result
        const checkKeyArr = this.result[`${sn}`] || []
        const checkArr = checkKeyArr.filter(item => item !== `${sn}~${chlIndex}`)
        this.result = {
          ...this.result,
          [`${sn}`]: [...checkArr, `${sn}~${chlIndex}`]
        }
      }
      this.channelCheckedSet = channelCheckedSet
      this.$nextTick(() => {
        // 判断是否全部勾选了
        if (children.every(item => checkSet.has(`${item.sn}~${item.chlIndex}`))) {
          // 全部在结果里面，则勾选上设备
          this.deviceChecked = {
            ...this.deviceChecked,
            [`${sn}`]: true
          }
        } else {
          // 去除设备勾选
          this.deviceChecked = {
            ...this.deviceChecked,
            [`${sn}`]: false
          }
        }
        // 将选中的通道传递出去
        this.emitCheckChannel()
      })
    },
    // 根据result获取选择的通道
    emitCheckChannel() {
      const channelKeyList = Object.values(this.result).reduce((pre, next) => {
        pre = pre.concat(next)
        return pre
      }, [])
      // 把添加的设备传入store
      const addChannelList = channelKeyList.map(key => ({ ...this.channelObj[key] }))
      // 发送input更新v-model
      this.$emit('input', addChannelList)
      // 发送select通知外部勾选变化
      this.$emit('select', addChannelList)
    },
    async handleMore(e, record) {
      e.stopPropagation() // 阻止冒泡
      if (this.type === 'apply' && this.channelDisabled) {
        return
      }
      // 没有通道的全量权限勾选则需要请求获取通道详情，拿到能力集再过滤通道权限选项
      if (!record.capabilityOptions) {
        // 请求通道详情，获取能力集确定权限勾选列表
        try {
          const { sn, chlIndex } = record
          const res = await getChannelDetail({ sn, chlIndex })
          const { capability } = res
          // 找到能力集
          let supportFun = []
          if (capability) {
            const capabilityObj = JSON.parse(capability)
            supportFun = capabilityObj.supportFun
          }
          // 过滤出通道支持的能力
          const capabilityOptions = this.channelCapabilitys.filter(item => {
            if (item.filterAble) {
              return !!supportFun.includes(item.supportAuth)
            }
            return true
          })
          // 通道能力集选项及勾选的能力集
          record.capabilityOptions = capabilityOptions.slice()
        } catch (err) {
          console.error(err)
        }
      }
      this.showCapabilityOptions = true
      this.record = { ...record }
      const { sn, chlIndex, capabilityOptions = [] } = record
      const { checkCapability = [] } = this.channelObj[`${sn}~${chlIndex}`]
      this.capabilityOptions = [...capabilityOptions]
      console.log('capabilityOptions', this.capabilityOptions)
      this.capabilitys = checkCapability.map(item => item.value)
      // this.$toast('点击更多')
    },
    async updateOperation(e, record) {
      e.stopPropagation() // 阻止冒泡
      if (!['permission', 'edit'].includes(this.type) || this.channelDisabled) {
        return
      }
      this.operationChecked = Boolean(this.devOperationObj[record.sn])
      this.currentDevice = { ...record }
      this.showOperaton = true
    },
    handleCancel() {
      this.showCapabilityOptions = false
      this.capabilityOptions = []
      this.capabilitys = []
    },
    handleOperationCancel() {
      this.showOperaton = false
      this.operationChecked = false
    },
    handleOperationConfirm() {
      this.showOperaton = false
      const { sn } = this.currentDevice

      this.$emit('updateDevOperation', {
        sn,
        operationChecked: this.operationChecked
      })
      this.operationChecked = false
    },
    // 更新权限
    updateCapability() {
      const { sn, chlIndex, capabilityOptions = [] } = this.record
      const checkCapability = capabilityOptions.filter(item => this.capabilitys.includes(item.value))
      this.channelObj = {
        ...this.channelObj,
        [`${sn}~${chlIndex}`]: {
          ...this.channelObj[`${sn}~${chlIndex}`],
          checkCapability
        }
      }
      this.record = { ...this.record, checkCapability }
      this.capabilityOptions = []
      this.capabilitys = []
      this.record = {}
      this.$nextTick(() => {
        // 将选中的通道传递出去
        this.emitCheckChannel()
      })
    },
    handleConfirm() {
      this.showCapabilityOptions = false
      const { capabilityOptions = [] } = this.record
      const callback = msg => {
        if (msg === 'success') {
          this.updateCapability()
        }
      }
      if (['edit', 'apply'].includes(this.type)) {
        // 编辑权限时将当前记录传递出去，方便外层更新数据
        this.$emit(
          'change',
          {
            ...this.record,
            checkCapability: capabilityOptions.filter(item => this.capabilitys.includes(item.value))
          },
          callback
        )
      } else {
        // this.channelObj = {
        //   ...this.channelObj,
        //   [`${sn}~${chlIndex}`]: {
        //     ...this.channelObj[`${sn}~${chlIndex}`],
        //     checkCapability: capabilityOptions.filter(item => this.capabilitys.includes(item.value))
        //   }
        // }
        // this.capabilityOptions = []
        // this.capabilitys = []
        // this.record = {}
        // this.$nextTick(() => {
        //   // 将选中的通道传递出去
        //   this.emitCheckChannel()
        // })
        this.updateCapability()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.device-camera-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  .device-collapse-box {
    width: calc(100vw - 40px);
    display: inline-flex;
    align-items: center;
    overflow: hidden;
    position: relative;
    height: 40px;
    .device-icon {
      width: 24px;
      height: 24px;
      margin: 0px 6px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .device-collapse-body {
      width: calc(100% - 40px);
    }
    .device-collapse-body-check {
      width: calc(100% - 60px);
    }
    .device-collapse-title {
      width: 100%;
      font-size: var(--font-size-body1-size, 16px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .device-collapse-capability {
      font-size: var(--font-size-text-size, 12px);
      color: var(--text-color-placeholder, #a3a3a3);
    }
    .device-share-over-tooltip {
      width: 100%;
      position: absolute;
      left: 5px;
      top: 23px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: var(--font-size-text-size, 12px);
      color: var(--error-bg-color-default, #ff0000);
    }
  }
  .channel-collapse-box {
    width: 100%;
    display: inline-flex;
    align-items: center;
    padding-left: 20px;
    box-sizing: border-box;
    .channel-icon {
      width: 24px;
      height: 24px;
      margin: 0px 6px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .channel-collapse-title {
      flex: 1;
      font-size: var(--font-size-body2-size, 14px);
      .channel-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 22px;
      }
      .channel-capability {
        width: 100%;
        overflow: hidden;
        font-size: var(--font-size-text-size, 12px);
        line-height: 18px;
      }
      .channel-capability-item {
        display: inline-flex;
        align-items: center;
      }
      .separator-box {
        display: inline-block;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        margin: 0px 4px;
      }
    }
  }
  .channel-more-box {
    width: 24px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    .more-icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
<style lang="scss">
.device-camera-wrapper {
  .van-collapse-item__content {
    padding: 2px 8px !important;
  }
  .van-cell {
    height: 60px;
    padding: 10px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
  }
  .van-cell__value {
    right: -15px;
    width: min-content;
    display: flex;
    justify-content: flex-end;
    .van-checkbox {
      justify-content: flex-end;
    }
  }
  .van-checkbox__icon {
    font-size: var(--font-size-body1-size, 16px);
    width: 20px;
  }
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
}
</style>
