<template>
  <div class="trusteeship-success-wrapper">
    <div class="trusteeship-success-content">
      <div class="trusteeship-success-title">{{ $t('trusteeshipSuccess') }}</div>
      <div class="trusteeship-success-text">
        <div class="trusteeship-success-desc">{{ $t('trusteeshipToUser') }}</div>
        <div class="trusteeship-success-desc">{{ sharer }}</div>
      </div>
      <div class="trusteeship-device-img">
        <theme-image alt="shareDevice" imageName="device_success.png" />
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('finish') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { gotoPage } from '@/utils/appbridge'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'TrusteeshipSuccess',
  components: {
    ThemeImage
  },
  props: {},
  data() {
    return {
      sharer: '' // 分享人的联系方式 邮箱或手机
    }
  },
  created() {},
  mounted() {},
  computed: {
    ...mapState('share', ['shareUser'])
  },
  methods: {
    // 确定
    handleConfirm() {
      // 跳转到服务页面
      gotoPage({
        pageRoute: 'service/home'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-success-wrapper {
  height: 100%;
  overflow: hidden;
  .trusteeship-success-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 124px 36px 0px 36px;
    .trusteeship-success-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
    .trusteeship-success-text {
      width: 100%;
      margin: 16px 0px;
    }
    .trusteeship-success-desc {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .trusteeship-device-img {
      width: 100%;
      text-align: center;
    }
  }
}
</style>
