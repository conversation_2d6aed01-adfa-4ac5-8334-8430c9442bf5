<template>
  <div class="one-click-defense">
    <van-dialog
      v-model="show"
      :title="defenseTitle[defenseType]"
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="defense-list-content">
        <div class="defense-list-line" v-for="(item, index) of dataList" :key="index">
          <div class="defense-line-name">{{ item.groupName }}</div>
          <div class="defense-line-status">
            <span v-if="item.reqStatus !== 0" :style="`color: ${DEFENSE_STATUS_COLOR[item.reqStatus || 1]}`">{{
              statusNameObj[item.reqStatus]
            }}</span>
            <img v-else class="defense-loading-img" :src="require('@/assets/img/common/defense/loading.gif')" />
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { DEFENSE_STATUS_COLOR } from '@/utils/options.js'
import { mapState } from 'vuex'
export default {
  props: {
    defenseType: {
      type: Number,
      dafault: 1
    },
    reqStatusList: {
      type: Array,
      default: () => []
    }
  },
  components: {},
  data() {
    return {
      DEFENSE_STATUS_COLOR,
      show: false,
      dataList: [],
      // 0为一键撤防 1表示外出布防 2表示在家布防 3表示一键消警
      defenseTitle: {
        0: this.$t('oneClickDisarm'),
        1: this.$t('outDefense'),
        2: this.$t('homeDefense'),
        3: this.$t('oneClickRemoval')
      },
      statusNameObj: {
        0: this.$t('pending'),
        1: this.$t('reqSuccess'),
        2: this.$t('reqFail')
      }
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType'])
  },
  watch: {
    reqStatusList: {
      handler(val) {
        if (JSON.stringify(val) !== JSON.stringify(this.dataList)) {
          this.dataList = JSON.parse(JSON.stringify(val))
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      this.$emit('confirm', {})
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel', {})
    },
    // 清除数据
    clearParam() {}
  }
}
</script>
<style lang="scss" scoped>
.one-click-defense {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--icon-color-primary, #393939);
  }
  .defense-list-content {
    width: 100%;
    max-height: 500px;
    padding: 10px;
    overflow: auto;
    box-sizing: border-box;
    font-size: var(--font-size-text-size, 12px);
    .defense-list-line {
      width: 100%;
      height: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 5px 0px;
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
      .defense-loading-img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
