@import './checkUserPwd.scss';
@import './upgrade.scss';
@import './navBar.scss';
@import './trusteeship/myInstaller.scss';
@import './trusteeship/deviceDetails.scss';
@import './trusteeship/check.scss';
@import './ipc-upgrade/index.scss';
@import './transfer/transferRequest.scss';
@import './share/shareManage.scss';
@import './defense/index.scss';
@import './line-bind/index.scss';
@import './household-management/householdManagement.scss';

html,
body,
#app {
  background-color: $USE44-background-color;
  color: $USE44-90-black-color;
}

// 根据主题 全局 修改vant样式
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $USE44-color-primary;
}

.van-button--primary {
  color: $USE44-font-color;
  background-color: $USE44-color-primary;
  border: 1px solid $USE44-color-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $USE44-90-black-color;
}

.van-dialog__message {
  color: $USE44-white-color;
}

.van-dialog__cancel,.van-dialog__confirm{
  color:$USE44-90-black-color;
}

input::-webkit-input-placeholder {
  color: $USE44-placeholder-color;
}

// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 40px;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .footer-btn {
    width: 345px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: $USE44-button-background-color;
    border-radius: 20px;
    color: $USE44-font-color;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $USE44-color-primary;

  .van-tabs__content {
    background-color: $USE44-background-color;
  }
}

// 弹窗背景色
.van-dialog {
  color: $USE44-white-color;
  background-color: $USE44-light-background-color;
}

// 弹窗按钮
.van-dialog__cancel, .van-dialog__confirm {
  color: $USE44-white-color;
  background-color: $USE44-background-color;
}

// 弹窗按钮border
.van-hairline--top::after {
  border-color: $USE44-border-color;
}
.van-hairline--left::after {
  border-color: $USE44-border-color;
}

// Tab背景色
.van-tabs__nav {
  background-color: $USE44-background-color;
}
.van-tab {
  color: $gray-color;
}
.van-tab--active {
  color: $USE44-color-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $USE44-color-primary;
  border-color: $USE44-color-primary;
}

// switch背景色
.van-switch {
  background-color: $gray-color;
}
.van-switch--on {
  background-color: $USE44-color-primary;
}

// cell背景色
.van-cell {
  color: $USE44-white-color;
  background-color: $USE44-light-background-color;
}
.van-cell::after {
  border-bottom: 1px solid $USE44-light-gray-color;
}
.van-collapse-item--border::after {
  border-top: 1px solid $USE44-light-gray-color;
}
.van-collapse-item__content {
  background-color: $USE44-light-background-color;
}
.van-cell--clickable:active {
  background-color: $USE44-light-background-color;
}