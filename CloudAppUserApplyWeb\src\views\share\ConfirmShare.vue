<template>
  <div class="confirm-share-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="confirm-share-content">
      <div class="confirm-share-head">
        <div class="confirm-share-desc">
          {{ $t('shareDesc') }}
        </div>
        <div class="confirm-share-desc">
          {{ $t('shareDesc2') }}
        </div>
        <van-field v-model="loginName" label="" disabled :placeholder="$t('pleaseEnter')" />
        <van-field v-model="recipientRemark" label="" :placeholder="$t('remark')" />
      </div>
      <div class="confirm-share-body">
        <div class="share-device-title">{{ $t('shareDevice') }} ({{ deviceChannelList.length || 0 }})</div>
        <div class="share-device-box">
          <choose-channel
            type="detail"
            :deviceChannelList="deviceChannelList"
            :devOperationObj="devOperationObj"
            :devSupportFunObj="devSupportFunObj"
          />
        </div>
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :loading="btnLoading" @click="handleConfirm">
        {{ $t('confirmShare') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import ChooseChannel from './components/ChooseChannel.vue'
import { CHANNEL_CAPABILITY_LIST, DEVICE_CAPABILITY_MAP } from '@/utils/options'
import { setCacheData, getCacheData } from '@/utils/appbridge'
import { mapState, mapMutations } from 'vuex'
import { setShareChannel } from '@/api/share'
import { encrypt, decrypt } from '@/utils/encrypt.js'

export default {
  name: 'ConfirmShare',
  components: {
    NavBar,
    ChooseChannel
  },
  props: {},
  data() {
    return {
      loginName: '', // 分享的邮箱或者手机号
      recipientRemark: '', // 分享备注
      deviceChannelList: [], // 选中分享的设备通道
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      btnLoading: false, // 按钮loading
      historyShareList: [],
      generateAESKey: '+GYLe29QjUFVsYZc/QUEC6g2P/61PjC312jwGvZw26o='
    }
  },
  created() {
    // console.log('chooseChannelList', this.chooseChannelList)
    // 从store中获取选中的通道
    this.createChannelTree(this.chooseChannelList.sort((a, b) => a.chlIndex - b.chlIndex))
    // 根据shareUser确定当前分享的联系方式
    const { active, email, mobile } = this.shareUser
    if (active === 'email') {
      this.loginName = email
    } else {
      this.loginName = mobile
    }
    // 获取历史分享列表
    this.getHistoryShareList()
  },
  mounted() {},
  computed: {
    ...mapState('share', ['shareUser', 'chooseChannelList', 'devSupportFunObj', 'devOperationObj'])
  },
  methods: {
    ...mapMutations('share', [
      'SET_SHARE_USER',
      'SET_ALL_CHANNEL_LIST',
      'SET_DEVICE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_CHOOSE_CHANNEL_LIST'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 获取历史分享
    getHistoryShareList() {
      const callback = data => {
        if (data) {
          const obj = JSON.parse(data)
          console.log('obj', obj)
          if (obj && obj.body) {
            this.historyShareList = (JSON.parse(obj.body) || [])
              .filter(item => item.shareName)
              .map(item => ({
                shareName: decrypt(item.shareName, this.generateAESKey)
              }))
          }
        }
      }
      getCacheData('historyShareList', callback)
    },
    // 更新历史分享列表
    updateHistory(shareName) {
      const historyShareList = this.historyShareList.slice()
      const index = historyShareList.findIndex(item => item.shareName === shareName)
      if (index > -1) {
        historyShareList.splice(index, 1)
      }
      historyShareList.unshift({ shareName })
      const newShareList = historyShareList.slice(0, 20)
      this.historyShareList = newShareList
      const storeShareList = newShareList.map(item => ({ shareName: encrypt(item.shareName, this.generateAESKey) }))
      setCacheData({ key: 'historyShareList', value: JSON.stringify(storeShareList) })
      console.log('setCacheData', JSON.stringify(storeShareList))
    },
    // 构建设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const deviceChannelList = []
      const deviceIndexObj = {} // 记录站点Id——设备sn及其在通道树中的索引
      allChannelList.forEach(item => {
        const { sn, deviceName } = item
        let temp = null
        // 判断设备
        if (deviceIndexObj[sn] !== undefined) {
          // 说明设备存在
          const index = deviceIndexObj[sn]
          temp = deviceChannelList[index]
          temp.children = deviceChannelList[index].children || []
          temp.children.push({ ...item })
        } else {
          // 说明设备不存在，直接添加
          temp = { sn, deviceName, children: [{ ...item }] }
          deviceIndexObj[sn] = deviceChannelList.length // 记录下站点的索引
          // 继续添加通道
          deviceChannelList.push(temp)
        }
      })
      this.deviceChannelList = deviceChannelList
    },
    // 确定分享
    async handleConfirm() {
      this.btnLoading = true
      this.$loading.show()
      try {
        const params = {
          loginName: this.loginName,
          recipientRemark: this.recipientRemark
        }
        const shareList = []
        const deviceShareList = []

        this.chooseChannelList.forEach(item => {
          const { sn, chlIndex, checkCapability = [] } = item
          const auth = checkCapability.reduce((pre, next) => {
            return pre.concat(next.value.split(','))
          }, [])
          shareList.push({ sn, chlIndex, auth })

          if (this.devOperationObj[sn]) {
            // 勾选了设备操作权限（这个是复合权限， 包括config, talk, armByCloud）
            const devAuth = [
              DEVICE_CAPABILITY_MAP.CONFIG,
              DEVICE_CAPABILITY_MAP.TALK,
              DEVICE_CAPABILITY_MAP.ARM_BY_CLOUD
            ]

            deviceShareList.push({ sn, auth: devAuth })
          } else {
            deviceShareList.push({ sn, auth: [] })
          }
        })
        const temp = {}
        const uniqueShareList = []
        deviceShareList.forEach(item => {
          if (!temp[item.sn]) {
            temp[item.sn] = item
            uniqueShareList.push(item)
          } else {
            // 如果已经存在相同的sn，则合并auth
            temp[item.sn].auth = [...new Set([...temp[item.sn].auth, ...item.auth])]
          }
        })

        params.shareList = shareList
        params.devShareList = uniqueShareList

        await setShareChannel(params)
        // 更新历史分享列表
        // 判断当前分享的是否为游客， 游客不加入到历史分享中
        const { active, email } = this.shareUser
        if (!(active === 'email' && email.indexOf('@') === -1)) {
          // email中不包含@符号则可以认为是游客
          this.updateHistory(this.loginName)
        }
        this.btnLoading = false
        // 跳转到分享成功的页面
        this.$utils.routerPush({
          path: '/share/shareSuccess'
        })
      } catch (err) {
        this.btnLoading = false
        console.error(err)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.confirm-share-wrapper {
  height: 100%;
  overflow: hidden;
  .confirm-share-content {
    width: 100%;
    height: calc(100% - 126px);
    overflow: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .confirm-share-head {
      width: 100%;
      min-height: 198px;
      padding: 10px 36px;
      box-sizing: border-box;
      .confirm-share-desc {
        text-align: center;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
    .confirm-share-body {
      width: 100%;
      overflow: hidden;
      // height: calc(100% - 284px);
      flex: 1;
      .share-device-title {
        width: 100%;
        height: 22px;
        line-height: 22px;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        padding: 10px;
        box-sizing: content-box;
      }
      .share-device-box {
        width: 100%;
        height: calc(100% - 42px);
        overflow: auto;
      }
    }
  }
}
</style>
<style lang="scss">
.confirm-share-head {
  .van-field {
    width: 100%;
    height: 22px;
    padding: 14px 0;
    box-sizing: content-box;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }
}
</style>
