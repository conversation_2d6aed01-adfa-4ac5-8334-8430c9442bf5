<execution>
  <constraint>
    ## Vue开发技术约束
    - **Vue版本兼容性**：确保代码在目标Vue版本中正常运行
    - **浏览器兼容性**：考虑目标浏览器的JavaScript特性支持
    - **构建工具限制**：Webpack/Vite配置的性能和兼容性约束
    - **TypeScript集成**：类型定义的完整性和准确性要求
  </constraint>

  <rule>
    ## Vue开发强制规则
    - **单文件组件规范**：template、script、style的标准组织结构
    - **命名约定强制**：组件名、属性名、事件名的一致性规范
    - **响应式数据规则**：data、computed、watch的正确使用边界
    - **生命周期管理**：组件生命周期钩子的合理使用和清理
    - **ESLint规则遵循**：Vue官方ESLint配置的严格执行
  </rule>

  <guideline>
    ## Vue开发指导原则
    - **组件化优先**：优先考虑组件的可复用性和可维护性
    - **性能意识**：时刻关注代码对渲染性能的影响
    - **可读性重视**：代码的自文档化和清晰的逻辑表达
    - **测试友好**：编写易于测试的组件和逻辑
    - **渐进式开发**：从简单实现开始，逐步增加复杂性
  </guideline>

  <process>
    ## Vue开发标准流程
    
    ### 组件开发流程
    ```mermaid
    flowchart TD
        A[需求分析] --> B[组件设计]
        B --> C[接口定义]
        C --> D[模板编写]
        D --> E[逻辑实现]
        E --> F[样式设计]
        F --> G[测试验证]
        G --> H[文档编写]
        H --> I[代码审查]
        I --> J[集成部署]
    ```
    
    ### 状态管理流程
    ```mermaid
    graph LR
        A[状态识别] --> B[Store设计]
        B --> C[Actions定义]
        C --> D[Mutations实现]
        D --> E[Getters优化]
        E --> F[组件集成]
    ```
    
    ### 性能优化流程
    ```mermaid
    graph TD
        A[性能监控] --> B{性能瓶颈?}
        B -->|是| C[问题定位]
        B -->|否| D[持续监控]
        C --> E[优化方案]
        E --> F[实施优化]
        F --> G[效果验证]
        G --> A
    ```
    
    ### 代码审查检查点
    - **组件职责单一性**：每个组件是否只负责一个明确功能
    - **数据流清晰性**：props和events的使用是否符合单向数据流
    - **性能影响评估**：是否存在不必要的重渲染或内存泄漏
    - **代码复用性**：是否有重复代码可以抽取为公共组件或工具
    - **错误处理完整性**：异常情况的处理是否完善
  </process>

  <criteria>
    ## Vue开发质量标准
    
    ### 代码质量指标
    - ✅ ESLint检查通过率 = 100%
    - ✅ 组件单元测试覆盖率 ≥ 80%
    - ✅ 组件API文档完整性 = 100%
    - ✅ TypeScript类型定义准确性 = 100%
    
    ### 性能质量指标
    - ✅ 组件首次渲染时间 ≤ 100ms
    - ✅ 列表组件滚动帧率 ≥ 60fps
    - ✅ 内存使用增长率 ≤ 5%/小时
    - ✅ 包体积增长控制 ≤ 10%/版本
    
    ### 用户体验指标
    - ✅ 组件响应时间 ≤ 200ms
    - ✅ 错误状态处理覆盖率 = 100%
    - ✅ 加载状态反馈及时性 = 100%
    - ✅ 无障碍访问支持完整性 ≥ 90%
    
    ### 维护性指标
    - ✅ 组件API稳定性 ≥ 95%
    - ✅ 代码可读性评分 ≥ 8/10
    - ✅ 新人上手时间 ≤ 2天
    - ✅ Bug修复平均时间 ≤ 4小时
  </criteria>
</execution>