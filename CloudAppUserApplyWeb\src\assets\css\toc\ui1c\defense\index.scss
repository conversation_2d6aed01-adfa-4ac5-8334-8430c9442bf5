.defense-list-wrapper  .defense-item-wrapper {
    background-color: $UI1C-light-background-color;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-title {
    color: $UI1C-white-color;
  }

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-text {
    color: $UI1C-font-color;
}

.add-defense-wrapper .add-defense-head {
    background-color: $UI1C-light-background-color;
}

.add-defense-wrapper .add-defense-head .add-defense-title {
    color: $UI1C-white-color;
}

.add-defense-wrapper .add-defense-head .add-defense-text {
    color: $UI1C-font-color;
}

.add-defense-wrapper .add-defense-device {
    color: $UI1C-font-color;
}

.add-defense-wrapper .device-content .device-list-wrapper {
    background-color: $UI1C-light-background-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-title {
    color: $UI1C-white-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-text {
    color: $UI1C-font-color;
}

.device-content .device-list-wrapper .device-item-wrapper {
    border-bottom: 1px solid $UI1C-border-color;
}

.ipc-setting-wrapper .ipc-setting-box {
    background-color: $UI1C-light-background-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-title {
    color: $UI1C-white-color;
}

.ipc-setting-wrapper .ipc-setting-desc {
    color: $UI1C-font-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-text {
    color: $UI1C-font-color;
}

.ipc-linkage-wrapper .ipc-configure {
    color: $UI1C-font-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list {
    background-color: $UI1C-light-background-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list .ipc-linkage-item {
    color: $UI1C-white-color;
    border-bottom: 1px solid $UI1C-border-color;
}

.channel-wrapper .channel-content .channel-list {
    background-color: $UI1C-light-background-color;
}

.area-name-wrapper .common-input {
    background: transparent!important;
}