<template>
  <div class="trusteeship-my-installer">
    <nav-bar @clickLeft="back" :showMore="appType === 'TOC' ? true : false" @showMore="showUnBindButton"></nav-bar>
    <!-- 解除绑定 -->
    <van-overlay :show="bindFlag" @click="bindFlag = false" v-if="appType === 'TOC'">
      <select-menu :options="options" class="menu" @choose="selectItem"></select-menu>
    </van-overlay>
    <div class="trusteeship-my-installer-content">
      <tvt-better-scroll
        class="trusteeship-tvt-better-scroll"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <!-- 企业信息 -->
        <div class="company-information">
          <div class="container">
            <div class="left">
              <img
                :src="
                  company.logo
                    ? company.logo
                    : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/logo.png')
                "
              />
            </div>
            <div class="right">
              <div class="txt-flex">
                <div class="name">{{ company.installerCoName }}</div>
              </div>
              <div class="email txt-flex">
                <img class="icon" :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/email.png')" />
                <div class="text text-over-ellipsis">{{ company.installerEmail }}</div>
              </div>
              <div class="tel txt-flex">
                <img class="icon" :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/tel.png')" />
                <div class="text">{{ company.tel }}</div>
              </div>
              <div class="address txt-flex">
                <img class="icon" :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/address.png')" />
                <div class="text">{{ company.addr }}</div>
              </div>
            </div>
          </div>
        </div>
        <template v-if="showTrusteeship">
          <div class="device-trusteeship-title">{{ $t('trusteeshipDevice') }}</div>
          <div class="device-trusteeship" v-if="dataList.length > 0">
            <div class="container-ul">
              <div
                class="container-li"
                v-for="(item, index) in dataList"
                :key="'device' + index"
                @click="goToDetails(item)"
              >
                <!-- 安装商未接收时显示：只有待接收显示-->
                <div class="device li-item">
                  <div class="name text-over-ellipsis">{{ item.devName }}</div>
                  <div class="status" v-if="item.status == 0">{{ statusLabel(item.status, statusOptions) }}</div>
                </div>
                <div class="li-item content text-over-ellipsis">{{ authLabel(item.authList, authListOptions) }}</div>
                <div class="li-item time">{{ timeMethod(item) }}</div>
              </div>
            </div>
          </div>
          <div class="no-data" v-else>
            <div class="no-data-img">
              <img :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')" />
            </div>
          </div>
        </template>
      </tvt-better-scroll>
    </div>
    <template v-if="showTrusteeship">
      <div class="footer" v-if="!(appType === 'TOB' && vmsUserType == 2)">
        <div class="footer-btn" @click="addTrusteeship">
          {{ $t('addTrusteeship') }}
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import SelectMenu from '@/components/SelectMenu.vue'
import { appSetTitle, appBack, appClose } from '@/utils/appbridge'
import { getUrlQuery, dateFormat, timeMethod } from '@/utils/common'
import { TRUSTEESHIP_STATUS, AUTH_LIST, TRUSTEESHIP_VALIDITY_LIST } from '@/utils/options.js'
import { statusLabel, authLabel } from '@/utils/trusteeship.js'
import { deviceTrusteeshipsList, vmsInstallersGet, installerInfo, installerUnbind } from '@/api/trusteeship.js'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'myInstaller',
  components: {
    NavBar,
    SelectMenu
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      company: {
        installerCoName: '',
        installerCoId: '',
        installerUserId: '', //安装商用户id
        installerEmail: '',
        logo: '',
        tel: '',
        addr: ''
      },
      bindFlag: false,
      pageNum: 1,
      pageSize: 9999,
      pullingStatus: 0,
      dataList: [],
      statusOptions: TRUSTEESHIP_STATUS(),
      authListOptions: AUTH_LIST(),
      validityList: TRUSTEESHIP_VALIDITY_LIST(),
      dateFormat: dateFormat,
      timeMethod: timeMethod,
      statusLabel: statusLabel,
      authLabel: authLabel,
      vmsInstallersGet: vmsInstallersGet,
      installerInfo: installerInfo,
      vmsUserType: 0, //1：上级用户 2：下级用户
      showTrusteeship: false // 是否展示托管--IL03 VMS定制 TOB UI1C 不展示
    }
  },
  mounted() {
    appSetTitle(this.$t('myInstaller'))
    let json = getUrlQuery(window.location.href)
    console.log(json, 'json')
    this.vmsUserType = json && json.userType ? json.userType : 0
    console.log(this.vmsUserType, 'this.vmsUserType')
    console.log(this.pageContent, 'navigator.userAgent')
    this.$nextTick(() => {
      this.getList({ type: 'down' })
      if (this.appType === 'TOC') {
        this.getInstallerInfo('installerInfo')
      } else if (this.appType === 'TOB') {
        this.getInstallerInfo('vmsInstallersGet')
      }
      if (this.appType === 'TOB' && this.style == 'UI1C') {
        // IL03 VMS定制 TOB UI1C 不展示
        this.showTrusteeship = false
      } else {
        this.showTrusteeship = true
      }
    })
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    options() {
      return [
        {
          label: this.$t('unBind'),
          value: 1,
          icon: require('@/assets/img/' + this.appStyleFlag + '/' + this.uiStyleFlag + '/unbind.png')
        }
      ]
    }
  },
  methods: {
    ...mapMutations('trusteeship', ['CLEAR_AVAILABLE_LIST']),
    back() {
      appBack()
    },
    // 获取安装商信息
    getInstallerInfo(functionName) {
      this[functionName]().then(res => {
        console.log(res, '查询安装商信息')
        if (res.basic.code == 200) {
          this.company = res.data
        }
      })
    },
    // 查询已托管的设备列表
    getList(param) {
      let that = this
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: 1 // 0: 可托管设备； 1：已发起托管设备；
      }
      this.$loading.show()
      deviceTrusteeshipsList(params)
        .then(res => {
          this.$loading.hide()
          if (res.basic.code == 200) {
            let data = res.data || []
            if (param && param.type === 'down') {
              that.dataList = []
            }
            that.dataList = data
            if (param && param.callback) param.callback()
          } else {
            if (param && param.callback) param.callback()
            that.dataList = []
          }
        })
        .catch(error => {
          this.$loading.hide()
          if (error.basic && error.basic.code === 32018) {
            setTimeout(() => {
              appClose()
            }, 1000)
          }
          if (param && param.callback) param.callback()
        })
    },
    pullingUp(callback) {
      console.log(callback, 'callback')
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.pageNum = 1
      this.getList({ type: 'down', callback })
    },
    addTrusteeship() {
      this.CLEAR_AVAILABLE_LIST()
      this.$router.push({ name: 'chooseDevice', params: { userType: this.vmsUserType } })
    },
    goToDetails(item) {
      this.CLEAR_AVAILABLE_LIST()
      this.$router.push({ name: 'deviceDetails', params: { id: item.id, userType: this.vmsUserType } })
    },
    selectItem(item) {
      //这里要考虑后续按钮下拉的可扩展性
      if (item.value == 1) {
        let tips = {
          message: this.$t('unBindTrusteeship'),
          cancelButtonText: this.$t('cancel'),
          confirmButtonText: this.$t('confirm')
        }
        if (this.style == 'UI1B') {
          tips['title'] = this.$t('tips')
        }
        this.$dialog
          .confirm(tips)
          .then(() => {
            installerUnbind().then(res => {
              if (res.basic.code == 200) {
                appBack()
              }
            })
          })
          .catch(() => {})
      }
    },
    showUnBindButton() {
      this.bindFlag = true
    }
  }
}
</script>
<style scoped lang="scss">
.trusteeship-my-installer {
  position: relative;
  height: calc(100% - 70px);
  // overflow: auto;
  overflow: hidden;
  .menu {
    position: absolute;
    top: 44px;
    right: 10px;
  }
  .company-information {
    .container {
      // margin-top: 19px;
      padding: 15px 15px;
      .left {
        width: 90%;
        height: 100px;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }
    }
  }
  .trusteeship-my-installer-content {
    height: 100%;
    padding: 10px 0px;
  }
  .trusteeship-tvt-better-scroll {
    height: 100%;
    min-height: 230px;
  }
}
</style>
