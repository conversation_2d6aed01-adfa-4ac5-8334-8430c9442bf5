<template>
  <div class="trusteeship-list-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="trusteeship-list-content">
      <tvt-better-scroll
        :class="'whole-tvt-better-scroll'"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <device-list :dataList="deveiceData" @click="handleClick" />
      </tvt-better-scroll>
    </div>
  </div>
</template>
<script>
import { mapState, mapMutations } from 'vuex'
import NavBar from '@/components/NavBar'
import { gotoPage } from '@/utils/appbridge'
import { deviceTrusteeshipsList } from '@/api/trusteeship.js'
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'
import DeviceList from './components/DeviceList.vue'

export default {
  name: 'TrusteeshipList',
  components: {
    NavBar,
    DeviceList
  },
  props: {},
  data() {
    return {
      deviceCapabilitys: DEVICE_CAPABILITY_LIST(), // 全量设备权限
      deveiceData: [],
      pullingStatus: 0
    }
  },
  created() {
    this.getTrusteeshipDevices()
  },
  mounted() {},
  computed: {
    ...mapState('share', ['shareUser'])
  },
  methods: {
    ...mapMutations('maxTrusteeship', ['SET_TRUSTEESHIP_RECORD']),
    back() {
      // 跳转到服务页面
      gotoPage({
        pageRoute: 'service/home'
      })
    },
    // 获取当前用户发起托管设备
    async getTrusteeshipDevices() {
      try {
        // type 0: 可托管设备； 1：已发起托管设备
        const res = await deviceTrusteeshipsList({ type: 1 })
        // console.log('查询当前用户发起托管设备', res)
        if (res.basic.code === 200) {
          const { data } = res
          this.deveiceData = data.map(item => {
            const { authList = [], effectiveTime = 0, expireTime = 0, status } = item
            const checkCapability = this.deviceCapabilitys.filter(item2 => authList.includes(item2.value))
            // 判断已接受的记录是否过期--额外加个过期状态
            let expiredStatus = 0 // 0 表示未过期 1 表示过期
            if (Number(status) === 1) {
              const nowTimeStamp = Date.parse(new Date())
              if (Number(expireTime) === 0) {
                // expireTime为0表示永久
                expiredStatus = 0 // 未过期
              } else {
                if (parseInt(expireTime) - parseInt(nowTimeStamp) < 1000) {
                  // 剩余时长小于1s
                  expiredStatus = 1 // 过期
                } else {
                  expiredStatus = 0
                }
              }
            }
            return {
              ...item,
              effectiveTime: Number(effectiveTime),
              checkCapability,
              expiredStatus
            }
          })
        }
      } catch (err) {
        console.error(err)
      }
    },
    async pullingUp(callback) {
      // 刷新
      this.getTrusteeshipDevices()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      this.getTrusteeshipDevices()
      if (callback) callback()
    },
    // 点击设备
    handleClick(record) {
      // 进入设备托管详情页面
      this.SET_TRUSTEESHIP_RECORD(JSON.parse(JSON.stringify(record)))
      // 进入设备选择页面
      const { id } = record
      this.$utils.routerPush({
        path: '/maxTrusteeship/trusteeshipDetail',
        query: { id, from: 'list' }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-list-wrapper {
  height: 100%;
  overflow: hidden;
  .trusteeship-list-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    box-sizing: border-box;
    .whole-tvt-better-scroll {
      height: calc(100% - 0px);
      overflow: auto;
    }
  }
}
</style>
