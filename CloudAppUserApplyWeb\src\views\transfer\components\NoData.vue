<template>
  <div class="no-data">
    <theme-image class="no-data-img" imageName="no_data_max.png" alt="noDataMax" />
    <div class="no-data-text">{{ text }}</div>
  </div>
</template>
<script>
import ThemeImage from '@/components/ThemeImage.vue'
import i18n from '@/lang'

export default {
  name: 'NoData',
  components: {
    ThemeImage
  },
  props: {
    text: {
      type: String,
      default: i18n.t('noData')
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.no-data {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  .no-data-img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 100px;
    img {
      width: 120px;
      height: 123px;
    }
    .theme-image-container {
      width: 120px;
      height: 123px;
    }
  }
  .no-data-text {
    text-align: center;
    font-family: 'Source Han Sans CN', sans-serif;
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-top: 20px;
  }
}
</style>
