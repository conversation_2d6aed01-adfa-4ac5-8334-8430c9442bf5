export default {
  upgrade: '云升级',
  cancel: '取消',
  confirm: '确定',
  deviceUpdate: '设备',
  cameraUpdate: '摄像机',
  allUpdate: '一键升级',
  updateNow: '升级',
  currentVersion: '当前版本',
  latestVersion: '最新版本',
  updateContent: '更新内容',
  hasLatestVersion: '已是最新版本',
  online: '在线',
  offline: '离线',
  waitDownload: '待下载',
  inprogress: '下载中',
  downloadFail: '下载失败',
  downloadFinished: '下载完成',
  inupgrade: '升级中',
  upgradeFail: '升级失败',
  upgradeSuccess: '升级成功',
  deviceUpgradeInfo: '升级过程中设备将断开连接并自动重启，确定升级？',
  upgradeTip:
    '下载完成后设备会自动升级，升级过程中设备将自动重启，升级过程中请勿手动重启设备或断开设备电源直到设备自动重启完成。',
  cameraUpgradeInfo: '升级过程中摄像机将断开连接并自动重启，确定升级？',
  pwdUserNameError: '用户名或密码错误',
  permissionAuth: '超级管理员权限认证',
  pleaseEnterUser: '请输入用户名',
  pleaseEnterPwd: '请输入密码',
  noCameraUpgrade: '未检测到可升级的摄像机',
  handleCheck: '检测更新',
  // H5需给出翻译
  checkSuccess: '检测成功',
  checkFail: '检测失败',
  viewUpdateContent: '查看更新内容',
  deviceDisconnected: '连接设备失败',
  updateNote: '更新说明',
  noData: '暂无数据',
  tips: '提示',
  // 结束
  paySuccess: '支付成功',
  payFail: '支付失败',
  done: '完成',
  rePurchase: '重新购买',
  cloudStorage: '云存储',
  INSTRUMENT_DECLINED: '交易超出卡限制',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: '付款人账户不能用于此交易',
  PAYER_ACCOUNT_RESTRICTED: '付款人账户被限制',
  TRANSACTION_LIMIT_EXCEEDED: '总支付金额超过交易限额',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: '交易超过了接收方的收款限额',
  fail: '交易失败 ',
  // 设备托管
  myInstaller: '我的安装商',
  trusteeshipDevice: '已托管设备',
  addTrusteeship: '新增托管',
  waitReceived: '待接收',
  received: '已接收',
  refuse: '拒绝',
  delete: '删除',
  operationSuccess: '操作成功', // 托管成功  取消托管成功
  operationFail: '操作失败',
  cancelTrusteeship: '取消托管',
  chooseDevice: '选择设备',
  noAvaiableDevice: '设备版本支持托管功能且通过绑定添加的设备，才能托管',
  leastChoose: '至少选择一个设备',
  finish: '完成',
  details: '详情',
  deviceDetails: '详情',
  live: '现场',
  rec: '回放',
  config: '配置',
  confirmTrusteeshipTip: '托管请求已发送给安装商，请等待安装商处理',
  cancelTrusteeshipTip: '取消托管后安装商无法为您提供远程维护服务确定取消？',
  unBindTrusteeship: '解绑后将取消所有设备托管，确定解除绑定？',
  trusteeshipPermissions: '托管权限：',
  trusteeshipTime: '托管时间：',
  unBind: '解除绑定',
  serviceException: '服务异常',
  netTimeOut: '网络连接超时',
  pullingText: '释放立即加载...',
  loosingText: '释放立即刷新...',
  loosing: '正在刷新...',
  loadingText: '加载中...',
  refreshComplete: '刷新完成',
  noMore: '没有更多了...',
  // 1.14.0
  password: '密码',
  ipcUpgrade: '云升级',
  pwdError: '密码错误，还可以尝试{0}次',
  pwdErrorLock: '错误次数过多已锁定，请稍后尝试！',
  noPermissions: '无权限',
  permission: '权限',
  validity: '有效期',
  permissionValidity: '授权',
  isSaveModify: '是否保存更改?',
  manyMinutes: '{0}分钟',
  manyHours: '{0}小时',
  manyDays: '{0}天',
  manyMinutesEn: '{0}分钟',
  manyHoursEn: '{0}小时',
  manyDaysEn: '{0}天',
  oneWeek: '1周',
  forever: '永久',
  expired: '已过期',
  residue: '剩余',
  // 转移请求
  transferRequest: '转移请求',
  acceptTransfer: '接受转移',
  refuseTransferConfirm: '确定拒绝转移?',
  bindInstallerText: '绑定安装商({account})后支持托管设备，是否立即绑定？',
  bindSuccess: '绑定成功',
  acceptSuccess: '接受成功',
  // 分享管理
  shareManage: '分享管理',
  shareDetail: '分享详情',
  acceptShare: '接受分享',
  from: '来自',
  permissionText: '您获得的权限',
  livePreview: '现场预览',
  playback: '回放',
  alarm: '报警',
  intercom: '对讲',
  gimbal: '云台',
  refuseShareConfirm: '确定拒绝分享?',
  acceptAll: '全部接受',
  exitShare: '退出分享',
  // 布防撤防
  defenseDeployment: '布防/撤防',
  oneClickDeployment: '一键布防',
  oneClickDisarm: '一键撤防',
  deploySuccess: '布防成功',
  disarmSuccess: '撤防成功',
  deployDesc: '防护已开启，点击撤防',
  disarmDesc: '防护已撤销，点击恢复',
  add: '新增',
  edit: '编辑',
  name: '名称',
  cameraSensor: '关联摄像机',
  deleteDeviceConfirm: '确定删除设备?',
  pleaseEnter: '请输入',
  groupNoDevice: '分组下未添加设备',
  // 预置点、巡航线添加--除superlive plus外
  addPoint: '添加',
  addSuccess: '添加成功',
  addFail: '添加失败',
  editSuccess: '编辑成功',
  editFail: '编辑失败',
  deleteSuccess: '删除成功',
  deleteFail: '删除失败',
  pointNameExist: '预置点名称已存在',
  noPointSelect: '没有预置点可选择，请先创建预置点',
  choosePoint: '选择',
  presetPoint: '预置点',
  presetPointName: '预置点名称',
  cruiseLineName: '巡航线名称',
  pleaseEnterPoint: '请输入预置点名称',
  pleaseEnterLine: '请输入巡航线名称',
  presetPointEmpty: '预置点列表不能为空！',
  lineNameExist: '巡航线名称已存在',
  presetPointLimit: '预置点数量不能超过{0}个！',
  manySecond: '{0}秒',
  oneMinute: '1分钟',
  speed: '速度',
  holdTime: '持续时间',
  deletePointConfirm: '确定删除预置点?',
  pleaseChoosePoint: '请选择预置点',
  pleaseChooseSpeed: '请选择速度',
  pleaseChooseHoldTime: '请选择持续时间',
  // line APP绑定解绑
  lineAuthBind: 'line授权绑定',
  bindFail: '绑定失败',
  binding: '绑定中',
  // 住户管理
  householdManagement: '楼栋与住户',
  addBuilding: '添加楼栋',
  buildingName: '楼栋名称',
  enterBuildingName: '请输入楼栋名称',
  buildingNum: '楼栋号',
  enterBuildingNum: '请输入楼栋号',
  relateDevice: '关联设备',
  roomNum: '房间数量',
  room: '房间',
  addRoom: '添加房间',
  roomName: '房间号',
  enterRoomName: '请输入房间号',
  household: '住户',
  addRoomMember: '添加住户',
  changeSuccessfully: '修改成功',
  email: '邮箱',
  enterMemberEmail: '请输入邮箱',
  mobile: '手机',
  enterMemberMobile: '请输入手机号',
  emailNameError: '邮箱格式不正确',
  mobileError: '手机号格式不正确',
  emailNameNotEmpty: '邮箱不能为空',
  mobileNotEmpty: '手机号不能为空',
  memberInMax: '此房间下住户数量达到上限',
  memberMobileRepeate: '此房间内已存在该手机号',
  emailRepeate: '此房间内已存在该邮箱',
  supportDash: '仅支持-，_和空格作为特殊字符',
  errorCode: {
    536870947: '用户名不存在',
    536870948: '用户名或者密码错误',
    536871060: '操作失败，请检查设备状态', //双系统功能 新增 设备当前不允许升级,请稍后再试
    10000: '连接设备失败', //app错误码
    550: '请求超时', //app返回的超时
    12344: '网络连接失败',
    12345: '网络连接超时', // 前端axios设置超时自用code
    23024: '提供的支付卡已过期',
    23025: '交易因违规而被拒绝',
    400: '参数错误', //app错误码
    404: '请求的资源（网页等）不存在',
    500: '系统异常',
    502: '服务器请求失败',
    503: '服务器异常',
    504: '服务器请求超时',
    32018: '数据不存在',
    32022: '{0}设备和安装商不在一个国家/区域，不支持托管',

    536871039: '无效参数',
    536870943: '无效参数',
    536870945: '操作失败，请检查设备状态', //'设备忙，请稍后重试', //设备忙（互斥）关系的错误码和对应的词条
    536871017: '操作失败，请检查设备状态', //'版本不匹配', // APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令告诉了一个更新的版本。此时APP触发升级NVR的话，就会返回
    536871082: '操作失败，请检查设备状态', //'无新版本',
    536871083: '操作失败，请检查设备状态', //'云升级版本不存在', //APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令取消掉了版本。此时APP触发升级NVR的话，就会返回
    536870940: '操作失败，请检查设备状态', //'设备未开启云升级功能', //云升级未开启
    536870934: '操作失败，请检查设备状态', //统一的模糊的提示
    1000: '参数错误',
    1005: '图片验证码错误',
    1007: '需要图片验证码',
    1008: '验证码已过期',
    1009: '验证码错误',
    1011: '未正确填写参数！',
    1012: 'API无法识别',
    1013: '验证码发送异常',
    1015: '用户已存在',
    1027: '请输入正确的设备序列号/安全码',
    1028: '该通道已经启用或禁用',
    4500: '参数错误',
    5000: '抱歉，您没有权限进行该项操作',
    5001: '当前用户无权限',
    6000: '当前业务状态不支持此操作',
    6001: '操作过于频繁',
    7000: '参数错误',
    7001: '用户不存在',
    7002: '旧密码错误！',
    7003: 'Token不合法',
    7004: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录',
    7005: '签名无效',
    7006: '手机号码已存在',
    7007: '用户被锁定,请联系管理员解锁',
    7009: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', // Token达到最大数量后被强制退出最久未访问的会话
    7010: '管理员帐号未激活',
    7011: '帐号未激活',
    7019: '用户名已存在',
    7021: '删除失败！请先清空该主机组下的所有主机',
    7023: '该邮箱已被绑定',
    7028: '模板已在项目中使用，无法删除！',
    7029: '模板名已存在！',
    7030: '该厂商编号和版本号组合已存在！',
    7032: '固件包已存在！',
    7034: '该固件包已发布任务，不能删除！',
    7042: '有其他任务处于启动状态',
    7043: '任务尚未审核！',
    7044: '操作失败，没有符合升级条件的设备!',
    7045: '任务未审核通过！',
    7056: '配套兼容管理中已含有该版本，不允许删除!',
    7057: '签发单不能为空!',
    7061: '修正失败，不能重复创建修正！',
    7066: '客户代号已存在！',
    7068: '客户代号或客户编码不存在！',
    7069: '数据量过多，请缩小范围重新搜索！',
    7081: '导入失败！',
    7082: '导出失败！',
    7084: '该客户国家代码已存在',
    7086: '系统异常，拒绝操作',
    7087: '商品已经存在！',
    7088: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', // 密码变更后被强制退出
    7090: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', //账号注销后被强制退出
    7093: '图文信息未配置！',
    7094: '服务条款信息不存在！',
    9000: '系统异常！',
    9001: '协议版本过低,老版本不再兼容，需升级',
    9002: '协议版本错误，版本字段无法识别或错误信息',
    9003: '验证码发送异常',
    9004: '数据库操作失败',
    9005: '数据不存在',
    9006: '数据已存在',
    9007: '查看失败，数据库中不存在此数据',
    9008: '数据不存在',
    9009: '数据异常',
    9500: '系统异常！',
    10001: '系统异常！',
    20021: '该Email已被使用',
    20024: '该账号已激活',
    20030: '链接已失效',
    33001: '没有权限操作该设备',
    33002: '没有权限操作该站点',
    33003: '站点不存在',
    33004: '设备名称长度必须在0~32之间',
    33010: '设备已存在', //设备推送配置已存在
    7072: '设备已存在',
    // 托管错误码
    32019: '操作失败',
    32021: '数据不存在',
    // 安装商转移
    34006: '转移记录不存在',
    34007: '只能接受来自同一用户的转移',
    7040: '设备不存在或不在线',
    7065: '设备通道已经被分享',
    // ipc 云升级错误码
    ipc: {
      499: '未知错误', //未知错误
      612: '操作失败，请检查设备状态', //直连ipc升级过程中点击检测更新
      730: '操作失败，请检查设备状态', //检查新版本信息时，无新版本信息
      731: '操作失败，请检查设备状态', //云升级功能未使能  升级guid错误
      732: '操作失败，请检查设备状态', //升级任务已存在
      735: '操作失败，请检查设备状态' //直连ipc关闭云升级开关后点击检测更新
    },
    // Line App绑定
    34003: 'state中信息不正确',
    34001: '该账号已被绑定',
    34004: '创建授权失败',
    34005: '操作失败：授权已失效，请重新获取',
    // 住户管理
    34021: '楼栋数量达到上限',
    34022: '楼栋名称已存在',
    34023: '楼栋号已存在',
    34024: '删除失败，请先删除楼栋下的房间和关联设备',
    34025: '关联失败，设备已被其他楼栋关联',
    34026: '此楼栋下设备数量达到上限',
    34027: '操作失败，该楼栋已被删除',
    34028: '房间号已存在',
    34029: '此楼栋下房间数量达到上限',
    34030: '此房间下住户数量达到上限',
    34031: '操作失败，该房间已被删除',
    34033: '删除失败，请先删除房间下的住户',
    34035: '此房间内已存在该手机号',
    34036: '此房间内已存在该邮箱',
    34037: '操作失败，该住户已被删除'
  }
}
