.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $border-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $vms-red;
  }

  .footer-btn {
    background-color: $UI1K-color-primary;
    color: $vms-white;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $vms-light-black3;
    }
  }

  .van-dialog__header {
    color: $black-color;
  }

  .cruise-line-div {
    border: 1px solid $placeholder-color;
    background-color: $vms-white;

    .common-input {
      background-color: transparent;      
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $white-color;
      color: $vms-light-black3;

      .household-title {
        color: $vms-light-black3;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $border-color;
      }

      &::after {
        border-color: $border-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $UI1K-color-primary;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $UI1K-color-primary;
      color: $vms-white;
    }
  }
  .no-data-text {
    .footer-btn {
      background-color: $UI1K-color-primary;
      color: $vms-white;
    }
  }
}

.household-item-box {
  background: $white-color;
  color: $vms-light-black3;
  .household-title {
    color: $vms-light-black3;
  }

  .required-icon {
    color: $vms-red;
  }
  .right-value {
    color: $vms-gray;
  }
}