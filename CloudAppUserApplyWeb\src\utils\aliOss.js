const OSS = require('ali-oss')
import { getContentType } from './common.js'

const SECURE_MAP = {
  http: false,
  https: true
}

/**
 * 根据后台返回的storageUrl解析region，storageUrl分IPV4和IPV6两种情况，深圳为例
 * IPv4：oss-cn-shenzhen.aliyuncs.com
 * IPv6：cn-shenzhen.oss.aliyuncs.com
 * oss配置所需region标准格式为oss-cn-shenzhen
 */
const getRegionByStorageUrl = storageUrl => {
  const storageUrlArr = storageUrl.split('.')
  let region = storageUrlArr[0]
  if (storageUrlArr[1] === 'oss') {
    // IPV6
    region = 'oss-' + region
  }
  return region
}

/**
 * OSS简单上传
 * 适用于小文件（通常小于5MB）的上传
 * @param {Object} uploadToken - 上传凭证配置
 * @param {File} file - 要上传的文件
 * @param {string} fileUrl - 文件在OSS中的路径
 * @returns {Promise} 上传结果
 */
export const simpleUpload = (uploadToken, file, fileUrl) => {
  return new Promise((resolve, reject) => {
    const OSS_CONFIG = {
      accessKeyId: uploadToken.accessKeyId,
      accessKeySecret: uploadToken.accessKeySecret,
      stsToken: uploadToken.securityToken,
      bucket: uploadToken.bucketName,
      region: getRegionByStorageUrl(uploadToken.storageUrl),
      secure: uploadToken.protocol === 'https'
    }

    const client = new OSS(OSS_CONFIG)

    client
      .put(fileUrl, file, {
        headers: {
          'Content-Type': getContentType(file)
        }
      })
      .then(result => {
        console.log('OSS简单上传成功:', result)
        resolve(result)
      })
      .catch(error => {
        console.error('OSS简单上传失败:', error)
        reject(error)
      })
  })
}

// oss分片上传
// 参考链接:
// https://help.aliyun.com/document_detail/64047.html?spm=a2c4g.11186623.6.1364.1fc15557FLkrTs
// https://github.com/ali-sdk/ali-oss?spm=a2c4g.11186623.2.12.74c218c9xLNsVQ#multipartuploadname-file-options
// https://help.aliyun.com/knowledge_detail/94568.html#concept-lhz-qvt-4fb
export const multipartUpload = (config, file, cb, checkpoint = null, errorCallBack, successCallBack) => {
  const OSS_CONFIG = {
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    stsToken: config.securityToken,
    bucket: config.bucketName,
    region: getRegionByStorageUrl(config.storageUrl),
    secure: SECURE_MAP[config.protocol]
  }
  const fileName = config.faceFileUrl
  try {
    let client1 = new OSS(OSS_CONFIG)
    let client2 = new OSS(OSS_CONFIG)
    let client3 = new OSS(OSS_CONFIG)
    client1
      .multipartUpload(fileName, file, {
        progress: async function (percentage, cpt) {
          // 断点记录点。 浏览器重启后无法直接继续上传，需用户手动触发进行设置。
          if (cpt) {
            let content1 = JSON.stringify(percentage)
            let content2 = JSON.stringify(cpt)
            client2.put(`${fileName}.percentage`, new Blob([content1], { type: 'application/json' }))
            client3.put(`${fileName}.checkpoint`, new Blob([content2], { type: 'application/json' }))
          }
          if (typeof cb === 'function') cb(percentage, cpt)
        },
        checkpoint,
        headers: {}
      })
      .then(() => {
        if (typeof successCallBack === 'function') successCallBack()
      })
      .catch(err => {
        // 捕获网络连接超时异常
        if (typeof errorCallBack === 'function') errorCallBack(err)
      })
  } catch (e) {
    if (typeof errorCallBack === 'function') errorCallBack(e)
  }
}

/**
 * 智能选择OSS上传方式
 * 根据文件大小自动选择简单上传或分片上传
 * @param {Object} uploadToken - 上传凭证配置
 * @param {File} file - 要上传的文件
 * @param {string} fileUrl - 文件在OSS中的路径
 * @param {Function} progressCallback - 进度回调函数（仅分片上传时使用）
 * @returns {Promise} 上传结果
 */
export const smartUpload = (uploadToken, file, fileUrl, progressCallback) => {
  const fileSize = file.size
  const MULTIPART_THRESHOLD = 5 * 1024 * 1024 // 5MB阈值

  if (fileSize > MULTIPART_THRESHOLD) {
    // 大文件使用分片上传
    return new Promise((resolve, reject) => {
      multipartUpload({ ...uploadToken, faceFileUrl: fileUrl }, file, progressCallback, null, reject, resolve)
    })
  } else {
    // 小文件使用简单上传
    return simpleUpload(uploadToken, file, fileUrl)
  }
}
