<template>
  <van-popup
    :value="visible"
    @change="value => $emit('update:visible', value)"
    position="bottom"
    round
    :close-on-click-overlay="false"
    get-container="#app"
  >
    <div class="site-description-pop-container">
      <div class="header">
        <span class="title">
          {{ $t('siteDescTitle') }}
        </span>
      </div>
      <div class="content">
        <p>
          {{ $t('siteDesc1') }}
        </p>
        <p>
          {{ $t('siteDesc2') }}
        </p>
      </div>
      <van-button class="footer-btn" type="primary" @click="$emit('update:visible', false)">
        {{ $t('iKnow') }}
      </van-button>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'SiteDescPopup',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss">
.site-description-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    height: 52px;
    padding: 12px;
    position: relative;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    .title {
      display: inline-block;
      width: 100%;
      color: var(--text-color-primary, #1a1a1a);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-btn {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 14px;
    }
  }
  .content {
    padding: 20px 16px;
    color: var(--text-color-secondary, #3d3c3c);
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 10px auto 0;
  }
}
</style>
