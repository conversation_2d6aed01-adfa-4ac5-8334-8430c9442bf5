<template>
  <div class="choose-system">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" />
    <div class="choose-system-content">
      <div class="choose-system-list">
        <div class="choose-title">{{ $t('chooseAlarmSystem') }}</div>
        <van-radio-group v-model="selectedSystem">
          <div class="radio-item" v-for="(item, index) in alarmSystems" :key="index">
            <van-radio :name="item.value" class="choose-system-radio"
              >{{ item.value }}
              <template #icon="props">
                <theme-image
                  class="img-icon"
                  alt="check"
                  :imageName="props.checked ? 'alarm-system/radio_checked.png' : 'alarm-system/radio_no_check.png'"
                />
              </template>
            </van-radio>
          </div>
        </van-radio-group>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <van-button class="footer-btn alarm-footer-btn" type="primary" @click="handleNext">{{
        $t('nextStep')
      }}</van-button>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'

export default {
  name: 'ChooseSystem',
  data() {
    return {
      selectedSystem: 'Risco',
      alarmSystems: [{ value: 'Tyco' }, { value: 'Risco' }, { value: 'Pima' }]
    }
  },
  methods: {
    ...mapMutations('alarmSystem', ['SET_SYSTEM_TYPE']),
    goBack() {
      this.$router.back()
    },
    handleNext() {
      // 保存选择的报警系统
      this.SET_SYSTEM_TYPE(this.selectedSystem)

      // Tyco跳转到选择登录方式页面，其余跳转到登录页
      if (this.selectedSystem === 'Tyco') {
        this.$router.push({
          path: '/alarmSystem/chooseAccount',
          query: {
            systemType: this.selectedSystem
          }
        })
      } else {
        this.$router.push({
          path: '/alarmSystem/alarmLogin',
          query: {
            systemType: this.selectedSystem
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-system {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  &-list {
    margin-top: 20px;

    .choose-title {
      font-size: 18px;
      margin-bottom: 30px;
      text-align: center;
      font-weight: 400;
    }

    .radio-item {
      margin-bottom: 24px;

      .van-radio {
        display: flex;
        align-items: center;

        ::v-deep .van-radio__label {
          margin-left: 12px;
          font-size: 16px;
          line-height: 1.5;
        }

        ::v-deep .van-radio__icon {
          .theme-image-container {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
}
</style>
