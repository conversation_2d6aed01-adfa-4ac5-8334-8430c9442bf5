<template>
  <div class="ipc-setting-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="ipc-setting-content">
      <div class="ipc-setting-box">
        <div class="ipc-setting-line">
          <div class="ipc-setting-left">
            <div class="ipc-setting-title">{{ $t('defensiveLinkageItem') }}</div>
            <div class="ipc-setting-desc">
              <div class="ipc-desc-label">{{ $t('defensiveDesc') }}</div>
            </div>
          </div>
          <div class="ipc-setting-text">
            <div class="arrow-icon-box">
              <van-icon name="arrow" class="arrow-icon" @click="editIPCLinkage" />
            </div>
          </div>
        </div>
      </div>
      <div class="ipc-setting-box">
        <div class="ipc-setting-line">
          <div class="ipc-setting-left">
            <div class="ipc-setting-title">{{ $t('bypassHome') }}</div>
            <div class="ipc-setting-desc">
              <div class="ipc-desc-label">{{ $t('bypassHomeDesc') }}</div>
            </div>
          </div>
          <div class="ipc-setting-switch">
            <van-switch v-model="bypassSwitch" @change="handleSwitch" size="20px" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations, mapActions } from 'vuex'
import { MAX_IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import { addDefenseGroup } from '@/api/maxDefense'
import { appSetWebBackEnable } from '@/utils/appbridge'
export default {
  name: 'AddDefense',
  components: {
    NavBar
  },
  data() {
    return {
      ipcLinkageList: MAX_IPC_LINKAGE_LIST_FULLNAME(),
      linkName: null,
      bypassSwitch: 0,
      linkageList: []
    }
  },
  created() {
    appSetWebBackEnable(true)
    if (this.channelRecord && this.channelRecord.name) {
      this.$route.meta.title = this.channelRecord.name
    }
  },
  mounted() {
    if (this.channelRecord) {
      const { sn, chlIndex, chlId, extra = {} } = this.channelRecord
      const { bypassSwitch, linkageList = [] } = extra
      this.bypassSwitch = Boolean(bypassSwitch)
      this.linkageList = linkageList
      const filterLinkList = this.ipcLinkageList.filter(item => linkageList.includes(item.value))
      let linkName = ''
      if (linkageList.length > 0) {
        if (linkageList.length === this.ipcLinkageList.length) {
          linkName = this.$t('all')
        } else {
          linkName = filterLinkList.map(item => item.label).join('')
        }
      }
      this.linkName = linkName
      const newIpcLinkageList = JSON.parse(JSON.stringify(this.ipcLinkageList))
      // 找到设备支持的能力集，过滤出支持的能力集选项
      const supportFun = this.capabilityObj[`${sn}~${chlIndex}~${chlId}`] || []
      // 根据能力集找到可以支持联动项
      const filterLinkageList = newIpcLinkageList.filter(item => !item.filterAble || supportFun.includes(item.value))
      this.ipcLinkageList = filterLinkageList
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('maxDefense', ['addType', 'channelRecord', 'defenseRecord', 'capabilityObj']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getDefenseList', 'updateDeviceMutexStatus']),
    ...mapMutations('maxDefense', [
      'SET_CHANNEL_RECORD',
      'SET_DEFENSE_GROUP_LIST',
      'SET_CHANNEL_LIST',
      'SET_DEFENSE_RECORD'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 编辑IPC联动项 把信息存过去
    editIPCLinkage(record, index) {
      // this.SET_AVAILABLE_LIST([this.deviceInfo])
      this.$router.push({ name: 'maxIpcLinkage', params: { index, from: 'edit', id: record.id || 0 } })
    },
    // 切换旁路
    async handleSwitch(value) {
      const flag = Number(value)
      const { sn, chlIndex, chlId, extra } = this.channelRecord
      // 编辑布防组
      const { id, groupName, channelList, status } = this.defenseRecord
      const newChannelList = JSON.parse(JSON.stringify(channelList))
      // 找到channelList中对应的channelRecord
      const channelItem = newChannelList.find(
        item => item.sn === sn && item.chlIndex === chlIndex && item.chlId === chlId
      )
      if (channelItem) {
        channelItem.extra = {
          ...extra,
          bypassSwitch: flag
        }
      }
      // 编辑时发送请求
      if (id) {
        // 构造传参
        const params = {
          id,
          groupName,
          status,
          details: newChannelList.map(item => {
            const { sn, chlIndex, chlId, extra } = item
            return {
              sn,
              chlIndex,
              chlId,
              extra: JSON.stringify(extra)
            }
          }),
          addMethod: this.addType === 'bind' ? 1 : 2
        }
        this.$loading.show()
        try {
          const curDefenseRecord = {
            ...this.defenseRecord,
            channelList: newChannelList
          }
          const that = this
          const callback = async (msg, reqStatus) => {
            console.log('msg', msg, 'reqStatus', reqStatus)
            // console.log('设备请求状态', msg)
            if (msg === 'SUCCESS') {
              await addDefenseGroup(params)
              // toast提示
              that.$toastSuccess(that.$t('saveSuccess'))
              // 更新defenseRecord
              that.SET_DEFENSE_RECORD(curDefenseRecord)
              // 更新channelList
              that.SET_CHANNEL_LIST(newChannelList.slice())
              // 更新当前的channelRecord
              const newChannelRecord = {
                ...that.channelRecord,
                extra: {
                  ...that.channelRecord.extra,
                  bypassSwitch: flag
                }
              }
              that.SET_CHANNEL_RECORD(newChannelRecord)
              // console.log('res', res)
              const cb = () => {
                that.$loading.hide()
              }
              await that.getDefenseList({ callback: cb })
            } else {
              // 找到失败且code不为200的
              const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
              if (reqItem) {
                const { code } = reqItem
                if (Number(code) === 550) {
                  // 超时提示连接设备失败
                  that.$toast(that.$t('deviceDisconnected'))
                } else {
                  that.$toast(that.$t(`errorCode.${code}`))
                }
              } else {
                that.$toastFail(that.$t('saveFail'))
              }
            }
          }
          // 发送协议到对应的设备
          this.updateDeviceMutexStatus({ record: curDefenseRecord, callback })
        } catch (err) {
          console.error(err)
        } finally {
          this.$loading.hide()
        }
      } else {
        // 新增时不用发送请求直接更新记录
        // 更新defenseRecord
        this.SET_DEFENSE_RECORD({
          ...this.defenseRecord,
          channelList: newChannelList
        })
        // 更新当前的channelRecord
        const newChannelRecord = {
          ...this.channelRecord,
          extra: {
            ...this.channelRecord.extra,
            bypassSwitch: flag
          }
        }
        this.SET_CHANNEL_RECORD(newChannelRecord)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.ipc-setting-wrapper {
  position: relative;
  height: 100%;
  overflow: auto;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  .ipc-setting-content {
    width: 100%;
    height: calc(100% - 44px);
    padding: 10px 0px;
    box-sizing: border-box;
    overflow: auto;
  }
  .ipc-setting-box {
    width: 100%;
    min-height: 70px;
    padding: 0px 15px;
    box-sizing: border-box;
    .ipc-setting-line {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 12px 0px;
    }
    .ipc-setting-left {
      flex: 1;
    }
    .ipc-setting-title {
      height: 24px;
      font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      line-height: 24px;
      white-space: nowrap;
    }
    .ipc-setting-text {
      display: flex;
      align-items: center;
      font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      line-height: 24px;
      overflow: hidden;
      margin-left: 20px;
    }
    .arrow-img {
      width: 24px;
      height: 24px;
    }
    .arrow-icon-box {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .arrow-icon {
      font-size: var(--font-size-body1-size, 16px);
      color: var(--text-color-primary, #1a1a1a);
    }
  }
  .ipc-setting-desc {
    min-height: 20px;
    line-height: 20px;
    width: 100%;
  }
}
</style>
