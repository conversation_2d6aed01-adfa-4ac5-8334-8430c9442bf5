const path = require('path')
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
const compressionPlugin = require('compression-webpack-plugin')
// const webpack = require('webpack')

const resolve = dir => path.join(__dirname, dir)
const name = 'superlive-app-web'
// 生产环境，测试和正式
const IS_PROD = ['production', 'prod'].includes(process.env.NODE_ENV)

const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  publicPath: IS_PROD ? '/userapp/' : '/', // 署应用包时的基本 URL。 vue-router hash 模式使用
  outputDir: 'dist', //  生产环境构建文件的目录
  assetsDir: 'static', //  outputDir的静态资源(js、css、img、fonts)目录
  lintOnSave: !IS_PROD,
  productionSourceMap: false, // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  devServer: {
    port: 9020, // 端口
    open: false, // 启动后打开浏览器
    client: {
      overlay: false
    },
    proxy: {
      '/h5/v1.0': {
        target: process.env.VUE_APP_PROXY_URL, // env.development or 自己的.local
        changeOrigin: true,
        pathRewrite: {
          '^/h5/v1.0': ''
        },
        secure: false
      },
      '/userstatic': {
        target: process.env.VUE_WEBSTATIC_PROXY_URL,
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/userapp/userstatic': '/userstatic'
        }
      },
      '/vmsstatic': {
        target: process.env.VUE_WEBSTATIC_PROXY_URL,
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/userapp/userstatic': '/vmsstatic'
        }
      },
      '/partnerstatic': {
        target: process.env.VUE_WEBSTATIC_PROXY_URL,
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/partner/userstatic': '/partnerstatic'
        }
      }
    }
  },
  css: {
    extract: IS_PROD, // 是否将组件中的 CSS 提取至一个独立的 CSS 文件中 (而不是动态注入到 JavaScript 中的 inline 代码)。
    sourceMap: false,
    loaderOptions: {
      scss: {
        // 向全局sass样式传入共享的全局变量, $src可以配置图片cdn前缀
        // 详情: https://cli.vuejs.org/guide/css.html#passing-options-to-pre-processor-loaders
        additionalData: `
          @import "assets/css/mixin.scss";
          @import "assets/css/variables.scss";
          `
      }
    }
  },
  configureWebpack: config => {
    config.name = name
    config.plugins.push(new NodePolyfillPlugin())
    // // 设置chunk数目限制，避免出现太多小的js文件
    // config.plugins.push(
    //   new webpack.optimize.LimitChunkCountPlugin({
    //     maxChunks: 10 // 来限制 chunk 的最大数量，避免出现太多小的chunk
    //   })
    // )
    if (IS_PROD) {
      config.plugins.push(
        new compressionPlugin({
          filename: '[path][base].gz', //  使得多个.gz文件合并成一个文件，这种方式压缩后的文件少，建议使用
          algorithm: 'gzip', //开启压缩方式
          test: /\.js$|\.css$/, // 使用正则给匹配到的文件做压缩，这里是给html、css、js以及字体（.ttf和.woff和.eot）做压缩
          threshold: 1024, //以字节为单位压缩超过这个的文件，使用默认值10240
          minRatio: 0.8, // 最小压缩比率，官方默认0.8
          deleteOriginalAssets: false
          //是否删除原有静态资源文件，即只保留压缩后的.gz文件，建议这个置为false，还保留源文件。原因： // 如果出现访问.gz文件访问不到的时候，还可以访问源文件。
        })
      )
    }
  },
  chainWebpack: config => {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // 别名 alias
    config.resolve.alias
      .set('@', resolve('src'))
      .set('assets', resolve('src/assets'))
      .set('api', resolve('src/api'))
      .set('views', resolve('src/views'))
      .set('components', resolve('src/components'))
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    config
      // https://webpack.js.org/configuration/devtool/#development
      .when(!IS_PROD, config => config.devtool('cheap-source-map'))

    config.when(IS_PROD, config => {
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          // cacheGroups 下可以可以配置多个组，每个组根据test设置条件，符合test条件的模块
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'),
            minChunks: 3, //  被至少用三次以上打包分离
            priority: 5, // 优先级
            reuseExistingChunk: true // 表示是否使用已有的 chunk，如果为 true 则表示如果当前的 chunk 包含的模块已经被抽取出去了，那么将不会重新生成新的。
          },
          node_vendors: {
            name: 'chunk-libs',
            chunks: 'initial', // 只打包初始时依赖的第三方
            test: /[\\/]node_modules[\\/]/,
            priority: 10
          },
          vantUI: {
            name: 'chunk-vantUI', // 单独将 vantUI 拆包
            priority: 20, // 数字大权重到，满足多个 cacheGroups 的条件时候分到权重高的
            test: /[\\/]node_modules[\\/]_?vant(.*)/
          }
        }
      })
      config.optimization.runtimeChunk('single')
    })
  }
})
