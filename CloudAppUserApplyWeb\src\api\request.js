/* eslint-disable no-undef */
import axios from 'axios'
import { Toast } from 'vant'
import { getBasic, noticeAppLogout } from '@/utils/basic'
import { specialCode } from '@/utils/specialCode'
import { getParamsFromUserAgent } from '@/utils/common'
import { isPhone } from '@/utils/common.js'
import { handleError as reportLog } from '@/utils/appLogger'
import { isGuest, appReqeustNative, appLog } from '@/utils/appbridge'
import Vuei18n from '@/lang'
import store from '@/store'

// 生产地址
// axios.defaults.baseURL = serverBase; // 配置axios请求的地址

const request = axios.create({
  baseURL: '',
  timeout: 60000 //改成60s
})

// 格式化请求参数
const formatParams = params => {
  const basic = getBasic()
  return {
    basic,
    data: params
  }
}

// 判断是否为游客
function requestIsGuest() {
  return new Promise(resolve => {
    try {
      isGuest(res => {
        // console.log('isGuest返回结果', res)
        // Toast(`isGuest返回结果: ${res}`)
        // 从APP获取当前用户是否为游客
        const resData = JSON.parse(res)
        const isGuest = resData.body.isGuest || false
        resolve(isGuest)
        console.log('isGuest', isGuest)
      })
    } catch (err) {
      // 判断游客失败则认为是正式用户，直接通知退出
      resolve(false)
    }
  })
}

// 游客重新登录
let reLoginPromise = null
const reLoginPromiseFn = errCode => {
  reLoginPromise = new Promise(resolve => {
    let req = {
      url: 'APP_RELOGIN',
      params: {
        code: errCode.toString()
      },
      timeout: 3000
    }
    try {
      appReqeustNative(req, function (res) {
        // 游客重新登录回调函数
        // Toast(`测试游客刷新res: ${res}`)
        // console.log(`测试游客刷新res: ${res}`)
        setTimeout(() => {
          reLoginPromise = null
        }, 60000)
        const { code, body } = JSON.parse(res)
        if (code === 200) {
          console.log('body', body, typeof body)
          if (body) {
            const { token } = typeof body === 'string' ? JSON.parse(body) : body
            // console.log('从body中解析出token', token)
            if (token) {
              // APP重新登录，则直接刷新
              // console.log('获取到token', token)
              resolve(token)
              return
            }
          }
        }
        resolve()
      })
    } catch (err) {
      console.log('reLoginPromiseFn异常错误', err)
      resolve()
    }
  })
  return reLoginPromise
}

// 正式用户刷新token
let refreshTokenPromise = null
const refreshTokenPromiseFn = errCode => {
  refreshTokenPromise = new Promise(resolve => {
    // console.log('进入refreshTokenPromiseFn方法')
    const req = {
      url: 'APP_REFRESH_TOKEN',
      params: {
        code: errCode.toString()
      },
      timeout: 3000
    }
    // console.log('进入app刷新token appReqeustNative', req)
    try {
      appReqeustNative(req, function (res) {
        // console.log('app刷新token返回结果', res)
        setTimeout(() => {
          refreshTokenPromise = null
        }, 60000)
        const { code, body } = JSON.parse(res)
        if (code === 200) {
          if (body) {
            const { token } = body
            // console.log('token', token)
            if (token) {
              // APP重新登录，则直接刷新
              // console.log('获取到token', token)
              resolve(token)
              return
            }
          }
        }
        resolve()
      })
    } catch (err) {
      console.log('refreshTokenPromiseFn异常错误', err)
      resolve()
    }
  })
  return refreshTokenPromise
}

let showOtherMsg = true

async function requestHandler(config) {
  config.data = config.data || {}
  const { data, type, basic } = config.data
  if (process.env.NODE_ENV === 'development') {
    config.url = `${config.baseUrl || config.url.startsWith('/h5/v1.0') ? '' : '/h5/v1.0'}${config.url}`
  } else {
    config.url = `${config.url}`
  }
  if (data && basic) return config //如果传参时有传 basic 则不需要再加一次basic
  if (isPhone()) {
    // console.log('进入app/getToken', isPhone())
    await store.dispatch('app/getToken')
  }
  config.data = type === 'upload' ? data : formatParams(config.data)
  // console.log(config, 'config')
  return config // 返回这个配置对象，如果没有返回，这个请求就不会发送出去
}

async function responseHandler(response) {
  if (response.status != 200) {
    return false
  }
  let code
  if (response.data.basic) {
    code = response.data.basic.code
    // console.log('code', code, 'response', response)
    if (code === 200) {
      // noticeAppLogout(7003)
      return response.data
    } else if (code === 20004 || code === 10002 || code === 7009 || code === 7088 || code === 7090) {
      noticeAppLogout(code)
      return Promise.reject(code)
    } else if (code === 7003 || code === 7004) {
      // console.log('7003/7004错误码', code)
      const { bridgeType } = getParamsFromUserAgent()
      if (bridgeType === 'superMax') {
        return new Promise((resolve, reject) => {
          // superlive max 的7003、7004错误码单独处理
          /**
           * 7003 的错误码：
            游客relogin(游客relogin失败则退出登录)、正式用户都是直接通知APP退出登录；
            7004的错误码：
            游客relogin(游客relogin失败则退出登录)，正式用户是续签(正式用户续签失败退出登录)
           * 
           */
          requestIsGuest().then(isGuest => {
            if (isGuest) {
              // 游客则通知重新登录
              // console.log('游客通知app/relogin')
              if (!reLoginPromise) {
                appLog('log/info', `${new Date()} url:${response.config.url}触发reLoginPromiseFn游客重新登录`)
                reLoginPromiseFn(code)
                  .then(async token => {
                    // appLog(
                    //   'log/info',
                    //   `${new Date()} url:${response.config.url}触发reLoginPromiseFn游客重新登录回调， token: ${token}`
                    // )
                    // Toast(`${response.config.url}重新登录成功，token: ${token}`)
                    if (token) {
                      // 游客通知app/relogin成功，刷新接口请求
                      const newData = JSON.parse(response.config.data)
                      newData.basic.token = token // 更新下最新的token
                      response.config.data = newData
                      // console.log('reLoginPromiseFn重新调用函数', 'response.config', response.config)
                      const res = await request.request(response.config)
                      // console.log('reLoginPromiseFn重新调用函数返回', 'res', res)
                      // appLog(
                      //   'log/info',
                      //   `${new Date()} url: ${response.config.url
                      //   } 在reLoginPromiseFn续游客重新登录后重新调用，返回结果res: ${res}`
                      // )
                      resolve(res)
                    } else {
                      // 游客通知app/relogin失败则直接退出登录
                      noticeAppLogout(code)
                      reject({ code })
                    }
                  })
                  .catch(err => {
                    // console.log('游客reLoginPromiseFn异常', err, 'code', code)
                    appLog(
                      'log/warn',
                      `${new Date()} url:${response.config.url}触发reLoginPromiseFn续签失败， err: ${err}`
                    )
                    // 游客通知app/relogin失败则直接退出登录
                    noticeAppLogout(code)
                    reject({ code })
                  })
              } else {
                appLog('log/info', `${new Date()} url:${response.config.url}触发reLoginPromise游客重新登录`)
                reLoginPromise
                  .then(async token => {
                    // appLog(
                    //   'log/info',
                    //   `${new Date()} url:${response.config.url}触发reLoginPromise游客重新登录回调， token: ${token}`
                    // )
                    // Toast(`${response.config.url}重新登录成功，token: ${token}`)
                    if (token) {
                      // 游客通知app/relogin成功，刷新接口请求
                      const newData = JSON.parse(response.config.data)
                      newData.basic.token = token // 更新下最新的token
                      response.config.data = newData
                      // console.log('reLoginPromise重新调用函数', 'response.config', response.config)
                      const res = await request.request(response.config)
                      // console.log('reLoginPromise重新调用函数返回', 'res', res)
                      // appLog(
                      //   'log/info',
                      //   `${new Date()} url: ${response.config.url
                      //   } 在reLoginPromise续游客重新登录后重新调用，返回结果res: ${res}`
                      // )
                      resolve(res)
                    } else {
                      // 游客通知app/relogin失败则直接退出登录
                      noticeAppLogout(code)
                      reject({ code })
                    }
                  })
                  .catch(err => {
                    // console.log('游客reLoginPromise异常', err, 'code', code)
                    appLog(
                      'log/warn',
                      `${new Date()} url:${response.config.url}触发reLoginPromise续签失败， err: ${err}`
                    )
                    // 游客通知app/relogin失败则直接退出登录
                    noticeAppLogout(code)
                    reject({ code })
                  })
              }
            } else {
              if (code === 7003) {
                // 正式用户通知app/logout
                noticeAppLogout(code)
                reject({ code })
              } else {
                // 正式用户通知app/refreshToken
                if (!refreshTokenPromise) {
                  appLog('log/info', `${new Date()} url:${response.config.url}触发refreshTokenPromiseFn续签`)
                  refreshTokenPromiseFn(code)
                    .then(async token => {
                      // console.log('refreshTokenPromiseFn token', token, 'response', response)
                      // appLog(
                      //   'log/info',
                      //   `${new Date()} url:${response.config.url}触发refreshTokenPromiseFn续签回调， token: ${token}`
                      // )
                      // Toast(`${response.config.url}续签成功，token: ${token}`)
                      if (token) {
                        // 正式用户续签成功，刷新接口请求
                        const newData = JSON.parse(response.config.data)
                        newData.basic.token = token // 更新下最新的token
                        response.config.data = newData
                        // console.log('refreshTokenPromiseFn重新调用函数', 'response.config', response.config)
                        const res = await request.request(response.config)
                        // console.log('refreshTokenPromiseFn重新调用函数返回', 'res', res)
                        // appLog(
                        //   'log/info',
                        //   `${new Date()} url: ${response.config.url
                        //   } 在refreshTokenPromiseFn续签后重新调用，返回结果res: ${res}`
                        // )
                        resolve(res)
                      } else {
                        // 正式用户续签失败退出登录
                        noticeAppLogout(code)
                        reject({ code })
                      }
                    })
                    .catch(err => {
                      // console.log('正式用户refreshTokenPromiseFn异常', err, 'code', code)
                      appLog(
                        'log/warn',
                        `${new Date()} url:${response.config.url}触发refreshTokenPromiseFn续签失败， err: ${err}`
                      )
                      // 正式用户续签失败退出登录
                      noticeAppLogout(code)
                      reject({ code })
                    })
                } else {
                  appLog('log/info', `${new Date()} url:${response.config.url}触发refreshTokenPromise续签`)
                  refreshTokenPromise
                    .then(async token => {
                      // console.log('refreshTokenPromise token', token)
                      // Toast(`${response.config.url}续签成功，token: ${token}`)
                      if (token) {
                        // appLog(
                        //   'log/info',
                        //   `${new Date()} url:${response.config.url}触发refreshTokenPromise续签回调， token: ${token}`
                        // )
                        // 正式用户续签成功，刷新接口请求
                        const newData = JSON.parse(response.config.data)
                        newData.basic.token = token // 更新下最新的token
                        response.config.data = newData
                        // console.log('refreshTokenPromise重新调用函数', 'response.config', response.config)
                        const res = await request.request(response.config)
                        // console.log('refreshTokenPromise重新调用函数返回', 'res', res)
                        // appLog(
                        //   'log/info',
                        //   `${new Date()} url: ${response.config.url
                        //   } 在refreshTokenPromise续签后重新调用，返回结果res: ${res}`
                        // )
                        resolve(res)
                      } else {
                        // 正式用户续签失败退出登录
                        noticeAppLogout(code)
                        reject({ code })
                      }
                    })
                    .catch(err => {
                      // console.log('正式用户refreshTokenPromise异常', err, 'code', code)
                      appLog(
                        'log/warn',
                        `${new Date()} url:${response.config.url}触发refreshTokenPromiseFn续签失败， err: ${err}`
                      )
                      // 正式用户续签失败退出登录
                      noticeAppLogout(code)
                      return Promise.reject({ code })
                    })
                }
              }
            }
          })
        })
      } else {
        // 直接退出登录
        noticeAppLogout(code)
        return Promise.reject(code)
      }
    } else if (code === 34006) {
      // 转移记录不存在不需要统一弹窗提示，由页面自己处理
      return Promise.reject(res.data)
    } else {
      const codeMsg = Vuei18n.t(`errorCode.${code}`) //改为这种写法后 即使没有翻译 codeMsg也不为false
      if (codeMsg && codeMsg !== `errorCode.${code}`) {
        if (showOtherMsg) {
          Toast(codeMsg)
          showOtherMsg = false
          setTimeout(() => {
            showOtherMsg = true
          }, 2000)
        }
      } else if (!specialCode.includes(code)) {
        if (showOtherMsg) {
          Toast(codeMsg)
          showOtherMsg = false
          setTimeout(() => {
            showOtherMsg = true
          }, 2000)
        }
      }
      return Promise.reject(response.data)
    }
  } else {
    return response.data
  }
}

function responseHandlerWithoutMsg(res) {
  if (res.status != 200) {
    return false
  }
  let code
  if (res.data.basic) {
    code = res.data.basic.code
    if (code === 200) {
      return res.data
    } else if (code === 20004 || code === 10002 || code === 7009 || code === 7088 || code === 7090 || code === 7004) {
      noticeAppLogout(code)
      return Promise.reject(code)
    } else if (code === 7003) {
      // 进行token续签
      noticeAppLogout(code)
      return Promise.reject(code)
    } else {
      return Promise.reject(res.data)
    }
  } else {
    return res.data
  }
}

function responseErrorHandler(error) {
  // axios判断请求超时进行处理
  if (error.message.includes('timeout')) {
    if (showOtherMsg) {
      Toast(Vuei18n.t('errorCode.12345'))
      showOtherMsg = false
      setTimeout(() => {
        showOtherMsg = true
      }, 2000)
    }
  } else if (error && error.response) {
    // 处理失败的状态码  状态码404 500 503之类的
    let statusCode = error.response.status
    if (showOtherMsg) {
      Toast(Vuei18n.t(`errorCode.${statusCode}`))
      showOtherMsg = false
      setTimeout(() => {
        showOtherMsg = true
      }, 2000)
    }
  } else if (error.message == 'Network Error') {
    if (showOtherMsg) {
      Toast(Vuei18n.t('errorCode.12344'))
      showOtherMsg = false
      setTimeout(() => {
        showOtherMsg = true
      }, 2000)
    }
  }
  reportLog({
    type: 'SERVICE_ERROR',
    url: error?.config?.url,
    message: error.message
  })
  return Promise.reject(error)
}

// 请求拦截器
request.interceptors.request.use(requestHandler, error => {
  return Promise.reject(error)
})

// 响应拦截器
request.interceptors.response.use(responseHandler, responseErrorHandler)

export default request

export function createCustomRequest({ autoErrorMsg = true }) {
  const customRequest = axios.create({
    baseURL: '',
    timeout: 60000 //改成60s
  })

  customRequest.interceptors.request.use(requestHandler, error => {
    return Promise.reject(error)
  })

  if (autoErrorMsg) {
    customRequest.interceptors.response.use(responseHandler, responseErrorHandler)
  } else {
    customRequest.interceptors.response.use(responseHandlerWithoutMsg, responseErrorHandler)
  }
  return customRequest
}
