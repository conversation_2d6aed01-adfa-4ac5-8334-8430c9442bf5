<template>
  <!-- 满意度收集 选星星组件 -->
  <div class="level-choose">
    <div class="choose-div">
      <div class="stars-choose" v-for="(item, index) in startsArr" :key="index">
        <div class="every-star" @click="changeSelect(item, index)">
          <theme-image alt="star" :imageName="isSelect(item)" />
        </div>
      </div>
    </div>
    <div class="stars-note">{{ startsNote }}</div>
  </div>
</template>
<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'starsChoose',
  components: {
    ThemeImage
  },
  props: {},
  data() {
    return {
      startsArr: [{ checked: false }, { checked: false }, { checked: false }, { checked: false }, { checked: false }],
      noteArr: [this.$t('starOne'), this.$t('starTwo'), this.$t('starThree'), this.$t('starFour'), this.$t('starFive')]
    }
  },
  created() {},
  mounted() {},
  computed: {
    // 将 noteArr 改为计算属性
    // noteArr() {
    //   return [this.$t('starOne'), this.$t('starTwo'), this.$t('starThree'), this.$t('starFour'), this.$t('starFive')]
    // },
    //根据选择的星星数来显示提示语
    startsNote() {
      let arr = []
      arr = this.startsArr.filter(item => item.checked)
      if (arr.length) {
        return this.noteArr[arr.length - 1]
      } else {
        return ''
      }
    }
  },
  methods: {
    changeSelect(item, index) {
      // 选星星的逻辑 如果直接选了第4颗星 那么前面的都默认选了 如果取消了第2颗星 就只剩下1颗星了
      let newCheck = !item.checked
      if (newCheck) {
        for (let i = 0; i <= index; i++) {
          this.$set(this.startsArr, i, { checked: newCheck })
        }
      } else {
        for (let i = this.startsArr.length - 1; i >= index; i--) {
          this.$set(this.startsArr, i, { checked: newCheck })
        }
      }
      let arr = []
      arr = this.startsArr.filter(item => item.checked)
      this.$emit('change', arr.length)
    },
    isSelect(item) {
      return item.checked ? 'star_select.png' : 'star.png'
    }
  }
}
</script>
<style lang="scss" scoped>
.level-choose {
  .choose-div {
    display: flex;
    align-items: center;
    justify-content: center;
    .stars-choose {
      margin-right: 20px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .stars-note {
    min-height: 24px;
    text-align: center;
    font-size: var(--font-size-body1-size, 16px);
  }
}
</style>
