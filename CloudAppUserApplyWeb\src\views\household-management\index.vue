<template>
  <div class="household-manangement-wrapper">
    <nav-bar @clickLeft="back" showPlus @showPlus="addBuilding"></nav-bar>
    <tvt-better-scroll
      class="tvt-better-scroll"
      @pullingUp="pullingUp"
      @pullingDown="pullingDown"
      :pullingStatus="pullingStatus"
    >
      <template v-if="loading || dataList.length">
        <van-cell class="household-item" v-for="item in dataList" :key="item.id" @click="editBuilding(item)">
          <!-- 使用 title 插槽来自定义标题 -->
          <template #title>
            <div class="household-title">{{ item.buildingName }}</div>
            <div class="household-text">
              <div class="household-line-text">{{ $t('buildingNum') }} ：{{ item.buildingNo }}</div>
              <div class="household-line-text">{{ $t('roomNum') }} ： {{ item.roomNum }}</div>
            </div>
          </template>
        </van-cell>
      </template>

      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''" alt="noData" imageName="no_data.png" />
        </div>
        <div class="no-data-text">
          <van-button class="footer-btn" type="primary" @click="addBuilding">
            {{ $t('add') }}
          </van-button>
        </div>
      </div>
    </tvt-better-scroll>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import { queryBuildingList, queryBuildingDevices } from '@/api/householdManagement.js'
import { appClose, appSetBackToClose } from '@/utils/appbridge'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'householdManagement',
  components: {
    NavBar,
    ThemeImage
  },
  props: {},
  data() {
    return {
      dataList: [],
      listParams: {
        pageNum: 1, // 查所有
        pageSize: 1000
      },
      pullingStatus: 0,
      loading: false
    }
  },
  mounted() {
    appSetBackToClose(1)
    this.$nextTick(() => {
      this.getList()
    })
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_DEVICE_LIST', 'SET_BUILDING_INFO']),
    back() {
      appClose()
    },
    addBuilding() {
      // 新增楼栋时,
      // 初始化楼栋信息
      this.SET_BUILDING_INFO({})
      // 初始化设备列表
      this.queryDeviceList()
      this.$router.push('/household/addBuilding')
    },
    async queryDeviceList() {
      try {
        const { data } = await queryBuildingDevices({ id: this.buildingId, pageNum: 1, pageSize: 1000 })

        this.SET_DEVICE_LIST(data.records)
      } catch (error) {
        console.error(error)
      }
    },
    editBuilding(item) {
      const { id } = item
      this.SET_BUILDING_INFO({ ...item })
      this.$router.push({
        path: '/household/addBuilding',
        query: {
          id
        }
      })
    },
    pullingUp(callback) {
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.listParams.pageNum = 1
      this.getList({ type: 'down', callback })
    },

    async getList({ callback } = {}) {
      try {
        this.$loading.show()
        this.loading = true
        const { data } = await queryBuildingList(this.listParams)

        this.dataList = data.records || []
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.loading = false
        callback && callback()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  .tvt-better-scroll {
    padding: 10px 0;
    height: calc(100% - 64px);
  }
  .household-item {
    padding: 12px 16px;
    &::after {
      border: none;
    }
    .household-title {
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      line-height: 24px;
      margin-bottom: 10px;
    }
    .household-line-text {
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      line-height: 24px;
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 135px;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .footer-btn {
        padding: 0 35px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
