<template>
  <div class="channel-list-content">
    <div class="channel-list">
      <van-cell-group>
        <van-cell
          v-for="(item, index) of curChannelList"
          clickable
          :key="`${item.sn}~${item.chlIndex}~${item.chlId}`"
          :title="`${item.name}`"
        >
          <template #title>
            <div class="channel-cell-wrapper">
              <theme-image
                class="delete-icon"
                alt="delete"
                imageName="prohibit.png"
                @click.stop.native="e => handleDelete(e, item, index)"
              />
              <div class="channel-checkbox-content">{{ item.name }}</div>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'deleteChannel',
  components: {
    ThemeImage
  },
  props: {
    channelList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      curChannelList: [] // 展示的通道
    }
  },
  created() {},
  mounted() {},
  watch: {
    channelList: {
      handler(val) {
        if (val && JSON.stringify(val) !== JSON.stringify(this.curChannelList)) {
          this.curChannelList = val.slice()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 点击删除通道
    handleDelete(e, item, index) {
      // 阻止冒泡事件
      e.stopPropagation()
      // 通知外部删除
      this.$emit('click', item, index)
    }
  }
}
</script>

<style lang="scss" scoped>
.van-hairline--top-bottom::after {
  border-width: 0px;
}
// .van-hairline-unset--top-bottom::after {
//   border-width: 0px;
// }
.channel-list {
  .van-cell {
    padding: 0px;
  }
}
.channel-list-content {
  width: 100%;
  height: calc(100%);
  box-sizing: border-box;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
}
</style>
<style lang="scss">
.van-collapse-item__content {
  padding: 2px 0px !important;
}
.channel-cell-wrapper {
  width: 100%;
  height: 52px;
  padding: 15px 0px;
  box-sizing: border-box;
  display: flex;
}
.delete-icon {
  width: 24px;
  height: 24px;
  img {
    margin-right: 8px !important;
  }
}
.channel-checkbox-content {
  flex: 1;
}
</style>
