<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="$t('doorCard')" @clickLeft="back" showPlus @showPlus="addCard"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll">
      <template v-if="cardList.length">
        <van-swipe-cell v-for="(item, index) in cardList" :key="index">
          <van-cell class="household-item" center is-link @click="editCard(item, index)">
            <template #title>
              <span class="right-value text-over-ellipsis">{{ item }}</span>
            </template>
          </van-cell>
          <template #right>
            <van-button square type="danger" class="swipe-right-btn" @click="deleteCard(index)">
              <theme-image class="refuse-img" alt="delete" imageName="refuse.png" />
            </van-button>
          </template>
        </van-swipe-cell>
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''" alt="noData" imageName="no_data.png" />
        </div>
        <div class="no-data-text">
          <van-button class="add-btn" type="primary" @click="addCard">
            {{ $t('add') }}
          </van-button>
        </div>
      </div>
    </tvt-better-scroll>

    <!-- 添加/编辑门禁卡弹窗 -->
    <edit-dialog
      ref="cardDialog"
      :title="$t('doorCardNo')"
      :value="tempCardNo"
      :maxLength="20"
      type="number"
      @confirm="confirmCard"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import EditDialog from './dialog/EditDialog.vue'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'addDoorCard',
  components: {
    NavBar,
    ThemeImage,
    EditDialog
  },
  data() {
    return {
      cardList: [],
      tempCardNo: '',
      currentEditIndex: -1
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['memberInfo']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    }
  },
  mounted() {
    const { cardNo = '' } = this.memberInfo
    this.cardList = cardNo ? cardNo.split(',') : []
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_MEMBER_INFO']),
    back() {
      this.updateMemberInfo()
      this.$router.back()
    },
    updateMemberInfo() {
      const cardNo = this.cardList.join(',')
      this.SET_MEMBER_INFO({
        ...this.memberInfo,
        cardNo
      })
    },
    addCard() {
      if (this.cardList.length >= 5) {
        this.$toast(this.$t('maxCardLimit', [5]))
        return
      }
      this.tempCardNo = ''
      this.currentEditIndex = -1
      this.$refs.cardDialog.show = true
    },
    editCard(value, index) {
      this.tempCardNo = value
      this.currentEditIndex = index
      this.$refs.cardDialog.show = true
    },
    deleteCard(index) {
      this.cardList.splice(index, 1)
    },
    confirmCard(value) {
      if (this.currentEditIndex === -1) {
        // 添加新卡
        // 判断此卡号是否已经存在
        if (this.cardList.includes(value)) {
          this.$toast(this.$t('cardExist'))
          return
        }
        this.$refs.cardDialog.show = false
        this.cardList.push(value)
      } else {
        // 判断此卡号是否与其他卡号重复（排除当前编辑的索引）
        if (this.cardList.some((card, index) => card === value && index !== this.currentEditIndex)) {
          this.$toast(this.$t('cardExist'))
          return
        }
        this.$refs.cardDialog.show = false
        // 编辑现有卡
        const tempCardList = this.cardList.slice()
        tempCardList[this.currentEditIndex] = value
        this.cardList = tempCardList
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 80px;
  box-sizing: border-box;

  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .household-item {
    ::v-deep .van-cell__title {
      height: 24px;
    }

    ::v-deep .van-cell__value {
      height: 24px;
    }
    padding-top: 12px;
    padding-bottom: 12px;
    &::after {
      border: none;
    }

    .right-value {
      display: inline-block;
      width: 100%;
    }
  }

  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 12px 16px;
    background: $white-color;
    text-align: center;

    .card-count {
      color: $vms-gray;
      font-size: 14px;
    }
  }

  .swipe-right-btn {
    height: 100%;
    width: 65px;
    color: $white-color;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refuse-img {
    width: 24px;
    height: 24px;
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .add-btn {
        padding: 0 35px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
