<template>
  <div class="ipc-upgrade-list">
    <nav-bar @clickLeft="back"></nav-bar>
    <!-- ipc升级 -->
    <div class="ipc-upgrade">
      <div class="title">{{ bridgeType === 'superMax' ? $t('cameraUpdate') : '' }}</div>
      <div class="container">
        <div class="title-text">
          <div class="left">
            <div class="title-text-img">
              <theme-image alt="camera" imageName="camera.png" />
            </div>
            <div class="title-text-title text-over-ellipsis">{{ ipcInfo.ipcName }}</div>
          </div>
          <!-- 最新版本则隐藏升级按钮 -->
          <div class="right">
            <div
              :class="['title-text-button', !isIpcNeedUpgrade ? 'title-button-disabled' : '']"
              v-if="isShowUpgrade"
              @click="upgrade"
            >
              {{ $t('updateNow') }}
            </div>
            <div
              :class="[
                'title-text-button',
                !isIpcNeedUpgrade || [1, 2].includes(ipcInfo._state) ? 'title-button-disabled' : ''
              ]"
              v-else
              @click="cancelCloudUpgrade"
            >
              {{ $t('cancel') }}
            </div>
          </div>
        </div>
        <div class="list-content" v-if="isIpcNeedUpgrade">
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('currentVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">{{ ipcInfo.oldVer || '--' }}</div>
          </div>
          <div class="list-content-row" v-if="ipcInfo.ver">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('latestVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">{{ ipcInfo.ver || '--' }}</div>
          </div>
          <div class="list-content-row" v-if="isIpcNeedUpgrade">
            <div class="view-btn" @click="viewUpdateContent(ipcInfo.verNote)">
              {{ $t('viewUpdateContent') }}
            </div>
          </div>
          <!-- ipc升级 状态 升级中、升级失败、离线-->
          <div class="list-content-row" v-if="ipcInfo._state">
            <div class="download-status">
              <div
                v-if="ipcInfo.showIpcStatusText"
                class="download-status-text"
                :style="`color: ${statusColor[ipcInfo.ipcStatusColor]}`"
              >
                {{ ipcInfo.ipcStatusText }}
              </div>
              <span class="progress" v-if="ipcInfo.progress" :style="`color: ${statusColor[ipcInfo.ipcStatusColor]}`"
                ><span>(</span>{{ ipcInfo.progress }}<span>)</span></span
              >
            </div>
          </div>
          <!-- 提示 -->
          <div class="list-content-row" v-if="isShowUpgradeTip">
            <div class="download-tip">{{ $t('upgradeTip') }}</div>
          </div>
        </div>
        <!-- 摄像机 没有更新的 -->
        <div class="list-content" v-else>
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('currentVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              {{ devInfo.oldVer ? devInfo.oldVer : devInfo.ver ? devInfo.ver : '' }}
            </div>
          </div>
          <div class="list-content-row">
            <div class="has-latest-version">{{ $t('hasLatestVersion') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 摄像机升级 -->
    <div class="footer">
      <div class="footer-btn" @click="checkVersion">{{ $t('handleCheck') }}</div>
    </div>
    <!-- 查看 更新内容弹框 -->
    <van-popup v-model="showUpdateContent" class="pop-dialog" :close-on-click-overlay="false">
      <div class="pop-div">
        <div class="dialog-title">{{ $t('updateNote') }}</div>
        <div class="dialog-close-img" @click="closePopup">
          <theme-image alt="close" imageName="close.png" />
        </div>
      </div>
      <div class="update-box">
        <div class="update-content">{{ updateContent }}</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appSetTitle, appBack } from '@/utils/appbridge'
import { getParamsFromUserAgent, getUrlQuery, getMaxUrlQuery, debounce } from '@/utils/common'
import { getCloudUpgradeInfo, cloudUpgrade, getUpgradeResult } from '@/api/cloudUpgrade'
import { STATUS_COLOR_THEME } from '@/utils/options.js'
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'upgradeList',
  components: {
    NavBar,
    ThemeImage
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      devId: '',
      bindState: 0, // SN和安全码 添加的   bindState是1     ip添加的 bindState是0  IP添加的设备升级是要输入账号密码
      isIpcNeedUpgrade: true,
      showUpdateContent: false,
      isShowUpgrade: true, // 升级 还是 取消按钮的选择
      updateContent: '',
      ipcInfo: {
        state: '',
        ipcName: '',
        version: '',
        newVersion: '',
        newVersionGUID: '',
        progress: '',
        versionNote: '',
        showIpcStatusText: false, //升级的状态展示
        ipcStatusColor: '',
        ipcStatusText: ''
      },
      timer: null,
      cloudUpgradeStateMap: {
        // normal: this.$t('online'), //正常状态 不处于升级过程，就是正常在线状态
        upgradePrepare: this.$t('inupgrade'), //升级准备中
        upgrading: this.$t('inupgrade'), //升级中
        upgradeFail: this.$t('upgradeFail'), //升级失败
        offLine: this.$t('offline') //app给的code判断的设备离线
      },
      errorCode: null, //10000 设备离线
      bridgeType: null, // APP类型，需要区分出superlive max
      statusTextObj: {
        0: 'waitingForUpgrade',
        1: 'downloading',
        2: 'downloadSuccess',
        3: 'downloadFail',
        4: 'installing',
        5: 'installSuccess',
        6: 'installFail',
        7: 'offLine'
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  mounted() {
    appSetTitle(this.$t('upgrade'))
    const { bridgeType } = getParamsFromUserAgent()
    this.bridgeType = bridgeType
    let json = {}
    if (bridgeType === 'superMax') {
      // superlive max走正常的网址解析
      json = getMaxUrlQuery(window.location.href)
    } else {
      json = getUrlQuery(window.location.href)
    }
    this.devId = json.devId
    this.bindState = json.bindState
    this.ipcInfo.ipcName = decodeURIComponent(decodeURIComponent(json.devName))
    console.log(this.devId, this.ipcInfo.ipcName, this.language, this.bindState)
    // 之前superlive cloud等APP的逻辑
    this.getCloudUpgradeInfo()
    this.startTimer() //开启定时器
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    isShowUpgradeTip() {
      // let stateList = ['upgradePrepare', 'upgrading', 'offLine']
      let stateList = [
        'waitingForUpgrade',
        'downloading',
        'downloadFail',
        'downloadNetException',
        'downloadFailNodeInvalid',
        'downloadFailOSSException',
        'downloadFailNodeDisconnect',
        'downloadFailFileWritExecption',
        'downloadFailFileReadExecption',
        'downloadFailFileOpenExecption',
        'downloadSuccess',
        'installing',
        'offLine'
      ]
      return !!stateList.includes(this.ipcInfo.state)
    },
    languageFlag() {
      return this.language == 'zh'
    },
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    statusColor() {
      return STATUS_COLOR_THEME(this.appStyleFlag, this.uiStyleFlag)
    }
  },
  methods: {
    back() {
      clearInterval(this.timer)
      this.timer = null
      appBack()
    },
    // 查询IPC云升级信息
    async getCloudUpgradeInfo() {
      const params = {
        sn: this.devId
      }
      try {
        const res = await getCloudUpgradeInfo(params)
        const { device, packages = [] } = res.data
        // 遍历packages记录设备及通道的升级信息
        const packagesObj = packages.reduce((pre, next) => {
          const { verId } = next
          pre[verId] = { ...next }
          return pre
        }, {})
        this.packagesObj = packagesObj
        const { oldVer, ver, verId } = device
        this.devInfo = { ...this.devInfo, ...device, ...(packagesObj[verId] || {}) }
        this.isIpcNeedUpgrade = !!oldVer && !!ver // 可升级版本号，有新版本时才有此节点
      } catch (err) {
        console.error(err)
      }
    },
    // 设备升级
    upgrade() {
      if (!this.isIpcNeedUpgrade) {
        this.$toast(this.$t('hasLatestVersion'))
      } else if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      } else if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      } else if (this.bindState == 0) {
        this.$refs.checkPwd.show = true // 用户名密码添加的设备
      } else {
        this.cloudUpgrade()
      }
    },
    // IPC 开始云升级
    async cloudUpgrade() {
      // 已经是最新版本则不响应事件
      if (!this.isIpcNeedUpgrade) return
      try {
        const params = {
          sn: this.devId
        }
        const res = await cloudUpgrade(params)
        console.log('执行云升级返回res', res)
      } catch (err) {
        console.error(err)
      }
    },
    // 检测更新 先关闭定时器 检测完后再开启轮询
    checkVersion: debounce(async function () {
      this.$toast.loading({
        // superlive max显示加载中文字
        message: this.bridgeType === 'superMax' ? this.$t('loadingText') : '',
        className: 'upgrade-check-loading',
        forbidClick: true,
        duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
      })
      await this.getCloudUpgradeInfo()
      this.$toast.clear()
    }, 100),
    // 查看更新内容
    viewUpdateContent(content) {
      this.showUpdateContent = true
      this.updateContent = content || this.$t('noData')
    },
    closePopup() {
      this.showUpdateContent = false
      setTimeout(() => {
        this.updateContent = ''
      }, 300) // 弹框动画api 默认是300ms 这里处理是滚动条位置初始化
    },
    // 查询云升级 设备和通道信息的定时器
    startTimer() {
      let that = this
      this.timer = setInterval(() => {
        that.getUpgradeResult()
      }, 3000)
    },
    // 查询云升级结果
    async getUpgradeResult() {
      try {
        let resultIds = []
        const { resultId } = this.devInfo
        if (resultId) {
          resultIds.push(resultId)
        }
        this.cameraList.forEach(item => {
          item.resultId && resultIds.push(item.resultId)
        })
        if (resultIds.length === 0) {
          return
        }
        const params = {
          resultIds
        }
        const res = await getUpgradeResult(params)
        // 遍历云升级返回结果，把状态加入设备
        const { state } = res.data[0]
        const stateText = this.statusTextObj[state]
        if (resultId) {
          this.devInfo = {
            ...this.devInfo,
            _state: state,
            state: stateText,
            showDevStatusText: true,
            devStatusText: this.cloudUpgradeStateMap[stateText],
            devStatusColor: stateText
          }
        }
        // 升级后重新请求云升级信息
        if (state > 1) {
          this.getCloudUpgradeInfo()
        }
        console.log('云升级返回结果', res)
      } catch (err) {
        console.error(err)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.ipc-upgrade-list {
  height: calc(100% - 70px);
  overflow: auto;
}
.title-button-disabled {
  color: var(--brand-bg-color-disabled, #d6e4fe);
  border: 1px solid var(--brand-bg-color-disabled, #d6e4fe);
}
</style>
