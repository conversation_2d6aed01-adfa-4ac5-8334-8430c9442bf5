<template>
  <div class="household-manangement-wrapper choose-device">
    <nav-bar :title="$t('relateDevice')" @clickLeft="back"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <template v-if="deviceList.length">
        <van-cell class="household-item" v-for="item in deviceList" :key="item.sn" @click="toggleItem(item)">
          <!-- 使用 title 插槽来自定义标题 -->
          <template #title>
            <div class="household-title">{{ item.devName }}</div>
          </template>
          <template #right-icon>
            <van-icon class="device-select-icon success" v-if="item.status === 1" name="checked" />
            <van-icon class="device-select-icon" v-else name="circle" />
          </template>
        </van-cell>
      </template>

      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''" alt="noData" imageName="no_data.png" />
        </div>
        <div class="no-data-text">{{ $t('noData') }}</div>
      </div>
    </tvt-better-scroll>
    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleClick">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import { queryBuildingDevices, updateBuildingDevices } from '@/api/householdManagement.js'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'addRoom',
  components: {
    NavBar,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isAdd: true,
      pullingStatus: 0,
      buildingId: '',
      params: {
        pageNum: 1,
        pageSize: 1000
      }
    }
  },
  mounted() {
    if (this.$route.query.buildingId) {
      this.isAdd = false
      this.buildingId = this.$route.query.buildingId
      this.queryDeviceList()
    } else {
      this.isAdd = true
      this.buildingId = ''
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['deviceList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_DEVICE_LIST', 'UPDATE_DEVICE_LIST']),
    back() {
      this.$router.go(-1)
    },
    async handleClick() {
      if (this.isAdd) {
        this.back()
      } else {
        try {
          this.$loading.show()
          await updateBuildingDevices({
            id: this.buildingId,
            snList: this.deviceList.filter(device => device.status === 1).map(device => device.sn)
          })
          this.back()
          this.$toast(this.$t('changeSuccessfully'))
        } catch (error) {
          console.error(error)
        } finally {
          this.$loading.hide()
        }
      }
    },
    async pullingDown(callback) {
      await this.queryDeviceList()
      callback()
    },
    toggleItem(device) {
      this.UPDATE_DEVICE_LIST({
        ...device,
        status: device.status === 1 ? 0 : 1
      })
    },
    async queryDeviceList() {
      try {
        this.$loading.show()
        const { data } = await queryBuildingDevices({ id: this.buildingId, pageNum: 1, pageSize: 1000 })

        this.SET_DEVICE_LIST(data.records || [])
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 90px;
  box-sizing: border-box;

  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;

    .household-item {
      padding-top: 12px;
      padding-bottom: 12px;
      &::after {
        border: none;
      }

      .right-value {
        display: inline-block;
        width: 160px;
      }
    }

    .device-select-icon {
      font-size: var(--font-size-h5-size, 18px);
      line-height: 24px;
      height: 24px;
      display: inline-block;
    }
  }

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 135px;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .add-btn {
        width: 100px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
