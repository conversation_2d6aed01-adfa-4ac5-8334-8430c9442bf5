// 记录错误，以及console.error, 并发送到app 日志里
// 实行双轨制，输出到app和本地互不影响
// 主要需要记录三种问题
// 处理接口异常
// 页面自身错误
// 与app交互异常, 目前没定好通用的错误协议，暂时不搞

import { appLog } from './appbridge'

const logLevel = {
  info: 'log/info',
  error: 'log/error',
  warn: 'log/warn',
  debug: 'log/debug'
}

const H5Prefix = 'H5_ERROR: '

let lastData = null
export function handleError(data) {
  try {
    let logContent = JSON.stringify(data, null, ' ')

    if (lastData === logContent) {
      return
    }
    lastData = logContent

    appLog(logLevel.error, H5Prefix + logContent)
  } catch (error) {
    appLog(
      logLevel.error,
      H5Prefix +
        JSON.stringify({
          type: 'CONSOLE_ERROR',
          message: 'fail to log error'
        })
    )
  }
}

function initErrorLogger() {
  // 页面自身错误
  window.addEventListener('error', event => {
    const { message, filename, lineno, colno, error } = event

    const logData = {
      type: 'JS_ERROR',
      message: message,
      file: filename,
      line: lineno,
      column: colno,
      stack: error?.stack || '无堆栈信息'
    }
    handleError(logData)
  })

  // 捕获未处理的Promise rejection
  window.addEventListener('unhandledrejection', event => {
    const {
      reason: { message, stack }
    } = event
    const logData = {
      type: 'PROMISE_REJECTION',
      message: message || '未知错误',
      stack: stack || '无堆栈信息'
    }

    handleError(logData)
  })

  const oldErorHandler = console.error

  console.error = (...params) => {
    const msg = params.map(item => {
      if (item instanceof Error) {
        return {
          msg: item.message,
          stack: item.stack
        }
      }
      return item
    })
    handleError({
      type: 'CONSOLE_ERROR',
      message: msg
    })

    oldErorHandler.apply(console, params)
  }

  // 接口异常已经在接口模块处理
  // 路由异常已经在路由模块处理
}

initErrorLogger()
