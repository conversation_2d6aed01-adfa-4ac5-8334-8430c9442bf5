<template>
  <div class="panel-outputs-box">
    <!-- DSC 标题 -->
    <div class="dsc-title">{{ siteName || 'DSC' }}</div>
    <!-- DSC输出区域 -->
    <div class="alarm-box-wrapper output-section">
      <div class="output-devices">
        <output-device
          v-for="(output, index) in dscGroup.outputs"
          :key="`dsc${groupIndex}-${index}`"
          :output="output"
          @action="handleOutputAction"
        />
      </div>
    </div>
  </div>
</template>

<script>
import OutputDevice from './OutputDevice.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'DscGroup',
  components: {
    OutputDevice
  },
  props: {
    dscGroup: {
      type: Object,
      required: true,
      validator(value) {
        return value && Array.isArray(value.outputs) && typeof value.id !== 'undefined'
      }
    },
    groupIndex: {
      type: Number,
      required: true
    }
  },
  emits: ['output-action'],
  computed: {
    ...mapGetters('alarmSystem', ['siteName'])
  },
  methods: {
    handleOutputAction(payload) {
      this.$emit('output-action', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
.dsc-title {
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  padding: 0 16px;
}
.alarm-box-wrapper {
  border-radius: 8px;
  overflow: hidden;
}
</style>
