<template>
  <div class="trusteeship-check">
    <div class="container-ul">
      <div class="container-li" v-for="(item, index) in availableList" :key="'item' + index">
        <div class="li-title">
          <div class="li-title-left">
            <van-checkbox v-model="item.flag">
              <template #icon="props">
                <img
                  class="check-img"
                  @click="curShow(item)"
                  :src="
                    props.checked
                      ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check.png')
                      : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_no.png')
                  "
                />
              </template>
            </van-checkbox>
            <div class="li-name text-over-ellipsis">{{ item.devName }}</div>
          </div>
        </div>
        <div :class="item.show ? 'data-ul' : ''" v-show="item.show">
          <!-- 这里修改成权限和有效期跳到另外的页面选择后带回 -->
          <div class="v-li" @click.stop="choosePermission(index)">
            <div class="v-li-content">
              <div class="v-li-label">{{ $t('permission') }}</div>
              <div class="v-li-value">{{ resAuth(item) }}</div>
            </div>
            <div class="v-li-icon check-img"><i class="arrow-right"></i></div>
          </div>
          <div class="v-li" @click.stop="chooseValidity(index)">
            <div class="v-li-content">
              <div class="v-li-label">{{ $t('validity') }}</div>
              <div class="v-li-value">{{ resValidity(item) }}</div>
            </div>
            <div class="v-li-icon check-img"><i class="arrow-right"></i></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { TRUSTEESHIP_VALIDITY_LIST } from '@/utils/options.js'
export default {
  name: 'check',
  components: {},
  props: {},
  data() {
    return {
      pageContent: navigator.userAgent,
      validityList: TRUSTEESHIP_VALIDITY_LIST()
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('trusteeship', ['availableList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    curShow(item) {
      this.$emit('curShow', item)
    },
    resAuth(item) {
      let authStr = ''
      if (item.authList && item.authList.length) {
        item.authList.forEach(o => {
          if (o.flag) {
            authStr += this.$t(o.label) + '、'
          }
        })
      }
      authStr = authStr.slice(0, authStr.length - 1)
      return authStr
    },
    resValidity(item) {
      let str = ''
      let i = this.validityList.findIndex(o => o.value == item.effectiveTime)
      if (i !== -1) {
        str = this.validityList[i].label
      }
      return str
    },
    // 选择权限
    choosePermission(index) {
      this.$router.push({ name: 'permission', params: { index: index, from: 'add' } })
    },
    // 选择有效期
    chooseValidity(index) {
      this.$router.push({ name: 'validity', params: { index: index, from: 'add' } })
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-check {
  .container-ul {
    background-color: var(--bg-color-white, #ffffff);
    padding: 2px 0px;
    .container-li {
      border-bottom: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      padding: 0px 15px;
      &:last-child {
        border: 0;
      }
      .check-img {
        width: 20px;
        height: 20px;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        i {
          position: absolute;
          transition: all ease 0.6s;
          top: 2px;
          width: 14px;
          height: 14px;
          display: inline-block;
          background-image: url('../assets/img/common/arrow_up.png');
          background-repeat: no-repeat;
          background-size: 14px;
          background-position-y: center;
          background-position-x: center;
        }
      }
      .arrow-up {
        transform: rotate(0deg);
      }
      .arrow-down {
        transform: rotate(180deg);
      }
      .arrow-right {
        transform: rotate(90deg);
      }
      .li-title {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
        .li-title-left {
          display: flex;
        }
        .li-name {
          margin-left: 10px;
          max-width: 280px;
          font-weight: 500;
        }
      }
    }
    .data-ul {
      border-top: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      padding: 4px 0;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--text-color-placeholder, #8f8e93);
    }
    .v-li {
      padding: 6px 0 6px 30px;
      display: flex;
      justify-content: space-between;
      .v-li-content {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      .v-li-value {
        text-align: right;
        padding-right: 10px;
      }
    }
  }
}
</style>
