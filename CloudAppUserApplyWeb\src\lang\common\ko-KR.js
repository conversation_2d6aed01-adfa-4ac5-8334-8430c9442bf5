// 韩语
export default {
  upgrade: '클라우드 업그레이드',
  cancel: '취소',
  confirm: '확인',
  deviceUpdate: '장치',
  cameraUpdate: '카메라',
  allUpdate: '전체 업그레이드',
  updateNow: '업그레이드',
  currentVersion: '현재 버전',
  latestVersion: '최신 버전',
  updateContent: '업데이트 내용',
  hasLatestVersion: '이미 최신 버전입니다',
  online: '온라인',
  offline: '오프라인',
  waitDownload: '다운로드 대기 중',
  inprogress: '다운로드 중',
  downloadFail: '다운로드 실패',
  downloadFinished: '다운로드 완료',
  inupgrade: '업그레이드 중',
  upgradeFail: '업그레이드 실패',
  upgradeSuccess: '업그레이드 성공',
  deviceUpgradeInfo: '업그레이드 중에는 장치가 연결이 끊기고 자동으로 재시작됩니다. 업그레이드하시겠습니까?',
  upgradeTip: '업그레이드 중에는 장치가 연결이 끊기고 자동으로 재시작됩니다',
  cameraUpgradeInfo: '업그레이드 중에는 카메라가 연결이 끊기고 자동으로 재시작됩니다. 업그레이드하시겠습니까?',
  pwdUserNameError: '사용자 이름 또는 비밀번호 오류',
  permissionAuth: '최고 관리자 권한 인증',
  pleaseEnterUser: '사용자 이름을 입력하세요',
  pleaseEnterPwd: '비밀번호를 입력하세요',
  noCameraUpgrade: '업그레이드 가능한 카메라가 감지되지 않았습니다',
  handleCheck: '업데이트 감지',
  paySuccess: '결제 성공',
  payFail: '결제 실패',
  done: '완료',
  rePurchase: '재구매',
  cloudStorage: '클라우드 저장소',
  INSTRUMENT_DECLINED: '거래가 카드 한도를 초과했습니다',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: '결제자의 계정은 이 거래에 사용할 수 없습니다',
  PAYER_ACCOUNT_RESTRICTED: '결제자의 계정이 제한되었습니다',
  TRANSACTION_LIMIT_EXCEEDED: '총 결제 금액이 거래 한도를 초과했습니다',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: '거래가 수신자의 수금 한도를 초과했습니다',
  myInstaller: '나의 설치업체',
  trusteeshipDevice: '관리 중인 장치',
  addTrusteeship: '호스팅 추가',
  waitReceived: '수신 대기 중',
  received: '수신 완료',
  refuse: '거부',
  delete: '삭제',
  operationSuccess: '작업 성공',
  operationFail: '작업 실패',
  cancelTrusteeship: '호스팅 취소',
  chooseDevice: '장치 선택',
  noAvaiableDevice: '장치 버전이 호스팅 기능을 지원하고 계정에 바인딩된 경우에만 호스팅할 수 있습니다',
  leastChoose: '최소한 하나의 장치를 선택하세요',
  details: '상세 정보',
  live: '라이브',
  rec: '재생',
  config: '구성',
  confirmTrusteeshipTip: '호스팅 요청이 설치업체에게 전송되었습니다. 설치업체가 처리할 때까지 기다려 주세요',
  cancelTrusteeshipTip:
    '호스팅을 취소하면 설치업체가 원격 유지보수 서비스를 제공할 수 없습니다. 정말로 취소하시겠습니까?',
  unBindTrusteeship: '장치 바인딩을 해제하면 모든 호스팅이 취소됩니다. 정말로 해제하시겠습니까?',
  trusteeshipPermissions: '호스팅 권한',
  trusteeshipTime: '호스팅 시간',
  unBind: '바인딩 해제',
  serviceException: '서비스 예외',
  pullingText: '당겨서 즉시 로딩...',
  loosingText: '당겨서 즉시 새로고침...',
  loosing: '새로고침 중...',
  loadingText: '로딩 중...',
  refreshComplete: '새로고침 완료',
  noMore: '더 이상 없음...',
  checkSuccess: '검사 성공',
  checkFail: '검사 실패',
  viewUpdateContent: '업데이트 내용 보기',
  deviceDisconnected: '장치 연결 실패',
  updateNote: '업데이트 설명',
  noData: '데이터 없음',
  tips: '알림',
  password: '비밀번호',
  pwdError: '비밀번호 오류, {0}회 더 시도할 수 있습니다',
  pwdErrorLock: '오류 횟수가 너무 많아 잠겼습니다. 나중에 다시 시도하세요!',
  noPermissions: '권한 없음',
  permission: '권한',
  validity: '유효 기간',
  permissionValidity: '권한 유효 기간',
  isSaveModify: '변경 내용을 저장하시겠습니까?',
  manyMinutes: '{0}분',
  manyHours: '{0}시간',
  manyDays: '{0}일',
  oneWeek: '1주',
  forever: '영구',
  expired: '만료됨',
  residue: '잔여',
  transferRequest: '전송 요청',
  acceptTransfer: '전송 수락',
  refuseTransferConfirm: '전송을 거부하시겠습니까?',
  bindInstallerText: '설치업체({account})를 바인딩하면 장치 호스팅이 가능합니다. 지금 바인딩하시겠습니까?',
  bindSuccess: '바인딩 성공',
  acceptSuccess: '수락 성공',
  from: '발신자',
  shareManage: '공유 관리',
  shareDetail: '공유 상세',
  acceptShare: '공유 수락',
  permissionText: '획득한 권한',
  livePreview: '라이브 미리보기',
  playback: '재생',
  alarm: '알람',
  intercom: '인터폰',
  gimbal: 'PTZ(팬틸트줌)',
  refuseShareConfirm: '공유 거부를 확인하시겠습니까?',
  acceptAll: '전체 수락',
  exitShare: '공유 종료',
  cancelDefense: '해제',
  homeDefense: '재택 경비 설정',
  outDefense: '외출 경비 설정',
  defenseDeployment: '경비/해제',
  oneClickDeployment: '원클릭 경비',
  oneClickDisarm: '원클릭 해제',
  oneClickRemoval: '원클릭 알람 해제',
  deploySuccess: '경비 설정 성공',
  disarmSuccess: '경비 해제 성공',
  add: '추가',
  edit: '편집',
  setting: '설정',
  all: '전체',
  name: '이름',
  cameraSensor: '카메라 연결',
  deleteDeviceConfirm: '장치를 삭제하시겠습니까?',
  onlySameDevice: '같은 그룹 내에서는 동일한 장치의 카메라만 추가할 수 있습니다',
  pleaseChooseChannel: '채널을 선택하세요',
  pleaseAddCameraSensor: '카메라/센서를 추가하세요',
  defensiveLinkageItem: '경비 해제 연동 항목',
  defensiveDesc: '경비 해제 상태에서 선택한 연동 항목은 작동하지 않습니다',
  bypassHome: '재택 경비 우회 설정',
  bypassHomeDesc: '활성화하면 재택 경비 모드에서 해당 존이 자동 우회됩니다',
  ipcSound: 'IPC 사운드',
  ipcLight: 'IPC 조명',
  pleaseChooseLinkage: '경비 해제 연동 항목을 선택하세요',
  deleteConfirm: '삭제하시겠습니까?',
  pleaseAddGroup: '먼저 그룹을 추가하세요',
  groupChannelEmpty: '경비 그룹에 채널이 추가되지 않았습니다',
  removalSuccess: '알람 해제 명령이 전송되었습니다',
  removalFail: '알람 해제 전송 실패',
  reqSuccess: '성공',
  reqFail: '실패',
  groupLimit: '최대 {limit}개의 그룹 추가를 지원합니다',
  areaGroup: '영역 그룹',
  pleaseEnter: '입력하세요',
  groupNoDevice: '그룹에 장치가 추가되지 않았습니다',
  addPoint: '추가',
  addSuccess: '추가 성공',
  addFail: '추가 실패',
  editSuccess: '편집 성공',
  editFail: '편집 실패',
  deleteSuccess: '삭제 성공',
  deleteFail: '삭제 실패',
  pointNameExist: '프리셋 포인트 이름이 이미 존재합니다',
  noPointSelect: '선택할 수 있는 프리셋 포인트가 없습니다. 먼저 프리셋 포인트를 생성하세요',
  choosePoint: '선택',
  presetPoint: '프리셋 포인트',
  presetPointName: '프리셋 포인트 이름',
  cruiseLineName: '순찰 경로 이름',
  pleaseEnterPoint: '프리셋 포인트 이름을 입력하세요',
  pleaseEnterLine: '순찰 경로 이름을 입력하세요',
  presetPointEmpty: '프리셋 포인트 목록은 비워둘 수 없습니다!',
  lineNameExist: '순찰 경로 이름이 이미 존재합니다',
  presetPointLimit: '프리셋 포인트는 {0}개를 초과할 수 없습니다!',
  manySecond: '{0}초',
  oneMinute: '1분',
  speed: '속도',
  holdTime: '지속 시간',
  deletePointConfirm: '프리셋 포인트를 삭제하시겠습니까?',
  pleaseChoosePoint: '프리셋 포인트를 선택하세요',
  pleaseChooseSpeed: '속도를 선택하세요',
  pleaseChooseHoldTime: '지속 시간을 선택하세요',
  lineAuthBind: '라인 권한 바인딩',
  bindFail: '바인딩 실패',
  binding: '바인딩 중',
  targetFaceManagement: '안면라이브러리 관리',
  targetName: '이름',
  targetType: '유형',
  cardId: '카드번호',
  strangerList: '방문자',
  whiteList: '화이트리스트',
  blackList: '블랙리스트',
  admin: '관리자',
  filter: '필터',
  searchTargetFace: '이름을 입력해 주세요.',
  personType: '인원 유형',
  addPerson: '인원 추가',
  personFace: '얼굴',
  floor: '층수',
  verificationMethod: '해제 모드',
  lockPermission: '도어락 권한',
  startTime: '시작 시간',
  endTime: '종료 시간',
  gender: '성별',
  age: '나이',
  telephone: '휴대폰',
  termOfValidity: '유효기간',
  foreverValid: '항상',
  custom: 'Custom',
  jobNumber: 'ID',
  male: '남자',
  female: '여자',
  Password: 'PIN 코드',
  FaceMatch: '안면비교',
  SwipingCard: '카드',
  MatchAndPass: '안면비교 + PIN 코드',
  MatchAndCard: '안면비교 + 카드',
  MatchorCard: '안면비교 또는 카드',
  AllType: '안면비교 또는 비밀번호 또는 카드',
  doorLock: '도어락',
  'personFace.uploadTip': '얼굴 이미지를 업로드 하십시오.',
  'personFace.sizeTip': '이미지 크기는 200k 초과해서는 안 됩니다.',
  ageRangeTip: '나이 범위는 {min}~{max}',
  floorRangeTip: '층수 범위는 {min}~{max}',
  roomRangeTip: '방번호 범위는 {min}~{max}',
  cardIdTip: '카드수량은 최대 {max}개 초과해서는 안 됩니다!',
  passwordRangeTip: 'PIN 코드 길이 범위는 {min}~{max}',
  passwordLengthTip: 'PIN 코드 길이는 {length}',
  timeRangeTip: '종료 시간은 시작 시간보다 나중이어야 합니다!',
  doorLockTip: '도어락을 최소 하나 이상 선택해 주세요.',
  jobNumberTip: 'ID를 입력해 주세요.',
  cardIdsTip: '카드번호를 입력해 주세요.',
  confirmDeletePerson: '인원을 삭제하겠습니까?',
  cardIdExist: '카드번호가 이미 존재합니다.',
  faceMatchErrorCode: {
    '-2': '매개변수 오류!',
    '-3': '이미지가 조건에 맞지 않습니다',
    '-5': '이미지가 조건에 맞지 않습니다',
    '-6': '인원수가 제한을 초과하였습니다.',
    '-11': '이미지 포맷을 지원하지 않습니다.',
    '-12': '이미지 데이터 이상',
    '-16': '도입 실패',
    '-18': '이미지 크기가 제한을 초과했습니다.',
    '-19': '특징 라이브러리 기록 업데이트 실패',
    '-20': '특징 라이브러리 기록 추가 실패',
    '-21': '이미지 특징 확보 실패',
    '-22': '베이스 이미지 저장 실패',
    '-23': '인원 정보 저장 실패',
    '-24': '인원 정보 저장 실패',
    '-25': '동일한 얼굴이 이미 존재합니다.',
    '-26': '알 수 없는 기능 라이브러리 작동 오류입니다.',
    '-30': '잠금 해제 모드 매개변수 오류입니다.',
    '-31': '비밀번호는 중복될 수 없습니다.',
    '-32': '비밀번호를 비워둘수 없습니다.',
    '-33': 'ID 번호를 비워둘수 없습니다.',
    '-34': '카드번호를 비워둘수 없습니다.',
    '-35': '얼굴 이미지 없습니다.',
    499: '매개변수 검증에 실패했습니다.'
  },
  householdManagement: '동 및 거주자',
  addBuilding: '동 추가',
  buildingName: '동이름',
  enterBuildingName: '동 이름을 입력해 주세요.',
  buildingNum: '동호수',
  enterBuildingNum: '동호수를 입력해 주세요.',
  relateDevice: '기기 연동',
  roomNum: '방 수량',
  room: '방',
  addRoom: '방 추가',
  roomName: '방번호',
  enterRoomName: '방번호를 입력해 주세요.',
  household: '거주자',
  addRoomMember: '거주자 추가',
  changeSuccessfully: '성공적으로 수정되었습니다.',
  email: 'Email',
  enterMemberEmail: '이메일 주소를 입력해 주세요.',
  mobile: '휴대폰',
  enterMemberMobile: '전화번호를 입력해 주세요.',
  emailNameError: '이메일 형식이 올바르지 않습니다.',
  mobileError: '전화번호 형식이 올바르지 않습니다.',
  emailNameNotEmpty: '이메일을 비워둘 수 없습니다.',
  mobileNotEmpty: '전화번호를 비워둘 수 없습니다.',
  memberInMax: '이 방의 거주자 수가 최대 한도에 도달했습니다.',
  memberMobileRepeate: '이 전화번호는 이미 이 방에 존재합니다.',
  emailRepeate: '이 이메일은 이미 이 방에 존재합니다.',
  supportDash: '특수 부호로 -, _ 및 공백만 지원합니다.',
  pageUpdateTitle: '업데이트 안내',
  pageUpdateContent: '페이지가 업데이트되었습니다. 새로고침해 주세요.',
  ok: '확인',
  errorCode: {
    400: '매개변수 오류',
    404: '요청한 리소스(웹 페이지 등)가 존재하지 않습니다',
    1000: '매개변수 오류',
    1005: '이미지 인증 코드 오류',
    1007: '이미지 인증 코드가 필요합니다',
    1008: '인증 코드가 만료되었습니다',
    1009: '인증 코드 오류',
    1011: '매개변수가 올바르게 입력되지 않았습니다!',
    1012: 'API를 인식할 수 없습니다',
    1013: '인증 코드 전송 실패',
    1015: '사용자가 이미 존재합니다',
    1027: '정확한 장치 시리얼 번호/보안 코드를 입력하세요',
    1028: '해당 채널이 이미 활성화되었거나 비활성화되었습니다',
    4500: '매개변수 오류',
    5000: '죄송합니다. 이 작업을 수행할 권한이 없습니다',
    5001: '현재 사용자에게 권한이 없습니다',
    6000: '현재 비즈니스 상태에서는 이 작업을 지원하지 않습니다',
    6001: '작업이 너무 자주 수행되고 있습니다',
    7000: '매개변수 오류',
    7001: '사용자가 존재하지 않습니다',
    7002: '기존 비밀번호 오류!',
    7003: '토큰이 유효하지 않습니다!',
    7004: '안녕하세요. 장시간 미사용 또는 다른 장치에서 로그인으로 인해 계정이 로그아웃되었습니다. 다시 로그인해주세요',
    7005: '서명이 유효하지 않습니다',
    7006: '휴대폰 번호가 이미 존재합니다.',
    7007: '사용자가 잠겨 있습니다. 관리자에게 문의하여 잠금을 해제해 주세요.',
    7009: '안녕하세요. 장시간 미사용 또는 다른 기기에서 로그인되어 계정이 로그아웃되었습니다. 다시 로그인해 주세요.',
    7010: '관리자 계정이 활성화되어 있지 않습니다.',
    7011: '계정이 활성화되어 있지 않습니다.',
    7019: '사용자 이름이 이미 존재합니다.',
    7021: '삭제 실패! 먼저 이 호스트 그룹에 속한 모든 호스트를 삭제해 주세요.',
    7023: '이 메일박스는 이미 연결되어 있습니다.',
    7028: '이 템플릿은 프로젝트에서 사용 중이므로 삭제할 수 없습니다!',
    7029: '템플릿 이름이 이미 존재합니다!',
    7030: '데이터가 이미 존재합니다!',
    7032: '펌웨어 패키지가 이미 존재합니다!',
    7034: '펌웨어 패키지가 이미 배포되어 삭제할 수 없습니다!',
    7040: '장치가 존재하지 않거나 오프라인 상태입니다.',
    7042: '다른 작업이 시작 상태에 있습니다.',
    7043: '작업이 승인되지 않았습니다!',
    7044: '작업 실패. 업그레이드 가능한 장치가 없습니다!',
    7045: '작업이 승인되지 않았습니다!',
    7056: '이 버전은 호환성 관리에 포함되어 있어 삭제할 수 없습니다!',
    7057: '발행 문서는 비워둘 수 없습니다!',
    7061: '수정 실패, 다시 수정을 생성할 수 없습니다!',
    7065: '채널이 이미 공유되었습니다.',
    7066: '고객 코드가 이미 존재합니다!',
    7068: '고객 코드가 존재하지 않습니다!',
    7069: '데이터가 너무 많습니다. 범위를 좁혀 다시 검색해 주세요!',
    7072: '장치가 이미 존재합니다.',
    7081: '가져오기 실패!',
    7082: '내보내기 실패!',
    7084: '해당 고객 국가 코드가 이미 존재합니다.',
    7086: '시스템 이상, 작업이 거부되었습니다',
    7087: '상품이 이미 존재합니다!',
    7088: '안녕하세요, 장시간 미작업 또는 다른 기기 로그인 등의 이유로 계정이 로그아웃 되었습니다. 다시 로그인해 주세요.',
    7090: '안녕하세요, 장시간 미작업 또는 다른 기기 로그인 등의 이유로 계정이 로그아웃 되었습니다. 다시 로그인해 주세요.',
    7093: '텍스트 및 이미지 정보가 설정되지 않았습니다!',
    7094: '서비스 약관 정보가 존재하지 않습니다!',
    9000: '시스템 이상!',
    9001: '프로토콜 버전이 너무 낮습니다. 이전 버전은 더 이상 호환되지 않으므로 업그레이드가 필요합니다.',
    9002: '프로토콜 버전 오류, 인식할 수 없는 버전 필드 또는 오류 메시지',
    9003: '인증 코드 전송 실패',
    9004: '데이터베이스 작업 실패',
    9005: '데이터가 존재하지 않습니다.',
    9006: '데이터가 이미 존재합니다.',
    9007: '조회할 데이터가 존재하지 않습니다.',
    9008: '데이터가 존재하지 않습니다.',
    9009: '데이터 이상',
    9500: '이스템 이상!',
    10000: '장치 연결 실패',
    10001: '이스템 이상!',
    12344: '네트워크 연결 실패',
    12345: '네트워크 연결 시간 초과',
    20021: '이 이메일은 이미 사용되었습니다.',
    20024: '계정이 활성화되었습니다.',
    20030: '링크가 만료되었습니다.',
    20070: '서로 다른 데이터 센터에 있어 이 사용자를 초대하지 못했습니다.',
    20071: '서로 다른 딜러에 속해 있어 이 사용자를 초대하지 못했습니다.',
    23024: '제공된 결제 카드가 만료되었습니다',
    23025: '거래가 위반으로 인해 거부되었습니다',
    32018: '데이터가 존재하지 않습니다',
    32019: '작업 실패',
    32021: '데이터가 존재하지 않습니다.',
    32022: '{0} 장치와 설치업체가 같은 국가/지역에 있지 않아 호스팅이 지원되지 않습니다',
    33001: '이 장치를 조작할 권한이 없습니다.',
    33002: '이 사이트를 조작할 권한이 없습니다.',
    33003: '사이트가 존재하지 않습니다.',
    33004: '장치 이름의 길이는 0에서 32 사이여야 합니다.',
    33010: '장치가 이미 존재합니다.',
    33601: '중복된 장치입니다!',
    33602: '최대 경계 그룹 수 제한!',
    33603: '작업 실패!',
    34001: '이 계정은 이미 연결되어 있습니다.',
    34003: '상태 정보가 올바르지 않습니다.',
    34004: '권한 부여 실패',
    34005: '작업 실패: 권한이 만료되었습니다. 다시 획득해 주세요.',
    34006: '장치 전송 정보가 존재하지 않습니다.',
    34007: '동일한 사용자로부터의 전송만 허용됩니다.',
    34021: '건물 수가 한도에 도달했습니다.',
    34022: '건물 이름이 이미 존재합니다.',
    34023: '건물 번호가 이미 존재합니다.',
    34024: '삭제 실패. 먼저 이 건물의 방과 장치를 삭제해 주세요.',
    34025: '연결 실패. 이 장치는 이미 다른 건물에 연결되어 있습니다.',
    34026: '이 건물의 장치 수가 한도에 도달했습니다.',
    34027: '작업 실패. 이 건물은 삭제되었습니다.',
    34028: '이 방 번호는 이미 존재합니다.',
    34029: '이 건물의 방 개수가 한도에 도달했습니다.',
    34030: '이 방의 거주자 수가 한도에 도달했습니다.',
    34031: '작업 실패. 이 방은 삭제되었습니다.',
    34033: '삭제 실패. 먼저 이 방의 거주자들을 삭제해 주세요.',
    34035: '이 전화번호는 이미 이 방에 존재합니다.',
    34036: '이 이메일은 이미 이 방에 존재합니다.',
    34037: '작업 실패. 이 거주자는 삭제되었습니다.',
    536870934: '작업 실패, 장치 상태를 확인해 주세요.',
    536870940: '작업 실패, 장치 상태를 확인해 주세요.',
    536870943: '유효하지 않은 매개변수',
    536870945: '작업 실패, 장치 상태를 확인해 주세요.',
    536870947: '사용자 이름이 존재하지 않습니다',
    536870948: '사용자 이름 또는 비밀번호 오류',
    536871017: '작업 실패, 장치 상태를 확인해 주세요.',
    536871039: '유효하지 않은 매개변수',
    536871060: '작업 실패, 장치 상태를 확인하세요',
    536871082: '작업 실패, 장치 상태를 확인해 주세요.',
    536871083: '작업 실패, 장치 상태를 확인해 주세요.',
    ipc: {
      499: '알 수 없는 오류',
      500: '시스템 예외!',
      502: '서버 요청 실패',
      503: '서버 예외',
      504: '서버 요청 시간 초과',
      550: '요청 시간 초과',
      612: '작업 실패, 장치 상태를 확인하세요',
      730: '작업 실패, 장치 상태를 확인하세요',
      731: '작업 실패, 장치 상태를 확인하세요',
      732: '작업 실패, 장치 상태를 확인하세요',
      735: '작업 실패, 장치 상태를 확인하세요'
    }
  }
}
