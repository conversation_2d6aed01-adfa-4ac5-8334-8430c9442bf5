<template>
  <div class="theme-image-container">
    <!-- 占位图 -->
    <!-- <img v-if="!themeImg"  src="@/assets/img/common/placeholder.png" alt="loading" /> -->
    <!-- 使用placeholder占位图的basa64 -->
    <img
      v-if="!themeImg"
      class="theme-image"
      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAADVJREFUSEvt0rEJAAAMAkHdf2lH+MruU0uEw+Z8Pf+PBSgskUQogAFXJBEKYMAVSYQCGLivaHrEABkDr5mAAAAAAElFTkSuQmCC"
      alt="loading"
    />
    <!-- 实际图片 -->
    <img v-else alt="img" class="theme-image" :src="themeImg" v-bind="$attrs" v-on="$listeners" @error="handleError" />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getRemoteBasePath } from '@/utils/common.js'
export default {
  name: 'ThemeImage',
  props: {
    imageName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      themeImg: null
      // 添加缓存对象
      // imageCache: new Map()
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType', 'imageCache']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    imgSrc() {
      // return this.imageName ? require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`) : null
      if (!this.imageName) return null
      // 先尝试加载远程图片
      try {
        const remoteUrl = this.getRemoteImageUrl(this.imageName)
        if (remoteUrl) return remoteUrl
      } catch (error) {
        console.warn('Remote image URL generation failed:', error)
      }
      // 如果远程图片URL生成失败，fallback到本地图片
      return require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`)
    },
    fallbackImgSrc() {
      // return this.imageName ? require(`@/assets/img/common/${this.imageName}`) : null
      if (!this.imageName) return null
      // 同样先尝试加载远程备用图片
      try {
        const remoteFallbackUrl = this.getRemoteFallbackImageUrl(this.imageName)
        if (remoteFallbackUrl) return remoteFallbackUrl
      } catch (error) {
        console.warn('Remote fallback image URL generation failed:', error)
      }
      // 如果远程备用图片URL生成失败，使用本地图片
      // 先尝试本地定制主题图片，再使用common中的图片
      try {
        return require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`)
      } catch (error) {
        console.warn('Local theme image not found, falling back to common image:', error)
        return require(`@/assets/img/common/${this.imageName}`)
      }
    }
  },
  watch: {
    imageName: {
      handler() {
        this.refreshImg()
      },
      immediate: true
    }
  },
  async created() {
    this.refreshImg()
  },
  methods: {
    async refreshImg() {
      if (!this.imageName) {
        this.themeImg = null
        return
      }

      // 检查缓存
      // const cachedUrl = this.imageCache.get(this.imageName)
      // 检查Vuex中的缓存
      const cachedUrl = this.imageCache[this.imageName]
      if (cachedUrl) {
        this.themeImg = cachedUrl
        return
      }

      // 获取可能的图片URL
      const themeUrl = this.getRemoteImageUrl(this.imageName)
      const fallbackUrl = this.getRemoteFallbackImageUrl(this.imageName)

      try {
        // 先检查主题图片是否存在
        const themeExists = await this.isHasImg(themeUrl)
        if (themeExists) {
          this.themeImg = themeUrl
          // this.imageCache.set(this.imageName, themeUrl)
          // 使用Vuex缓存
          this.$store.commit('app/SET_IMAGE_CACHE', {
            key: this.imageName,
            value: themeUrl
          })
          return
        }

        // 如果主题图片不存在，检查fallback图片
        const fallbackExists = await this.isHasImg(fallbackUrl)
        if (fallbackExists) {
          this.themeImg = fallbackUrl
          // this.imageCache.set(this.imageName, fallbackUrl)
          // 使用Vuex缓存
          this.$store.commit('app/SET_IMAGE_CACHE', {
            key: this.imageName,
            value: fallbackUrl
          })
          return
        }

        // 如果远程图片都不存在，尝试本地图片
        // 先尝试本地定制主题图片，再使用common中的图片
        try {
          this.themeImg = require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`)
        } catch (error) {
          console.warn('Local theme image not found, falling back to common image:', error)
          this.themeImg = require(`@/assets/img/common/${this.imageName}`)
        }
        // this.imageCache.set(this.imageName, this.themeImg)
        this.$store.commit('app/SET_IMAGE_CACHE', {
          key: this.imageName,
          value: this.themeImg
        })
      } catch (err) {
        console.warn('Image loading failed:', err)
        // 出错时使用本地fallback图片
        // 先尝试本地定制主题图片，再使用common中的图片
        try {
          this.themeImg = require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`)
          // this.imageCache.set(this.imageName, this.themeImg)
          this.$store.commit('app/SET_IMAGE_CACHE', {
            key: this.imageName,
            value: this.themeImg
          })
        } catch (e) {
          try {
            this.themeImg = require(`@/assets/img/common/${this.imageName}`)
            this.$store.commit('app/SET_IMAGE_CACHE', {
              key: this.imageName,
              value: this.themeImg
            })
          } catch (err) {
            this.themeImg = null
          }
        }
      }
    },

    // 优化图片检查方法
    async isHasImg(imgurl) {
      if (!imgurl) return false

      // 如果已知这个URL是404，直接返回false
      // if (this.imageCache.get(imgurl) === false) {
      //   return false
      // }
      // 如果已知这个URL是404，直接返回false
      if (this.imageCache[imgurl] === false) {
        return false
      }

      return new Promise(resolve => {
        const ImgObj = new Image()
        ImgObj.src = imgurl

        ImgObj.onload = () => {
          resolve(true)
        }

        ImgObj.onerror = () => {
          // 记录404的URL
          // this.imageCache.set(imgurl, false)
          // 记录404的URL到Vuex
          this.$store.commit('app/SET_IMAGE_CACHE', {
            key: imgurl,
            value: false
          })
          resolve(false)
        }
      })
    },
    // 添加新的方法来生成远程图片URL
    getRemoteImageUrl(imageName) {
      const appType = this.appStyleFlag
      const UI = this.uiStyleFlag
      // 这里可以根据实际需求配置远程图片的基础URL和规则
      // const REMOTE_BASE_URL = '/webStaticWeb/static/images'
      const REMOTE_BASE_URL = getRemoteBasePath(appType)

      // 可以根据不同的条件返回不同的远程图片URL
      // console.log('请求主题图片', `${REMOTE_BASE_URL}/${UI}/images/${imageName}`)
      return `${REMOTE_BASE_URL}/${UI}/images/${imageName}`
    },
    // 添加新的方法来生成远程备用图片URL
    getRemoteFallbackImageUrl(imageName) {
      const appType = this.appStyleFlag
      // const UI = this.uiStyleFlag
      // const REMOTE_FALLBACK_BASE_URL = '/webStaticWeb/static/images/common'
      const REMOTE_FALLBACK_BASE_URL = getRemoteBasePath(appType)
      // console.log('请求公共图片', `${REMOTE_FALLBACK_BASE_URL}/common/images/${imageName}`)
      return `${REMOTE_FALLBACK_BASE_URL}/common/images/${imageName}`
    },
    handleError() {
      //   console.log('进入错误处理')
      this.themeImg = this.fallbackImgSrc
    }
  }
}
</script>
<style lang="scss">
.theme-image-container {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  .theme-image {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
</style>
