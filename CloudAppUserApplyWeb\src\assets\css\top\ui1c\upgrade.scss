.upgrade-list {
  ::v-deep.van-popup--center {
    width: 320px;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 6px;
    right: 6px;
  }
  
  ::v-deep.van-popup {
    width: 300px;
  }
  .pop-dialog {
    width: 320px;
    border-radius: 8px 8px 8px 8px;
    .pop-div {
      position: relative;
    }
    .dialog-title {
      width: 300px;
      font-weight: 700;
      height: 54px;
      line-height: 54px;
      font-size: 15px;
      color: $partner-black;
      text-align: center;
    }
    .update-box{
      padding: 8px;
      .update-content {
        max-height:394px;
        overflow-y: scroll;
        line-height: 22px;
        padding: 0 10px 18px 16px;
        word-break: break-all;
      }
    }
    .dialog-close-img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 24px;
      height: 24px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .title-text {
    height: 28px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title-text-img {
        width: 35px;
        height: 27px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .title-text-button {
    width: 70px;
    height: 25px;
    line-height: 25px;
    border-radius: 12.5px 12.5px 12.5px 12.5px;
    background: $partner-white;
    text-align: center;
    border: 1px solid $partner-primary;
    color: $partner-primary;
  }
  .view-btn {
    color: $partner-primary;
    font-weight: 500;
    font-size: 14px;
  }
  .title {
    color: $partner-gray;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    font-weight: 700;
  }
  .title-text-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 27px;
    margin-left: 15px;
    max-width: 190px;
  }
  .download-status {
    color: $partner-primary;
    font-size: 14px;
    display: flex;
    .download-status-text {
      margin-right: 8px;
    }
    .progress{
      color:$partner-black;
    }
  }
  .device-upgrade {
    .container {
      background-color: $partner-white;
      padding: 15px 15px;
    }
  }
  // 列表通用
  .list-content {
    padding-top: 20px;
    .list-content-row {
      display: flex;
      line-height: 24px;
      .label {
        width: 112px;
        flex-shrink: 0;
        color: $partner-gray;
      }
      .value {
        max-width: 240px;
        flex: 1;
        word-wrap: break-word;
        white-space: pre-wrap;
        color: $partner-black;
      }
      .zh-label{
        width: 70px;
      }
      .zh-value{
        max-width: 280px;
      }
    }
    .download-tip {
      line-height: 24px;
      font-size: 14px;
    }
    .has-latest-version {
      color: $partner-black;
    }
  }
  .camera-upgrade {
    padding: 0 0 8px 0;
    .title-div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-img {
        margin-right: 15px;
        width: 17px;
        height: 17px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .container-ul {
      background-color: $partner-white;
      padding: 2px 15px;
      .container-li {
        border-bottom: 1px solid $partner-light-gray;
        padding: 12px 0;
        &:last-child {
          border: 0;
        }
      }
    }
  }
  .camera-no-upgrade {
    margin: 0;
    padding-left: 15px;
    line-height: 28px;
    min-height: 60px;
    background-color: $partner-white;
  }
  .footer{
    background: $partner-white;
  }
}