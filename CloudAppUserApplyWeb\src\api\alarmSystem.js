import axios from 'axios'
import store from '@/store'

// Risco云平台基础URL
// 开发环境使用代理，生产环境直接访问
const RISCO_BASE_URL = process.env.NODE_ENV === 'development' ? '/risco-api' : 'https://api.riscocloud.com'

// 创建Risco专用的axios实例，不使用项目的请求拦截器
const riscoAxios = axios.create({
  baseURL: RISCO_BASE_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 认证配置
const AUTH_CONFIG = {
  risco: {
    noAuth: ['/api/auth/login'],
    noSession: ['/Login', '/GetAll']
  },
  pima: {
    noAuth: ['/api/WebUser/GetPairEntities', '/api/WebUser/Pair'],
    noPanel: ['/api/Panel/Authenticate'],
    headers: () => ({
      webUserId: store.state.app?.deviceId || 'default_user_id',
      osType: store.state.app?.platform === 'ios' ? '1' : '2',
      osVersion: store.state.app?.osVersion || '10.0',
      appVersion: store.state.app?.version || '2.0.0'
    })
  }
}

// 通用认证注入器
const injectAuth = (config, system) => {
  const { url } = config
  const rules = AUTH_CONFIG[system]
  const { riscoLoginInfo, siteLoginInfo } = store.state.alarmSystem

  const needsAuth = skipList => !skipList?.some(path => url.includes(path))

  if (system === 'risco') {
    needsAuth(rules.noAuth) &&
      riscoLoginInfo?.accessToken &&
      (config.headers.Authorization = `${riscoLoginInfo.tokenType || 'Bearer'} ${riscoLoginInfo.accessToken}`)

    url.includes('/site/') &&
      needsAuth(rules.noSession) &&
      siteLoginInfo?.sessionId &&
      ((config.data = config.data || {}), (config.data.sessionToken = siteLoginInfo.sessionId))
  } else if (system === 'pima') {
    Object.assign(config.headers, rules.headers())

    needsAuth(rules.noAuth) && siteLoginInfo?.sessionId && (config.headers.sessionToken = siteLoginInfo.sessionId)

    url.includes('/api/Panel/') &&
      needsAuth(rules.noPanel) &&
      siteLoginInfo?.pairEntityId &&
      (config.headers.pairEntityId = siteLoginInfo.pairEntityId)
  }

  return config
}

// Risco请求拦截器：使用配置驱动的认证注入
riscoAxios.interceptors.request.use(
  config => injectAuth(config, 'risco'),
  error => Promise.reject(error)
)

/**
 * Risco登录接口
 * @param {Object} data - 登录参数
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @returns {Promise} 登录结果
 */
export const riscoLogin = data => {
  return riscoAxios.post('/api/auth/login', data)
}

/**
 * 获取用户的所有站点
 * @returns {Promise} 站点列表
 */
export const getAllSites = () => {
  return riscoAxios.post('/api/wuws/site/GetAll')
}

/**
 * 登录到指定站点
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据
 * @param {string} reqData.pinCode - PIN码(1-6位数字)
 * @param {string} reqData.languageId - 语言ID
 * @param {number} reqData.devicePlatform - 设备平台(1: SmartPhone, 2: WebBrowser)
 * @returns {Promise} 登录结果
 */
export const loginToSite = (siteId, reqData) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/Login`, reqData)
}

/**
 * 获取站点详情
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据（可选，sessionToken会自动注入）
 * @returns {Promise} 站点详情信息
 */
export const getSiteDetails = (siteId, reqData = {}) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/GetDetails`, reqData)
}

/**
 * 获取Panel状态
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据
 * @param {boolean} reqData.fromControlPanel - 是否来自控制面板
 * @returns {Promise} Panel状态信息
 */
export const getPanelState = (siteId, reqData = { fromControlPanel: true }) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/ControlPanel/GetState`, reqData)
}

/**
 * 获取Panel事件日志
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据
 * @param {string} reqData.newerThan - 时间过滤（可选）
 * @param {number} reqData.offset - 偏移量，默认0
 * @param {number} reqData.count - 数量，默认50
 * @returns {Promise} 事件日志列表
 */
export const getEventLog = (siteId, reqData = { offset: 0, count: 50 }) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/ControlPanel/GetEventLog`, reqData)
}

/**
 * 控制Panel状态 (Arm panel - no partition)
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据
 * @param {number} reqData.newSystemStatus - 新的系统状态
 * @returns {Promise} 控制结果
 */
export const armPanel = (siteId, reqData) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/ControlPanel/Arm`, reqData)
}

/**
 * 控制分区状态 (Arm panel partition)
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据
 * @param {Array} reqData.partitions - 分区列表
 * @param {number} reqData.partitions[].id - 分区ID
 * @param {number} reqData.partitions[].armedState - 当前布防状态
 * @param {number} reqData.partitions[].readyState - 新的状态
 * @param {number} reqData.partitions[].alarmState - 报警状态
 * @param {number} reqData.exitDelay - 退出延迟
 * @param {Array} reqData.lastArmFailReasons - 上次布防失败原因
 * @returns {Promise} 控制结果
 */
export const armPartition = (siteId, reqData) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/ControlPanel/PartArm`, reqData)
}

/**
 * Risco站点退出登录
 * @param {number} siteId - 站点ID
 * @param {Object} reqData - 请求数据（可选，sessionToken会自动注入）
 * @returns {Promise} 退出登录结果
 */
export const riscoLogout = (siteId, reqData = {}) => {
  return riscoAxios.post(`/api/wuws/site/${siteId}/Logout`, reqData)
}

// Pima系统基础URL
const PIMA_BASE_URL =
  process.env.NODE_ENV === 'development' ? '/pima-api' : 'https://releasepimalink-new-qo.azurewebsites.net'

// 创建Pima专用的axios实例
const pimaAxios = axios.create({
  baseURL: PIMA_BASE_URL,
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Pima请求拦截器：使用配置驱动的认证注入
pimaAxios.interceptors.request.use(
  config => injectAuth(config, 'pima'),
  error => Promise.reject(error)
)

/**
 * 获取Pima系统的通知消息列表 (Get Notifications)
 * 检索指定系统的消息列表，按发送日期降序排列
 *
 * @param {Object} reqData - 请求数据
 * @param {boolean} reqData.data - false=应用过滤器, true=无过滤器(全部)
 * @returns {Promise} 通知消息列表
 *
 * 响应数据结构：
 * [
 *   {
 *     "Message": "Picture from VISUAL ZONE26", // 消息内容 (string)
 *     "PictureUrls": ["Image1", "Image2", "Image3"], // 图片URL数组 (string[])
 *     "SentDate": "2017-01-01T10:42:53.3966079Z" // 发送时间 (string)
 *   }
 * ]
 */
export const getNotifications = reqData => {
  return pimaAxios.post('/api/WebUser/GetNotifications', reqData)
}

/**
 * 设置Pima系统的关闭警报器
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 关闭警报器结果
 *
 */
export const setMuteSiren = reqData => {
  return pimaAxios.post('/api/Panel/UnSetSiren', reqData)
}

/**
 * 获取Pima系统的面板列表 (Get Pair Entities)
 * 该服务将检索为给定客户端定义的所有用户面板
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 面板列表
 *
 * 响应数据结构：
 * [
 *   {
 *     "pairId": "MzU3MzE1MDUwMDExNjY4LTEyMzQ5Njc4OTAxMjM0NTY=", // 面板ID (string)
 *     "name": "Roy's AlarmView", // 面板名称 (string)
 *     "unreadNotificationCount": 0, // 未读通知数量 (int)
 *     "timeZone": "jerusalem (utc+2:00)", // 时区信息 (string)
 *     "utcTimeOffsetSeconds": 7200, // UTC时间偏移秒数 (int)
 *     "KeyStatus": 0 // 按键状态 (int) - 参见附录中的按键状态说明
 *   }
 * ]
 */
export const getPairEntities = reqData => {
  return pimaAxios.post('/api/WebUser/GetPairEntities', reqData)
}

/**
 * 更新Pima系统的面板名称 (Update Pair Name)
 * 更新现有用户系统名称
 *
 * @param {Object} reqData - 请求数据
 * @param {string} reqData.data - 面板新名称 (Panel New Name)
 * @returns {Promise} 更新结果
 *
 * 请求数据格式：
 * {
 *   "data": "Panel New Name" // 面板新名称 (string)
 * }
 */
export const setPairName = reqData => {
  return pimaAxios.post('/api/WebUser/SetPairName', reqData)
}

/**
 * 解除Pima系统的面板配对 (Unpair WebUser)
 * 解除用户与面板的配对关系，执行面板移除操作
 *
 * @param {Object} reqData - 请求数据
 * @param {string} reqData.data - 面板ID (Pair ID)
 * @returns {Promise} 解除配对结果
 *
 * 请求数据格式：
 * {
 *   "data": "MzU3MzE1MDUwMDExNjY4LTEyMzQ5Njc4OTAxMjM0NTY=" // 面板ID (string)
 * }
 *
 * 功能说明：
 * - 解除当前认证系统与指定面板的配对关系
 * - 实际执行面板移除操作，可能包括面板删除
 * - Pair ID可以是用户自己的面板或其他用户的面板ID（如果当前用户是master）
 */
export const unpairWebUser = reqData => {
  return pimaAxios.post('/api/WebUser/UnPair', reqData)
}

/**
 * Pima系统面板身份验证 (Panel Authentication)
 * 对当前用户/客户端与选定面板进行身份验证
 *
 * @param {Object} reqData - 请求数据
 * @param {string} reqData.data - 用户密码 (User's password)
 * @returns {Promise} 身份验证结果
 *
 * 请求数据格式：
 * {
 *   "data": "1111" // 用户密码 (string)
 * }
 *
 * 响应数据结构：
 * {
 *   "webUserType": 2, // 用户类型 (int)
 *   "sessionToken": "cc95GTOXdSeWYa1eCjBCXybUq2dkDmJH_NsbJxnE1M37IU0" // 会话令牌 (string)
 * }
 *
 * webUserType 说明：
 * - 0: Unknown
 * - 1: Regular
 * - 2: Master
 * - 3: Installer
 * - 4: 24H
 * - 5: Duress
 */
export const authenticatePanel = reqData => {
  return pimaAxios.post('/api/Panel/Authenticate', reqData)
}

/**
 * 获取Pima系统面板状态 (Get General Status)
 * 获取配对面板的主要详细信息，需要身份验证和有效的会话令牌
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 面板状态信息
 *
 * 请求数据格式：
 * {
 *   "data": null
 * }
 *
 * 响应数据结构：
 * {
 *   "partitions": [
 *     {
 *       "Name": "Partition1", // 分区名称 (string)
 *       "Number": 1, // 分区编号 (int)
 *       "Status": 1, // 分区状态 (int)
 *       "showDisarmBtn": false, // 显示撤防按钮 (bool)
 *       "showFullArmBtn": true, // 显示全布防按钮 (bool)
 *       "showHome1Btn": true, // 显示Home1按钮 (bool)
 *       "showHome2Btn": true, // 显示Home2按钮 (bool)
 *       "showHome3Btn": true, // 显示Home3按钮 (bool)
 *       "showHome4Btn": true, // 显示Home4按钮 (bool)
 *       "showShabatBtn": false // 显示安息日按钮 (bool)
 *     }
 *   ],
 *   "status": 1, // 面板整体状态 (int)
 *   "showPartBtn": true, // 显示分区按钮 (bool)
 *   "showPartTextOnHome2": false, // 在Home2上显示分区文本 (bool)
 *   "showFullArmBtn": true, // 显示全布防按钮 (bool)
 *   "showHome1Btn": true, // 显示Home1按钮 (bool)
 *   "showHome2Btn": true, // 显示Home2按钮 (bool)
 *   "showHome3Btn": true, // 显示Home3按钮 (bool)
 *   "showHome4Btn": true, // 显示Home4按钮 (bool)
 *   "showShabatBtn": true, // 显示安息日按钮 (bool)
 *   "showDisarmBtn": false // 显示撤防按钮 (bool)
 * }
 *
 * 状态码说明：
 * - 0: Unknown/Mix Partitions
 * - 1: Disarm
 * - 2: FullArm
 * - 3: Home1
 * - 4: Home2
 * - 5: Home3
 * - 6: Home4
 * - 7: ShabatON
 * - 8: ShabatOFF
 */
export const getPimaGeneralStatus = reqData => {
  return pimaAxios.post('/api/Panel/GetGeneralStatus', reqData)
}

/**
 * 设置Pima系统分区状态 (Set Partitions)
 * 该服务用于设置每个分区的状态，需要身份验证和有效的会话令牌
 *
 * @param {Object} reqData - 请求数据
 * @param {Array} reqData.data - 分区状态数组
 * @param {number} reqData.data[].Number - 分区编号 (int)
 * @param {number} reqData.data[].Status - 状态码 (int)
 * @returns {Promise} 设置结果
 *
 *
 * 状态码说明：
 * - 1: Disarm (撤防)
 * - 2: FullArm (全布防)
 * - 3: Home1 (在家模式1)
 * - 4: Home2 (在家模式2)
 * - 5: Home3 (在家模式3)
 * - 6: Home4 (在家模式4)
 * - 7: ShabatON (安息日开启)
 * - 8: ShabatOFF (安息日关闭)
 * ```
 *
 */
export const setPimaPartitions = reqData => {
  return pimaAxios.post('/api/Panel/SetPartitions', reqData)
}

/**
 * 获取Pima系统的故障列表 (Get Faults)
 * 检索系统故障列表，返回故障文本数组
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 故障列表
 *
 * 响应数据结构：
 * [
 *   "fault text 1",
 *   "fault text 2",
 *   ...
 * ]
 */
export const getFaults = reqData => {
  return pimaAxios.post('/api/Panel/GetFaults', reqData)
}

/**
 * 获取Pima系统日志 (Get Log)
 * 检索系统日志，返回日志文本列表
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 系统日志列表
 *
 * 响应数据结构：
 * [
 *   "line 1",
 *   "line 2",
 *   ...
 * ]
 */
export const getPimaLog = reqData => {
  return pimaAxios.post('/api/Panel/GetLog', reqData)
}

/**
 * 获取Pima系统的输出设备列表 (Get Outputs)
 * 检索系统中配置的所有输出设备信息，包括名称、过滤状态和开关状态
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 输出设备列表
 *
 * 请求数据格式：
 * {
 *   "data": null
 * }
 *
 * 响应数据结构：
 * [
 *   {
 *     "IsFiltered": false, // 是否被过滤，可用于编辑输出屏幕 (bool)
 *     "IsOpen": false,     // 是否开启状态 (bool)
 *     "Name": "PCM",       // 输出设备名称 (string)
 *     "Number": 0          // 输出设备ID (int)
 *   }
 * ]
 */
export const getPimaOutputs = reqData => {
  return pimaAxios.post('/api/Panel/GetOutputs', reqData)
}

/**
 * 设置Pima系统的输出设备状态 (Set Outputs)
 * 该服务用于设置每个输出设备的开关状态
 *
 * @param {Object} reqData - 请求数据
 * @param {Array} reqData.data - 输出设备状态数组
 * @param {number} reqData.data[].number - 输出设备ID (int)
 * @param {boolean} reqData.data[].active - 是否激活 (bool) - true=开启, false=关闭
 * @returns {Promise} 设置结果
 *
 * 请求数据格式：
 * {
 *   "data": [
 *     {
 *       "number": 0,    // 输出设备ID (int)
 *       "active": true  // 激活状态 (bool) - true=开启, false=关闭
 *     }
 *   ]
 * }
 *
 * 注意：
 * - 默认情况下应用程序不应发送超过一个输出设备
 * - true = 设置输出开启
 * - false = 设置输出关闭
 */
export const setPimaOutputs = reqData => {
  return pimaAxios.post('/api/Panel/SetOutputs', reqData)
}

/**
 * 设置Pima系统的输出设备名称 (Set Outputs Name)
 * 为当前配对的面板设置输出设备的名称
 *
 * @param {Object} reqData - 请求数据
 * @param {Array} reqData.data - 输出设备数组
 * @param {number} reqData.data[].number - 输出设备ID (int)
 * @param {string} reqData.data[].name - 输出设备名称 (string)
 * @returns {Promise} 设置结果
 *
 * 请求数据格式：
 * {
 *   "data": [
 *     {
 *       "number": 0,
 *       "name": "bed room"
 *     }
 *   ]
 * }
 *
 * 注意：该服务支持接收多个输出设备的JSON数组，但默认应用程序不应发送超过一个输出设备
 */
export const setPimaOutputsName = reqData => {
  return pimaAxios.post('/api/Panel/SetOutputsName', reqData)
}

/**
 * 获取Pima系统的通知过滤器配置
 *
 * 完整的通知类型列表：
 * - ID: 0, Name: "NotificationsFilterType-EnabledAll" - 启用所有通知
 * - ID: 1, Name: "NotificationsFilterType-Burglary" - 入侵报警
 * - ID: 2, Name: "NotificationsFilterType-Panic" - 紧急报警
 * - ID: 3, Name: "NotificationsFilterType-Fire" - 火警
 * - ID: 4, Name: "NotificationsFilterType-Duress" - 胁迫报警
 * - ID: 5, Name: "NotificationsFilterType-Medical" - 医疗报警
 * - ID: 6, Name: "NotificationsFilterType-Tamper" - 防拆报警
 * - ID: 7, Name: "NotificationsFilterType-Faults" - 故障报警
 * - ID: 8, Name: "NotificationsFilterType-ArmDisarm" - 布撤防事件
 * - ID: 20, Name: "NotificationsFilterType-AlarmRestore" - 报警恢复
 * - ID: 90, Name: "NotificationsFilterType-Picture" - 图片通知
 * - ID: 100, Name: "NotificationsFilterType-InvalidReportCode" - 无效报告代码
 */
export const getNotificationsFilter = reqData => {
  return pimaAxios.post('/api/WebUser/GetNotificationsFilter', reqData)
}

/**
 * 设置Pima系统的通知过滤器配置 (Set filter)
 * 在服务器上保存特定系统允许的消息类型
 */
export const updateNotificationsFilter = reqData => {
  return pimaAxios.post('/api/WebUser/SetNotificationsFilter', reqData)
}

/**
 * Pima系统面板断开连接 (Panel Disconnect)
 * 断开当前用户与面板的连接，退出登录
 *
 * @param {Object} reqData - 请求数据
 * @param {null} reqData.data - 请求数据为null
 * @returns {Promise} 断开连接结果
 *
 * 请求数据格式：
 * {
 *   "data": null
 * }
 */
export const pimaDisconnect = reqData => {
  return pimaAxios.post('/api/Panel/Disconnect', reqData)
}
