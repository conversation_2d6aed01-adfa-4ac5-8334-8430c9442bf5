<template>
  <div class="reset-dev-pwd">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <div class="reset-tips">{{ $t('resetPwdTips') }}</div>
    <div class="card">
      <installer-info :data="detail" />
    </div>
    <div class="device-list">
      <div class="device-title">
        {{ $t('deviceUpdate') }}
      </div>
      <div class="device-item">
        <div class="device-icon-wrapper"></div>
        <div class="device-info">
          <div class="device-name text-over-ellipsis">
            {{ deviceName }}
          </div>
          <!-- <div class="device-permission">{{ $t('permission') }}：--</div>
          <div class="device-time">{{ $t('duration') }}：--</div> -->
        </div>
      </div>
    </div>
    <div class="footer-container">
      <van-button v-if="isExpired" class="footer-btn expired" type="primary" disabled>
        {{ $t('expired') }}
      </van-button>
      <van-button v-else class="footer-btn" type="primary" @click="handleReset">{{ $t('modifyDevPwd') }} </van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import InstallerInfo from '@/views/max-hosting/components/InstallerInfo.vue'
import { getInstallerInfo } from '@/api/maxHosting'
import { formatInstallerInfo } from '@/views/max-hosting/common'
import { appBack, appRequestDevice } from '@/utils/appbridge'
import { getDevDetail } from '@/api/device'
import { getRequestContent } from '@/api/device'
import { transformXml } from '@/utils/common.js'
export default {
  name: 'resetDevPwd',
  components: {
    NavBar,
    InstallerInfo
  },
  data() {
    return {
      detail: {},
      deviceName: '',
      installerUserId: null,
      sn: null,
      isExpired: null, //消息是否已过期
      mac: ''
    }
  },
  mounted() {
    this.init()
  },

  methods: {
    formatInstallerInfo,
    init() {
      this.sn = this.$route.params?.sn
      this.installerUserId = this.$route.query?.installerUserId
      const pushEventTime = this.$route.query?.pushEventTime
      const currentTimestamp = Math.floor(Date.now() / 1000)
      const timeDifference = currentTimestamp - pushEventTime // 计算时间差
      this.isExpired = timeDifference > 24 * 60 * 60 //有效期24小时
      this.getDetail()
      this.getDevDetail()
    },
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.installerUserId
      })
      console.log('detail', detail)
      if (detail) {
        this.detail = formatInstallerInfo(detail)
      }
    },
    getDevDetail() {
      getDevDetail({ sn: this.sn, returnChl: false }).then(res => {
        if (res.data !== null && res.data.devInfo) {
          const devInfo = res.data.devInfo
          const { name, mac } = devInfo
          this.deviceName = name
          this.mac = mac
        }
      })
    },
    // 重置密码先查询设备密码强度
    handleReset() {
      let that = this
      const req = {
        devId: this.sn,
        url: 'queryPasswordSecurity',
        params: getRequestContent()
      }
      appRequestDevice(req, function (res) {
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        if (resData.code === 200) {
          let xmlObject = transformXml(resData.body)
          let xmlObjRes = xmlObject.response
          if (xmlObjRes) {
            const contentObj = xmlObjRes.content
            that.pwdLevel = contentObj.pwdSecureSetting.pwdSecLevel.__text
            that.$router.push({ name: 'modifyDevPwd', params: { sn: that.sn, pwdLevel: that.pwdLevel, mac: that.mac } })
          }
        } else {
          that.$toastFail(that.$t(`errorCode.${resData.code}`))
        }
      })
    },
    clickLeft() {
      appBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.reset-dev-pwd {
  width: 100%;
  background: var(--bg-color-white, #ffffff);
  height: calc(100vh);
  box-sizing: border-box;
  .reset-tips {
    padding: 20px 24px;
    color: var(--text-color-secondary, #50546d);
    font-size: var(--font-size-body2-size, 14px);
    font-weight: 400;
    line-height: 22px;
  }
  .footer-container {
    position: fixed;
    bottom: 0px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    background-color: var(--bg-color-white, #ffffff);
    height: 80px;
  }
  .footer-btn {
    min-width: 167px;
    height: 40px;
    border: 1px solid var(--bg-color-secondary, #eeeeee);
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
    &.primary {
      background: var(--brand-bg-color-active, #1d71f3);
    }
    &.disabled {
      width: 327px;
    }
  }
}
.card {
  width: calc(100% - 24px);
  margin: 0 auto;
}
.device-list {
  margin-left: 12px;
}
.device-title {
  align-self: stretch;
  color: var(--text-color-primary, #82879b);
  font-family: 'PingFang SC';
  font-size: var(--font-size-text-size, 12px);
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  text-transform: capitalize;
  margin: 10px 0;
}
.device-item {
  display: flex;
  align-items: center;
  height: 72px;
  padding: 12px 0;
  border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  .device-info {
    max-width: calc(100% - 85px);
  }

  .device-icon-wrapper {
    background-color: var(--bg-color-primary, #f9fafc);
    width: 78px;
    height: 48px;
    border-radius: 2px;
    margin-right: 7px;
    background-image: url('@/assets/img/common/device.png');
    background-position: center;
    background-clip: border-box;
    background-size: cover;
  }
  .device-name {
    color: var(--text-color-primary, #1a1a1a);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 3px;
  }
}
</style>
