<template>
  <div class="my-share-wrapper">
    <div class="my-share-content">
      <template v-if="!isInitReq || myShareList.length">
        <share-list :dataList="myShareList" @click="handleClick" />
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('noShareDevice') }}</div>
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('initiateShare') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import ShareList from './components/ShareList.vue'
import { getUserShareList } from '@/api/share'
import { mapState, mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'myShare',
  components: {
    ShareList,
    ThemeImage
  },
  props: {
    activeTab: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isInitReq: false, // 是否请求过数据
      queryParams: {
        resourceTypes: [1, 2], // 1 通道分享；2 设备分享
        queryType: 1 // 1 我分享给别人的  2 别人分享给我的
      },
      pullingStatus: 0
    }
  },
  computed: {
    ...mapState('share', ['myShareList'])
  },
  watch: {
    activeTab: {
      handler(newValue) {
        if (newValue === 'myShare') {
          this.getList()
        }
      },
      immediate: true
    }
  },
  methods: {
    ...mapMutations('share', ['SET_SHARE_RECORD', 'SET_MY_SHARE_LIST', 'SET_DEV_OPERATION_OBJ', 'SET_DEV_LIST']),
    async getList() {
      try {
        this.isInitReq = true
        this.$loading.show()
        let { data } = await getUserShareList(this.queryParams)
        const devOperationObj = {}

        const devList = []

        const myData = data.map(item => {
          return {
            ...item,
            shareList: item.shareList.filter(item => {
              if (item.resourceType === 2) {
                const auth = JSON.parse(item.auth)
                if (auth?.length > 0) {
                  devOperationObj[item.sn] = true
                } else {
                  devOperationObj[item.sn] = false
                }
                devList.push({
                  sn: item.sn,
                  orderId: item.id
                })
                return false
              }
              return true
            }),
            status: item.shareList[0].status
          }
        })

        // 存储请求数据
        this.SET_MY_SHARE_LIST(myData)
        this.SET_DEV_OPERATION_OBJ(devOperationObj)
        this.SET_DEV_LIST(devList)
      } catch (error) {
        if (error.code && error.msg) {
          this.$toastFail(error.msg)
        }
      } finally {
        this.$loading.hide()
      }
    },
    // 点击我的分享记录
    async handleClick(item) {
      // 请求全量列表，并通过列表判断当前记录是否存在

      const { data } = await getUserShareList(this.queryParams)
      const myData = data.map(item => {
        return {
          ...item,
          shareList: item.shareList.filter(item => item.resourceType !== 2),
          status: item.shareList[0].status
        }
      })
      const record = myData.find(item2 => item2.userId === item.userId)
      if (record) {
        // 说明当前分享记录仍然存在，则直接进入分享详情页
        this.SET_SHARE_RECORD({ ...record })
        // 进入编辑通道页面
        this.$utils.routerPush({
          path: '/share/shareDetail?type=edit'
        })
        // 存储请求数据
        this.SET_MY_SHARE_LIST(myData)
      } else {
        // 否则吐司提示“无法查看，该设备已被取消分享”，并刷新列表
        this.$toastFail(this.$t('shareViewFail'))
        // 存储请求数据
        this.SET_MY_SHARE_LIST(myData)
      }
    },
    // 发起分享
    handleConfirm() {
      // 进入选择分享人页面
      this.$utils.routerPush({
        path: '/share/chooseSharer'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.my-share-wrapper {
  height: 100%;
  overflow: auto;
  border-top: 1px solid var(--outline-color-primary, #e7e7e7);
  .my-share-content {
    height: calc(100vh - 190px);
    overflow: auto;
    box-sizing: border-box;
  }
  .no-data {
    width: 100%;
    height: calc(100%);
    overflow: hidden;
    .no-data-img {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 100px;

      img {
        width: 120px;
        height: 123px;
      }
      .theme-image-container {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-top: 20px;
    }
  }
  .footer {
    padding: 10px 0px 6px 0px;
  }
}
</style>
