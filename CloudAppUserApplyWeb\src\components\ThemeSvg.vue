<template>
  <div v-html="svgContent" :style="{ color: svgColor }"></div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'ThemeSvg',
  props: {
    color: {
      type: String,
      default: '#000'
    },
    imageName: {
      type: String,
      default: null
    }
  },
  mounted() {
    this.updateColor()
  },
  data() {
    return {
      themeImg: null
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    svgColor() {
      return this.color
    },
    imgSrc() {
      return this.imageName ? require(`@/assets/img/${this.appStyleFlag}/${this.uiStyleFlag}/${this.imageName}`) : null
    },
    fallbackImgSrc() {
      return this.imageName ? require(`@/assets/img/common/${this.imageName}`) : null
    }
  },
  watch: {
    color: {
      handler() {
        this.updateColor()
      }
    },
    imageName: {
      handler() {
        this.refreshImg()
      },
      immediate: true
    }
  },
  async created() {
    this.refreshImg()
  },
  methods: {
    async refreshImg() {
      // 判断对应的图片是否存在，存在则用定制的图标否则用公共的
      let response
      try {
        response = await fetch(this.imgSrc)
        if (!response.ok) {
          // 用公共图片
          response = await fetch(this.fallbackImgSrc)
        }
      } catch (err) {
        // 异常情况下也用公用图片
        response = await fetch(this.fallbackImgSrc)
      }
      this.svgContent = await response.text()
      this.updateColor()
    },
    updateColor() {
      this.$nextTick(() => {
        const svgElement = this.$el.querySelector('svg')
        if (svgElement) {
          svgElement.setAttribute('fill', this.svgColor)
        }
      })
    }
  }
}
</script>
