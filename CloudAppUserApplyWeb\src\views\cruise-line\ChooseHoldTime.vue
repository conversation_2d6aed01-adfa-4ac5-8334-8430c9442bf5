<template>
  <div class="choose-point-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="choose-point-content">
      <div class="choose-point-list">
        <div
          class="choose-point-item"
          v-for="(item, index) in timeList"
          :key="'item.value' + index"
          @click="chooseItem(item)"
        >
          <div
            :class="[
              'choose-point-name',
              'text-over-ellipsis',
              pointRecord.holdTime === item.value ? 'active-item' : ''
            ]"
          >
            {{ item.label }}
          </div>
          <img
            alt="checked"
            class="check-img"
            :class="uiStyleFlag === 'ui1b' ? 'vms-item-img' : ''"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/checked.png')"
            v-if="pointRecord.holdTime === item.value"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'ChooseHoldTime',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {}
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('cruiseLine', ['devType', 'pointRecord', 'holdTimeList', 'ipcHoldTimeList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    timeList() {
      return this.devType === '1' ? this.ipcHoldTimeList : this.holdTimeList
    }
  },
  methods: {
    ...mapMutations('cruiseLine', ['SET_POINT_RECORD']),
    back() {
      this.$router.go(-1)
    },
    chooseItem(item) {
      this.SET_POINT_RECORD({
        ...this.pointRecord,
        holdTime: item.value
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-point-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .choose-point-content {
    width: 100%;
    height: calc(100% - 44px);
    padding: 35px 16px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  .choose-point-list {
    width: 100%;
    border-radius: 10px;
    .choose-point-item {
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      padding: 11px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      &:last-child {
        border: 0;
      }
      .check-img {
        width: 36px;
        height: 36px;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.choose-point-wrapper .nav-bar .nav-bar-center {
  font-size: var(--font-size-body1-size, 16px) !important;
  font-weight: 600;
}
</style>
