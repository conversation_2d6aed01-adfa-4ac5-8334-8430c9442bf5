<template>
  <div class="choose-device-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="choose-device-content">
      <template v-if="!isInitReq || innerDeviceList.length">
        <choose-channel
          v-model="checkChannels"
          type="permission"
          ref="chooseChannelRef"
          @updateDevOperation="updateDevOperation"
          :deviceChannelList="innerDeviceList"
          :devOperationObj="devOperationObj"
          :devSupportFunObj="devSupportFunObj"
        />
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('noShareDevice') }}</div>
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleNextStep">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import ChooseChannel from './components/ChooseChannel.vue'
import { CHANNEL_CAPABILITY_LIST } from '@/utils/options'
import { mapState, mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'ChangeCapability',
  components: {
    NavBar,
    ChooseChannel,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isInitReq: false, // 是否请求过数据
      checkChannels: [], // 选中的通道
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      channelSupportFunObj: {}, // 通道能力集
      innerDeviceList: [] // 设备列表
    }
  },
  created() {
    this.createChannelTree(this.chooseChannelList.sort((a, b) => a.chlIndex - b.chlIndex))
  },
  mounted() {
    this.$refs.chooseChannelRef.selectAll(true)
  },
  computed: {
    ...mapState('share', ['deviceChannelList', 'chooseChannelList', 'devSupportFunObj', 'devOperationObj'])
  },
  methods: {
    ...mapMutations('share', [
      'SET_SHARE_USER',
      'SET_ALL_CHANNEL_LIST',
      'SET_DEVICE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_CHOOSE_CHANNEL_LIST',
      'SET_DEV_OPERATION_OBJ',
      'SET_DEV_OPERATION_OBJ_BY_SN'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 构建设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const deviceChannelList = []
      const deviceIndexObj = {} // 记录站点Id——设备sn及其在通道树中的索引
      allChannelList.forEach(item => {
        const { sn, deviceName } = item
        let temp = null
        // 判断设备
        if (deviceIndexObj[sn] !== undefined) {
          // 说明设备存在
          const index = deviceIndexObj[sn]
          temp = deviceChannelList[index]
          temp.children = deviceChannelList[index].children || []
          temp.children.push({ ...item })
        } else {
          // 说明设备不存在，直接添加
          temp = { sn, deviceName, children: [{ ...item }] }
          deviceIndexObj[sn] = deviceChannelList.length // 记录下站点的索引
          // 继续添加通道
          deviceChannelList.push(temp)
        }
      })
      this.innerDeviceList = deviceChannelList
      const devOperationObj = {}
      deviceChannelList.forEach(item => {
        const { sn } = item

        if (this.devSupportFunObj[sn]?.includes('shareDevOp')) {
          devOperationObj[sn] = this.devOperationObj[sn] === undefined ? true : this.devOperationObj[sn]
        }
      })
      this.SET_DEV_OPERATION_OBJ(devOperationObj)
    },
    updateDevOperation({ sn, operationChecked }) {
      this.SET_DEV_OPERATION_OBJ_BY_SN({ sn, operationChecked })
    },
    // 下一步
    handleNextStep() {
      // 将当前选中的通道（包括权限）暂存下来
      this.SET_CHOOSE_CHANNEL_LIST(JSON.parse(JSON.stringify(this.checkChannels)))
      this.checkChannels = [] // 重置当前选中通道
      // 进入确认分享页
      this.$utils.routerPush({
        path: '/share/confirmShare'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-device-wrapper {
  height: 100%;
  overflow: hidden;
  .choose-device-content {
    width: 100%;
    height: calc(100% - 130px);
    overflow: auto;
    box-sizing: border-box;
    .no-data {
      width: 100%;
      height: calc(100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      .no-data-img {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 120px;
          height: 123px;
        }
        .theme-image-container {
          width: 120px;
          height: 123px;
        }
      }
      .no-data-text {
        text-align: center;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-top: 20px;
      }
    }
  }
}
</style>
