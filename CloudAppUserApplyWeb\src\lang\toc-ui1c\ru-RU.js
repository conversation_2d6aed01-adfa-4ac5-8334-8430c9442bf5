// 俄语(ru)  客户提供了翻译就用客户的，没有提供的用英文
export default {
  cancel: 'Отмена',
  confirm: 'ОК',
  deviceUpdate: 'Устройство',
  cameraUpdate: 'Камера',
  updateNow: 'Обновление',
  currentVersion: 'Версия',
  latestVersion: 'Последняя версия',
  updateContent: 'Обновить контент',
  hasLatestVersion: 'Самая последняя версия',
  online: 'В сети',
  offline: 'Не в сети',
  waitDownload: 'В ожидании скачивания',
  inprogress: 'Загрузка',
  downloadFail: 'Загрузка не удалась',
  downloadFinished: 'Загрузка завершена',
  inupgrade: 'Обновление',
  upgradeFail: 'Не удалось обновить',
  upgradeSuccess: 'Успешно обновить!',
  deviceUpgradeInfo:
    'Во время обновления устройство будет отключено и автоматически перезагружено. Вы обязательно обновите?',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo:
    'Во время обновления камера будет отключена и автоматически перезапускается. Вы обязательно обновите?',
  pwdUserNameError: 'Ошибка в имени пользователя или пароле',
  permissionAuth: 'Аутентификация авторитета супер администратора',
  pleaseEnterUser: 'Пожалуйста введите имя пользователя',
  pleaseEnterPwd: 'введите пароль',
  noCameraUpgrade: 'Не обнаружена обновляемой камеры',
  handleCheck: 'Проверить обновления',
  done: 'Законченный',
  delete: 'Удалить',
  operationSuccess: '',
  operationFail: 'Ошибка действия',
  cancelTrusteeship: '',
  chooseDevice: 'Выберите устройство',
  noAvaiableDevice: '',
  leastChoose: '',
  details: 'индивидуальный',
  live: 'Просмотр',
  rec: 'Воспроизведение',
  config: 'Настроить',
  unBind: 'Отвязка',
  checkSuccess: 'Проверено успешно',
  checkFail: 'Проверка не удалась',
  viewUpdateContent: 'Посмотреть содержимое обновления',
  deviceDisconnected: 'Не удалось подключиться к устройству',
  updateNote: 'Обновить заметку',
  noData: 'Нет данных',
  tips: 'Советы',
  password: 'Пароль',
  pwdError: 'Ошибка пароля. Вы можете попробовать еще {0} раз.',
  pwdErrorLock: 'Устройство заблокировано из-за повторяющихся ошибок. Пожалуйста, повторите попытку позже',
  noPermissions: 'Нет разрешений',
  permission: 'Разрешение',
  validity: 'Период действия',
  permissionValidity: 'Срок действия разрешения',
  isSaveModify: 'Подтвердите сохранение изменений',
  manyMinutes: '{0}мин',
  manyHours: '{0}ч',
  manyDays: '{0}д',
  manyMinutesEn: '{0}мин', // 缺少翻译
  manyHoursEn: '{0}ч', // 缺少翻译
  manyDaysEn: '{0}д', // 缺少翻译
  oneWeek: '1 неделя',
  forever: 'Навсегда',
  expired: 'Истекший',
  residue: 'Остаток',
  bindSuccess: 'Ассоциация прошла успешно',
  from: 'c',
  changeSuccessfully: 'Изменение прошло успешно',
  email: 'Эл. адрес',
  enterMemberEmail: 'Введите адрес электронной почты',
  mobile: 'Телефонный номер',
  enterMemberMobile: 'Введите номер телефона',
  emailNameError: 'Ошибка формата электронной почты',
  emailNameNotEmpty: 'E-mail не может быть пустым!',

  upgrade: 'Cloud Upgrade',
  allUpdate: 'Upgrade All',
  paySuccess: 'Payment succeeded',
  payFail: 'Payment failed',
  rePurchase: 'Repurchase',
  cloudStorage: 'Cloud Storage',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  myInstaller: 'My installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Share with installer',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  confirmTrusteeshipTip:
    'The sharing request has been sent to the installer. Please wait for the installer to process it',
  cancelTrusteeshipTip:
    'After canceling sharing the installer is unable to provide you with remote maintenance services. Please confirm',
  unBindTrusteeship: 'After unbinding all device sharing will be cancelled. Please confirm',
  trusteeshipPermissions: 'Sharing permissions',
  trusteeshipTime: 'Share time',
  serviceException: 'Service exception',
  pullingText: 'Pull down to Loading...',
  loosingText: 'Free to refresh...',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more',
  errorCode: {
    400: 'Ошибка параметра',
    503: 'Исключение сервера',
    1000: 'Ошибка параметра',
    1005: 'Код ошибки аутентификации изображения',
    1007: 'Требуется код подтверждения изображения',
    1009: 'Ошибка кода подтверждения',
    1012: 'Неизвестный API',
    1015: 'Пользователь уже существует',
    4500: 'Ошибка параметра',
    5000: 'У вас нет прав для выполнения этого действия',
    6001: 'Частое действие',
    7000: 'Ошибка параметра',
    7001: 'Пользователь не существует',
    7002: 'Ошибка в старом пароле',
    7003: 'Недействительный токен',
    7006: 'Номер смартфона уже существует',
    7010: 'Учетная запись администратора не активирована',
    7019: 'Имя пользователя уже существует',
    7023: 'Связанный адрес электронной почты',
    7081: 'Не удалось импортировать',
    7082: 'Ошибка экспорта',
    9004: 'Ошибка операции с базой данных',
    12344: 'Ошибка подключения к сети',
    12345: 'Превышено время подключения к сети',
    32019: 'Ошибка действия',
    34004: 'Ошибка доступа',
    536870943: 'Неверный параметр',
    536870947: 'Имя пользователя не существует.',
    536870948: 'Ошибка имени пользователя или пароля',
    536871039: 'Неверный параметр',
    536871060: 'Невозможно подключиться к устройству, проверьте состояние устройства',

    10000: 'Failed to connect device',
    550: 'Request timeout', //app返回的超时
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    404: 'The requested resource (web page, etc.) does not exist',
    500: 'System exception',
    502: 'Server request failed',
    504: 'Server request timeout',
    32018: 'The data doesnt exist.',
    32022: 'Hosting service is not supported because {0} device and installer are not in the same country/region.',

    536870945: 'Operation failure, please check the device status', //'Device busy, please try again later',
    536871017: 'Operation failure, please check the device status', // 版本不匹配
    536871082: 'Operation failure, please check the device status', //'无新版本',
    536871083: 'Operation failure, please check the device status', //'云升级版本不存在'
    536870940: 'Operation failure, please check the device status', //云升级未开启
    *********: 'Operation failure, please check the device status',
    1008: 'The verification code has expired',
    1011: 'The parameter is not filled in correctly!',
    1013: 'The verification code failed to send',
    1027: 'Please enter the correct device serial number/security code',
    1028: 'The camera is enabled or disabled',
    5001: 'The current user has no permission',
    6000: 'The current business status does not support this operation',
    7004: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7005: 'Invalid signature',
    7007: 'The user is locked, please contact the administrator to unlock',
    7009: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7011: 'Account not activated',
    7021: 'Deletion failed! Please clear all hosts under this host group first',
    7028: 'The template has been used in the project and cannot be deleted!',
    7029: 'The template name already exists!',
    7030: 'The data already exists!',
    7032: 'The firmware package already exists!',
    7034: 'The firmware package has been released and cannot be deleted!',
    7042: 'There are other tasks in the startup state',
    7043: 'The task has not been approved!',
    7044: 'Operation failed. There are no devices eligible for upgrade!',
    7045: 'The task is not approved!',
    7056: 'This version has been included in the supporting compatibility management and cannot be deleted!',
    7057: 'Issuing document cannot be blank!',
    7061: 'Correction failed, cannot create correction again!',
    7066: 'The customer code already exists!',
    7068: 'The customer code does not exist!',
    7069: 'Too much data, please narrow the scope and search again!',
    7084: 'The customer country code already exists',
    7086: 'The operation is refused due to system exception',
    7087: 'The product already exists!',
    7088: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7090: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7093: 'Image and text information is not configured!',
    7094: 'The service terms information does not exist!',
    9000: 'System exception!',
    9001: 'The protocol version is too low. The old version is no longer compatible and needs to be upgraded',
    9002: 'Protocol version error, unrecognized version field or error message',
    9003: 'Failed to send verification code',
    9005: 'The data does not exist',
    9006: 'The data already exists',
    9007: 'The data to be viewed does not exist',
    9008: 'The data does not exist',
    9009: 'Data exception',
    9500: 'System exception!',
    10001: 'System exception!',
    20021: 'This email has been used',
    20024: 'The account has been activated',
    20030: 'The link has expired',
    33001: 'No permission to operate this device',
    33002: 'No permission to operate this site',
    33003: 'The site does not exist',
    33004: 'The length of the device name must be between 0 and 32',
    33010: 'The device already exists',
    7072: 'The device already exists',
    // 托管错误码
    32021: 'Data does not exist',
    // ipc 云升级错误码
    ipc: {
      499: 'неизвестная ошибка.', //未知错误
      612: 'Operation failure, please check the device status',
      730: 'Operation failure, please check the device status', //检查新版本信息时，无新版本信息
      731: 'Operation failure, please check the device status', //云升级功能未使能  升级guid错误
      732: 'Operation failure, please check the device status', //升级任务已存在
      735: 'Operation failure, please check the device status'
    }
  }
}
