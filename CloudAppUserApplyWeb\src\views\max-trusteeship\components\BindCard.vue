<template>
  <div class="bind-card-wrapper">
    <div class="bind-card-content">
      <div class="bind-card-head">
        <div class="bind-card-title">{{ $t('introduceText') }}</div>
        <div :class="['bind-card-name', 'text-over-ellipsis2', isZh ? 'bind-card-name-zh' : 'bind-card-name-en']">
          {{ installerInfo.installerCoName || '' }}
        </div>
        <div class="bind-card-info">
          <div class="bind-card-icon">
            <img
              alt="avatar"
              :src="installerInfo.logo ? installerInfo.logo : require('@/assets/img/common/trusteeship/avatar.png')"
            />
          </div>
          <div class="bind-card-right">
            <div class="card-info-line">
              <img class="card-info-icon" alt="email" :src="require('@/assets/img/common/email.png')" />
              <div class="card-info-text">{{ installerInfo.installerEmail || '' }}</div>
            </div>
            <div class="card-info-line">
              <img class="card-info-icon" alt="tel" :src="require('@/assets/img/common/tel.png')" />
              <div class="card-info-text">{{ installerInfo.tel || '' }}</div>
            </div>
            <div class="card-info-line">
              <img class="card-info-icon" alt="address" :src="require('@/assets/img/common/address.png')" />
              <div class="card-info-text">{{ installerInfo.addr || '' }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bind-card-body">
        <div class="bind-device-content">
          <div class="bind-device-text">
            {{ deviceNum ? $t('trustDeviceCount', [deviceNum]) : $t('noTrusteeship') }}
          </div>
          <div class="bind-device-operate" @click="handleDetail" v-if="showDetail">{{ $t('viewDetail') }}</div>
        </div>
        <div class="bind-card-btn">
          <van-button class="footer-btn" type="primary" @click="handleClick">
            {{ $t('immediateTrusteeship') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'BindCard',
  components: {},
  props: {
    deviceNum: {
      type: Number,
      default: 0
    },
    installerInfo: {
      type: Object,
      default: () => ({ logo: '' })
    },
    showDetail: {
      type: Boolean,
      dafault: false
    }
  },
  data() {
    return {}
  },
  computed: {
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    },
    handleDetail() {
      this.$emit('detail')
    }
  }
}
</script>
<style lang="scss" scoped>
.bind-card-wrapper {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Source Han Sans CN', sans-serif;
  margin-top: 10px;
  .bind-card-content {
    width: calc(100% - 16px);
    max-width: 359px;
    // width: 359px;
    height: 300px;
    border-radius: 10px;
    box-shadow: 0 2px 4px 0 #00000040;
    background-color: var(--bg-color-white, #ffffff);
  }
  .bind-card-head {
    // width: 359px;
    width: 100%;
    height: 160px;
    background-image: url('@/assets/img/common/trusteeship/bind_bg.png');
    background-repeat: no-repeat;
    background-position-y: 0px;
    background-position-x: center;
    background-size: 100%;
    .bind-card-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      padding-top: 23px;
    }
    .bind-card-name {
      width: 100%;
      text-align: left;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      margin: 5px 0px 10px 0px;
      padding: 0px 30px 0px 130px;
      box-sizing: border-box;
    }
    .bind-card-name-zh {
      word-break: break-all;
    }
    .bind-card-name-en {
      word-break: normal;
    }
    .bind-card-info {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--bg-color-white, #ffffff);
      .bind-card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .bind-card-right {
        width: 158px;
        height: 60px;
      }
      .card-info-line {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .card-info-icon {
          width: 16px;
          height: 16px;
        }
        .card-info-text {
          width: 100%;
          padding-left: 8px;
          box-sizing: border-box;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }
  .bind-card-body {
    // width: 359px;
    width: 100%;
    height: 140px;
    border-radius: 0px 0px 10px 10px;
    .bind-device-content {
      width: 100%;
      height: 60px;

      padding: 18px 28px;
      box-sizing: border-box;
      font-family: 'Source Han Sans CN', sans-serif;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .bind-device-text {
        width: calc(100% - 85px);
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
      }
      .bind-device-operate {
        width: 85px;
        text-align: right;
      }
    }
    .bind-card-btn {
      padding-top: 16px;
      text-align: center;
      .footer-btn {
        width: 327px;
        height: 40px;
        border-radius: 23px;
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
