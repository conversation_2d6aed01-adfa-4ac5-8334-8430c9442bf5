<template>
  <div class="device-trusteeship-wrapper">
    <nav-bar :showBack="false" :showEllipsis="true" @showEllipsis="showUnBindButton"></nav-bar>
    <!-- 解除绑定 -->
    <van-overlay :show="bindFlag" @click="bindFlag = false" class-name="unbind-overlay">
      <select-menu :options="options" class="menu" @choose="selectItem"></select-menu>
    </van-overlay>
    <div class="device-trusteeship-content">
      <!-- 有转移设备时展示 -->
      <div class="transfer-device-content" v-if="transferList.length">
        <div class="transfer-device-left">
          <div class="transfer-device-title text-ellipsis">{{ $t('deviceTransfer') }}</div>
          <div class="transfer-device-text text-ellipsis">
            {{ $t('transferDeviceCount', [transferList[0].devNum]) }}
          </div>
        </div>
        <div class="transfer-device-right">
          <div class="operate-btn reject-btn" @click="handleReject">
            {{ $t('reject') }}
          </div>
          <div class="operate-btn accept-btn" @click="handleAccept">
            {{ $t('acceptTransfer') }}
          </div>
        </div>
      </div>
      <tvt-better-scroll
        :class="transferList.length ? 'tvt-better-scroll' : 'whole-tvt-better-scroll'"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <!-- 未绑定安装商时展示 -->
        <un-bind-card v-if="!bindInstaller" @click="handleBind" />
        <!-- 绑定安装商时展示 -->
        <template v-else>
          <bind-card
            :deviceNum="deviceNum"
            :installerInfo="installerInfo"
            @click="handleTrusteeship"
            :showDetail="deveiceData && deveiceData.length > 0"
            @detail="handleDetail"
          />
          <!-- 设备托管列表 不在这展示了，放入单独的列表页 -->
          <!-- <div class="device-list-wrapper" v-if="deveiceData.length">
            <div class="device-list-title">{{ $t('deviceTrusteeship') }}</div>
            <div class="device-list-content">
              <device-list :dataList="deveiceData" @click="handleClick" />
            </div>
          </div> -->
        </template>
        <!-- 云存储卡片 -->
        <cloud-storage-card
          v-if="isCloudStorageAvailable"
          :cloudStorageBuyChlNum="cloudStorageBuyChlNum"
          :isGuest="isGuest"
        />
      </tvt-better-scroll>
    </div>
    <!-- 解除绑定确定弹窗 -->
    <van-popup v-model="showUnBind" round position="bottom" :style="{ height: '224px' }">
      <popup-confirm
        :title="$t('unBind')"
        :text="$t('unBindDesc')"
        :cancelText="$t('notNow')"
        :confirmText="$t('stillUnbind')"
        @cancel="cancelUnBind"
        @confirm="confirmUnBind"
      />
    </van-popup>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import SelectMenu from '@/components/SelectMenu.vue'
import PopupConfirm from '@/components/PopupConfirm.vue'
import BindCard from './components/BindCard.vue'
import UnBindCard from './components/UnbindCard.vue'
// import DeviceList from './components/deviceList.vue'
import CloudStorageCard from './components/CloudStorageCard.vue'
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'
import { bridgeRegisterHandler, openDialog, gotoPage, openH5, receiveDevice, isGuest } from '@/utils/appbridge'
import { mapMutations } from 'vuex'
import { deviceTrusteeshipsList, installerUnbind } from '@/api/trusteeship.js'
import { transferRequestList, isPartner, transferHandle, transferDetail } from '@/api/transfer'
import { isCloudStorageAvailable, getCloudStorageChannels } from '@/api/cloudStorage'

export default {
  name: 'DeviceTrusteeship',
  components: {
    NavBar,
    SelectMenu,
    PopupConfirm,
    UnBindCard,
    BindCard,
    // DeviceList,
    CloudStorageCard
  },
  props: {},
  data() {
    return {
      bindInstaller: false, // 是否绑定过安装商
      installerInfo: {}, // 安装商信息
      bindFlag: false,
      showUnBind: false, // 解除绑定弹窗
      deviceCapabilitys: DEVICE_CAPABILITY_LIST(), // 全量设备权限
      pullingStatus: 0,
      deveiceData: [],
      listParams: {
        pageNum: 0, // 查所有
        pageSize: 100,
        queryType: 2, // 转移给我
        transferStatus: 0, // 待接收的设备
        userName: null, //转移发起人或转移接受人的登录名，为空查所有
        userType: 2 // 用户类型 1:C用户, 2:安装商, 3:B-VMS用户，为空查所有
      },
      transferList: [], // 待转移的设备
      showReject: false, // 拒绝转移二次确认弹窗
      isCloudStorageAvailable: false, // 云存储是否可用
      cloudStorageBuyChlNum: 0, // 购买云存储通道的数量
      isGuest: null
    }
  },
  created() {
    this.refreshRequest()
    bridgeRegisterHandler('viewAppear', async () => {
      // console.log('viewAppear回调函数参数')
      this.refreshRequest()
    })
    bridgeRegisterHandler('viewDisappear', async () => {
      // console.log('viewDisappear回调函数参数')
      this.bindFlag = false
    })
  },
  mounted() {},
  computed: {
    options() {
      return [
        {
          label: this.$t('unBind'),
          value: 1,
          disabled: !this.bindInstaller,
          iconName: 'unbind.png'
        }
      ]
    },
    // 当前用户名下正在被托管的设备数量
    deviceNum() {
      // 根据当前用户发送托管设备列表找到被接受的设备数量
      const arr = (this.deveiceData || []).filter(item => item.status === 1) // 0:待接收, 1:已接收, 2:拒绝
      return arr.length
    }
  },
  methods: {
    ...mapMutations('maxTrusteeship', ['SET_TRUSTEESHIP_RECORD']),
    // 判断当前用户是否为游客
    isUserGuest() {
      isGuest(res => {
        // console.log('isGuest返回结果', res)
        // 从APP获取当前用户是否为游客
        const resData = JSON.parse(res)
        this.isGuest = resData.body.isGuest
      })
    },
    showUnBindButton() {
      this.bindFlag = true
    },
    selectItem(item) {
      //这里要考虑后续按钮下拉的可扩展性
      if (item.value == 1) {
        bridgeRegisterHandler('unBindPopup', data => {
          const { operate } = data
          if (operate === 'confirm') {
            this.confirmUnBind()
          }
        })
        openDialog({
          url: '/maxTrusteeship/unBindPopup',
          observerName: 'unBindPopup'
        })
      }
    },
    // 刷新当前页面请求
    async refreshRequest() {
      this.isUserGuest()
      this.getTransferList()
      const flag = await this.getInstallerInfo()
      // 绑定过安装商才请求托管设备
      flag && this.getTrusteeshipDevices()
      // 获取云存储是否可用
      this.isCloudStorageAvailable = await this.isAvailable()
      // 可用就继续获取云存储购买状态
      this.isCloudStorageAvailable && this.getCloudStorageStatus()
    },
    // 获取安装商信息
    async getInstallerInfo() {
      try {
        const res = await isPartner()
        if (res.basic.code == 200) {
          this.installerInfo = res.data
          // 根据请求到的安装商信息判断是否绑定过安装商
          this.bindInstaller = !!res.data.installerUserId
          return !!res.data.installerUserId
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 获取当前用户发起托管设备
    async getTrusteeshipDevices() {
      try {
        // type 0: 可托管设备； 1：已发起托管设备
        const res = await deviceTrusteeshipsList({ type: 1 })
        // console.log('查询当前用户发起托管设备', res)
        if (res.basic.code === 200) {
          const { data } = res
          this.deveiceData = data.map(item => {
            const { authList = [], effectiveTime = 0, expireTime = 0, status } = item
            const checkCapability = this.deviceCapabilitys.filter(item2 => authList.includes(item2.value))
            // 判断已接受的记录是否过期--额外加个过期状态
            let expiredStatus = 0 // 0 表示未过期 1 表示过期
            if (Number(status) === 1) {
              const nowTimeStamp = Date.parse(new Date())
              if (Number(expireTime) === 0) {
                // expireTime为0表示永久
                expiredStatus = 0 // 未过期
              } else {
                if (parseInt(expireTime) - parseInt(nowTimeStamp) < 1000) {
                  // 剩余时长小于1s
                  expiredStatus = 1 // 过期
                } else {
                  expiredStatus = 0
                }
              }
            }
            return {
              ...item,
              effectiveTime: Number(effectiveTime),
              checkCapability,
              expiredStatus
            }
          })
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 查询转移给当前用户的设备
    async getTransferList() {
      try {
        const { data } = await transferRequestList(this.listParams)
        // 按照transferUserId的维度将转移记录合并，一个安装商只展示一条消息
        const transferList = []
        data.records.forEach(item => {
          const { transferUserId, transferUserLoginName } = item
          const idx = transferList.findIndex(item2 => item2.transferUserId === transferUserId)
          if (idx > -1) {
            transferList[idx].list.push({ ...item })
            transferList[idx].devNum++
          } else {
            transferList.push({
              transferUserId,
              transferUserLoginName,
              devNum: 1,
              list: [{ ...item }]
            })
          }
        })
        this.transferList = transferList
      } catch (error) {
        console.error(error)
      }
    },
    // 取消解除绑定
    cancelUnBind() {},
    // 确定解除绑定
    async confirmUnBind() {
      try {
        const res = await installerUnbind()
        if (res.basic.code === 200) {
          // 解绑成功则清空安装商信息
          this.bindInstaller = false
          this.installerInfo = {}
          this.refreshRequest()
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 绑定
    handleBind() {
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      // this.$toast('进入绑定安装商页面')
      gotoPage({
        pageRoute: 'user/qrcode',
        params: {
          title: this.$t('bindInstaller'),
          content: this.$t('bindInstallerDesc')
        }
      })
    },
    // 进入托管设备列表页面
    handleDetail() {
      // 进入托管列表
      openH5({
        url: '/maxTrusteeship/trusteeshipList'
      })
    },
    // 进入托管页面
    handleTrusteeship() {
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      // 进入选择设备
      openH5({
        url: '/maxTrusteeship/chooseDevice'
      })
    },
    // 点击设备
    handleClick(record) {
      // 进入设备托管详情页面
      this.SET_TRUSTEESHIP_RECORD(JSON.parse(JSON.stringify(record)))
      // 打开H5页面并传参过去
      const { id } = record
      openH5({
        url: '/maxTrusteeship/trusteeshipDetail',
        params: { id }
      })
    },
    // 点击拒绝转移
    handleReject() {
      bridgeRegisterHandler('rejectTransferPopup', data => {
        // console.log('回调函数参数', data)
        const { operate } = data
        if (operate === 'confirm') {
          this.confirmReject()
        }
      })
      openDialog({
        url: '/maxTrusteeship/rejectTransferPopup',
        observerName: 'rejectTransferPopup'
      })
    },
    async confirmReject() {
      this.$loading.show()
      try {
        // const ids = this.transferList.map(item => item.id)
        // 拒绝第一条转移记录的设备
        const ids = this.transferList[0].list.map(item => item.id)
        const result = await transferHandle({
          ids,
          accept: false
        })
        if (result) {
          // 通知APP刷新设备列表
          receiveDevice({ type: 'transfer' })
        }
        this.showReject = false
        // this.transferList = []
        // 去除第一条转移记录
        const transferList = this.transferList.slice()
        transferList.shift()
        this.transferList = transferList
        this.$loading.hide()
        this.$toastSuccess(this.$t('rejected'))
      } catch (error) {
        console.error(error)
        this.$loading.hide()
        if (error.basic.code === 34006) {
          this.rejectTransferCallFn()
        }
      }
    },
    // 拒绝失败后toast提示并刷新列表
    async rejectTransferCallFn() {
      // 转移记录不存在，需要进一步判断是过期还是取消
      const ids = this.transferList[0].list.map(item => item.id)
      const params = { ids }
      const res = await transferDetail(params)
      const { data } = res
      const { transferList = [] } = data[0] || {}
      // 判断详情中设备的状态--直接取第一个的状态  0:待接受 1：已接受 2：拒绝 3：删除 4:过期
      const { transferStatus } = transferList[0]
      this.recordStatus = Number(transferStatus)
      switch (Number(transferStatus)) {
        case 1:
          this.$toastFail(this.$t('transferAcceptedDesc'))
          break
        case 2:
          this.$toastFail(this.$t('transferRejectedDesc'))
          break
        case 3:
          this.$toastFail(this.$t('applyCancelledDesc'))
          break
        case 4:
          this.$toastFail(this.$t('applyExpiredDesc'))
          break
        default:
          this.$toastFail(this.$t('errorCode.34006'))
      }
      // 刷新转移列表
      this.getTransferList()
    },
    // 点击接受转移
    handleAccept() {
      // 进入设备转移申请页面--携带参数过去
      const { list = [] } = this.transferList[0]
      const transferRecordIDs = list.map(item => String(item.id))
      // 进入转移申请页面
      openH5({
        url: '/maxTransfer/transferApplay',
        params: { transferRecordIDs: JSON.stringify(transferRecordIDs) }
      })
    },
    // 获取当前云存储是否购买，有几路通道购买
    getCloudStorageStatus() {
      getCloudStorageChannels({ devId: '', applicationId: 1 }).then(res => {
        if (res.basic.code === 200) {
          this.cloudStorageBuyChlNum = res?.data || 0
        }
      })
    },
    async isAvailable() {
      try {
        const res = await isCloudStorageAvailable({})
        return res.data?.some(item => item.applicationId === 1)
      } catch (error) {
        console.error(error)
      }
    },
    async pullingUp(callback) {
      // 刷新
      this.getTransferList()
      const flag = await this.getInstallerInfo()
      // 绑定过安装商才请求托管设备
      flag && this.getTrusteeshipDevices()
      this.isCloudStorageAvailable = await this.isAvailable()
      this.isCloudStorageAvailable && this.getCloudStorageStatus()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      this.getTransferList()
      const flag = await this.getInstallerInfo()
      // 绑定过安装商才请求托管设备
      flag && this.getTrusteeshipDevices()
      this.isCloudStorageAvailable = await this.isAvailable()
      this.isCloudStorageAvailable && this.getCloudStorageStatus()
      if (callback) callback()
    }
  }
}
</script>
<style lang="scss" scoped>
.device-trusteeship-wrapper {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);
  height: 100%;
  overflow: hidden;
  position: relative;
  .nav-bar {
    background-color: transparent;
  }
  .unbind-overlay {
    /* 安全区的范围 */
    padding-top: var(--safeAreaTop);
    background: transparent;
    .select-menu {
      width: 100%;
      position: relative;
      top: 30px;
      right: 10px;
    }
  }
  .device-trusteeship-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    box-sizing: border-box;
    .tvt-better-scroll {
      height: calc(100% - 60px);
      overflow: auto;
    }
    .whole-tvt-better-scroll {
      height: calc(100% - 0px);
      overflow: auto;
    }
    .unbind-card-wrapper {
      width: 100%;
      height: 300px;
      display: flex;
      justify-content: center;
      align-items: center;
      .unbind-card-content {
        width: 359px;
        height: 300px;
        border-radius: 10px;
      }
      .unbind-card-head {
        width: 359px;
        height: 137px;
        background-image: url('@/assets/img/common/trusteeship/unbind_bg.png');
        background-repeat: no-repeat;
        background-position-y: center;
        background-position-x: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .unbind-card-title {
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-h4-size, 20px);
          font-style: normal;
          font-weight: 500;
          line-height: 28px;
        }
        .unbind-card-text {
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
        .unbind-card-tag {
          height: 16px;
          flex-shrink: 0;
          border-radius: 10px;
          margin-top: 4px;
          padding: 0px 6px;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
        }
      }
      .unbind-card-body {
        width: 359px;
        height: 163px;
        border-radius: 0px 0px 10px 10px;
        .unbind-card-line {
          width: 100%;
          height: 24px;
          padding: 0px 60px;
          box-sizing: border-box;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 6px;
        }
        .trusteeship-line-text {
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
          margin-left: 8px;
        }
        .unbind-card-btn {
          margin-top: 20px;
          text-align: center;
        }
      }
    }
    .device-list-wrapper {
      width: 100%;
      height: calc(100% - 300px);
      .device-list-title {
        font-family: 'Source Han Sans CN', sans-serif;
        width: 100%;
        height: 60px;
        padding: 18px 14px 0 28px;
        box-sizing: border-box;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-list-content {
        width: 100%;
        height: calc(100% - 60px);
        overflow: auto;
      }
    }
    .transfer-device-content {
      width: 100%;
      min-height: 60px;
      padding: 10px 10px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .transfer-device-left {
        flex: 1;
        height: 100%;
        overflow: hidden;
        .transfer-device-title {
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-body2-size, 14px);
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .transfer-device-text {
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
      .transfer-device-right {
        height: 100%;
        display: flex;
        align-items: center;
        .operate-btn {
          display: inline-flex;
          width: max-content;
          height: 24px;
          padding: 6px 14px;
          box-sizing: border-box;
          justify-content: space-between;
          align-items: center;
          border-radius: 12px;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
        .reject-btn {
          color: var(--error-bg-color-default, #ff3d3d);
          border: 1px solid var(--error-bg-color-default, #ff3d3d);
        }
        .accept-btn {
          color: var(--brand-bg-color-default, #3277fc);
          border: 1px solid var(--brand-bg-color-default, #3277fc);
          margin-left: 10px;
        }
      }
      .text-ellipsis {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
<style lang="scss">
.app-container {
  background-color: transparent;
}
</style>
