//获取当前通道所有的巡航线列表
export const urlChlCruiseList = chlId => {
  const xml = `<condition><chlId>${chlId}</chlId></condition>`
  return xml
}

// 获取当前巡航线的所有信息--巡航线名称，编号，预置点列表
export const urlChlCruise = (chlId, cruiseIndex) => {
  const xml = `<condition><chlId>${chlId}</chlId><cruiseIndex>${cruiseIndex}</cruiseIndex></condition>`
  return xml
}

// 新增巡航线
export const urlCreateChlCruise = (chlId, name, presets) => {
  let itemList = ''
  presets.forEach(item => {
    itemList += `<item index="${item.index}"><speed>${item.speed}</speed><holdTime>${item.holdTime}</holdTime></item>`
  })
  const xml = `<content><name><![CDATA[${name}]]></name><chlId>${chlId}</chlId><presets  type="list">${itemList}</presets></content>`
  return xml
}

// 编辑巡航线名称
export const urlEditChlCruiseName = (chlId, name, cruiseIndex) => {
  const xml = `<content><index>${cruiseIndex}</index><name><![CDATA[${name}]]></name><chlId>${chlId}</chlId></content>`
  return xml
}

// 编辑巡航线
export const urlEditChlCruise = (chlId, cruiseIndex, name, presets) => {
  let itemList = ''
  presets.forEach(item => {
    itemList += `<item index="${item.index}"><name><![CDATA[${item.name}]]></name><speed>${item.speed}</speed><holdTime>${item.holdTime}</holdTime></item>`
  })
  const xml = `<content><index>${cruiseIndex}</index><name><![CDATA[${name}]]></name><chl id="${chlId}"></chl><presets  type="list">${itemList}</presets></content>`
  return xml
}

// IPC相关
const OCX_XML_Header = '<?xml version="1.0" encoding="UTF-8"?>'
const Font = '<config xmlns="http://www.ipc.com/ver10" version="1.7">'
const Back = '</config>'
const OCX_XML_Config = Font + Back

// IPC-获取所有预置点列表
export const urlPtzGetPresets = () => {
  const result = OCX_XML_Header + OCX_XML_Config
  return result
}

// IPC-获取所有巡航线列表
export const urlPtzGetCruises = () => {
  const result = OCX_XML_Header + OCX_XML_Config
  return result
}

// IPC-获取设备当前巡航线的所有信息--巡航线名称，编号，预置点列表
export const urlPtzGetCruise = chlId => {
  let result = OCX_XML_Header
  result += Font
  result += '<cruiseInfo>' + '<id>' + chlId + '</id>' + '</cruiseInfo>'
  result += Back
  return result
}

// IPC-添加巡航线
export const urlPtzAddCruise = (name, presets) => {
  let result = OCX_XML_Header + Font
  result += '<cruiseInfo version="1.7" xmlns="http://www.ipc.com/ver10">'
  result += `<name><![CDATA[${name || ''}]]></name>`
  let itemList = ''
  presets.forEach(item => {
    itemList += `<item id="${item.index}"><name><![CDATA[${item.name}]]></name><speed>${item.speed}</speed><holdTime>${item.holdTime}</holdTime></item>`
  })
  result += `<presetInfo type="array">${itemList}</presetInfo>`
  result += '</cruiseInfo>'
  result += Back
  return result
}

// IPC-编辑巡航线
export const urlPtzModifyCruise = (id, name, presets) => {
  let result = OCX_XML_Header + Font
  result += '<cruiseInfo version="1.7" xmlns="http://www.ipc.com/ver10">'
  result += `<id>${id}</id>`
  result += `<name><![CDATA[${name || ''}]]></name>`
  let itemList = ''
  presets.forEach(item => {
    itemList += `<item id="${item.index}"><name><![CDATA[${item.name}]]></name><speed>${item.speed}</speed><holdTime>${item.holdTime}</holdTime></item>`
  })
  result += `<presetInfo type="array">${itemList}</presetInfo>`
  result += '</cruiseInfo>'
  result += Back
  return result
}

// IPC-获取预置点的时间范围
export const urlPtzGetCaps = () => {
  const result = OCX_XML_Header + OCX_XML_Config
  return result
}
