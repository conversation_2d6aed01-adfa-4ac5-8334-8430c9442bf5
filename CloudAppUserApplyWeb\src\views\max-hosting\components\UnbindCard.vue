<template>
  <div class="unbind-card-wrapper">
    <div class="unbind-card-content">
      <div class="unbind-card-head">
        <div class="unbind-card-text-wrapper">
          <div class="unbind-card-title">{{ $t('trusteeshipService') }}</div>
          <div class="unbind-card-text">{{ $t('trusteeshipText') }}</div>
        </div>
        <img src="@/assets/img/common/trusteeship/service_front.png" class="service-front" />
      </div>
      <div class="unbind-card-body">
        <div class="unbind-card-line">
          <img
            alt="operate"
            class="unbind-card-icon"
            :src="require('@/assets/img/common/trusteeship/operate_2x.png')"
          />
          <div class="trusteeship-line-text">{{ $t('operateText') }}</div>
        </div>
        <div class="unbind-card-line">
          <img alt="rocket" class="unbind-card-icon" :src="require('@/assets/img/common/trusteeship/rocket_2x.png')" />
          <div class="trusteeship-line-text">{{ $t('fastText') }}</div>
        </div>
        <div class="unbind-card-line">
          <img
            alt="protect"
            class="unbind-card-icon"
            :src="require('@/assets/img/common/trusteeship/protect_2x.png')"
          />
          <div class="trusteeship-line-text">{{ $t('protectText') }}</div>
        </div>
        <div class="unbind-card-btn">
          <van-button class="footer-btn" type="primary" @click="handleClick">
            {{ $t('immediateTrusteeship') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'UnBindCard',
  components: {},
  props: {},
  data() {
    return {}
  },
  methods: {
    handleClick() {
      this.$emit('startHost')
    }
  }
}
</script>
<style lang="scss" scoped>
.unbind-card-wrapper {
  width: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;

  .unbind-card-content {
    width: calc(100% - 16px);
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;
    box-shadow: 0 2px 4px 0 #0000000d;
    position: relative;
    background-color: var(--bg-color-white, #ffffff);
  }
  .unbind-card-head {
    // width: 359px;
    width: 100%;
    height: 156px;
    background-repeat: no-repeat;
    background-position-y: 0px;
    background-position-x: center;
    background-size: 100%;
    border-radius: 6px;
    position: relative;
    background: linear-gradient(180deg, #fff 30%, #e6effb 100%);
    box-shadow: 0 4px 4px 0 #0000000d;

    .unbind-card-text-wrapper {
      position: absolute;
      top: 50%;
      left: 5%;
      transform: translateY(-50%);
      z-index: 1;
      max-width: 48%;
    }
    .unbind-card-title {
      font-family: 'PingFang SC';
      font-size: var(--font-size-h4-size, 20px);
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
      margin-bottom: 2px;
    }
    .unbind-card-text {
      font-family: 'PingFang SC';
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .service-front {
      position: absolute;
      top: 17%;
      left: 53%;
      width: 144px;
      height: 86px;
      z-index: 0;
    }
  }

  .unbind-card-body {
    margin-top: 30px;
    width: 100%;
    border-radius: 0px 0px 10px 10px;
    .unbind-card-line {
      width: 100%;
      padding: 0px 10px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      margin-bottom: 18px;
      transform: translateY(-6px);
      .unbind-card-icon {
        width: 24px;
        height: 24px;
      }
    }
    .trusteeship-line-text {
      width: 100%;
      color: var(--icon-color-primary, #2b2b2b);
      font-family: 'PingFang SC';
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-left: 8px;
      /* overflow: hidden; */
      word-break: break-word;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    .unbind-card-btn {
      padding: 9px 0 24px 20px;
      .footer-btn {
        height: 32px;
        flex-shrink: 0;
        border: 1px solid var(--brand-bg-color-default, #3277fc);
        color: var(--brand-bg-color-default, #3277fc);
        border-radius: 23px;
        background: transparent;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        padding: 0 22px;
      }
    }
  }
}
</style>
