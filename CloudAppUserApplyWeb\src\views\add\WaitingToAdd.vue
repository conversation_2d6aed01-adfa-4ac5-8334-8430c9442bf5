<template>
  <div class="waiting-to-add">
    <div class="waiting-img">
      <van-image width="303px" height="120px" :src="require('@/assets/img/common/site/result_icon.png')" />
      <div class="adding-tips" v-if="addStatus === 1">
        {{ $t('waitingTips') }}
      </div>
      <div v-if="addStatus === 2 && successSnList?.length > 0">
        {{ $t('successAdd', { num: successSnList?.length }) }}
      </div>
      <div v-if="addStatus === 2 && deviceList.length - successSnList.length > 0">
        {{ $t('failAdd', { num: deviceList.length - successSnList.length }) }}
      </div>
    </div>
    <div class="card-ul" v-if="deviceList?.length > 0">
      <div class="card-li" v-for="(card, index) in deviceList" :key="index">
        <dev-card :hiddenHaddle="true" :devInfo="card" />
      </div>
    </div>
    <div class="bottom-btn-box" v-if="addStatus === 2">
      <div class="bottom-btn" @click="onFinish">{{ $t('finished') }}</div>
    </div>
  </div>
</template>

<script>
import DevCard from './components/DevCard.vue'
import { gotoPage, appRequestAddBindDevices } from '@/utils/appbridge'
import { mapMutations, mapState, mapActions } from 'vuex'
export default {
  name: 'waitingToAdd',
  components: { DevCard },
  data() {
    return {
      checkSnList: [],
      successSnList: [],
      checkFailSnList: [],
      deviceList: [],
      closeIcon: require('@/assets/img/common/close_icon.png'),
      timer: null,
      addStatus: 1, // 1-正在添加 2：流程结束
      timeNum: 0
    }
  },
  computed: {
    ...mapState('device', ['addDevInfo']),
    ...mapState('app', ['sid'])
  },
  mounted() {
    this.init()
  },
  methods: {
    ...mapMutations('device', ['SET_ADD_DEV_INFO']),
    ...mapActions('app', ['getToken']),
    init() {
      this.deviceList = this.addDevInfo.map(item => {
        return {
          ...item,
          bindStatus: 1 // 1 加载中 2成功  3 失败
        }
      })
      const params = this.deviceList.map(item => {
        return {
          plainSn: item.snPlain,
          code: item.code
        }
      })
      try {
        appRequestAddBindDevices(params, response => {
          const res = JSON.parse(response).body
          res?.forEach(resultItem => {
            this.deviceList.forEach((deviceItem, index) => {
              if (resultItem.plainSn === deviceItem.snPlain) {
                if (resultItem.errorCode === 200) {
                  this.deviceList[index].bindStatus = 2 // 绑定成功
                  this.successSnList.push(deviceItem.snPlain) // 成功列表
                } else {
                  if (resultItem.errorCode === 7063) {
                    const userName = resultItem?.extend?.nickname
                    this.$set(this.deviceList[index], 'userName', userName)
                  }
                  this.deviceList[index].bindStatus = 3
                  this.$set(this.deviceList[index], 'errorCode', resultItem?.errorCode)
                }
              }
            })
          })
          this.addStatus = 2
        })
      } catch {
        this.deviceList.forEach((deviceItem, index) => {
          this.deviceList[index].bindStatus = 3
          this.$set(this.deviceList[index], 'errorCode', '')
        })
        this.addStatus = 2
      }
    },
    onFinish() {
      gotoPage({
        pageRoute: 'device/list'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.waiting-to-add {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0 10px;
  .waiting-img {
    height: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 9px;
    .van-image {
      margin-bottom: 7px;
      margin-top: 72px;
    }
    .adding-tips {
      color: var(--text-color-placeholder, #a3a3a3);
      font-size: var(--font-size-body2-size, 14px);
      font-weight: 400;
      line-height: 26px;
    }
  }
}
.card-ul {
  height: calc(100% - 360px);
  width: 100%;
  overflow-y: auto;
  .card-li {
    margin-top: 8px;
  }
}
.bottom-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 60px;
  width: 100vw;
  background: var(--bg-color-white, #ffffff);
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom-btn {
  width: 327px;
  height: 40px;
  border-radius: 23px;
  background: var(--brand-bg-color-active, #1d71f3);
  line-height: 40px;
  text-align: center;
  color: var(--bg-color-white, #ffffff);
  font-size: var(--font-size-body1-size, 16px);
  font-weight: 500;
}
</style>
