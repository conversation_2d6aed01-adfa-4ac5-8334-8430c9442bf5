<template>
  <div class="choose-device-wrapper">
    <template v-if="type === 'detail'">
      <nav-bar @clickLeft="back"></nav-bar>
    </template>
    <template v-else>
      <tvt-multiple-nav
        :inSelected="inSelected"
        :value="isSelectAll"
        @clickLeft="back"
        @toSelect="toSelect"
        @changeValue="changeValue"
      />
    </template>
    <div :class="['choose-device-content', type === 'check' ? 'choose-device-body' : '']">
      <choose-channel
        ref="chooseChannelRef"
        :type="type"
        v-model="checkChannels"
        :deviceChannelList="deviceChannelList"
        :devOperationObj="devOperationObj"
        :devSupportFunObj="devSupportFunObj"
        @updateDevOperation="updateDevOperation"
        @select="handleSelect"
        @change="handleChange"
      />
    </div>
    <div class="footer" v-if="type === 'check'">
      <van-button class="footer-btn" type="primary" :disabled="btnDisabled" @click="handleConfirm">
        {{ $t('cancelShare') }}
      </van-button>
    </div>
    <!-- 取消分享确定弹窗 -->
    <van-popup v-model="showPopup" round position="bottom" :style="{ height: '254px' }">
      <popup-confirm
        :title="$t('cancelShare')"
        :text="$t('cancelShareDesc')"
        :cancelText="$t('notNow')"
        :canfirmText="$t('stillCancel')"
        @cancel="cancelShare"
        @confirm="confirmShare"
      />
    </van-popup>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import TvtMultipleNav from '@/components/TvtMultipleNav.vue'
import ChooseChannel from './components/ChooseChannel.vue'
import PopupConfirm from '@/components/PopupConfirm.vue'
import { CHANNEL_CAPABILITY_LIST, DEVICE_CAPABILITY_MAP } from '@/utils/options'
import { mapState, mapMutations } from 'vuex'
import { updateShareAuth, getDeviceDetail, deleteShareChannel } from '@/api/share'
import { appSetWebBackEnable, appLog } from '@/utils/appbridge'

export default {
  name: 'ShareDeteil',
  components: {
    NavBar,
    TvtMultipleNav,
    ChooseChannel,
    PopupConfirm
  },
  props: {},
  data() {
    return {
      type: 'edit', // 当前展示状态 check 通道勾选  edit 通道权限编辑
      allChannelCount: [], // 所有通道的数量
      checkChannels: [], // 选中的通道
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      channelSupportFunObj: {}, // 通道能力集
      channelObj: {},
      inSelected: false,
      isSelectAll: false, // 是否选择全部
      showPopup: false,
      deviceChannelList: [],
      devSupportFunObj: {} // 设备能力集
    }
  },
  async created() {
    appSetWebBackEnable(true)
    // 从路由中获取type,用作默认
    const query = this.$route.query
    if (query.type) {
      this.type = query.type
    }
    const { shareList = [] } = this.shareReocrd
    const snList = shareList.map(item => item.sn)
    await this.getAllChannelCapability(snList)
    // 通道按照chlIndex排序
    this.createChannelTree(shareList.sort((a, b) => a.chlIndex - b.chlIndex))
  },
  computed: {
    ...mapState('share', ['shareReocrd', 'devOperationObj', 'devList']),
    // 按钮是否可点击
    btnDisabled() {
      return this.checkChannels.length === 0
    }
  },
  methods: {
    ...mapMutations('share', ['SET_DEV_SUPPORT_FUN_OBJ', 'SET_DEV_OPERATION_OBJ_BY_SN']),
    back() {
      this.$router.go(-1)
    },
    // 获取所有设备的能力集
    async getAllChannelCapability(snList) {
      try {
        // 获取所有通道的详情
        const resArr = await Promise.all(snList.map(sn => getDeviceDetail({ sn, returnChl: true })))
        const capabilityObj = {}
        resArr.forEach(res => {
          const { data } = res
          const { chlInfos = [], devInfo = {} } = data

          this.devSupportFunObj[devInfo.sn] = devInfo?.capability?.supportFun || []

          chlInfos.forEach(chlItem => {
            const { sn, chlIndex, capability } = chlItem
            // // 找到能力集
            const supportFun = capability && capability.supportFun ? capability.supportFun : null
            if (supportFun) {
              capabilityObj[`${sn}~${chlIndex}`] = supportFun
            }
          })
        })
        this.channelSupportFunObj = capabilityObj
        this.SET_DEV_SUPPORT_FUN_OBJ(this.devSupportFunObj)
        // 根据结果构建
      } catch (err) {
        console.error(err)
      }
    },
    // 构建设备、通道的树形结构
    createChannelTree(allChannelList) {
      this.allChannelCount = allChannelList.length
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const deviceChannelList = []
      const deviceIndexObj = {} // 记录设备sn及其在通道树中的索引
      const channelObj = {} // 记录设备sn-通道chlIndex及其对应的通道信息
      allChannelList.forEach(item => {
        const { sn, devName, chlIndex, auth } = item
        // 过滤出通道支持的能力
        const supportFun = this.channelSupportFunObj[`${sn}~${chlIndex}`] || []
        const capabilityOptions = this.channelCapabilitys.filter(item => {
          if (item.filterAble) {
            const authArr = item.supportAuth ? [item.supportAuth] : item.value.split(',') // 针对现场和回放这种多合一权限的
            return authArr.every(authItem => supportFun.includes(authItem))
          }
          return true
        })
        let checkCapability = capabilityOptions.slice()
        // 过滤出通道勾选的能力
        if (auth) {
          const authList = JSON.parse(auth)
          checkCapability = checkCapability.filter(item => {
            const authArr = item.value.split(',') // 针对现场和回放这种多合一权限的
            return authArr.every(authItem => authList.includes(authItem))
          })
        }
        // 通道能力集选项及勾选的能力集
        item.capabilityOptions = capabilityOptions.slice()
        item.checkCapability = checkCapability
        channelObj[`${sn}~${chlIndex}`] = { ...item }
        let temp = null
        // 判断设备
        if (deviceIndexObj[sn] !== undefined) {
          // 说明设备存在
          const index = deviceIndexObj[sn]
          temp = deviceChannelList[index]
          temp.children = deviceChannelList[index].children || []
          temp.children.push({ ...item })
        } else {
          // 说明设备不存在，直接添加
          temp = { sn, deviceName: devName, children: [{ ...item }] }
          deviceIndexObj[sn] = deviceChannelList.length // 记录下站点的索引
          // 继续添加通道
          deviceChannelList.push(temp)
        }
      })
      this.channelObj = channelObj
      this.deviceChannelList = deviceChannelList
    },
    async updateDevOperation({ sn, operationChecked }) {
      try {
        const device = this.devList.find(item => item.sn === sn)

        if (device) {
          // 修改设备的操作权限
          const { orderId } = device
          const params = { shareId: orderId, auth: [] }

          if (operationChecked) {
            const devAuth = [
              DEVICE_CAPABILITY_MAP.CONFIG,
              DEVICE_CAPABILITY_MAP.TALK,
              DEVICE_CAPABILITY_MAP.ARM_BY_CLOUD
            ]

            params.auth = devAuth
          }

          await updateShareAuth(params)
          this.SET_DEV_OPERATION_OBJ_BY_SN({ sn, operationChecked })
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 编辑权限
    async handleChange(record, callback) {
      try {
        // 修改设备的权限
        const { id, checkCapability = [] } = record
        const auth = checkCapability.reduce((pre, next) => {
          return pre.concat(next.value.split(','))
        }, [])
        const params = { shareId: id, auth }
        await updateShareAuth(params)
        // 设置成功
        callback && callback('success')
        record.auth = auth.slice()
        this.$toastSuccess(this.$t('settingSuccess'))
      } catch (err) {
        callback && callback('error')
        console.error(err)
      }
    },
    toSelect() {
      this.inSelected = true
      this.type = 'check'
    },
    changeValue(value) {
      this.isSelectAll = value
      this.$refs.chooseChannelRef.selectAll(value)
    },
    selectAll(value) {
      this.isSelectAll = value
    },
    // 通道勾选变化监听
    handleSelect(val) {
      if (val.length && val.length === this.allChannelCount) {
        this.isSelectAll = true
      } else {
        this.isSelectAll = false
      }
    },
    // 弹窗取消按钮事件
    cancelShare() {
      this.inSelected = false
      this.type = 'edit'
      this.checkChannels = []
      this.isSelectAll = false
      this.$refs.chooseChannelRef.selectAll(false)
      this.showPopup = false
    },
    // 弹窗确定事件
    async confirmShare() {
      // console.log(`${new Date()} 取消分享`)
      appLog('log/info', `${new Date()} 取消分享`)
      try {
        this.showPopup = false
        this.$loading.show()
        const ids = this.checkChannels.map(item => item.id)
        const sns = this.checkChannels.map(item => item.sn)

        this.devList.forEach(item => {
          if (sns.includes(item.sn)) {
            ids.push(item.orderId)
          }
        })

        await deleteShareChannel({ ids })
        this.$loading.hide()
        this.$toastSuccess(this.$t('cancelShare'))

        this.cancelShare()
        // console.log(`${new Date()} 取消分享成功`)
        appLog('log/info', `${new Date()} 取消分享成功`)
        // 进入分享管理页面
        this.$utils.routerPush({
          path: '/share/shareManagment'
        })
      } catch (err) {
        this.showPopup = false
        this.$loading.hide()
        console.error(err)
      }
    },
    // 取消分享
    handleConfirm() {
      // 确认弹窗
      this.showPopup = true
    }
  }
}
</script>
<style lang="scss" scoped>
.choose-device-wrapper {
  height: 100%;
  overflow: hidden;
  .choose-device-content {
    width: 100%;
    height: calc(100% - 54px);
    overflow: auto;
    box-sizing: border-box;
  }
  .choose-device-body {
    height: calc(100% - 144px);
  }
}
</style>
