<template>
  <!-- 解除绑定确定弹窗 -->
  <van-popup
    v-model="showUnBind"
    round
    position="bottom"
    :style="{ height: isZh ? '224px' : '294px' }"
    class="unbind-dialog"
    @click-overlay="cancelUnBind"
  >
    <popup-confirm
      :title="$t('unBind')"
      :text="$t('unBindDesc')"
      :cancelText="$t('notNow')"
      :confirmText="$t('stillUnbind')"
      :overlay="false"
      @cancel="cancelUnBind"
      @confirm="confirmUnBind"
    />
  </van-popup>
</template>
<script>
import { postDataToH5, closeDialog } from '@/utils/appbridge'
import PopupConfirm from '@/components/PopupConfirm.vue'

export default {
  name: 'unBindPopup',
  components: {
    PopupConfirm
  },
  props: {},
  data() {
    return {
      showUnBind: true // 解除绑定
    }
  },
  created() {},
  mounted() {},
  computed: {
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    // 取消解除绑定
    cancelUnBind() {
      // 发送操作结果给托管管理页面
      postDataToH5({
        observerName: 'unBindPopup',
        data: { operate: 'cancel' }
      })
      closeDialog()
    },
    // 确定解除绑定
    confirmUnBind() {
      // 发送操作结果给托管管理页面
      postDataToH5({
        observerName: 'unBindPopup',
        data: { operate: 'confirm' }
      })
      closeDialog()
    }
  }
}
</script>
<style lang="scss" scoped>
.share-success-wrapper {
  background-color: transparent;
  height: 100%;
  overflow: hidden;
  .share-success-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 124px 36px 0px 36px;
    .share-success-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
    .share-success-text {
      width: 100%;
      margin: 16px 0px;
    }
    .share-success-desc {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
</style>
<style lang="scss">
html body #app:has(.app-container .unbind-dialog) {
  background-color: var(--bg-color-dialogs, #0000001a);
}
.app-container:has(.unbind-dialog) {
  background-color: transparent;
}
</style>
