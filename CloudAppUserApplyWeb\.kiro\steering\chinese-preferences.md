---
inclusion: always
---

# Chinese Localization & Cultural Preferences

## Language Standards
- Use Simplified Chinese as primary Chinese variant
- Follow GB/T 15834 standards for Chinese text processing
- Maintain consistent terminology across all Chinese translations
- Use proper Chinese punctuation (。，！？：；""''（）【】)

## UI/UX Conventions
- Text direction: Left-to-right (LTR) for Chinese content
- Font preferences: System fonts with fallbacks (PingFang SC, Microsoft YaHei, SimSun)
- Line height: 1.5-1.8 for optimal Chinese character readability
- Character spacing: Default spacing, avoid excessive letter-spacing

## Content Guidelines
- Use formal tone (正式语调) for system messages and documentation
- Prefer concise expressions over verbose descriptions
- Use Arabic numerals (1, 2, 3) instead of Chinese numerals for technical content
- Date format: YYYY年MM月DD日 or YYYY-MM-DD
- Time format: 24-hour format (HH:mm)

## Technical Considerations
- Ensure UTF-8 encoding for all Chinese text
- Test text overflow with longer Chinese translations
- Consider character width differences in responsive layouts
- Validate input methods compatibility (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.)

## Cultural Adaptations
- Color preferences: Red for success/positive actions, avoid green for warnings
- Icon usage: Prefer universally understood symbols over text-heavy icons
- Navigation patterns: Support both horizontal and vertical menu layouts
- Error messages: Provide constructive guidance rather than technical jargon

## Translation Quality
- Maintain context-aware translations for technical terms
- Use consistent terminology for alarm system components
- Provide tooltips or help text for complex technical concepts
- Regular review of translations with native speakers