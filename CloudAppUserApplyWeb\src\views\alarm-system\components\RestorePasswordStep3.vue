<template>
  <div class="restore-password-step">
    <div class="restore-password-content">
      <div class="restore-title">{{ $t('setNewPassword') }}</div>
      <div class="restore-subtitle">{{ $t('pleaseSetNewPassword') }}</div>

      <div class="restore-form">
        <common-input
          type="password"
          v-model="password"
          :placeholder="$t('newPassword')"
          :errorMessage="passwordError"
          borderType="full"
        />

        <div style="margin-top: 16px">
          <common-input
            type="password"
            v-model="confirmPassword"
            :placeholder="$t('confirmNewPassword')"
            :errorMessage="confirmPasswordError"
            borderType="full"
          />
        </div>
      </div>
    </div>

    <div class="restore-actions">
      <van-button class="action-btn ok-btn" type="cancel" @click="handleSubmit" :loading="loading">{{
        $t('submit')
      }}</van-button>
      <van-button class="action-btn cancel-btn" type="cancel" @click="handleCancel">{{ $t('cancel') }}</van-button>
    </div>
  </div>
</template>

<script>
import CommonInput from './CommonInput.vue'

export default {
  name: 'RestorePasswordStep3',
  components: {
    CommonInput
  },
  props: {
    email: {
      type: String,
      required: true
    },
    authCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      password: '',
      confirmPassword: '',
      passwordError: '',
      confirmPasswordError: '',
      loading: false
    }
  },
  watch: {
    password(newVal) {
      // 当输入内容时，清除错误提示
      if (newVal && this.passwordError === this.$t('enterNewPwd')) {
        this.passwordError = ''
      }
    },
    confirmPassword(newVal) {
      // 当输入内容时，清除错误提示
      if (newVal && this.confirmPasswordError === this.$t('enterConfirmPwd')) {
        this.confirmPasswordError = ''
      }
    }
  },
  methods: {
    async handleSubmit() {
      this.passwordError = ''
      this.confirmPasswordError = ''

      if (!this.password) {
        this.passwordError = this.$t('enterNewPwd')
        return
      }

      if (!this.confirmPassword) {
        this.confirmPasswordError = this.$t('enterConfirmPwd')
        return
      }

      if (this.password !== this.confirmPassword) {
        this.confirmPasswordError = this.$t('notMatchPsw')
        return
      }

      this.loading = true
      try {
        // 提交重置密码的请求
        this.$emit('submit', {
          email: this.email,
          authCode: this.authCode,
          newPassword: this.password
        })
        this.clearForm()
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleCancel() {
      this.clearForm()
      this.$emit('cancel')
    },
    // 清除表单数据和错误信息
    clearForm() {
      this.password = ''
      this.confirmPassword = ''
      this.passwordError = ''
      this.confirmPasswordError = ''
      this.loading = false
    }
  }
}
</script>
