.trusteeship-my-installer {
  position: relative;
  height: 100%;
  overflow: auto;
  .tvt-better-scroll {
    height: calc(100% - 400px);
  }
  .company-information {
    .container {
      width: calc(100% - 32px);
      margin: 20px auto 0;
      box-sizing: border-box;
      border-radius: 10px;
      background-color:$UI1C-light-background-color;
      .right {
        color: $UI1C-50-white-color;
        .txt-flex {
          display: flex;
          padding-bottom: 6px;
          img {
            width: 19px;
            height: 19px;
            padding-right: 7px;
          }
        }
        .name {
          margin-top: 4px;
          font-size: 16px;
          line-height: 24px;
          font-weight: 500;
          word-break: break-all;
          color:$UI1C-white-color;
        }
        .text {
          line-height: 19px;
          font-size: 14px;
          color:$UI1C-50-white-color;
          word-break: break-all;
        }
      }
    }
  }
  .device-trusteeship-title {
    color: $UI1C-font-color;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    padding-top: 10px;
    font-weight: 700;
  }
  .device-trusteeship {
    .container-ul {
      background-color:$UI1C-light-background-color;
      padding: 2px 0px;
      .container-li {
        border-bottom: 1px solid $UI1C-light-gray-color;
        padding: 8px 15px;
        &:last-child {
          border: 0;
        }
        .li-item {
          margin: 6px 0;
          font-size: 14px;
          font-weight: 500;
          color: $UI1C-50-white-color;
        }
        .device {
          display: flex;
          justify-content: space-between;
          .status {
            width: 100px;
            text-align: right;
          }
          .name {
            flex: 1;
            font-size: 16px;
            color: $UI1C-white-color;
          }
        }
      }
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      img {
        width: 235px;
        height: 211px;
      }
    }
  }
}