/**
 * 认证信息localStorage缓存工具
 * 主要用于开发环境提升开发体验，避免频繁重新登录
 */

const STORAGE_KEYS = {
  RISCO_LOGIN: 'risco_login_cache',
  SITE_LOGIN: 'site_login_cache'
}

// 开发环境缓存7天，生产环境缓存1小时
const CACHE_DURATION = process.env.NODE_ENV === 'development' ? 7 * 24 * 60 * 60 * 1000 : 60 * 60 * 1000

/**
 * 保存认证信息到localStorage
 * @param {string} key - 存储键名
 * @param {Object} data - 要存储的数据
 */
export const saveAuthToStorage = (key, data) => {
  try {
    const cacheData = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + CACHE_DURATION
    }
    localStorage.setItem(key, JSON.stringify(cacheData))
  } catch (error) {
    console.warn('Failed to save auth data to localStorage:', error)
  }
}

/**
 * 从localStorage获取认证信息
 * @param {string} key - 存储键名
 * @returns {Object|null} 认证数据或null
 */
export const getAuthFromStorage = key => {
  try {
    const cached = localStorage.getItem(key)
    if (!cached) return null

    const cacheData = JSON.parse(cached)
    const now = Date.now()

    // 检查是否过期
    if (now > cacheData.expiresAt) {
      localStorage.removeItem(key)
      return null
    }

    return cacheData.data
  } catch (error) {
    console.warn('Failed to get auth data from localStorage:', error)
    return null
  }
}

/**
 * 清除localStorage中的认证信息
 * @param {string} key - 存储键名
 */
export const clearAuthFromStorage = key => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.warn('Failed to clear auth data from localStorage:', error)
  }
}

/**
 * 保存Risco登录信息到localStorage
 * @param {Object} riscoLoginInfo - Risco登录信息
 */
export const saveRiscoLoginToStorage = riscoLoginInfo => {
  saveAuthToStorage(STORAGE_KEYS.RISCO_LOGIN, riscoLoginInfo)
}

/**
 * 从localStorage获取Risco登录信息
 * @returns {Object|null} Risco登录信息或null
 */
export const getRiscoLoginFromStorage = () => {
  return getAuthFromStorage(STORAGE_KEYS.RISCO_LOGIN)
}

/**
 * 保存站点登录信息到localStorage
 * @param {Object} siteLoginInfo - 站点登录信息
 */
export const saveSiteLoginToStorage = siteLoginInfo => {
  saveAuthToStorage(STORAGE_KEYS.SITE_LOGIN, siteLoginInfo)
}

/**
 * 从localStorage获取站点登录信息
 * @returns {Object|null} 站点登录信息或null
 */
export const getSiteLoginFromStorage = () => {
  return getAuthFromStorage(STORAGE_KEYS.SITE_LOGIN)
}

/**
 * 清除所有认证缓存
 */
export const clearAllAuthStorage = () => {
  clearAuthFromStorage(STORAGE_KEYS.RISCO_LOGIN)
  clearAuthFromStorage(STORAGE_KEYS.SITE_LOGIN)
}

// 导出存储键名常量供其他模块使用
export { STORAGE_KEYS }
