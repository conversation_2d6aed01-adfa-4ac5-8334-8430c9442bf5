/**
 * 生成basic数据
 * @param ver     {string} 必填    -- 版本号
 * @param time    {number} 必填    -- 时间戳
 * @param id      {string} 必填    -- 0
 * @param nonce   {string} 必填    -- 32位随机数
 * @param token   {string} 非必填  -- 登陆成功后必填，从后台获取，在localStorage中存取
 * @param sign    {string} 非必填  -- 签名，从后台获取，在localStorage中存取
 */
import { appReqeustNative } from '@/utils/appbridge'
import { isPhone } from '@/utils/common.js'
import store from '@/store'

// 从app获取token
export function getToken() {
  let req = {
    url: 'USER_TOKEN_INFO',
    params: '',
    timeout: 3000
  }
  return new Promise(resolve => {
    appReqeustNative(req, function (res) {
      // console.log('USER_TOKEN_INFO', res)
      resolve(res)
    })
  })
}

// 错误码通知app退出登录--加上频控，确保多个接口同时调用时只触发一次(5s内)
let tokenErrLogout = false
export function noticeAppLogout(code) {
  if (tokenErrLogout) return
  tokenErrLogout = true
  setTimeout(() => {
    tokenErrLogout = false
  }, 5000)
  let req = {
    url: 'APP_LOGOUT',
    params: {
      code: code.toString()
    },
    timeout: 3000
  }
  // console.log(req, '通知app退出登录')
  appReqeustNative(req, function (res) {
    console.log(res, 'app退出登录')
  })
}

export const getBasic = ({
  ver = '1.0',
  time = new Date().getTime(),
  id = 2,
  nonce = Math.floor(Math.random() * 2147483646 + 1),
  token = isPhone() ? store.state.app.token : localStorage.getItem('token')
} = {}) => {
  return { ver, time, id, nonce, token }
}
