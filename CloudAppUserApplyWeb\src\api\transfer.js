import request from '@/api/request'
import baseUrl from '@/api/index.js'

const baseUrl2 = '/mobile_v1.0'

// 转移请求的接口
// superlive  vms 查询转移请求列表
export const transferRequestList = data => request.post(`${baseUrl}/devices/list-transfer`, data)

// superLivePlus 处理转移通道
export const transferHandle = data => request.post(`${baseUrl}/devices/confirm-transfer`, data)

// superLivePlus 查询是否绑定过安装商
export const isPartner = data => request.post(`${baseUrl}/user/installer/info`, data)

// superLivePlus 绑定安装商
export const bindPartner = data => request.post(`${baseUrl}/installer/bind`, data)

// superliveMax 查询转移记录详情
export const transferDetail = data => request.post(`${baseUrl}/devices/transfer-detail`, data)

// 查询站点转移详情
export const getTransferInfo = data => request.post(`${baseUrl2}/user/site/get-transfer-info`, data)

// 确认站点转移
export const confirmTransfer = data => request.post(`${baseUrl2}/user/site/confirm-transfer`, data)

// 查询站点托管详情
export const getTrusteeshipInfo = data => request.post(`${baseUrl2}/user/trust-ticket/get`, data)

// 处理站点托管
export const dealTrusteeshipApply = data => request.post(`${baseUrl2}/user/trust/handle`, data)

// 查询分享交付详情
export const getDeviceShareInfo = data => request.post(`${baseUrl2}/user/site/get-share-info`, data)

// 处理分享交付
export const handleDeviceShareInfo = data => request.post(`${baseUrl2}/user/site/confirm-share`, data)

// 查询移动交付详情
export const getDeviceChangeSiteInfo = data => request.post(`${baseUrl2}/user/move-ticket/get`, data)

// 处理移动交付
export const handleDeviceChangeSiteInfo = data => request.post(`${baseUrl2}/user/device-move/handle`, data)
