<template>
  <div class="user-feedback">
    <nav-bar @clickLeft="back"></nav-bar>
    <stars-choose @change="starsChange" class="choose"></stars-choose>
    <div class="description">
      <van-field
        ref="descriptionField"
        v-model="description"
        rows="5"
        autosize
        type="textarea"
        maxlength="200"
        :placeholder="$t('feedBackPlaceholder1')"
        show-word-limit
      />
    </div>
    <van-button :class="['footer-btn', score && description ? '' : 'button-disabled']" @click="confirm" type="primary">
      {{ $t('confirm') }}
    </van-button>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import NavBar from '@/components/NavBar'
import { appClose, showToast } from '@/utils/appbridge'
import StarsChoose from '@/views/user-feedback/StarsChoose'
import { feedbackAdd } from '@/api/feedback.js'

export default {
  name: 'UserFeedback',
  components: {
    NavBar,
    StarsChoose
  },
  props: {},
  data() {
    return {
      score: 0, // 1-5才允许提交
      description: ''
    }
  },
  mounted() {
    // 确保在 DOM 更新后执行 自动聚焦
    this.$nextTick(() => {
      if (this.$refs.descriptionField) {
        this.$refs.descriptionField.focus()
      }
    })
  },
  computed: {
    ...mapState('app', ['style', 'appType', 'version', 'appId']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    back() {
      appClose()
    },
    // 选中的星星数
    starsChange(data) {
      this.score = data
    },
    async confirm() {
      if (this.score === 0) return
      if (!this.description) return
      // 调用接口
      this.$loading.show()
      let params = {
        score: this.score,
        description: this.description,
        appId: this.appId,
        version: this.version
      }
      try {
        let res = await feedbackAdd(params)
        this.$loading.hide()
        if (res.basic.code === 200) {
          showToast({
            msg: this.$t('feedBackSuccess'),
            style: 0
          })
          this.back()
        }
      } catch (err) {
        this.$loading.hide()
        console.error(err)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.user-feedback {
  width: 100%;
  // height: 100%;
  background-color: var(--bg-color-white, #ffffff);
  .choose {
    margin-top: 50px;
    margin-bottom: 20px;
    ::v-deep .stars-note {
      margin: 20px 16px 24px 16px;
    }
  }
  .description {
    margin: 10px 16px;
    ::v-deep .van-field__control {
      max-height: 190px; /* 设置最大高度 */
      padding-left: 2px;
      padding-right: 2px;
    }
  }
  .van-cell {
    border-radius: 6px;
  }
  ::v-deep .van-field__word-limit {
    color: var(--text-color-placeholder, #a3a3a3);
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 20px auto 0;
    color: #ffffffe6;
  }
  ::v-deep .van-button--primary {
    border: 0;
  }
}
</style>
<style lang="scss">
.app-container:has(.user-feedback) {
  background-color: var(--bg-color-white, #ffffff) !important ;
}
</style>
