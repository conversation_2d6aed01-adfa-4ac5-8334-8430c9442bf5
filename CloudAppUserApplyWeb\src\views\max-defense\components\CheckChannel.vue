<template>
  <div class="channel-list-content">
    <div class="channel-list">
      <!-- 通道勾选 onlineStatus 0 禁用 1 在线 2 不在线 99 未知-->
      <van-checkbox-group v-model="checkResult" @change="handleChange">
        <van-checkbox
          v-for="(item, index) of curChannelList"
          :key="`${item.sn}~${item.chlIndex}~${item.chlId}~${index}`"
          class="channel-cell-wrapper"
          :disabled="item.onlineStatus !== 1"
          :name="`${item.sn}~${item.chlIndex}~${item.chlId}`"
          :ref="`${item.sn}~${item.chlIndex}~${item.chlId}~${index}`"
        >
          <div :class="['channel-checkbox-content', item.isDisabled ? 'channel-checkbox-content-disabled' : '']">
            <div :class="['channel-checkbox-title', item.groupName ? 'channel-checkbox-title2' : '']">
              <div class="channel-ellipsis-text">
                {{ item.name }}
              </div>
              <div class="channel-ellipsis-text channel-desc-text">
                {{ item.devDesc }}
              </div>
            </div>
            <div class="channel-checkbox-desc channel-ellipsis-text" v-if="item.groupName">
              {{ $t('addedTo', [`"${item.groupName}"`]) }}
            </div>
          </div>
        </van-checkbox>
      </van-checkbox-group>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: 'AddChannel',
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pullingStatus: 0,
      checkResult: [], // 通道勾选
      curChannelList: [] // 当前通道列表
    }
  },
  created() {},
  mounted() {
    // console.log('allChannelList', this.allChannelList)
    this.refreshChannel()
  },
  computed: {
    ...mapState('maxDefense', ['allChannelList', 'groupChannelList'])
  },
  watch: {
    value: {
      handler(val) {
        if (JSON.stringify(val) !== JSON.stringify(this.checkResult)) {
          // 值不一样则更新
          this.$nextTick(() => {
            this.checkResult = val ? (Array.isArray(val) ? val.slice() : val) : null
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getChannelList']),
    ...mapMutations('maxDefense', [
      'SET_ALL_CHANNEL_LIST',
      'SET_SITE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_ADD_CHANNEL_LIST',
      'SET_DEFENSE_RECORD'
    ]),
    refreshChannel() {
      // const { id } = this.defenseRecord
      console.log('allChannelList', this.allChannelList)
      const curChannelList = []
      this.allChannelList.map(item => {
        // 判断是否被其他分组添加
        const groupRecord =
          this.groupChannelList.find(
            item2 => item2.sn === item.sn && item2.chlIndex === item.chlIndex && item2.chlId === item.chlId
          ) || {}
        const { groupName } = groupRecord
        const isDisabled = item.onlineStatus !== 1 // 是否禁用
        curChannelList.push({
          ...item,
          groupName,
          isDisabled
        })
        this.curChannelList = curChannelList
      })
    },
    handleChange() {
      // // 通知外部勾选发生变化
      this.$emit('change', this.checkResult.slice())
    }
  }
}
</script>

<style lang="scss" scoped>
.van-hairline--top-bottom::after {
  border-width: 0px;
}
// .van-hairline-unset--top-bottom::after {
//   border-width: 0px;
// }
.channel-list {
  .van-cell {
    padding: 0px;
  }
}
.channel-list-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  .channel-list {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
}
</style>
<style lang="scss">
.van-collapse-item__content {
  padding: 2px 0px !important;
}
.channel-cell-item {
  .van-cell__title {
    width: 100%;
  }
}
.channel-cell-wrapper {
  width: 100%;
  height: 52px;
  padding: 15px 0px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  overflow: hidden;
  .channel-cell-item {
    width: 100%;
  }
  .van-checkbox__label {
    width: calc(100% - 28px);
    display: flex;
    align-items: center;
  }
  .channel-checkbox-content {
    flex: 1;
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
  }
  .channel-checkbox-title {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    overflow: hidden;
  }
  .channel-checkbox-title2 {
    max-width: calc(100% - 120px);
  }
  .channel-checkbox-desc {
    max-width: 120px;
  }
  .channel-desc-text {
    font-size: var(--font-size-text-size, 12px);
  }
  .channel-ellipsis-text {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  .van-checkbox {
    margin-right: 10px;
  }
}
</style>
