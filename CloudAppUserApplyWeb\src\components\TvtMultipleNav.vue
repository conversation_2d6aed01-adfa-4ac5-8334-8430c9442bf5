<template>
  <div class="tvt-nav-bar">
    <div class="tvt-nav-bar-left">
      <van-icon name="arrow-left" @click="$emit('clickLeft')" />
    </div>
    <div class="tvt-nav-bar-center">
      <span class="title">{{ title || $t($route.meta.title) }}</span>
    </div>
    <div class="tvt-nav-bar-right">
      <select-circle v-if="inSelected" :value="value" @changeValue="changeValue" />
      <theme-image v-else class="list-check-icon" alt="list-check" imageName="list_check.png" @click="toSelect" />
    </div>
  </div>
</template>

<script>
import SelectCircle from './SelectCircle.vue'
import ThemeImage from '@/components/ThemeImage.vue'
import { mapState } from 'vuex'

export default {
  name: 'tvtNavBar',
  components: {
    SelectCircle,
    ThemeImage
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    value: {
      type: Boolean,
      require: true
    },
    inSelected: {
      type: Boolean,
      require: true
    }
  },
  data() {
    return {
      name: ''
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    changeValue(value) {
      this.$emit('changeValue', value)
    },
    toSelect() {
      this.$emit('toSelect')
    }
  }
}
</script>

<style lang="scss" scoped>
.tvt-nav-bar {
  position: sticky;
  top: 0;
  z-index: 20;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 12px;
  box-sizing: border-box;
  .tvt-nav-bar-left {
    display: flex;
    align-items: center;
    margin-right: 12px;
  }
  .tvt-nav-bar-center {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: bold;
    .title {
      line-height: 44px;
    }
  }
  .tvt-nav-bar-right {
    display: inline-block;
    height: 44px;
    display: flex;
    align-items: center;
    .list-check-icon {
      width: 24px;
      height: 24px;
    }
    .icon-select {
      font-size: var(--font-size-h3-size, 24px);
      &.icon-select--selected {
        color: var(--brand-bg-color-default, #3277fc);
      }
    }
  }
}
</style>
