@import './navBar.scss';
@import './share/index.scss';
@import './trusteeship/index.scss';
@import './upgrade.scss';
@import './ipc-upgrade/index.scss';
@import './checkUserPwd.scss';
@import './max-defense/index.scss';
@import './transfer/index.scss';
@import './user-feedback/index.scss';

html,
body,
#app {
  background-color: $vms-background;
  color: $vms-black;
}

.app-container {
  background-color: $max-background;
}

// 根据主题 全局 修改vant样式  VMS 
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $max-primary;
}

.van-button--primary {
  color: $vms-white;
  background-color: $max-primary;
  border: 1px solid $max-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $black-color;
}

.van-dialog__cancel{
  color:$vms-main-black;
}

.van-dialog__message{
  color:$vms-light-black;
  font-size: 14px;
}

// superlive max 自定义toast样式
.max-custom-toast {
  display: inline;
  min-height: 30px;
  padding: 8px 10px;
  width: auto;
  min-width: 88px;
  box-sizing: border-box;
  .van-toast__text {
    display: inline-block;
    width: 100%;
    text-align: left;
    margin-top: 0px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
  .van-toast__text::before {
    content: "";
    display: inline-block;
    width: 30px;
  }
}
.max-toast-icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  position: absolute;
  top: 8px;
  & img {
    width: 24px;
    height: 24px;
  }
}

// superlive max 自定义toast样式 -- 正常的toast样式加宽
.max-wide-custom-toast {
  min-width: 200px;
}

// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  // padding: 10px 0px 6px 0px; // 统一调整
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: $vms-white;
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 327px;
    height: 40px;
    background: $max-primary;
    border-radius: 23px;
    text-align: center;
    color: $vms-white;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $max-primary;
}

// Tab背景色
.van-tabs__nav {
  background-color: $vms-white;
}
.van-tab {
  color: $gray-color;
}
.van-tab--active {
  color: $max-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $max-primary;
  border-color: $max-primary;
}

// switch背景色
.van-switch--on {
  background-color: $max-primary;
}
