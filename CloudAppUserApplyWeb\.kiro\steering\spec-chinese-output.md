---
inclusion: always
---

# Spec文档中文输出规范

## 核心要求
当生成Spec相关文档（requirements.md、design.md、tasks.md）时，必须使用中文输出。

## 文档语言规范

### Requirements.md（需求文档）
- **文档标题**：使用中文，如"需求文档"、"功能需求规格说明"
- **章节标题**：使用中文，如"项目概述"、"功能需求"、"验收标准"
- **用户故事**：使用中文格式："作为[角色]，我希望[功能]，以便[价值]"
- **验收标准**：使用中文EARS格式："当[事件]时，系统应该[响应]"

### Design.md（设计文档）
- **文档标题**：使用中文，如"设计文档"、"系统设计说明"
- **章节标题**：使用中文，如"系统概述"、"架构设计"、"组件设计"、"数据模型"、"错误处理"、"测试策略"
- **技术描述**：使用中文描述，技术术语可保留英文但需要中文解释

### Tasks.md（任务文档）
- **文档标题**：使用中文，如"实施计划"、"开发任务清单"
- **任务描述**：使用中文描述每个开发任务
- **任务细节**：使用中文说明实施要点和注意事项
- **需求引用**：使用中文格式，如"_需求: 1.1_"

## 中文技术写作规范

### 术语处理
- **保留英文的情况**：API名称、代码关键字、框架名称（如Vue.js、React）
- **中英文混合**：技术术语首次出现时使用"中文(English)"格式
- **统一术语**：建立项目术语词汇表，保持一致性

### 格式规范
- **标点符号**：使用中文标点符号（。，！？：；""''（）【】）
- **数字格式**：技术文档中使用阿拉伯数字（1、2、3）
- **日期时间**：使用YYYY年MM月DD日格式
- **列表格式**：使用中文序号或符号

### 语言风格
- **正式语调**：使用正式的技术文档语调
- **简洁明确**：避免冗长的描述，追求简洁明确
- **逻辑清晰**：按照中文的逻辑习惯组织内容
- **专业术语**：使用准确的技术术语，避免口语化表达

## 示例模板

### 需求文档示例
```markdown
# 需求文档

## 项目概述
[项目的中文描述]

## 功能需求

### 需求1：用户认证功能
**用户故事**：作为系统用户，我希望能够安全登录系统，以便访问个人数据。

#### 验收标准
1. 当用户输入正确的用户名和密码时，系统应该允许用户登录
2. 当用户输入错误的凭据时，系统应该显示错误提示信息
```

### 设计文档示例
```markdown
# 设计文档

## 系统概述
[系统的中文概述]

## 架构设计
[架构的中文描述]

## 组件设计
[组件的中文设计说明]
```

### 任务文档示例
```markdown
# 实施计划

- [ ] 1. 搭建项目基础架构
  - 创建Vue.js项目结构
  - 配置开发环境和构建工具
  - _需求: 1.1_

- [ ] 2. 实现用户认证模块
  - 开发登录组件
  - 集成身份验证API
  - _需求: 2.1, 2.2_
```

## 质量检查清单
- [ ] 所有标题和章节名称使用中文
- [ ] 用户故事使用中文格式
- [ ] 技术描述使用中文，专业术语适当保留英文
- [ ] 使用正确的中文标点符号
- [ ] 保持术语的一致性
- [ ] 语言风格正式且专业
- [ ] 逻辑结构清晰易懂

## 注意事项
- 在生成任何Spec文档时，优先考虑中文用户的阅读习惯
- 保持技术准确性的同时，确保中文表达的自然流畅
- 对于复杂的技术概念，提供必要的中文解释
- 遵循中文技术文档的写作规范和行业标准