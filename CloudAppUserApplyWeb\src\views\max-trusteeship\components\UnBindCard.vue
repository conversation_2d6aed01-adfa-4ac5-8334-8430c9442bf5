<template>
  <div class="unbind-card-wrapper">
    <div class="unbind-card-content">
      <div class="unbind-card-head">
        <div class="unbind-card-title">{{ $t('trusteeshipService') }}</div>
        <div class="unbind-card-text">{{ $t('trusteeshipText') }}</div>
      </div>
      <div class="unbind-card-body">
        <div class="unbind-card-line">
          <img alt="operate" :src="require('@/assets/img/common/trusteeship/operate.png')" />
          <div class="trusteeship-line-text">{{ $t('operateText') }}</div>
        </div>
        <div class="unbind-card-line">
          <img alt="operate" :src="require('@/assets/img/common/trusteeship/rocket.png')" />
          <div class="trusteeship-line-text">{{ $t('fastText') }}</div>
        </div>
        <div class="unbind-card-line">
          <img alt="operate" :src="require('@/assets/img/common/trusteeship/protect.png')" />
          <div class="trusteeship-line-text">{{ $t('protectText') }}</div>
        </div>
        <div class="unbind-card-btn">
          <van-button class="footer-btn" type="primary" @click="handleClick">
            {{ $t('immediateTrusteeship') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'UnBindCard',
  components: {},
  props: {},
  data() {
    return {}
  },
  methods: {
    handleClick() {
      this.$emit('click')
    }
  }
}
</script>
<style lang="scss" scoped>
.unbind-card-wrapper {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;

  .unbind-card-content {
    width: calc(100% - 16px);
    max-width: 359px;
    // width: 359px;
    height: 300px;
    border-radius: 10px;
    box-shadow: 0 2px 4px 0 #00000040;
    background-color: var(--bg-color-white, #ffffff);
  }
  .unbind-card-head {
    // width: 359px;
    width: 100%;
    height: 137px;
    background-image: url('@/assets/img/common/trusteeship/unbind_bg.png');
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
    background-size: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .unbind-card-title {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-h4-size, 20px);
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
    }
    .unbind-card-text {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .unbind-card-tag {
      height: 18px;
      flex-shrink: 0;
      border-radius: 10px;
      margin-top: 4px;
      padding: 0px 6px;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
  }
  .unbind-card-body {
    // width: 359px;
    width: 100%;
    border-radius: 0px 0px 10px 10px;
    background-color: var(--bg-color-white, #ffffff);
    .unbind-card-line {
      width: 100%;
      height: 24px;
      padding: 0px 30px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 6px;
      transform: translateY(-6px);
    }
    .trusteeship-line-text {
      width: 100%;
      // overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 12px;
      margin-left: 8px;
    }
    .unbind-card-btn {
      padding-top: 9px;
      padding-bottom: 24px;
      text-align: center;
      .footer-btn {
        width: 327px;
        height: 40px;
        border-radius: 23px;
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
