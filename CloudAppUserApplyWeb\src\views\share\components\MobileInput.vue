<template>
  <div>
    <van-field
      class="input-item"
      v-model.trim="mobile"
      type="tel"
      :maxlength="countryLocale === 'CN' ? 11 : 20"
      :placeholder="$t('enterMemberMobile')"
      @input="handleInput"
      @blur="e => validateMobile()"
    >
      <template #label>
        <div class="country-select" @click="openOptions">
          <!-- <span class="country-locale">
            {{ countryLocale }}
          </span> -->
          <span class="country-code"> +{{ countryCode }} </span>
          <van-icon name="arrow-down" class="select-icon" />
        </div>
      </template>
      <template #right-icon>
        <span class="cruise-line-close">
          <theme-image
            alt="clear"
            imageName="input_close.png"
            v-if="mobile"
            @click="clearMobile"
            style="width: 20px; height: 20px; margin-right: 14px"
          />
          <theme-image alt="scan" imageName="scan_code.png" @click="handleScan" />
        </span>
      </template>
    </van-field>
    <span class="error-msg" v-show="errorMsg"> {{ errorMsg }} </span>
    <van-popup v-model="showCountryOptions" position="bottom">
      <van-picker
        ref="optionRef"
        :confirm-button-text="$t('confirm')"
        :cancel-button-text="$t('cancel')"
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showCountryOptions = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { validateCommonPhone } from '@/utils/validate'
import { MOBILE_COUNTRY_CODE } from 'tvtcloudcountrycode'
import ThemeImage from '@/components/ThemeImage.vue'
import { appSelectCountry } from '@/utils/appbridge'

export default {
  name: 'MobileInput',
  components: {
    ThemeImage
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      mobile: '',
      countryLocale: '',
      countryCode: '',
      showCountryOptions: false,
      errorMsg: ''
    }
  },
  computed: {
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    },
    columns() {
      const sorted = this.isZh
        ? MOBILE_COUNTRY_CODE.sort((a, b) => a.zh.localeCompare(b.zh))
        : MOBILE_COUNTRY_CODE.sort((a, b) => (a.en < b.en ? -1 : 1))
      return sorted.map(item => {
        if (this.isZh) {
          return item.zh
        }
        return item.en
      })
    },
    countryMobile() {
      return `${this.countryCode}+${this.mobile}`
    }
  },
  watch: {
    countryCode() {
      this.$emit('input', this.countryMobile)
    },
    mobile() {
      this.$emit('input', this.countryMobile)
    },
    value: {
      handler(val) {
        if (val && this.countryMobile !== val) {
          this.resolveValue()
        }
      },
      immediate: true
    }
  },
  created() {
    if (!this.value) {
      // value未赋值时根据语言初始化国家区号
      this.resolveValue()
      this.initCountry()
    }
  },
  methods: {
    resolveValue() {
      if (this.value) {
        if (this.value.indexOf('+') === -1) {
          this.mobile = this.value
        } else {
          const [code, number] = this.value.split('+')
          const country = MOBILE_COUNTRY_CODE.find(country => country.code === Number(code))
          this.mobile = number
          if (country) {
            this.countryCode = code
            this.countryLocale = country.locale
          }
        }
      }
    },
    validateMobile(validateEmpty = false) {
      if (this.mobile === '') {
        if (validateEmpty) {
          this.errorMsg = this.$t('mobileNotEmpty')
          return false
        }
        this.errorMsg = ''
        return true
      }
      if (!validateCommonPhone(this.mobile, this.countryLocale)) {
        this.errorMsg = this.$t('mobileError')
        return false
      }
      this.errorMsg = ''
      return true
    },
    handleInput() {
      this.mobile = this.mobile.replace(/[^\d]/gi, '')
    },
    initCountry() {
      const language = window.navigator.language
      const [, locale] = language.split('-')
      // lang = lang ? lang.toLowerCase() : 'zh'
      console.log('locale', locale)
      let index = MOBILE_COUNTRY_CODE.findIndex(v => v.locale === locale)
      console.log('index', index, 'MOBILE_COUNTRY_CODE', MOBILE_COUNTRY_CODE)
      if (index < 0) {
        index = MOBILE_COUNTRY_CODE.findIndex(v => v.locale === 'CN')
      }
      this.setCountry(index)
    },
    clearMobile() {
      this.mobile = ''
      this.errorMsg = ''
    },
    onConfirm(value, index) {
      this.setCountry(index)
      this.showCountryOptions = false
      this.validateMobile()
    },
    setCountry(index) {
      this.countryLocale = MOBILE_COUNTRY_CODE[index].locale
      this.countryCode = MOBILE_COUNTRY_CODE[index].code
    },
    openOptions() {
      // 打开下拉选项--废弃
      // const country = MOBILE_COUNTRY_CODE.find(
      //   item => Number(item.code) === Number(this.countryCode) && item.locale === this.countryLocale
      // )
      // this.showCountryOptions = true
      // if (country) {
      //   this.$nextTick(() => {
      //     this.$refs.optionRef.setColumnValue(0, this.isZh ? country.zh : country.en)
      //   })
      // }
      // 打开APP的区号选择页面
      // 优化 JSON 参数构建
      const countryData = {
        code: this.countryCode,
        locale: this.countryLocale
      }
      const params = JSON.stringify(countryData)
      appSelectCountry(params, res => {
        if (res) {
          try {
            const parsedRes = JSON.parse(res)
            const body = parsedRes?.body ? JSON.parse(parsedRes.body) : null
            if (body) {
              const { code, locale } = body
              this.countryCode = code
              this.countryLocale = locale
            }
          } catch (error) {
            console.error('appSelectCountry error:', error)
          }
        }
      })
    },
    handleScan() {
      this.$emit('handleScan')
    }
  }
}
</script>
<style lang="scss" scoped>
.input-item {
  width: 100%;
  height: 52px;
  line-height: 52px;
  padding: 0px;
  font-size: var(--font-size-body2-size, 14px);
  margin: auto;
  &::before {
    content: '';
    position: absolute;
    box-sizing: border-box;
    width: 100%;
    pointer-events: none;
    left: 0px;
    bottom: 0px;
    border-bottom: 1px solid var(--outline-color-primary, #ebedf0);
    transform: scaleY(0.5);
  }
  &::v-deep .van-field__label {
    width: unset;
  }
  .cruise-line-close {
    display: flex;
    align-items: center;
    .theme-image-container {
      width: 24px;
      height: 24px;
    }
    img {
      width: 24px;
      height: 24px;
    }
  }
}
.error-msg {
  display: block;
  width: 100%;
  padding-top: 10px 0px 20px 0px;
  margin: auto;
  font-size: var(--font-size-text-size, 12px);
  font-weight: 400;
  color: var(--error-bg-color-default, #ff0000);
}
.country-locale {
  display: inline-block;
}
.select-icon {
  margin: 0 6px 0 12px;
}
.country-code {
  display: inline-block;
  color: var(--text-color-disabled, #d1d1d1);
}
</style>
