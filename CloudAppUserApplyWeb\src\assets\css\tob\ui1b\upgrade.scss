.upgrade-list {
  ::v-deep.van-popup--center {
    width: 320px;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 6px;
    right: 6px;
  }
  
  ::v-deep.van-popup {
    width: 300px;
  }
  .pop-dialog {
    border-radius: 8px 8px 8px 8px;
    font-size: 16px;
    width: 320px;
    .pop-div {
      position: relative;
    }
    .dialog-title {
      width: 300px;
      font-weight: 700;
      height: 24px;
      padding-top: 32px;
      line-height: 24px;
      color: $vms-main-black;
      text-align: center;
    }
    .update-box{
      padding: 8px;
      .update-content {
        max-height:394px;
        overflow-y: scroll;
        line-height: 22px;
        color:$vms-light-black;
        padding: 0 10px 18px 16px;
        word-break: break-all;
      }
    }
    .dialog-close-img {
      position: absolute;
      top: 20px;
      right: 16px;
      width: 24px;
      height: 24px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .title-text {
    height: 38px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title-text-img {
        width: 24px;
        height: 24px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .title-text-button {
    width: 74px;
    height: 36px;
    border: 1px solid $vms-primary;
    line-height: 36px;
    background: $vms-white;
    border-radius: 4px;
    text-align: center;
    color: $vms-primary;
  }
  .view-btn {
    color: $vms-primary;
    font-weight: 500;
    font-size: 14px;
  }
  .title {
    color: $vms-gray;
    height: 30px;
    line-height: 30px;
    padding-left: 16px;
    margin-top: 16px;
    font-weight: 700;
  }
  .title-text-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 27px;
    margin-left: 16px;
    max-width: 190px;
  }
  .download-status {
    color: $vms-primary;
    font-size: 14px;
    display: flex;
    .download-status-text {
      margin-right: 8px;
    }
    .progress{
      color:$vms-black;
    }
  }
  .download-status-box {
    color: $vms-primary;
    font-size: 12px;
    .download-status-text {
      margin-right: 8px;
    }
    .upgrade-progress {
      margin: 20px 0px 8px 0px;
    }
  }
  .device-upgrade {
    .container {
      margin: 0px 16px;
      background-color: $vms-light-white;
      border-radius: 10px;
      padding:16px;
    }
  }
  // 列表通用
  .list-content {
    padding-top: 10px;
    .list-content-row {
      display: flex;
      line-height: 24px;
      .label {
        width:36%;
        flex-shrink: 0;
        color: $vms-gray;
      }
      .value {
        width:64%;
        flex: 1;
        word-wrap: break-word;
        white-space: pre-wrap;
        color: $vms-black;
      }
      .zh-label{
        width:20%;
      }
      .zh-value{
        width:80%;
      }
    }
    .download-tip {
      line-height: 24px;
      font-size: 14px;
    }
    .has-latest-version {
      color: $vms-black;
    }
  }
  .camera-upgrade {
    margin: 0px 16px;
    .title{
      line-height: 20px;
      padding-left: 0px;
    }
    .title-div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-img {
        margin-right: 16px;
        width: 22px;
        height: 22px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .container-ul {
      border-radius: 10px;
      background-color: $vms-white;
      padding: 2px 16px;
      .container-li {
        border-bottom: 1px solid $vms-light-gray;
        padding: 12px 0;
        &:last-child {
          border: 0;
        }
      }
    }
  }
  .camera-has-upgrade{
    border-radius: 10px;
  }
  .camera-no-upgrade {
    border-radius: 10px;
    padding-left: 16px;
    line-height: 28px;
    min-height: 60px;
    background-color: $vms-white;
  }
  .footer {
    position: fixed;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px;
    .footer-btn {
      width: 375px;
      height:70px;
      line-height: 46px;
      text-align: center;
      background-color: $vms-white;
      color: $vms-black;
    }
  }
}