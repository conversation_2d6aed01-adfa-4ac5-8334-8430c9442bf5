import { getChlList, getSensorList, appRequestDevice } from '@/utils/appbridge'
import { MAX_IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import { transformXml } from '@/utils/common'

// 公共的请求APP方法，fn为需要调用app的方法，req为请求参数
function commonRequestApp(fn, req) {
  return new Promise(resolve => {
    fn(req, function (res) {
      let resData = res.replace(/\\t|\\n/g, '')
      resData = JSON.parse(resData)
      resolve(resData)
    })
  })
}

// 根据设备能力集找到所有联动项
function getIpcLinkageList(state, sn, chlIndex, chlId) {
  const { ipcLinkageList, capabilityObj } = state
  const newIpcLinkageList = JSON.parse(JSON.stringify(ipcLinkageList))
  // 找到设备支持的能力集，过滤出支持的能力集选项
  const supportFun = capabilityObj[`${sn}~${chlIndex}~${chlId}`] || []
  // 根据能力集找到可以支持联动项
  const filterLinkageList = newIpcLinkageList.filter(item => !item.filterAble || supportFun.includes(item.value))
  return filterLinkageList
}

// 检查请求状态 0 未全部请求完 1 全部请求成功 2 有请求失败
function checkReqStatus(reqStatus, callback) {
  if (reqStatus.some(val => val.status === 2)) {
    // 有请求失败则回调失败
    callback && callback('ERROR', reqStatus)
  } else if (reqStatus.every(val => val.status === 1)) {
    // 全部请求成功才回调成功
    callback && callback('SUCCESS', reqStatus)
  }
}

export default {
  namespaced: true,
  state: () => ({
    initStatus: false, // 是否第一次进入
    noDataImg: null,
    sn: null,
    devId: null,
    addType: null, // 添加方式：bind绑定  sn/ip非绑定
    ipcLinkageList: MAX_IPC_LINKAGE_LIST_FULLNAME(),
    allChannelList: [], // 站点通道列表
    channelObj: {}, // 站点Id-设备sn-通道chlIndex-传感器chlId及其对应的通道信息
    capabilityObj: {}, // 设备能力集  设备sn-通道chlIndex-传感器chlId及其对应的能力集数组
    defenseGroupList: [], // 布防组列表
    groupChannelList: [], // 布防组列表对应的所有通道
    defenseRecord: {
      groupName: '区域分组',
      channelList: []
    }, // 记录当前新增或编辑的布防组
    channelList: [], // 编辑某个布防组时对应的通道
    channelRecord: {} // 编辑的通道信息
  }),
  getters: {},
  mutations: {
    SET_INIT_STATUS(state, data) {
      state.initStatus = data
    },
    SET_NO_DATA_IMG(state, data) {
      state.noDataImg = data
    },
    SET_SN(state, data) {
      state.sn = data
    },
    SET_DEVID(state, data) {
      state.devId = data
    },
    SET_ADD_TYPE(state, data) {
      state.addType = data
    },
    SET_ALL_CHANNEL_LIST(state, data) {
      state.allChannelList = data
    },
    SET_CHANNEL_OBJ(state, data) {
      state.channelObj = data
    },
    SET_CAPABILITY_OBJ(state, data) {
      state.capabilityObj = data
    },
    SET_DEFENSE_GROUP_LIST(state, data) {
      state.defenseGroupList = data
    },
    SET_GROUP_CHANNEL_LIST(state, data) {
      state.groupChannelList = data
    },
    SET_CHANNEL_RECORD(state, data) {
      state.channelRecord = data
    },
    SET_CHANNEL_LIST(state, data) {
      state.channelList = data
    },
    SET_DEFENSE_RECORD(state, data) {
      // 将新增的通道加入到通道列表中
      state.defenseRecord = data
    }
  },
  actions: {
    // 查询设备所有通道列表
    async getChannelList({ commit, state }) {
      try {
        const params = {
          devId: state.devId
        }
        const [resData, resData2] = await Promise.all([
          commonRequestApp(getChlList, params),
          commonRequestApp(getSensorList, params)
        ])
        // console.log('请求所有通道', resData)
        // console.log('请求所有传感器', resData2)
        // 遍历所有的通道列表
        const channelList = resData.body.slice()
        channelList.forEach(item => {
          item.nodeType = 'channel'
          item.chlId = null
        })
        const sensorList = resData2.body.slice()
        sensorList.forEach(item => {
          item.nodeType = 'sensor'
          // // 将guid赋值给chlId,保持统一
          item.chlIndex = 0
          item.chlId = item.guid
          // 传感器默认在线
          item.onlineStatus = 1
          // 传感器的devDesc指明了归属于哪个通道，如果没有对应的通道，则默认为设备的传感器，使用设备名称
          if (!item.devDesc) {
            item.devDesc = item.deviceName
          }
        })
        const channelObj = {} // 记录设备sn-通道chlIndex-传感器chlId及其对应的通道信息
        const capabilityObj = {} // 记录设备sn-通道chlIndex-传感器chlId及其对应的能力集
        // const allChannelList = [...channelList, ...sensorList]
        // 将传感器放到通道下  --原则：传感器的devDesc指明了归属于哪个通道，如果没有对应的通道，则默认为设备的传感器，放到最后面
        const allChannelList = []
        let filterSensorList = sensorList.slice()
        channelList.forEach(item => {
          allChannelList.push(item)
          // 找到对应的传感器
          const channelSensor = filterSensorList.filter(item2 => item2.devDesc === item.name)
          allChannelList.push(...channelSensor)
          filterSensorList = filterSensorList.filter(item2 => item2.devDesc !== item.name)
        })
        // 将剩余的设备传感器传入
        allChannelList.push(...filterSensorList)
        allChannelList.forEach(item => {
          const { sn, chlIndex, chlId, capability } = item
          channelObj[`${sn}~${chlIndex}~${chlId}`] = { ...item }
          // 找到能力集
          if (capability) {
            capabilityObj[`${sn}~${chlIndex}~${chlId}`] = capability.slice()
          }
        })
        // console.log('capabilityObj', capabilityObj)
        commit('SET_ALL_CHANNEL_LIST', allChannelList)
        commit('SET_CHANNEL_OBJ', channelObj)
        commit('SET_CAPABILITY_OBJ', capabilityObj)
        return resData
      } catch (error) {
        console.error(error)
      }
    },
    // 查询布防撤防列表
    async getDefenseList({ commit, dispatch, state }, { callback } = {}) {
      try {
        this.reqDefenseList = false
        // 动态引入接口，避免在store初始引入时获取不到store.state.app报错
        const { getDefenseGroupList } = await import('@/api/maxDefense')
        const res = await getDefenseGroupList({ sn: state.sn })
        const resData = res.data || []
        commit('SET_DEFENSE_GROUP_LIST', resData)
        // 遍历所有的分组，拿到groupIdList，请求所有的detail信息
        const ids = resData.map(item => item.id) || []
        if (ids.length) {
          await dispatch('getAllDetails', { ids })
        }
        callback && callback()
      } catch (error) {
        console.error(error)
      }
    },
    // 查询所有分组下面的通道
    async getAllDetails({ commit }, { ids }) {
      try {
        this.reqDefenseList = false
        // 动态引入接口，避免在store初始引入时获取不到store.state.app报错
        const { getDefenseDetail } = await import('@/api/maxDefense')
        const res = await getDefenseDetail({ ids })
        const resData = res.data || []
        commit('SET_GROUP_CHANNEL_LIST', resData)
      } catch (error) {
        console.error(error)
      }
    },
    // 发送协议到对应的设备（不考虑联动项的勾选与否状态互斥效果，只修改勾选的联动项，针对新增和删除） -- 新增时全部为撤防状态，删除时恢复布防状态，固定为外出布防
    // eslint-disable-next-line
    async updateDeviceStatus({ state }, { record, callback }) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 在家布防 2 外部布防
      const { status, channelList } = record
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }
      // 跟设备交互
      let req = null
      try {
        // 动态引入接口，避免在store初始引入时获取不到store.state.app报错
        const { urlDefenseSwitchNodes } = await import('@/api/maxDefense')
        req = {
          devId: state.devId,
          url: 'editNodeDefenseStatus',
          params: urlDefenseSwitchNodes(status, channelList)
        }
      } catch (error) {
        // 提示网络异常
        if (callback) callback('ERROR', '99006')
        return
      }
      const resData = await commonRequestApp(appRequestDevice, req)
      const errorCode = resData.code
      if (resData.code == 200) {
        const xmlObject = transformXml(resData.body)
        if (xmlObject.response.status == 'success') {
          // 处理结果
          if (callback) callback('SUCCESS')
        } else {
          if (callback) callback('ERROR')
        }
      } else {
        if (callback) callback('ERROR', errorCode)
      }
    },
    // 更新设备布撤防状态(考虑联动项的勾选与否状态互斥效果，针对编辑联动项，在家布防旁路和切换分组状态)
    // eslint-disable-next-line
    async updateDeviceMutexStatus({ state }, { record, callback }) {
      const { status, channelList } = record
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }

      // 勾选的联动项和未勾选的联动项发送相反的指令
      // 遍历channelList，将每个channel拆分成两份：勾选的联动项和未勾选的联动项
      const checkChannelList = []
      const noChannelList = []
      channelList.forEach(item => {
        let { extra, sn, chlIndex, chlId } = item
        if (typeof extra === 'string') {
          extra = JSON.parse(extra)
        }
        const { bypassSwitch = 0, linkageList = [] } = extra
        // 根据当前设备能力集支持的所有联动项目找到未被勾选的联动项
        const ipcLinkageList = getIpcLinkageList(state, sn, chlIndex, chlId)
        const noLinkageList = ipcLinkageList
          .filter(item => linkageList.indexOf(item.value) === -1)
          .map(item => item.value)
        // 有已勾选的联动项
        if (linkageList.length) {
          checkChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: linkageList.slice()
            }
          })
        }
        // 有未勾选的联动项
        if (noLinkageList.length) {
          noChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: noLinkageList.slice()
            }
          })
        }
      })
      const reqStatus = [
        { status: 0, code: 200 },
        { status: 0, code: 200 }
      ] // 请求状态：0 pending 1 success 2 fail
      const reqArr = [checkChannelList, noChannelList]
      // 动态引入接口，避免在store初始引入时获取不到store.state.app报错
      let urlDefenseSwitchNodes = null
      try {
        const maxDefenseApi = await import('@/api/maxDefense')
        urlDefenseSwitchNodes = maxDefenseApi.urlDefenseSwitchNodes
      } catch (error) {
        // 提示网络异常
        const newReqStatus = reqStatus.map(() => ({ status: 2, code: '99006' }))
        if (callback) callback('ERROR', newReqStatus)
        return
      }
      reqArr.forEach(async (item, index) => {
        if (item.length) {
          const req = {
            devId: state.devId,
            url: 'editNodeDefenseStatus',
            params: urlDefenseSwitchNodes(status, item, index) // 用index区分是勾选还是未勾选，对应的布撤防状态是相反的
          }
          const resData = await commonRequestApp(appRequestDevice, req)
          // const errorCode = resData.code
          if (resData.code == 200) {
            const xmlObject = transformXml(resData.body)
            if (xmlObject.response.status == 'success') {
              // 处理结果
              reqStatus[index] = {
                status: 1,
                code: 200
              }
            } else {
              reqStatus[index] = {
                status: 2,
                code: 200
              }
            }
          } else {
            reqStatus[index] = {
              status: 2,
              code: resData.code
            }
          }
          // 检查单个分组是否请求都发送完
          checkReqStatus(reqStatus, callback)
        } else {
          // 没有联动项默认不发送请求，直接默认成功
          reqStatus[index] = {
            status: 1,
            code: 200
          }
          // 检查是否请求都发送完
          checkReqStatus(reqStatus, callback)
        }
      })
    }
  }
}
