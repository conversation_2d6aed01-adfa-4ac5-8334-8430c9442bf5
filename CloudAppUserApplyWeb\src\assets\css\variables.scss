// superLive 色块
// 背景色
$background-color: #EBEDF1;
$--color-primary: #00BAFF;
$black-color:#393939;
$light-black-color:#4f4f4f;
$navbar-color:#283445;
$gray-color:#8F8E93;
$middle-gray-color:#CCCCCC;
$light-gray-color:#F2F4F8;
$green-color:#00bd28;
$dialog-btn-color:#3065E1;
$red-color:#FF3A39;
$orange-color:#ff8000;
$white-color:#fff;
$placeholder-color:#D9D9D9;
$border-color: #e7e7e7;

:export {
    colorPrimary: $--color-primary;
}

//定制客户 USE44 TOC UI2A
$USE44-background-color: #073769;
$USE44-light-background-color:#294a73;
$USE44-color-primary: #00BAFF;
$USE44-40-white-color:rgba(255,255,255,0.4);
$USE44-navbar-color:#0551D5;
$USE44-50-white-color:rgba(255,255,255,0.5);
$USE44-90-black-color:rgba(0,0,0,0.9);
$USE44-50-black-color:rgba(0,0,0,0.5);
$USE44-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$USE44-green-color:#00bd28;
$USE44-dialog-btn-color:#00BAFF;
$USE44-red-color:#FF3A39;
$USE44-orange-color:#ff8000;
$USE44-white-color:#fff;
$USE44-font-color:#073769;
$USE44-placeholder-color:rgba(0,0,0,0.5);
$USE44-dialog-border-color:#B1C3D3;
$USE44-button-background-color:#CDD7E2;
$USE44-border-color: #e7e7e7; // 调整

// 定制客户 ITFR16 TOC UI1E
$UI1E-background-color: #333333; // 调整
$UI1E-light-background-color: #444444; // 调整
$UI1E-color-primary: #C5D700; // 调整
$UI1E-40-white-color:rgba(255,255,255,0.4);
$UI1E-navbar-color:#C5D700; // 调整
$UI1E-50-white-color:#8F8E93; // 调整
$UI1E-90-black-color:rgba(0,0,0,0.9);
$UI1E-50-black-color:rgba(0,0,0,0.5);
$UI1E-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1E-green-color:#00bd28;
$UI1E-dialog-btn-color:#C5D700;
$UI1E-red-color:#FF3A39;
$UI1E-orange-color:#ff8000;
$UI1E-white-color: #fff; // 调整
$UI1E-font-color: #8F8E93; // 调整
$UI1E-placeholder-color:rgba(255,255,255,0.5);
$UI1E-dialog-border-color:#B1C3D3;
$UI1E-button-background-color:#C5D700; // 调整
$UI1E-border-color: #5a5a5a; // 调整

// 定制客户 IT20 TOC UI1G
$UI1G-background-color: #343434; // 调整
$UI1G-light-background-color: #444444; // 调整
$UI1G-color-primary: #FFCB33; // 调整
$UI1G-40-white-color:rgba(255,255,255,0.4);
$UI1G-navbar-color:#222222; // 调整
$UI1G-50-white-color:#8F8E93; // 调整
$UI1G-90-black-color:rgba(0,0,0,0.9);
$UI1G-50-black-color:rgba(0,0,0,0.5);
$UI1G-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1G-green-color:#00bd28;
$UI1G-dialog-btn-color:#FFCB33;
$UI1G-red-color:#FF3A39;
$UI1G-orange-color:#ff8000;
$UI1G-white-color: #fff; // 调整
$UI1G-font-color: #8F8E93; // 调整
$UI1G-placeholder-color:rgba(255,255,255,0.5);
$UI1G-dialog-border-color:#B1C3D3;
$UI1G-button-background-color:#FFCB33; // 调整
$UI1G-border-color: #5a5a5a; // 调整


// 定制客户 INS67 TOC UI1N
$UI1N-background-color: #343434; // 调整
$UI1N-light-background-color: #444444; // 调整
$UI1N-color-primary: #FFCB33; // 调整
$UI1N-40-white-color:rgba(255,255,255,0.4);
$UI1N-navbar-color:#222222; // 调整
$UI1N-50-white-color:#8F8E93; // 调整
$UI1N-90-black-color:rgba(0,0,0,0.9);
$UI1N-50-black-color:rgba(0,0,0,0.5);
$UI1N-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1N-green-color:#00bd28;
$UI1N-dialog-btn-color:#FFCB33;
$UI1N-red-color:#FF3A39;
$UI1N-orange-color:#ff8000;
$UI1N-white-color: #fff; // 调整
$UI1N-font-color: #8F8E93; // 调整
$UI1N-placeholder-color:rgba(255,255,255,0.5);
$UI1N-dialog-border-color:#B1C3D3;
$UI1N-button-background-color:#FFCB33; // 调整
$UI1N-border-color: #5a5a5a; // 调整

// 定制客户 IT19 TOC UI1B
$UI1B-background-color: #1C1F2B; // 调整
$UI1B-light-background-color: #32374A; // 调整
$UI1B-color-primary: #00BC70; // 调整
$UI1B-40-white-color:rgba(255,255,255,0.4);
$UI1B-navbar-color:#00BC70; // 调整
$UI1B-50-white-color:#8F8E93; // 调整
$UI1B-90-black-color:rgba(0,0,0,0.9);
$UI1B-50-black-color:rgba(0,0,0,0.5);
$UI1B-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1B-green-color:#00bd28;
$UI1B-dialog-btn-color:#00BC70;
$UI1B-red-color:#FF3A39;
$UI1B-orange-color:#ff8000;
$UI1B-white-color: #fff; // 调整
$UI1B-font-color: #8F8E93; // 调整
$UI1B-placeholder-color:rgba(255,255,255,0.5);
$UI1B-dialog-border-color:#B1C3D3;
$UI1B-button-background-color:#00BC70; // 调整
$UI1B-border-color: #5a5a5a; // 调整


// 定制客户 IL03 TOC UI1C
$UI1C-background-color: black; // 调整
$UI1C-light-background-color: #333333; // 调整
$UI1C-color-primary: #D90000; // 调整
$UI1C-40-white-color:rgba(255,255,255,0.4);
$UI1C-navbar-color:black; // 调整
$UI1C-50-white-color:#8F8E93; // 调整
$UI1C-90-black-color:rgba(0,0,0,0.9);
$UI1C-50-black-color:rgba(0,0,0,0.5);
$UI1C-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1C-green-color:#00bd28;
$UI1C-dialog-btn-color:#D90000;
$UI1C-red-color:#FF3A39;
$UI1C-orange-color:#ff8000;
$UI1C-white-color: #fff; // 调整
$UI1C-font-color: #8F8E93; // 调整
$UI1C-placeholder-color:rgba(255,255,255,0.5);
$UI1C-dialog-border-color:#B1C3D3;
$UI1C-button-background-color:#D90000; // 调整
$UI1C-border-color: #5a5a5a; // 调整

// 定制客户 PL14 TOC UI1D
$UI1D-background-color: #8F8E93; // 调整
$UI1D-light-background-color: #FFFFFF; // 调整
$UI1D-50-white-color: #8F8E93; // 调整
$UI1D-color-primary: #9E0000; // 调整
$UI1D-navbar-color:#9E0000; // 调整
$UI1D-button-background-color:#9E0000; // 调整
$UI1D-font-color: #8F8E93; // 调整
$UI1D-light-gray-color:#CCCCCC;//分割线颜色 // 调整

// 定制客户 IT25 TOC UI1F
$UI1F-background-color: #444444; // 调整
$UI1F-light-background-color: #333333; // 调整
$UI1F-color-primary: #1379AA; // 调整
$UI1F-40-white-color:rgba(255,255,255,0.4);
$UI1F-navbar-color:#1379AA; // 调整
$UI1F-50-white-color:#8F8E93; // 调整
$UI1F-90-black-color:rgba(0,0,0,0.9);
$UI1F-50-black-color:rgba(0,0,0,0.5);
$UI1F-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI1F-green-color:#00bd28;
$UI1F-dialog-btn-color:#1379AA;
$UI1F-red-color:#FF3A39;
$UI1F-orange-color:#ff8000;
$UI1F-white-color: #fff; // 调整
$UI1F-font-color: #8F8E93; // 调整
$UI1F-placeholder-color:rgba(255,255,255,0.5);
$UI1F-dialog-border-color:#B1C3D3;
$UI1F-button-background-color:#1379AA; // 调整
$UI1F-border-color: #5a5a5a; // 调整

// 定制客户 INN57 TOC UI1H
$UI1H-background-color: #F7F7F7; // 调整
$UI1H-light-background-color: #FFFFFF; // 调整
$UI1H-50-white-color: #8F8E93; // 调整
$UI1H-color-primary: #17B9B0; // 调整
$UI1H-navbar-color:#17B9B0; // 调整
$UI1H-button-background-color:#17B9B0; // 调整
$UI1H-font-color: #8F8E93; // 调整
$UI1H-light-gray-color:#CCCCCC;//分割线颜色 // 调整


// 定制客户 SuperCamPlus  TOC UI1I
// 定制客户 SmartEyesPlus（慧眼家） TOC UI1J
// SuperCamPlus和SmartEyesPlus（慧眼家）共用一套样式
$UI1I-background-color: #F7F7F7; // 调整
$UI1I-light-background-color: #FFFFFF; // 调整
$UI1I-50-white-color: #8F8E93; // 调整
$UI1I-color-primary: #3773ff; // 调整
$UI1I-navbar-color:#3773ff; // 调整
$UI1I-button-background-color:#3773ff; // 调整
$UI1I-font-color: #8F8E93; // 调整
$UI1I-light-gray-color:#CCCCCC;//分割线颜色 // 调整


// 定制客户 AE42 TOC UI1K
$UI1K-background-color: #F7F7F7; // 调整
$UI1K-light-background-color: #FFFFFF; // 调整
$UI1K-50-white-color: #8F8E93; // 调整
$UI1K-color-primary: #0076C0; // 调整
$UI1K-navbar-color:#0076C0; // 调整
$UI1K-button-background-color:#0076C0; // 调整
$UI1K-font-color: #8F8E93; // 调整
$UI1K-light-gray-color:#CCCCCC;//分割线颜色 // 调整

// 定制客户 AE43 TOC UI1M
$UI1M-navbar-color:#0041CD; // 调整


// 定制客户 USE44 MyGuardianLive TOC UI3A
$UI3A-background-color: #343434; // 调整
$UI3A-light-background-color: #444444; // 调整
$UI3A-color-primary: #F7B512; // 调整
$UI3A-40-white-color:rgba(255,255,255,0.4);
$UI3A-navbar-color:#F7B512; // 调整
$UI3A-50-white-color:#8F8E93; // 调整
$UI3A-90-black-color:rgba(0,0,0,0.9);
$UI3A-50-black-color:rgba(0,0,0,0.5);
$UI3A-light-gray-color:rgba(116,144,175,0.3);//分割线颜色
$UI3A-green-color:#00bd28;
$UI3A-dialog-btn-color:#F7B512;
$UI3A-red-color:#FF3A39;
$UI3A-orange-color:#ff8000;
$UI3A-white-color: #fff; // 调整
$UI3A-font-color: #8F8E93; // 调整
$UI3A-placeholder-color:rgba(255,255,255,0.5);
$UI3A-dialog-border-color:#B1C3D3;
$UI3A-button-background-color:#F7B512; // 调整
$UI3A-border-color: #5a5a5a; // 调整

// ---------------------------分割线--------------------------------
// vms 色块 中性
$vms-background: #F2F2F6;
$vms-primary: #2C5BFA; 
$vms-black:#000000;
$vms-main-black:#181818;
$vms-light-black:#646464;
$vms-gray:#959595; 
$vms-light-gray:#EEEEEE;
$vms-green:#00BE7F;
$vms-red:#FF3D3D;
$vms-white:#fff;
$vms-placeholder:#00000042;
$vms-light-white:#FEFFFE;
$vms-light-black2: #00000066;
$vms-light-black3: #000000e6;
$vms-delete-bg: #e34d59;

// ---------------------------分割线--------------------------------
// 安装商色块 中性
$partner-primary:#2C5BFA;
$partner-background: #F7F7F7;
$partner-black:#000000;
$partner-gray:#8F8E93;
$partner-deep-gray:#717171;
$partner-light-gray:#F2F4F8;
$partner-white:#fff;
$partner-placeholder:#949494;
$partner-red:#FF4D4F;
$partner-green:#00BE7F;
$partner-light-green:#32BAC0;
$partner-orange:#FF6B00;
$partner-middle-gray: #CCCCCC;

// superlive-max色块 中性
$max-background: #fff;
$max-light-black: #666666;
$max-gray: #a3a3a3;
$max-light-white: #F2F4F8;
$max-border: #EEEEEE;
$max-black: #1A1A1A;
$max-red: #FF3B30;
$max-red2: #FF3D3D;
$max-middle-black: #2b2b2b;
$max-tag-color: #D6E4FE;
$max-high-black: #393939;
$max-light-gray: #B5B5B5;
$max-primary: #3277FC;
$max-border2: #F7F7F7;
$max-green: #00C261;
$max-disabled: #D1D1D1;
$max-orange: #FF8833;
$max-gray2: #82879b;
