const OCX_XML_Header = '<?xml version="1.0" encoding="UTF-8"?>'
const Font = '<config xmlns="http://www.ipc.com/ver10" version="1.7">'
const Back = '</config>'
const OCX_XML_Config = Font + Back

// getCloudUpgradeInfo（获取云升级信息）
export const getCloudUpgradeInfoXml = () => {
  let result = OCX_XML_Header + OCX_XML_Config
  return result
}

// checkVersion（手动检测云升级版本）
export const checkVersionXml = () => {
  let result = OCX_XML_Header + OCX_XML_Config
  return result
}

// checkVersion（手动检测云升级版本） superlive max
export const maxCheckVersionXml = () => {
  return ''
}

//开始云升级  二次鉴权加升级 cloudUpgrade
export const cloudUpgradeXml = (password, versionGUID) => {
  let result = OCX_XML_Header
  result += Font
  result += "<versionGUID type='string'>" + '<![CDATA[' + versionGUID + ']]>' + '</versionGUID>'
  result += Back
  result += "<password type='string' encryptType='md5'>" + '<![CDATA[' + password + ']]>' + '</password>'
  return result
}

// 取消云升级 cancelCloudUpgrade
export const cancelCloudUpgradeXml = versionGUID => {
  let result = OCX_XML_Header
  result += Font
  result += "<versionGUID type='string'>" + '<![CDATA[' + versionGUID + ']]>' + '</versionGUID>'
  result += Back
  return result
}
