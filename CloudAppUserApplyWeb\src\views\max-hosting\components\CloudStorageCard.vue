<template>
  <div class="cloud-card-wrapper">
    <div class="cloud-card-content" @click="handleClickDetail">
      <div class="cloud-card-head">
        <div class="cloud-card-title">{{ titleText }}</div>
        <div class="cloud-card-text" :class="{ underlined: this.cloudStorageBuyChlNum }" @click="handleBoughtClick">
          {{ descrptionText }}
        </div>
        <div class="cloud-card-btn">
          <van-button class="footer-btn" type="primary" @click="handleClickPurchase">
            {{ $t('immediateBuy') }}
          </van-button>
        </div>
      </div>
      <div class="cloud-card-purchase-record" v-show="cloudStorageBuyChlNum" @click="handleClickRecord">
        {{ $t('purchaseRecords') }}
      </div>
      <img src="@/assets/img/common/trusteeship/cloud_front.png" class="cloud-front" />
    </div>
  </div>
</template>
<script>
import { gotoPage, openDialog } from '@/utils/appbridge'
export default {
  name: 'CloudStorageCard',
  components: {},
  props: {
    cloudStorageBuyChlNum: {
      type: Number,
      default: 0
    },
    isGuest: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    titleText() {
      return this.cloudStorageBuyChlNum ? this.$t('cloudStorageInUse') : this.$t('cloudStorage')
    },
    descrptionText() {
      return this.cloudStorageBuyChlNum ? this.$t('purchasedChannel') : this.$t('secureAndQuick')
    }
  },
  methods: {
    // 点击购买记录
    handleClickRecord(event) {
      event.stopPropagation()
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/orderRecords'
      })
    },
    //点击立刻购买
    handleClickPurchase(event) {
      event.stopPropagation()
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/purchase'
      })
    },
    // 点击卡片其它任意位置 进入详情页。
    handleClickDetail() {
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/main'
      })
    },
    // 已购买跳转
    handleBoughtClick(event) {
      event.stopPropagation()
      if (this.cloudStorageBuyChlNum) {
        gotoPage({
          pageRoute: 'cloudStorage/purchasedChannels'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cloud-card-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  font-family: 'PingFang SC';
  .cloud-card-content {
    width: calc(100% - 16px);
    height: 204px;
    border-radius: 10px;
    background: linear-gradient(180deg, #fff 30%, #e6effb 100%);
    box-shadow: 0 2px 4px 0 #0000000d;
    position: relative;
  }
  .cloud-card-head {
    height: 137px;
    max-width: 45%;
    position: absolute;
    top: 38.4px;
    left: 8%;
    .cloud-card-title {
      color: var(--text-color-primary, #1a1a1a);
      font-size: var(--font-size-h4-size, 20px);
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
      margin-bottom: 2px;
    }
    .cloud-card-text {
      color: var(--text-color-primary, #1a1a1a);
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      height: 60px;
    }
    .underlined {
      text-decoration: underline;
    }
  }

  .footer-btn {
    height: 32px;
    flex-shrink: 0;
    border: 1px solid var(--brand-bg-color-default, #3277fc);
    color: var(--brand-bg-color-default, #3277fc);
    border-radius: 23px;
    background: transparent;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 500;
    line-height: 22px;
    padding: 0 22px;
  }
  .cloud-card-purchase-record {
    position: absolute;
    right: 10px;
    top: 10px;
    color: var(--brand-bg-color-default, #3277fc);
    font-size: var(--font-size-text-size, 12px);
  }
  .cloud-front {
    position: absolute;
    top: 17%;
    left: 53%;
    width: 160px;
    height: 132px;
  }

  .cloud-card-body {
    // width: 359px;
    width: 100%;
    height: 67px;
    border-radius: 0px 0px 10px 10px;
    .cloud-card-line {
      width: 100%;
      height: 24px;
      padding: 0px 30px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 6px;
    }
    .trusteeship-line-text {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: 'PingFang SC';
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      margin-left: 8px;
    }
  }
}
</style>
