<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="detail-content">
        <div class="site-list">
          <div class="site">
            <div class="site-header" @click="openSitePopup">
              <div class="site-header-left">
                <img src="@/assets/img/common/trusteeship/site.png" class="site-icon" />
                {{ $t('site') }}
              </div>
              <div class="site-tool">
                <span class="site-name text-over-ellipsis">
                  {{ currentSite.siteName }}
                </span>
                <img src="@/assets/img/common/arrow_right.png" class="site-tool-icon" />
              </div>
            </div>
            <div v-if="deviceList?.length === 0" class="empty-list">
              <img src="@/assets/img/common/no_data_max.png" class="empty-img" />
              <span class="empty-info">
                {{ $t('noMoreDevice') }}
              </span>
            </div>
            <div class="site-content" v-for="device in deviceList" :key="device.sn">
              <div class="device">
                <dir class="device-header" @click="device.checked = !device.checked">
                  <img
                    class="img-icon"
                    :src="
                      device.checked
                        ? require('@/assets/img/common/check.png')
                        : require('@/assets/img/common/check_no.png')
                    "
                  />
                  <div class="device-name text-over-ellipsis">
                    {{ device.devName }}
                  </div>
                </dir>
                <div class="device-content">
                  <div class="device-permission" @click="openPermissionPopup(device)">
                    <div>
                      <div class="device-permission-title">
                        {{ $t('trustPermission') }}
                      </div>
                      <div class="device-permission-info">
                        {{ device.permissionTr }}
                      </div>
                    </div>
                    <img src="@/assets/img/common/more.png" class="device-content-icon" />
                  </div>
                  <div class="device-permission" @click="openTimePopup(device)">
                    <div>
                      <div class="device-permission-title">
                        {{ $t('trustTime') }}
                      </div>
                      <div class="device-permission-info">
                        {{ device.hostingTimeTr }}
                      </div>
                    </div>
                    <img src="@/assets/img/common/more.png" class="device-content-icon" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </tvt-better-scroll>

    <div class="footer-btn-container">
      <van-button :disabled="submitDisabled" class="footer-btn" type="primary" @click="handleHosting">
        {{ $t('confirm') }}
      </van-button>
    </div>
    <van-popup v-model="sitePopupVisible" round :close-on-click-overlay="false" position="bottom" get-container="#app">
      <div class="service-detail-site-popup-container">
        <div
          v-for="site in siteList"
          :key="site.id"
          class="site-change-item"
          :class="{ active: site.siteId === currentSite.siteId }"
          @click="selectSite(site)"
        >
          {{ site.siteName }}
        </div>
        <div class="site-change-item cancel-btn" @click="sitePopupVisible = false">
          {{ $t('cancel') }}
        </div>
      </div>
    </van-popup>
    <cancel-hosting-popup :id="currentSite.siteId" :site-num="siteList.length" ref="cancelHostingRef" />
    <set-permission-popup-vue
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setPermissionVisible"
      @submit="changePermission"
      ref="setPermissionRef"
    />
    <set-time-popup
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setTimeVisible"
      @submit="changeTime"
      ref="setTimeRef"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { showTimeStr2 } from '@/utils/common'
import CancelHostingPopup from './components/CancelHostingPopup'
import SetPermissionPopupVue from './components/SetPermissionPopup'
import SetTimePopup from './components/SetTimePopup'
import { getInstallerHostingSite, getInstallerInfo, createHosting, getInstallerHostingDevice } from '@/api/maxHosting'
import { formatInstallerInfo } from './common'

export default {
  name: 'SelectHostingDevice',
  components: {
    NavBar,
    CancelHostingPopup,
    SetPermissionPopupVue,
    SetTimePopup
  },
  data() {
    return {
      pullingStatus: 0,
      detail: {},
      siteList: {},
      deviceList: [],
      email: '',
      setPermissionVisible: false,
      setTimeVisible: false,
      sitePopupVisible: false,
      currentSite: {},
      currentDevice: {},
      installerUserId: ''
    }
  },
  computed: {
    submitDisabled() {
      return this.deviceList.filter(device => device.checked).length === 0
    }
  },
  created() {
    const { email, installerUserId } = this.$route.query

    if (email) {
      this.email = email
      this.installerUserId = installerUserId
      this.refreshRequest()
    } else {
      this.installerUserId = ''
      this.email = ''
    }
  },
  methods: {
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.installerUserId
      })

      this.detail = formatInstallerInfo(detail)
    },
    // 刷新当前页面请求
    async refreshRequest() {
      this.getDetail()

      // 接口永远至少有一个默认站点
      let { data: siteList } = await getInstallerHostingSite({
        installerUserId: this.installerUserId
      })

      this.siteList = siteList

      this.selectSite(this.siteList[0])
    },
    translateTime(device) {
      if (device.trustDuration === 0) {
        return this.$t('forever')
      }

      return showTimeStr2(0, device.trustDuration * 1000)
    },
    translatePermission(device) {
      if (!Array.isArray(device.authList)) {
        return ''
      }
      const sorted = {
        config: 0,
        live: 1,
        rec: 2
      }
      const arr = []
      device.authList.forEach(item => {
        // 进行排序，后端数据顺序会变
        const index = sorted[item]
        if (index !== undefined) {
          arr[index] = item
        }
      })

      return arr
        .filter(item => item)
        .map(item => this.$t(item))
        .join(' · ')
    },
    async pullingUp(callback) {
      // 刷新
      this.refreshRequest()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      await this.refreshRequest()

      if (callback) callback()
    },
    clickLeft() {
      if (this.$route.query.origin === 'addInstaller') {
        // 跳转到添加安装商
        this.$router.push({
          path: '/max/hosting/installer/add',
          query: {
            email: this.email
          }
        })
      } else {
        this.$router.back()
      }
    },
    async handleHosting() {
      await createHosting({
        installerUserId: this.installerUserId,
        siteId: this.currentSite.siteId,
        deviceTrusts: this.deviceList.filter(device => device.checked)
      })
      // 申请成功，进入提示页面
      this.$router.push({
        path: '/max/hosting/application/success',
        query: {
          email: this.detail.email
        }
      })
    },
    startStopHost() {
      this.sitePopupVisible = false
      this.$refs.cancelHostingRef.open()
    },
    openPermissionPopup(device) {
      this.currentDevice = device

      this.setPermissionVisible = true
    },
    changePermission(value) {
      this.currentDevice.authList = value
      this.currentDevice.permissionTr = this.translatePermission(this.currentDevice)
      this.setPermissionVisible = false
    },
    openTimePopup(device) {
      this.currentDevice = device

      this.setTimeVisible = true
    },
    changeTime(value) {
      this.setTimeVisible = false
      this.currentDevice.trustDuration = value
      this.currentDevice.hostingTimeTr = this.translateTime(this.currentDevice)
    },
    openSitePopup() {
      this.sitePopupVisible = true
    },
    selectSite(site) {
      this.currentSite = site

      this.queryDeviceInSite(site)
    },
    async queryDeviceInSite(site) {
      let { data: deviceList } = await getInstallerHostingDevice({
        siteId: site.siteId
      })

      this.deviceList = deviceList?.map(device => {
        const newDevice = { ...device }
        newDevice.authList = ['config', 'rec', 'live']
        newDevice.trustDuration = 0
        newDevice.permissionTr = this.translatePermission(newDevice)
        newDevice.hostingTimeTr = this.translateTime(newDevice)
        newDevice.checked = false

        return newDevice
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  overflow: hidden;
  position: relative;

  .tvt-better-scroll {
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 44px);
    padding-bottom: 80px;
    overflow: auto;
    box-sizing: border-box;
    margin-top: 10px;
    .tvt-better-scroll {
      height: calc(100% - 60px);
      overflow: auto;
    }
    .card {
      width: calc(100% - 16px);
      border-radius: 8px;
      overflow: hidden;
      background-color: transparent;
      margin: auto;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25098);
    }
  }
  .site-list {
    width: calc(100% - 20px);
    margin: 0 auto;
    .site-header {
      display: flex;
      align-items: center;
      height: 60px;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
    }
    .site-header-left {
      display: flex;
      align-items: center;
      width: 50%;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      .site-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
    }
    .site-tool {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 50%;
      .site-name {
        max-width: calc(100% - 30px);
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: right;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-right: 6px;
      }
      .site-tool-icon {
        width: 24px;
        height: 24px;
      }
    }
    .device-header {
      box-sizing: border-box;
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: start;
      align-items: center;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);

      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        width: 100%;
      }
      .img-icon {
        width: 24px;
        margin-right: 6px;
      }
    }
    .device-content {
      padding-left: 30px;
      .device-permission {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        height: 40px;
        box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
      }
      .device-permission-title {
        align-self: stretch;
        color: var(--icon-color-primary, #2b2b2b);
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-permission-info {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
      }
      .device-content-icon {
        width: 24px;
        height: 24px;
      }
    }
  }
  .empty-list {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-sizing: border-box;
    margin-top: 30%;

    .empty-img {
      width: 120px;
      margin-bottom: 25px;
    }
    .empty-info {
      color: var(--text-color-placeholder, #a3a3a3);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }

  .footer-btn-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding-bottom: 10px;
    background-color: var(--bg-color-white, #ffffff);
  }
  .footer-btn {
    display: block;
    margin: 0 auto;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
  }
}
</style>
<style lang="scss">
.service-detail-site-popup-container {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);

  .site-change-item {
    height: 52px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    background: #ffffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-primary, #1a1a1a);
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    background-color: var(--bg-color-white, #ffffff);

    &.active {
      color: var(--brand-bg-color-default, #3277fc);
    }
  }
  .cancel-btn {
    margin-top: 8px;
  }
}
.service-detail-change-name-popup-container {
  height: 196px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 16px;
    background-color: var(--bg-color-white, #ffffff);
    .cancel {
      color: var(--text-color-secondary, #50546d);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
    .title {
      color: var(--text-color-primary, #101d34);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .confirm {
      color: var(--brand-bg-color-active, #1d71f3);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      &.disabled {
        color: var(--text-color-placeholder, #a3a3a3);
      }
    }
  }
  .content {
    width: 100%;
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content-input {
    width: 300px;
    padding: 14px 0;
    color: var(--text-color-primary, #101d34);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  }
}
</style>
