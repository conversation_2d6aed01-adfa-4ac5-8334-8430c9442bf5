<template>
  <div class="user-card-wrapper">
    <div class="user-card-content">
      <div class="user-card-box">
        <div class="user-card-title">{{ $t('introduceText') }}</div>
        <div :class="['user-card-name', 'text-over-ellipsis2', isZh ? 'user-card-name-zh' : 'user-card-name-en']">
          {{ installerInfo.installerCoName || '' }}
        </div>
        <div class="user-card-info">
          <div class="user-card-icon">
            <img
              alt="avatar"
              :src="installerInfo.logo ? installerInfo.logo : require('@/assets/img/common/trusteeship/avatar.png')"
            />
          </div>
          <div class="user-card-right">
            <div class="card-info-line">
              <img class="card-info-icon" alt="email" :src="require('@/assets/img/common/email.png')" />
              <div class="card-info-text">{{ installerInfo.email || '' }}</div>
            </div>
            <div class="card-info-line">
              <img class="card-info-icon" alt="tel" :src="require('@/assets/img/common/tel.png')" />
              <div class="card-info-text">{{ installerInfo.mobile || '' }}</div>
            </div>
            <div class="card-info-line">
              <img class="card-info-icon" alt="address" :src="require('@/assets/img/common/address.png')" />
              <div class="card-info-text">{{ installerInfo.addr || '' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'UserCard',
  components: {},
  props: {
    installerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    handleClick() {
      this.$emit('click')
    }
  }
}
</script>
<style lang="scss" scoped>
.user-card-wrapper {
  width: 100%;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  .user-card-content {
    width: calc(100% - 16px);
    max-width: 359px;
    // width: 359px;
    height: 160px;
    border-radius: 10px;
  }
  .user-card-box {
    width: 100%;
    // width: 359px;
    height: 160px;
    background-image: url('@/assets/img/common/trusteeship/bind_bg.png');
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
    background-size: 100%;
    .user-card-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      padding-top: 23px;
    }
    .user-card-name {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      margin: 5px 0px 10px 0px;
      padding: 0px 30px 0px 130px;
      box-sizing: border-box;
    }
    .bind-card-name-zh {
      word-break: break-all;
    }
    .bind-card-name-en {
      word-break: normal;
    }
    .user-card-info {
      width: 100%;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      .user-card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .user-card-right {
        width: 158px;
        height: 60px;
      }
      .card-info-line {
        width: 100%;
        height: 20px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .card-info-icon {
          width: 16px;
          height: 16px;
        }
        .card-info-text {
          width: 100%;
          padding-left: 8px;
          box-sizing: border-box;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-family: 'Source Han Sans CN', sans-serif;
          font-size: var(--font-size-text-size, 12px);
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }
  }
  .user-card-body {
    width: 100%;
    // width: 359px;
    height: 140px;
    border-radius: 0px 0px 10px 10px;
    .bind-device-content {
      width: 100%;
      height: 60px;
      padding: 18px 28px;
      box-sizing: border-box;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .user-card-btn {
      padding-top: 16px;
      text-align: center;
      .footer-btn {
        width: 327px;
        height: 40px;
        border-radius: 23px;
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
