# AccountDetails.vue 退出登录功能实现

## 功能概述

为AccountDetails.vue组件实现了Risco系统的退出登录功能，直接清除store中的认证信息，无需调用API接口。

## 实现特点

### 🔄 智能退出逻辑
- **Risco系统**：直接清除store中的riscoLoginInfo和所有面板数据
- **其他系统**：清除所有面板相关数据
- **统一体验**：所有系统都跳转到登录页面

### 🎯 核心功能

#### 1. 退出确认对话框
```vue
<van-dialog
  v-model="showLogoutDialog"
  :title="$t('confirmLogout')"
  :message="$t('confirmLogoutTips')"
  show-cancel-button
  @confirm="confirmLogout"
  @cancel="showLogoutDialog = false"
/>
```

#### 2. 智能退出处理
```javascript
async confirmLogout() {
  this.showLogoutDialog = false
  
  try {
    this.$loading.show()
    
    if (this.isRiscoSystem) {
      // Risco系统：直接清除登录信息，无需调用API
      await this.performRiscoLogout()
    } else {
      // 其他系统：只清除本地数据
      await this.clearAllPanelData()
    }
    
    this.$loading.hide()
    this.$toast.success(this.$t('loggedOutSuccess'))
    
    // 跳转到登录页面
    this.$router.replace('/alarmSystem/login')
  } catch (error) {
    this.$loading.hide()
    console.error('Logout error:', error)
    this.$toast.fail(this.$t('loginFailed'))
  }
}
```

#### 3. Risco专用退出逻辑
```javascript
async performRiscoLogout() {
  try {
    // 清除Risco登录信息
    await this.clearRiscoLoginInfo()
    
    // 清除所有面板相关数据
    await this.clearAllPanelData()
    
    console.log('✅ Risco用户退出登录成功')
  } catch (error) {
    console.error('Failed to clear Risco login info:', error)
    // 即使清除失败，也要确保跳转到登录页
    throw error
  }
}
```

## 技术实现

### 导入的Vuex功能
```javascript
import { mapState, mapActions, mapGetters } from 'vuex'

// 在computed中
...mapState('alarmSystem', ['systemType', 'userInfo']),
...mapGetters('alarmSystem', ['isRiscoSystem']),

// 在methods中
...mapActions('alarmSystem', ['clearRiscoLoginInfo', 'clearAllPanelData']),
```

### 数据清除流程
1. **用户确认退出** → 显示确认对话框
2. **系统类型判断** → 根据isRiscoSystem决定清除策略
3. **Risco系统清除** → 清除riscoLoginInfo + 清除所有面板数据
4. **其他系统清除** → 只清除面板数据
5. **localStorage清除** → 自动清除localStorage缓存
6. **页面跳转** → 跳转到登录页面

## 用户体验

### 🎨 交互流程
1. 用户点击"退出登录"按钮
2. 显示确认对话框："确认退出登录吗？"
3. 用户确认后显示loading状态
4. 清除认证信息和缓存数据
5. 显示成功提示："退出登录成功"
6. 自动跳转到登录页面

### 🛡️ 错误处理
- **清除失败**：显示错误提示，但仍然跳转到登录页
- **网络异常**：不影响本地数据清除
- **异常安全**：确保用户能够正常退出

## 国际化支持

使用现有的翻译key：
- `confirmLogout`: "确认退出登录" / "Confirm Logout"
- `confirmLogoutTips`: "确认退出登录吗？" / "Are you sure you want to log out?"
- `loggedOutSuccess`: "退出登录成功" / "Logged out successfully"
- `loginFailed`: "登录失败" / "Login failed"

## 与其他组件的区别

### AccountDetails.vue vs PanelSettings.vue

| 特性 | AccountDetails.vue | PanelSettings.vue |
|------|-------------------|-------------------|
| 退出类型 | 用户登录退出 | 面板登录退出 |
| API调用 | 无需API | 需要调用退出API |
| 清除范围 | 用户信息 + 面板数据 | 面板数据 |
| 跳转目标 | 登录页面 | 面板列表页 |

## 测试验证

### 手动测试步骤
1. 登录Risco系统
2. 进入账户详情页面
3. 点击"退出登录"按钮
4. 确认退出操作
5. 验证数据清除和页面跳转

### 验证要点
- [ ] 确认对话框正确显示
- [ ] loading状态正确显示
- [ ] store中的riscoLoginInfo被清除
- [ ] localStorage缓存被清除
- [ ] 成功提示正确显示
- [ ] 正确跳转到登录页面
- [ ] 错误情况下的异常处理

## 代码质量

### 符合项目规范
- ✅ 使用mapActions而非直接调用$store.dispatch
- ✅ 遵循Vue组件标准结构
- ✅ 完整的错误处理机制
- ✅ 国际化支持
- ✅ 移动端友好的交互设计
- ✅ 代码注释清晰

### 性能优化
- ✅ 异步操作使用async/await
- ✅ 适当的loading状态管理
- ✅ 错误边界处理
- ✅ 内存清理完整

这个实现为Risco系统提供了简洁高效的退出登录功能，符合项目的设计原则和用户体验要求。