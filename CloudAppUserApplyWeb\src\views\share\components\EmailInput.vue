<template>
  <div>
    <van-field
      class="input-item"
      v-model.trim="email"
      :placeholder="$t('enterMemberEmail')"
      maxlength="50"
      @blur="e => validateEmail()"
    >
      <template #right-icon>
        <span class="cruise-line-close">
          <theme-image
            alt="clear"
            imageName="input_close.png"
            v-if="email"
            @click="clearEmail"
            style="width: 20px; height: 20px; margin-right: 14px"
          />
          <theme-image alt="scan" imageName="scan_code.png" @click="handleScan" />
        </span>
      </template>
    </van-field>
    <span class="input-error-msg" v-show="emailErrorMsg"> {{ emailErrorMsg }} </span>
  </div>
</template>
<script>
import { validateEmail } from '@/utils/validate'
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'EmailInput',
  components: {
    ThemeImage
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      email: '',
      emailErrorMsg: ''
    }
  },
  computed: {},
  watch: {
    email() {
      this.$emit('input', this.email)
    },
    value: {
      handler() {
        this.resolveValue()
      },
      immediate: true
    }
  },
  methods: {
    resolveValue() {
      if (this.value) {
        this.email = this.value
      }
    },
    validateEmail(validateEmpty = false) {
      if (this.email === '') {
        if (validateEmpty) {
          this.emailErrorMsg = this.$t('emailNameNotEmpty')
          return false
        }
        this.emailErrorMsg = ''
        return true
      }
      if (!validateEmail(this.email)) {
        this.emailErrorMsg = this.$t('emailNameError')
        return false
      }
      this.emailErrorMsg = ''
      return true
    },
    clearEmail() {
      this.email = ''
      this.emailErrorMsg = ''
    },
    handleScan() {
      this.$emit('handleScan')
    }
  }
}
</script>
<style lang="scss" scoped>
.input-item {
  width: 100%;
  height: 52px;
  line-height: 52px;
  padding: 0px;
  font-size: var(--font-size-body2-size, 14px);
  margin: auto;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    box-sizing: border-box;
    width: 100%;
    pointer-events: none;
    left: 0px;
    bottom: 0px;
    border-bottom: 1px solid var(--outline-color-primary, #ebedf0);
    transform: scaleY(0.5);
  }

  &::v-deep .van-field__label {
    width: unset;
  }

  .cruise-line-close {
    display: flex;
    align-items: center;
    .theme-image-container {
      width: 24px;
      height: 24px;
    }
    img {
      width: 24px;
      height: 24px;
    }
  }
}
.input-error-msg {
  display: block;
  width: 100%;
  padding-top: 10px;
  margin-bottom: auto;
  font-size: var(--font-size-text-size, 12px);
  font-weight: 400;
  color: var(--error-bg-color-default, #ff0000);
}
</style>
