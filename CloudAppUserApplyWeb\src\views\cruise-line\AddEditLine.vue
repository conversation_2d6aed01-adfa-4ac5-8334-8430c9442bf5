<template>
  <div class="cruise-line-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div :class="['cruise-line-content', type === 'editCruiseLine' ? 'edit-cruise-line-content' : '']">
      <div class="cruise-line-list">
        <div class="cruise-line-line">
          <div class="cruise-line-left">
            <div class="cruise-line-title">{{ $t('cruiseLineName') }}</div>
          </div>
          <div class="cruise-line-right">
            <div class="cruise-line-text">
              <span class="preset-ellipsis-text">{{ curiseRecord.name }}</span>
              <img
                @click="handleEditName"
                class="arrow-img"
                alt=""
                :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="add-preset-point">
        <div class="preset-label">
          {{ $t('presetPoint') }}
          ({{ curiseRecord && curiseRecord.presetPointList ? curiseRecord.presetPointList.length : 0 }})
        </div>
        <div @click="addPoint"><van-icon name="plus" class="preset-add-icon" /></div>
      </div>
      <div class="point-line-content">
        <div class="point-line-list" v-if="!curiseLoading || (presetPointList && presetPointList.length > 0)">
          <draggable v-model="presetPointList" group="people" @start="drag = true" @end="drag = false" handle=".handle">
            <van-swipe-cell
              v-for="(item, index) in presetPointList"
              :key="item.key || generatePersetKey(item, index)"
              :name="item.key || generatePersetKey(item, index)"
            >
              <div class="point-line-wrapper">
                <div class="point-line-left">
                  <div class="point-line-title">{{ item.name }}</div>
                  <div class="point-line-text">{{ $t('speed') }}：{{ item.speed }}</div>
                  <div class="point-line-text">
                    {{ $t('holdTime') }}：{{ item.holdTime | holdTimeLabel(allHoldTimeList) }}
                  </div>
                </div>
                <div class="point-line-right handle">
                  <div
                    class="ellipsis-img"
                    :style="{
                      backgroundImage: `url(${require(`@/assets/img/${appStyleFlag}/${uiStyleFlag}/ellipsis.png`)})`
                    }"
                  ></div>
                </div>
              </div>
              <template #right>
                <van-button square type="primary" class="swipe-right-btn" @click="() => handleEdit(item, index)">
                  <img
                    class="refuse-img"
                    alt=""
                    :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/edit.png')"
                  />
                </van-button>
                <van-button square type="danger" class="swipe-right-btn" @click="() => handleDelete(item, index)">
                  <img
                    class="refuse-img"
                    alt=""
                    :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/delete.png')"
                  />
                </van-button>
              </template>
            </van-swipe-cell>
          </draggable>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img alt="" :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')" />
          </div>
          <div class="no-data-btn">
            <div class="add-point-btn" @click="addPoint">{{ $t('add') }}</div>
          </div>
        </div>
      </div>
      <div class="cruise-line-footer" v-if="type === 'addCruiseLine'">
        <div class="footer-btn" @click="handleConfirm">{{ $t('confirm') }}</div>
      </div>
    </div>
    <!-- 编辑巡航线名称 -->
    <edit-line-name ref="editLineName" :name="curiseRecord.name" @confirm="lineNameConfirm"></edit-line-name>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import EditLineName from './EditLineName.vue'
import { mapState, mapMutations } from 'vuex'
import { appBack, onNotificationToApp, appRequestDevice } from '@/utils/appbridge'
import { transformXml, getUrlQuery, debounce } from '@/utils/common'
import { urlChlPresetList } from '@/api/presetPoint'
import {
  urlChlCruiseList,
  urlChlCruise,
  urlCreateChlCruise,
  urlEditChlCruise,
  urlPtzGetCruise,
  urlPtzGetPresets,
  urlPtzGetCruises,
  urlPtzModifyCruise,
  urlPtzAddCruise,
  urlPtzGetCaps
} from '@/api/cruiseLine'
export default {
  name: 'addEditLine',
  components: {
    NavBar,
    EditLineName
  },
  data() {
    return {
      cruiseIndex: '',
      type: 'addCruiseLine',
      curiseLoading: false,
      defaultHoldTime: null // 预置点默认时间
    }
  },
  created() {
    this.cruiseIndex = this.$route.params.id || null
    this.SET_CRUISES_INDEX(this.$route.params.id || null)
    this.type = this.$route.params.id ? 'editCruiseLine' : 'addCruiseLine'
    if (this.$route.params && this.$route.params.id) {
      this.$route.meta.title = this.$t('edit')
    } else {
      this.$route.meta.title = this.$t('addPoint')
    }
  },
  async mounted() {
    const json = getUrlQuery(window.location.href)
    let {
      devId,
      devType,
      chlId,
      // devCapability,
      chlCapability
    } = json
    this.SET_DEVID(devId)
    this.SET_DEV_TYPE(devType)
    // devCapability = devCapability ? JSON.parse(decodeURIComponent(decodeURIComponent(devCapability))) : null
    chlCapability = chlCapability ? JSON.parse(decodeURIComponent(decodeURIComponent(chlCapability))) : null
    this.SET_CHL_CAPABILITY(chlCapability)
    this.bindState = json.bindState
    chlId = decodeURIComponent(decodeURIComponent(chlId))
    this.SET_CHLID(chlId)
    if (!this.initStatus) {
      this.getChlPresetList()
      this.getChlCruiseList()
      this.SET_INIT_STATUS(true)
      if (this.type === 'editCruiseLine') {
        // 需要额外请求当前巡航线的信息
        this.getChlCruise()
      }
    }
    if (json.devType === '1') {
      // IPC才请求预置点时间等信息
      this.getPtzCap()
    }
  },
  computed: {
    ...mapState('app', ['style', 'language', 'appType']),
    ...mapState('cruiseLine', [
      'initStatus',
      'devType',
      'devId',
      'chlId',
      'chlCapability',
      'curiseRecord',
      'allHoldTimeList',
      'holdTimeList',
      'ipcHoldTimeList',
      'pointList',
      'cruisesList'
    ]),
    presetPointList: {
      get() {
        const presetPointList = this.curiseRecord.presetPointList.slice()
        return presetPointList
      },
      set(value) {
        const { name, index } = this.curiseRecord
        const newValue = value.slice().map((item, index) => ({ ...item, key: this.generatePersetKey(item, index) }))
        if (this.type === 'editCruiseLine') {
          this.SET_CRUISE_RECORD({
            name,
            index,
            presetPointList: newValue
          })
          const params = { name, index, presetPointList: newValue }
          this.$nextTick(() => {
            // 使用防抖
            this.debounceCruiseLine('edit', params)
          })
        } else {
          this.SET_CRUISE_RECORD({
            name,
            index,
            presetPointList: newValue
          })
        }
      }
    },
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  filters: {
    // holdTime转国际化文字
    holdTimeLabel(val, allHoldTimeList) {
      if (val && allHoldTimeList) {
        const item = allHoldTimeList.find(item => item.value === Number(val))
        if (item) {
          return item.label
        }
      }
      return ''
    }
  },
  methods: {
    ...mapMutations('cruiseLine', [
      'SET_INIT_STATUS',
      'SET_DEV_TYPE',
      'SET_DEVID',
      'SET_CHL_CAPABILITY',
      'SET_CRUISES_INDEX',
      'SET_CHLID',
      'SET_POINT_LIST',
      'SET_CRUISES_LIST',
      'SET_CRUISE_RECORD',
      'SET_POINT_RECORD',
      'SET_HOLD_TIME_LIST',
      'SET_IPC_HOLD_TIME_LIST'
    ]),
    // 根据预置点列表生成唯一的key,确保不重复
    generatePersetKey(item, index) {
      const key = `${item.name}-${index}-${this.generateUniqueId()}`
      return key
    },
    // 生成一个字符串，其中包含10个字符，由数字和小写字母组成
    generateUniqueId() {
      return new Date().getTime().toString(36)
    },
    debounceCruiseLine: debounce(function (operateType, params, callback) {
      this.operateCruiseLine(operateType, params, callback)
    }, 200),
    back() {
      // 先通知APP
      onNotificationToApp(this.type === 'editCruiseLine' ? 'kEditLineNotifycation' : 'kAddLineNotifycation')
      // 再走返回
      appBack()
    },
    // 获取当前通道下所有的预置点列表
    getChlPresetList() {
      const that = this
      const req = {
        devId: this.devId,
        url: 'queryChlPresetList',
        params: urlChlPresetList(this.chlId)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzGetPresets_I'
        req.params = urlPtzGetPresets()
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (that.devType === '1') {
            // IPC设备--单独处理
            const config = xmlObject.config
            if (config && config.presetInfo) {
              const { item = [] } = config.presetInfo
              let presets = []
              if (Array.isArray(item)) {
                // 数组说明是多个
                presets = item
              } else if (typeof item === 'object' && item !== null) {
                //对象说明是单个对象
                presets = [item]
              }
              // 遍历presets，生成已有的预置点列表
              const pointList = []
              presets.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_id']
                }
                pointList.push(temp)
              })
              that.SET_POINT_LIST(pointList)
            }
          } else {
            if (xmlObject.response.status == 'success') {
              // 处理设备
              const content = xmlObject.response.content
              let presets = []
              if (content && content.presets) {
                const { item = [] } = content.presets
                if (Array.isArray(item)) {
                  // 数组说明是多个
                  presets = item
                } else if (typeof item === 'object' && item !== null) {
                  //对象说明是单个对象
                  presets = [item]
                }
              }
              // 遍历presets，生成已有的预置点列表
              const pointList = []
              presets.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_index']
                }
                pointList.push(temp)
              })
              that.SET_POINT_LIST(pointList)
            }
          }
        }
      })
    },
    // 获取当前通道下所有的巡航线列表
    getChlCruiseList() {
      const req = {
        devId: this.devId,
        url: 'queryChlCruiseList',
        params: urlChlCruiseList(this.chlId)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzGetCruises_I'
        req.params = urlPtzGetCruises()
      }
      const that = this
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (that.devType === '1') {
            // IPC设备--单独处理
            const config = xmlObject.config
            let cruises = []
            if (config && config.cruiseInfo) {
              const { item = [] } = config.cruiseInfo
              if (Array.isArray(item)) {
                // 数组说明是多个
                cruises = item
              } else if (typeof item === 'object' && item !== null) {
                //对象说明是单个对象
                cruises = [item]
              }
              // 遍历cruises，生成已有的巡航线列表
              const cruisesList = []
              cruises.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_id']
                }
                cruisesList.push(temp)
              })
              that.SET_CRUISES_LIST(cruisesList)
              if (that.type === 'addCruiseLine') {
                // 新增时自动添加一个巡航线
                that.SET_CRUISE_RECORD({
                  name: null,
                  index: null,
                  presetPointList: []
                })
              }
            }
          } else {
            if (xmlObject.response.status == 'success') {
              // 处理设备
              const { content, type } = xmlObject.response
              let cruises = []
              if (content && content.cruises) {
                const { item } = content.cruises
                if (Array.isArray(item)) {
                  // 数组说明是多个
                  cruises = item
                } else if (typeof item === 'object' && item !== null) {
                  //对象说明是单个对象
                  cruises = [item]
                }
              }
              if (type && type.cruisePresetHoldTime) {
                // 有持续时间则从总的持续时间中过滤出对应的选项
                const holdTimeSet = new Set(type.cruisePresetHoldTime.split(',').map(item => Number(item)))
                const holdTimeList = that.allHoldTimeList.filter(item => holdTimeSet.has(item.value))
                that.SET_HOLD_TIME_LIST(holdTimeList)
              }
              // 遍历cruises，生成已有的巡航线列表
              const cruisesList = []
              cruises.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_index']
                }
                cruisesList.push(temp)
              })
              that.SET_CRUISES_LIST(cruisesList)
              if (that.type === 'addCruiseLine') {
                // 新增时自动添加一个巡航线名称
                that.SET_CRUISE_RECORD({
                  name: `cruise${cruisesList.length + 1}`,
                  index: null,
                  presetPointList: []
                })
              }
            }
          }
        }
      })
    },
    // 获取IPC时间选项
    getPtzCap() {
      const req = {
        devId: this.devId,
        url: 'PtzGetCaps_I',
        params: urlPtzGetCaps()
      }
      const that = this
      appRequestDevice(req, function (res) {
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          const { caps } = xmlObject.config
          const { cruisePresetMinHoldTime, cruisePresetMaxHoldTime, cruisePresetDefaultHoldTime } = caps
          // 过滤IPC预置点默认时间和时间选项
          that.defaultHoldTime = Number(cruisePresetDefaultHoldTime['__text'])
          const ipcHoldTimeList = that.allHoldTimeList.filter(
            item => item.value >= cruisePresetMinHoldTime['__text'] && item.value <= cruisePresetMaxHoldTime['__text']
          )
          that.SET_IPC_HOLD_TIME_LIST(ipcHoldTimeList)
        }
      })
    },
    // 获取当前巡航线信息
    getChlCruise() {
      this.curiseLoading = false
      const that = this
      const req = {
        devId: this.devId,
        url: 'queryChlCruise',
        params: urlChlCruise(this.chlId, this.cruiseIndex)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzGetCruise_I'
        req.params = urlPtzGetCruise(this.cruiseIndex)
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        try {
          if (resData.code == 200) {
            const xmlObject = transformXml(resData.body)
            if (that.devType === '1') {
              // IPC设备--单独处理
              const cruiseInfo = xmlObject.cruiseInfo
              let presets = []
              if (cruiseInfo) {
                const { name, id } = cruiseInfo
                const { presetInfo = {} } = cruiseInfo
                const { item = [] } = presetInfo
                if (Array.isArray(item)) {
                  // 数组说明是多个
                  presets = item
                } else if (typeof item === 'object' && item !== null) {
                  //对象说明是单个对象
                  presets = [item]
                }
                // 遍历presets，生成已有的预置点列表
                const presetPointList = []
                presets.forEach(pointItem => {
                  // 兼容鱼眼和球机两种方式
                  let speed = pointItem.speed
                  if (typeof speed === 'object') {
                    speed = pointItem.speed['__text']
                  }
                  let holdTime = pointItem.holdTime
                  if (typeof holdTime === 'object') {
                    holdTime = pointItem.holdTime['__text']
                  }
                  const temp = {
                    name: pointItem['name']['#cdata-section'],
                    index: pointItem['_id'],
                    speed,
                    holdTime
                  }
                  presetPointList.push(temp)
                })
                that.SET_CRUISE_RECORD({
                  name: name['#cdata-section'] || '',
                  index: id['__text'],
                  presetPointList
                })
              }
            } else {
              if (xmlObject.response.status == 'success') {
                // 处理设备
                const content = xmlObject.response.content
                const { name, index } = content
                let presets = []
                if (content && content.presets) {
                  const { item } = content.presets
                  if (Array.isArray(item)) {
                    // 数组说明是多个
                    presets = item
                  } else if (typeof item === 'object' && item !== null) {
                    //对象说明是单个对象
                    presets = [item]
                  }
                }
                // 遍历presets，生成已有的预置点列表
                const presetPointList = []
                presets.forEach(pointItem => {
                  const temp = {
                    name: pointItem['name']['#cdata-section'],
                    index: pointItem['_index'],
                    speed: pointItem.speed,
                    holdTime: pointItem.holdTime
                  }
                  presetPointList.push(temp)
                })
                that.SET_CRUISE_RECORD({
                  name: name['#cdata-section'] || '',
                  index,
                  presetPointList
                })
              }
            }
          }
          that.curiseLoading = true
        } catch (err) {
          that.curiseLoading = true
          console.error(err)
        }
      })
    },
    handleEditName() {
      this.$refs.editLineName.show = true // 编辑区域名称
    },
    lineNameConfirm(val) {
      this.$refs.editLineName.show = false
      const curiseRecord = {
        ...this.curiseRecord,
        name: val
      }
      if (this.type === 'editCruiseLine') {
        const { index, presetPointList = [] } = this.curiseRecord
        const params = { name: val, index, presetPointList }
        const callback = () => {
          this.getChlCruise() // 重新获取巡航线信息
        }
        this.$nextTick(() => {
          this.operateCruiseLine('edit', params, callback)
        })
      } else {
        this.SET_CRUISE_RECORD(curiseRecord)
      }
    },
    // 添加预置点
    addPoint() {
      // 判断当前巡航线下预置点是否已经有16个，有则提示数量限制
      const { presetPointList } = this.curiseRecord
      if (presetPointList && presetPointList.length >= 16) {
        this.$toast(this.$t('presetPointLimit', [16]))
        return
      }
      // 判断是否有预置点可供选择
      if (this.pointList && this.pointList.length) {
        const { name, index } = this.pointList[0]
        if (this.devType === '1') {
          // IPC不支持编辑速度，速度固定为1
          const holdTime = this.defaultHoldTime ? this.defaultHoldTime : this.ipcHoldTimeList[0].value
          this.SET_POINT_RECORD({ name, index, speed: 1, holdTime }) // 默认用第一个
        } else {
          const holdTime = this.defaultHoldTime ? this.defaultHoldTime : this.holdTimeList[0].value
          this.SET_POINT_RECORD({ name, index, speed: 5, holdTime }) // 默认用第一个
        }
        this.$router.push({ name: 'addEditCruisePoint', params: { cruiseType: this.type, type: 'add' } })
      } else {
        // 提示无预置点可供选择，请先创建
        this.$toast(this.$t('noPointSelect'))
      }
    },
    // 编辑预置点
    handleEdit(record, index) {
      this.SET_POINT_RECORD({
        ...record,
        speed: Number(record.speed),
        holdTime: Number(record.holdTime),
        pointIndex: index,
        type: 'edit'
      }) // 巡航线中可以添加重复的预置点，所以加入pointIdex标识预置点在列表中的位置
      this.$router.push({ name: 'addEditCruisePoint', params: { cruiseType: this.type, type: 'edit' } })
    },
    // 删除预置点
    handleDelete(item, index) {
      // 判断是否有预置点列表
      const { presetPointList = [] } = this.curiseRecord
      if (presetPointList.length <= 1) {
        this.$toast(this.$t('presetPointEmpty'))
        return false
      }
      const tips = {
        message: this.$t('deletePointConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      this.$dialog
        .confirm(tips)
        .then(() => {
          // on confirm
          const list = presetPointList.slice()
          // const index = list.findIndex(listItem => listItem.value === item.value)
          list.splice(index, 1)
          if (this.type === 'editCruiseLine') {
            const { name, index } = this.curiseRecord
            const params = { name, index, presetPointList: list }
            const callback = () => {
              this.SET_CRUISE_RECORD({
                ...this.curiseRecord,
                presetPointList: list
              })
            }
            this.$nextTick(() => {
              this.operateCruiseLine('delete', params, callback)
            })
          } else {
            this.SET_CRUISE_RECORD({
              ...this.curiseRecord,
              presetPointList: list
            })
          }
        })
        .catch(() => {
          // on cancel
          //   instance.open()
        })
    },
    handleConfirm() {
      // 判断是新增还是编辑
      const { name, index, presetPointList = [] } = this.curiseRecord
      // 判断是否填写了名字
      if (this.devType !== '1') {
        // IPC设备可以不要名字，NVR则需要填写名称
        if (!name || !name.trim()) {
          this.$toast(this.$t('pleaseEnterLine'))
          return false
        }
      }
      // 判断是否有预置点列表
      if (presetPointList.length === 0) {
        this.$toast(this.$t('presetPointEmpty'))
        return false
      }
      // 不用考虑跟自己重名的问题
      const nameSet = new Set(
        this.cruisesList.filter(item => item.index !== this.curiseRecord.index).map(item => item.name)
      )
      // 判断是否重名
      if (nameSet.has(name)) {
        this.$toast(this.$t('lineNameExist'))
        return false
      }
      const that = this
      const req = {
        devId: this.devId,
        url: this.type === 'editCruiseLine' ? 'editChlCruise' : 'createChlCruise',
        params:
          this.type === 'editCruiseLine'
            ? urlEditChlCruise(this.chlId, index, name, presetPointList)
            : urlCreateChlCruise(this.chlId, name, presetPointList)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = this.type === 'editCruiseLine' ? 'PtzModifyCruise_I' : 'PtzAddCruise_I'
        req.params =
          this.type === 'editCruiseLine'
            ? urlPtzModifyCruise(index, name, presetPointList)
            : urlPtzAddCruise(name, presetPointList)
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          let resFlag = false
          if (that.devType === '1') {
            // IPC设备
            if (xmlObject.config._status === 'success') {
              resFlag = true
            }
          } else {
            if (xmlObject.response.status == 'success') {
              resFlag = true
            }
          }
          if (resFlag) {
            // 消息提示的回调
            const closeFn = () => {
              // 先通知APP
              onNotificationToApp(that.type === 'editCruiseLine' ? 'kEditLineNotifycation' : 'kAddLineNotifycation')
              // 再走返回
              appBack()
            }
            // 添加/编辑成功后，返回
            if (that.type === 'editCruiseLine') {
              that.$toast({
                message: that.$t('editSuccess'),
                forbidClick: true,
                onClose: closeFn
              })
            } else {
              that.$toast({
                message: that.$t('addSuccess'),
                forbidClick: true,
                onClose: closeFn
              })
            }
          } else {
            if (that.type === 'editCruiseLine') {
              that.$toast(that.$t('editFail'))
            } else {
              that.$toast(that.$t('addFail'))
            }
          }
        }
      })
    },
    // 编辑巡航线
    operateCruiseLine(operateType, params, callback) {
      const that = this
      // 编辑直接发送请求，固化下来
      const { name, index, presetPointList = [] } = params
      const req = {
        devId: this.devId,
        url: 'editChlCruise',
        params: urlEditChlCruise(this.chlId, index, name, presetPointList)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzModifyCruise_I'
        req.params = urlPtzModifyCruise(index, name, presetPointList)
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          let resFlag = false
          if (that.devType === '1') {
            // IPC设备
            if (xmlObject.config._status === 'success') {
              resFlag = true
            }
          } else {
            if (xmlObject.response.status == 'success') {
              resFlag = true
            }
          }
          if (resFlag) {
            // 添加/编辑成功后，返回
            if (operateType === 'add') {
              that.$toast(that.$t('addSuccess'))
            } else if (operateType === 'edit') {
              that.$toast(that.$t('editSuccess'))
            } else if (operateType === 'delete') {
              that.$toast(that.$t('deleteSuccess'))
            }
            if (callback) callback()
          } else {
            if (operateType === 'add') {
              that.$toast(that.$t('addFail'))
            } else if (operateType === 'edit') {
              that.$toast(that.$t('editFail'))
            } else if (operateType === 'delete') {
              that.$toast(that.$t('deleteFail'))
            }
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.cruise-line-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.cruise-line-content {
  width: 100%;
  height: calc(100% - 130px);
  padding: 35px 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.edit-cruise-line-content {
  height: calc(100% - 44px);
}

.cruise-line-list {
  width: 100%;
  border-radius: 10px;
  .cruise-line-line {
    width: 100%;
    height: 46px;
    box-sizing: border-box;
    padding: 11px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    user-select: none !important; // 禁止IOS长按选中
    -webkit-user-select: none !important; // 禁止IOS长按选中
    -webkit-touch-callout: none;
    .cruise-line-left {
      height: 100%;
      display: flex;
      align-items: center;
      .cruise-line-title {
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        height: 100%;
      }
    }
    .cruise-line-right {
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      overflow: hidden;
      user-select: none !important; // 禁止IOS长按选中
      -webkit-user-select: none !important; // 禁止IOS长按选中
      -webkit-touch-callout: none;
      .cruise-line-text {
        width: 100%;
        height: 100%;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        text-align: right;
        padding-left: 20px;
        box-sizing: border-box;
      }
      .preset-ellipsis-text {
        width: calc(100% - 24px);
        line-height: 24px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.add-preset-point {
  height: 32px;
  line-height: 32px;
  padding-left: 15px;
  padding-right: 15px;
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  .preset-label {
    font-family: PingFangSC-Regular, sans-serif;
    font-weight: 400;
    font-size: var(--font-size-body2-size, 14px);
    line-height: 22px;
  }
}

.point-line-content {
  width: 100%;
  max-height: calc(100% - 60px);
  overflow-y: auto;
  .point-line-list {
    width: 100%;
    border-radius: 10px;
  }
  .point-line-wrapper {
    width: 100%;
    height: 98px;
    box-sizing: border-box;
    padding: 11px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .point-line-left {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      user-select: none !important; // 禁止IOS长按选中
      -webkit-user-select: none !important; // 禁止IOS长按选中
      -webkit-touch-callout: none !important; // 禁止IOS长按选中
      .point-line-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        height: 24px;
        line-height: 24px;
      }
      .point-line-text {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        height: 22px;
        line-height: 22px;
      }
    }
    .point-line-right {
      width: 64px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      user-select: none !important; // 禁止IOS长按选中
      -webkit-user-select: none !important; // 禁止IOS长按选中
      -webkit-touch-callout: none !important; // 禁止IOS长按选中
    }
    .ellipsis-img {
      width: 24px;
      height: 24px;
      user-select: none !important; // 禁止IOS长按选中
      -webkit-user-select: none !important; // 禁止IOS长按选中
      -webkit-touch-callout: none;
    }
  }
}

.no-data {
  padding: 8px 15px;
  .no-data-img {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
    img {
      width: 120px;
      height: 123px;
    }
  }
  .no-data-btn {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .add-point-btn {
    width: 102px;
    height: 40px;
    border-radius: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFangSC-Regular, sans-serif;
    font-weight: 400;
    font-size: var(--font-size-body1-size, 16px);
  }
}

.cruise-line-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px 0;
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% - 32px);
    height: 46px;
    border-radius: 10px;
    text-align: center;
    color: var(--bg-color-white, #ffffff);
  }
}
.swipe-right-btn {
  height: 100%;
}
</style>
<style lang="scss">
.cruise-line-wrapper .nav-bar .nav-bar-center {
  font-size: var(--font-size-body1-size, 16px) !important;
  font-weight: 600;
}
</style>
