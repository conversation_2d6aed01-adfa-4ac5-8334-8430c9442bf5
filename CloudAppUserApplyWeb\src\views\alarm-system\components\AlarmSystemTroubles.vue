<template>
  <div class="alarm-box-wrapper alarm-system-troubles">
    <div class="troubles-header">
      <div class="troubles-title">{{ $t('alarmTrouble') }}</div>
      <theme-image class="expand-icon" imageName="arrow_right.png" alt="expand" @click="handleExpandClick" />
    </div>
    <div class="troubles-content">
      <!-- 无数据 -->
      <div v-if="!troubles.length" class="no-data-box">
        <span class="no-data-text">{{ $t('noData') }}</span>
      </div>
      <!-- 故障列表 -->
      <template v-else>
        <div
          v-for="trouble in troubles"
          :key="trouble.id"
          class="alarm-bottom-box trouble-item"
          @click="handleTroubleClick(trouble)"
        >
          <div class="trouble-icon">
            <theme-image class="trouble-indicator" imageName="alarm-system/trouble.png" alt="trouble" />
          </div>
          <div class="trouble-details">
            <div class="trouble-type">{{ trouble.type }}</div>
            <div class="alarm-sub-text trouble-time">{{ trouble.time }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { handleCommonError } from '@/utils/alarmSystem'

export default {
  name: 'AlarmSystemTroubles',
  computed: {
    ...mapGetters('alarmSystem', ['siteId', 'sessionId', 'systemType', 'latestTrouble']),
    // 从store中获取trouble数据
    troubles() {
      return this.latestTrouble ? [this.latestTrouble] : []
    },
    // 检查是否有足够的数据来执行API调用
    canFetchTroubles() {
      return this.siteId && this.sessionId
    }
  },
  created() {
    this.fetchTroubleData()
  },
  methods: {
    ...mapActions('alarmSystem', ['fetchTroubles']),
    // 获取故障数据
    async fetchTroubleData() {
      if (!this.canFetchTroubles) {
        console.warn('Missing required data for fetching troubles')
        return
      }
      try {
        await this.fetchTroubles({
          count: 100
        })
      } catch (error) {
        console.error('Fetch troubles failed:', error)
        handleCommonError(error)
      }
    },
    // 处理故障项点击
    handleTroubleClick(trouble) {
      this.$emit('trouble-click', trouble)
    },
    // 处理展开按钮点击，跳转到Troubles页面
    handleExpandClick() {
      // 使用computed属性获取参数，更可靠
      this.$router.push({
        path: '/alarmSystem/troubles',
        query: {
          systemType: this.systemType,
          siteId: this.siteId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-troubles {
  padding: 0px 16px;
  box-sizing: border-box;
  .troubles-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    .troubles-title {
      font-size: 16px;
      font-weight: 500;
    }
    .expand-icon {
      width: 24px;
      height: 24px;
    }
  }
  .troubles-content {
    height: 65px;
    .no-data-box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
      .no-data-text {
        font-size: 12px;
      }
    }
    .trouble-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      &:last-child {
        border-bottom: none;
      }
      .trouble-icon {
        margin-right: 12px;
        .trouble-indicator {
          width: 32px;
          height: 32px;
        }
      }
      .trouble-details {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        .trouble-type {
          height: 18px;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 5px;
        }
        .trouble-time {
          height: 18px;
          font-weight: 400;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
