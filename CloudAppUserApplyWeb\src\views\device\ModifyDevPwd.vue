<template>
  <div class="set-pwd-content">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <div class="set-pwd-warp">
      <van-form ref="form" class="set-pwd-box form">
        <van-field
          v-model="password"
          :placeholder="$t('enterNewPwd')"
          :type="passwordType"
          name="password"
          maxlength="16"
          @blur="handleBlur"
          @click-right-icon="showPassword('passwordType')"
        >
          <template #label>
            <div class="field-label">
              {{ $t('newPwd') }}
              <span>*</span>
            </div>
          </template>
          <template #right-icon>
            <theme-image v-if="passwordType === 'password'" alt="closeEye" imageName="eye_close.png" />
            <theme-image v-else alt="eye" imageName="eye.png" />
          </template>
        </van-field>
        <van-field
          v-model="confirmPassword"
          :placeholder="$t('enterConfirmPwd')"
          name="confirmPassword"
          :type="confirmPasswordType"
          maxlength="16"
          @click-right-icon="showPassword('confirmPasswordType')"
        >
          <template #label>
            <div class="field-label">
              {{ $t('confirmNewPassword') }}
              <span>*</span>
            </div>
          </template>
          <template #right-icon>
            <theme-image v-if="confirmPasswordType === 'password'" alt="closeEye" imageName="eye_close.png" />
            <theme-image v-else alt="eye" imageName="eye.png" />
          </template>
        </van-field>
      </van-form>
      <div class="pwd-tip">{{ levelText[pwdLevel] }}</div>
      <!-- <div class="psd-lever">
        <div class="level-none" v-for="index in 3" :key="index" :class="levelColor(index)"></div>
      </div> -->
    </div>
    <div class="bottom-fixed-box">
      <van-button class="bottom-btn" round block @click="onSubmit">{{ $t('confirm') }}</van-button>
    </div>
  </div>
</template>

<script>
const DEFAULT_IPC_PWD_REG = /[<>]/g
import NavBar from '@/components/NavBar'
import { resetPassword, appBack, updateDevicePassword } from '@/utils/appbridge'
import ThemeImage from '@/components/ThemeImage.vue'
import { validatePassword } from '@/utils/validate'
export default {
  name: 'modifyDevPwd',
  components: { NavBar, ThemeImage },
  data() {
    return {
      password: '',
      confirmPassword: '',
      passwordType: 'password',
      confirmPasswordType: 'password',
      level: 0,
      pwdLevel: 'weak',
      levelText: {
        medium: this.$t('mediumPwd'),
        strong: this.$t('strongPwd'),
        stronger: this.$t('strongerPwd')
      },
      pwdTips: {
        medium: this.$t('mediumPwdTips'),
        strong: this.$t('strongPwdTips'),
        stronger: this.$t('strongerPwdTips')
      }
    }
  },
  watch: {
    password: {
      handler(value) {
        this.level = this.checkPasswordLevel(value)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.pwdLevel = this.$route.params?.pwdLevel
  },
  methods: {
    // 密码格式校验
    validateForm() {
      const { password, confirmPassword } = this
      if (!password || (password && !password.trim())) {
        this.$toastFail(this.$t('emptyPsw'))
        return false
      } else if (password && this.pwdLevel !== 'weak' && !validatePassword(password, this.pwdLevel)) {
        this.$toastFail(this.pwdTips[this.pwdLevel])
      } else if (password && password !== confirmPassword) {
        this.$toastFail(this.$t('notMatchPsw'))
        return false
      } else {
        return true
      }
    },
    levelColor(index) {
      if (index === 1) {
        return `level-${this.level}`
      } else if (index === 2 && this.level > 1) {
        return `level-${this.level}`
      } else if (index === 3 && this.level > 2) {
        return `level-${this.level}`
      }
    },
    checkPasswordLevel(password) {
      if (password === null || password.length === 0) {
        return '0'
      }
      if (password.length < 8) {
        return '1'
      }
      let flag = 0
      const digitRegex = /\d/
      const lowerCaseRegex = /[a-z]/
      const upperCaseRegex = /[A-Z]/
      const specialCharRegex = /[^a-zA-Z0-9]/
      if (digitRegex.test(password)) {
        flag++
      }
      if (lowerCaseRegex.test(password)) {
        flag++
      }
      if (upperCaseRegex.test(password)) {
        flag++
      }
      if (specialCharRegex.test(password)) {
        flag++
      }
      return flag
    },
    handleBlur() {
      this.$refs.form.validate('confirmPassword')
    },
    onSubmit() {
      if (this.validateForm()) {
        const { sn, mac } = this.$route.params
        const params = { devId: sn, newPassword: this.password }
        const callback = data => {
          const obj = JSON.parse(data)
          console.log('obj', obj)
          const { code } = obj || {}
          if (code === 200) {
            const reqParams = { devId: sn, password: this.password, mac }
            updateDevicePassword(JSON.stringify(reqParams)) //同步密码
            this.$router.go(-1)
            this.$toastSuccess(this.$t('changeSuccessfully'))
          } else {
            this.$toastFail(this.$t(`errorCode.${code}`))
          }
        }
        resetPassword(params, callback)
      }
    },
    showPassword(key) {
      if (this[key] === 'password') {
        this[key] = 'text'
      } else if (this[key] === 'text') {
        this[key] = 'password'
      }
    },
    onKeyUp() {
      // 密码过滤掉<>字符，与设备端保持一致
      this.form.password = this.form.password.replace(DEFAULT_IPC_PWD_REG, '')
    },
    clickLeft() {
      appBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.set-pwd-content {
  height: 100%;
  box-sizing: border-box;
  background-color: var(--bg-color-white, #ffffff);
  position: relative;
  .set-pwd-warp {
    margin-top: 10px;
    padding: 0 16px;
    .set-pwd-box {
      .van-cell {
        font-size: var(--font-size-body1-size, 16px);
        padding-left: 0;
        padding-right: 0;
        align-items: center;
        border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
        .field-label {
          color: var(--text-color-primary, #101d34);
          font-size: var(--font-size-body1-size, 16px);
          line-height: 24px;
          > span {
            color: var(--error-bg-color-default, #ff3d3d);
            font-size: var(--font-size-body1-size, 16px);
            line-height: 24px;
          }
        }
        &::after {
          border: 0 !important;
        }
      }
      .van-field__control {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .van-field__right-icon {
        display: flex;
        align-items: center;
        > img {
          width: 24px;
          height: 24px;
          vertical-align: middle;
        }
      }
    }
  }
  .pwd-tip {
    margin-top: 16px;
    color: var(--text-color-primary, #82879b);
    font-size: var(--font-size-text-size, 12px);
    font-weight: 400;
    line-height: 20px;
    white-space: pre-line;
  }
  .psd-lever {
    display: flex;
    margin-top: 20px;
    justify-content: flex-start;
    .level-none {
      width: 80px;
      height: 6px;
      border-radius: 3px;
      flex-shrink: 0;
      border-radius: 100px;
      background: var(--outline-color-primary, #ebedf0);
      margin-right: 8px;
    }
    .level-1 {
      background: var(--error-bg-color-default, #ff3d3d);
    }
    .level-2,
    .level-3 {
      background: #ff9500;
    }
    .level-4 {
      background: var(--success-bg-color-default, #00c261);
    }
  }
  .bottom-fixed-box {
    // 固定在最下边的按钮样式
    position: absolute;
    text-align: center;
    bottom: 24px;
    padding: 8px 12px;
    width: 100%;
    box-sizing: border-box;
    .bottom-btn {
      background-color: var(--brand-bg-color-active, #1d71f3);
      color: var(--bg-color-white, #ffffff);
      .van-button__text {
        font-weight: 500;
      }
    }
  }
}
</style>
