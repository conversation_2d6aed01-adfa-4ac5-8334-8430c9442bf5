<template>
  <van-overlay :show="show">
    <van-loading type="spinner" color="var(--text-color-placeholder, #8f8e93)" size="30px" />
  </van-overlay>
</template>

<script>
export default {
  name: 'tvtLoading',
  data() {
    return {
      show: false
    }
  }
}
</script>

<style lang="scss" scoped>
.van-overlay {
  background: rgba(0, 0, 0, 0);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
