export default {
  upgrade: 'Cloud Upgrade',
  cancel: 'Cancel',
  confirm: 'Confirm',
  deviceUpdate: 'Device',
  cameraUpdate: 'Camera',
  allUpdate: 'Upgrade All',
  updateNow: 'Upgrade',
  currentVersion: 'Current Version',
  latestVersion: 'Latest Version',
  updateContent: 'Update Content',
  hasLatestVersion: 'Running the latest version',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Download Waiting',
  inprogress: 'Downloading',
  downloadFail: 'Download failed',
  downloadFinished: 'Download complete',
  inupgrade: 'Upgrading',
  upgradeFail: 'Update failed',
  upgradeSuccess: 'Upgrade successfully',
  deviceUpgradeInfo: 'During the upgrade the device will be disconnected and automatically restarted. Please confirm',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo: 'During the upgrade the camera will be disconnected and automatically restarted. Please confirm.',
  pwdUserNameError: 'Username or password error',
  permissionAuth: 'Super admin authority authentication',
  pleaseEnterUser: 'Please enter username',
  pleaseEnterPwd: 'Please enter password',
  noCameraUpgrade: 'No upgradeable camera detected',
  handleCheck: 'Update Detection',
  paySuccess: 'Payment succeeded',
  payFail: 'Payment failed',
  done: 'Complete',
  rePurchase: 'Repurchase',
  cloudStorage: 'Cloud Storage',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  myInstaller: 'My installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Share with installer',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  delete: 'Delete',
  operationSuccess: 'Operation succeeded',
  operationFail: 'Operation failed',
  cancelTrusteeship: 'Cancel sharing with installer',
  chooseDevice: 'Select device',
  noAvaiableDevice: 'Showing devices supporting installer sharing and bound to your account.',
  leastChoose: 'Select at least one device',
  details: 'Details',
  live: 'Live',
  rec: 'Playback',
  config: 'Configuration',
  confirmTrusteeshipTip:
    'The sharing request has been sent to the installer. Please wait for the installer to process it.',
  cancelTrusteeshipTip:
    'After canceling sharing the installer is unable to provide you with remote maintenance services. Please confirm.',
  unBindTrusteeship: 'After unbinding all device sharing will be cancelled. Please confirm.',
  trusteeshipPermissions: 'Sharing permissions',
  trusteeshipTime: 'Share time',
  unBind: 'Unbinding',
  serviceException: 'Service exception',
  pullingText: 'Pull down to load...',
  loosingText: 'Free to refresh...',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more',
  checkSuccess: 'Checked successfully',
  checkFail: 'Check failed',
  viewUpdateContent: 'View update content',
  deviceDisconnected: 'Failed to connect to device',
  updateNote: 'Update note',
  noData: 'No Data',
  tips: 'Tips',
  password: 'Password',
  pwdError: 'Password error. You can try {0} more times',
  pwdErrorLock: 'The devcie is locked due to repeating errors. Please try again later.',
  noPermissions: 'No permissions',
  permission: 'Permission',
  validity: 'Validity',
  permissionValidity: 'Permission validity',
  isSaveModify: 'Confirm saving the changes',
  manyMinutes: '{0}min',
  manyHours: '{0}h',
  manyDays: '{0}d',
  oneWeek: '1 week',
  forever: 'Forever',
  expired: 'Expired',
  residue: 'Residue',
  errorCode: {
    400: 'Parameter error',
    404: 'The requested resource does not exist',
    500: 'System exception',
    502: 'Server request failed',
    503: 'Server exception',
    504: 'Server request timeout',
    550: 'Request timeout',
    10000: 'Failed to connect device',
    12344: 'Network connection failed',
    12345: 'Network connection timeout',
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    32018: 'The data doesnt exist.',
    32022: 'Hosting service is not supported because {0} device and installer are not in the same country/region.',
    536870947: 'Username does not exist',
    536870948: 'Username or password error',
    536871060: 'Operation failed. Please check the device status'
  }
}
