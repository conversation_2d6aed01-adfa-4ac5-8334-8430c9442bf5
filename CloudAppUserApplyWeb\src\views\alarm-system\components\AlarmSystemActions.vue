<template>
  <div class="alarm-box-wrapper alarm-system-actions">
    <div class="actions-header">
      <div class="actions-title">{{ $t('action') }}</div>
      <theme-image
        v-if="shouldShowExpandButton"
        class="expand-icon"
        imageName="arrow_right.png"
        alt="expand"
        @click="handleExpandClick"
      />
    </div>
    <div class="actions-buttons">
      <div
        v-for="action in actions"
        :key="action.type"
        :class="['action-button', action.type, { disabled: !canPerformAction }]"
        @click="handleAction(action)"
      >
        <theme-image class="action-icon" :imageName="`alarm-system/${action.icon}`" :alt="action.type" />
        <span class="action-text">{{ $t(action.type) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex'
import { armPanel, armPartition, setPimaPartitions } from '@/api/alarmSystem'
import { handleCommonError } from '@/utils/alarmSystem'

// 操作按钮配置 - 定义可用的报警系统操作
const ACTION_CONFIGS = [
  { type: 'home', icon: 'home.png' }, // 在家布防
  { type: 'away', icon: 'away.png' }, // 离家布防
  { type: 'disarm', icon: 'disarm.png' } // 撤防
]

// Pima系统状态码映射 - 对应SetPartitions接口的Status字段
const PIMA_STATUS_CODES = {
  home: 3, // Home1 - 在家布防模式
  away: 2, // FullArm - 全布防模式
  disarm: 1 // Disarm - 撤防模式
}

// Risco系统状态码映射 - 对应Arm接口的newSystemStatus字段
const RISCO_SYSTEM_STATUS_CODES = {
  home: 2, // PartialArm - 部分布防（在家模式）
  away: 1, // FullArm - 全布防（离家模式）
  disarm: 0 // Disarmed - 撤防状态
}

// Risco分区状态码映射 - 对应PartArm接口的armedState字段
const RISCO_PARTITION_STATUS_CODES = {
  home: 2, // 在家布防 - 部分传感器激活
  away: 3, // 离家布防 - 所有传感器激活
  disarm: 1 // 撤防 - 所有传感器关闭
}

export default {
  name: 'AlarmSystemActions',
  data() {
    return {
      loading: false,
      actions: ACTION_CONFIGS
    }
  },
  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo', 'panelState']),
    ...mapGetters('alarmSystem', [
      'systemType',
      'isPimaSystem',
      'isRiscoSystem',
      'siteId',
      'sessionId',
      'siteName',
      'canFetchPanelState'
    ]),
    // 判断是否有足够的数据进行API调用
    canPerformAction() {
      return this.canFetchPanelState && !this.loading
    },

    shouldShowExpandButton() {
      // 拥有多个分区时显示，使用统一的partitionsData数据源
      return this.partitionsData.length > 1
    },
    // 统一的分区数据获取器
    partitionsData() {
      // 优先使用浅层数据，如果没有则使用深层数据
      return this.panelState.partitions || this.panelState?.data?.state?.status?.partitions || []
    },
    // 判断是否有分区
    hasPartitions() {
      return this.partitionsData.length > 0
    }
  },
  methods: {
    ...mapMutations('alarmSystem', ['UPDATE_PANEL_STATUS']),
    ...mapActions('alarmSystem', ['fetchPanelState']),
    // 处理操作按钮点击
    async handleAction(action) {
      if (!this.canPerformAction) {
        return
      }

      try {
        // 设置加载状态
        this.loading = true
        this.$loading.show()

        // 执行系统特定的操作
        await this.executeSystemAction(action)

        // 刷新状态（Risco有分区的情况下不需要刷新，因为已在executeRiscoPartitionAction中更新）
        if (!(this.isRiscoSystem && this.hasPartitions)) {
          await this.fetchPanelState({
            siteId: this.siteId,
            systemType: this.systemType
          })
        }

        // 显示成功消息
        this.$toast(this.$t('operationSuccess'))
      } catch (error) {
        // 错误处理，添加操作上下文
        console.error(`Action ${action.type} failed:`, error)
        handleCommonError(error)
      } finally {
        // 清理加载状态
        this.loading = false
        this.$loading.hide()
      }
    },
    // 执行系统特定的操作
    async executeSystemAction(action) {
      if (this.isPimaSystem) {
        await this.executePimaAction(action)
      } else {
        await this.executeRiscoAction(action)
      }
    },
    // Pima系统操作处理
    async executePimaAction(action) {
      const statusCode = PIMA_STATUS_CODES[action.type]
      const partitionData = this.buildPimaPartitionData(this.partitionsData, statusCode)
      await setPimaPartitions({ data: partitionData })
    },

    // 构建Pima分区数据
    buildPimaPartitionData(partitions, statusCode) {
      if (partitions.length > 0) {
        return partitions.map(partition => ({
          Number: partition.Number || partition.id || 1,
          Status: statusCode
        }))
      }
      return [{ Number: 1, Status: statusCode }]
    },
    // Risco系统操作处理
    async executeRiscoAction(action) {
      if (this.hasPartitions) {
        // 有分区：使用PartArm接口
        await this.executeRiscoPartitionAction(action)
      } else {
        // 无分区：使用Arm接口
        await this.executeRiscoSystemAction(action)
      }
    },
    // 分区操作：使用PartArm接口
    async executeRiscoPartitionAction(action) {
      const partitions = this.partitionsData
      const targetState = RISCO_PARTITION_STATUS_CODES[action.type]
      const partitionData = this.buildRiscoPartitionData(partitions, targetState)

      const partArmResponse = await armPartition(this.siteId, partitionData)

      // 使用专门的mutation更新面板状态，只更新status相关字段
      if (partArmResponse?.response) {
        this.UPDATE_PANEL_STATUS(partArmResponse.response)
      }
    },

    // 构建Risco分区数据
    buildRiscoPartitionData(partitions, targetState) {
      return {
        partitions: partitions.map(partition => ({
          // 核心状态字段
          id: partition.id,
          armedState: targetState,

          // 状态字段（使用默认值）
          readyState: partition.readyState || 2,
          alarmState: partition.alarmState || 0,

          // 可选字段
          groups: partition.groups || null,
          exitDelayTO: partition.exitDelayTO || 0,
          lastArmFailReasons: partition.lastArmFailReasons || null
        })),

        // 全局设置
        exitDelay: 0,
        lastArmFailReasons: []
      }
    },
    // 系统操作：使用Arm接口
    async executeRiscoSystemAction(action) {
      const reqData = {
        newSystemStatus: RISCO_SYSTEM_STATUS_CODES[action.type]
      }

      await armPanel(this.siteId, reqData)
    },

    // 处理右侧展开图标点击
    handleExpandClick() {
      // 跳转到Actions页面
      this.$router.push({
        path: '/alarmSystem/actions',
        query: {
          systemType: this.systemType,
          siteName: this.siteName,
          siteId: this.siteId,
          sessionId: this.sessionId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-actions {
  padding: 10px 16px;
  box-sizing: border-box;
  .actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .actions-title {
      font-family: PingFangSC-Semibold, sans-serif;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
    .expand-icon {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }
  .actions-buttons {
    display: flex;
    justify-content: space-between;
    gap: 4px;
    .action-button {
      width: 81px;
      height: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      box-sizing: border-box;
      cursor: pointer;

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .action-icon {
        .theme-image-container {
          width: 20px;
          height: 20px;
        }
      }
      .action-text {
        height: 20px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: 14px;
        letter-spacing: 0;
        margin-left: 3px;
      }
      &:active:not(.disabled) {
        opacity: 0.8;
      }
    }
  }
}
</style>
