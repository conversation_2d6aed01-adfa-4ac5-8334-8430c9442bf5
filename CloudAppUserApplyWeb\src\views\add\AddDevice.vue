<template>
  <div class="add-device-warper">
    <nav-bar @clickLeft="back">
      <template #right>
        <div class="nav-bar-left-text" @click="back">{{ $t('skip') }}</div>
      </template></nav-bar
    >
    <div class="device-list-content">
      <div class="select-num">
        {{ `${$t('selectDevice')}${snList?.length > 0 ? snList?.length : ''}` }}
      </div>
      <div class="card-ul" v-if="deviceList?.length > 0">
        <div class="card-li" v-for="(card, index) in deviceList" :key="index" @click="handleToggleSelection(card)">
          <dev-card :hiddenHaddle="true" :devInfo="card" @openCodeInput="openCodeInput(card)" />
          <div class="choose-tips" v-if="snList?.includes(card.snPlain)">
            <img :src="require('@/assets/img/common/site/ok_choosed.png')" alt="" />
          </div>
        </div>
      </div>
      <div class="empty-box" v-else>
        <img :src="require(`@/assets/img/common/site/empty.png`)" alt="" />
        <div class="empty-tips">{{ $t('emptyDevice') }}</div>
      </div>
    </div>
    <div class="bottom-btn-box">
      <div class="bottom-btn" :class="{ 'cannot-btn': snList.length === 0 }" @click="onAddToSite">
        {{ $t('ensure') }}
      </div>
    </div>
    <!-- 输入安全码 -->
    <van-popup v-if="codePopShow" v-model="codePopShow" position="bottom" :close-on-click-overlay="false" round>
      <input-code :code="checkedCode" @closeCodePop="codePopShow = false" @setSecurityCode="setSecurityCode" />
    </van-popup>
  </div>
</template>

<script>
import DevCard from './components/DevCard.vue'
import NavBar from '@/components/NavBar.vue'
import InputCode from './components/InputCode.vue'
import { appClose, appRequestDeviceList } from '@/utils/appbridge'
import { mapMutations } from 'vuex'
export default {
  name: 'addDevice',
  components: { DevCard, NavBar, InputCode },
  data() {
    return {
      codePopShow: false,
      checkedSn: '',
      checkedCode: '',
      closeIcon: require('@/assets/img/common/close_icon.png'),
      deviceList: [],
      snList: []
    }
  },
  computed: {},
  mounted() {
    this.getDeviceList()
  },
  methods: {
    ...mapMutations('device', ['SET_ADD_DEV_INFO']),
    getDeviceList() {
      // 获取设备列表
      const idList = this.$route.query?.idList
      if (idList) {
        const params = JSON.stringify({ idList })
        appRequestDeviceList(params, res => {
          try {
            this.deviceList = JSON.parse(JSON.parse(res)?.body).deviceList
            this.deviceList = this.deviceList.filter((item, index) => {
              if (!item.code) {
                this.$set(this.deviceList[index], 'noCode', true)
              }
              return item.snPlain && (item.type === 2 || item.type === 3)
            })
          } catch (error) {
            console.log(error)
          }
        })
      }
    },

    handleToggleSelection(card) {
      if (card.noCode && !card?.code) {
        return
      }
      if (this.snList.includes(card.snPlain)) {
        this.snList = this.snList.filter(item => item !== card.snPlain)
      } else {
        this.snList.push(card.snPlain)
      }
    },
    back() {
      appClose()
    },
    saveDeviceData() {
      const list = this.deviceList.filter(item => this.snList.includes(item.snPlain))
      this.SET_ADD_DEV_INFO(list)
    },
    onAddToSite() {
      if (this.snList.length === 0) {
        return
      }
      this.saveDeviceData()
      this.$router.push(`/device/waitingAdd/`)
    },
    onConfirm() {
      this.$router.push({ name: 'waitingAdd' })
    },
    setSecurityCode(code) {
      this.deviceList.forEach((item, index) => {
        if (item.snPlain === this.checkedSn) {
          this.$set(this.deviceList[index], 'code', code)
        }
      })
      this.codePopShow = false
    },
    openCodeInput(card) {
      this.checkedSn = card.snPlain
      this.checkedCode = card.code
      this.codePopShow = true
    }
  }
}
</script>

<style lang="scss" scoped>
.add-device-warper {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .device-list-content {
    height: 100%;
    box-sizing: border-box;
    padding: 0 10px;
    .select-num {
      margin: 20px 0;
      color: var(--bg-color-black, #000000);
      font-size: var(--font-size-body2-size, 14px);
      font-weight: 600;
      line-height: 26px;
      margin-left: 6px;
    }
  }
}
.card-ul {
  height: calc(100% - 180px);
  overflow-y: auto;
  .card-li {
    position: relative;
    margin-top: 8px;
    .choose-tips {
      position: absolute;
      right: 0;
      top: 0;
      border-top-right-radius: 6px;
      background: linear-gradient(225deg, #1d71f3, #1d71f3 50%, transparent 50%, transparent 100%);
      width: 30px;
      height: 30px;
      img {
        width: 16px;
        height: 16px;
        margin: 2px 0 0 12px;
      }
    }
  }
}
.bottom-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 60px;
  width: 100vw;
  background: var(--bg-color-white, #ffffff);
  display: flex;
  box-shadow: 0 2px 12px 0 #00000029;
  justify-content: center;
  align-items: center;
}
.bottom-btn {
  width: 327px;
  height: 40px;
  border-radius: 23px;
  background: var(--brand-bg-color-active, #1d71f3);
  line-height: 40px;
  text-align: center;
  color: var(--bg-color-white, #ffffff);
  font-size: var(--font-size-body1-size, 16px);
  font-weight: 500;
}
.empty-box {
  padding-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    height: 216px;
    width: 180px;
    margin-bottom: 20px;
  }
  .empty-tips {
    font-size: var(--font-size-body2-size, 14px);
    color: var(--text-color-placeholder, #9a9ca2);
    line-height: 22px;
    text-align: center;
  }
}
.cannot-btn {
  background-color: var(--brand-bg-color-disabled, #d6e4fe);
}
</style>
