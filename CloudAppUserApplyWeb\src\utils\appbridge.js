import { getParamsFromUserAgent } from './common'
import router from '@/router'

/**
 * @param {Function} callback 回调函数
 *
 */
function setupWebViewJavascriptBridge(callback) {
  if (window.WebViewJavascriptBridge) {
    return callback(window.WebViewJavascriptBridge)
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback)
  }
  window.WVJBCallbacks = [callback]
  const WVJBIframe = document.createElement('iframe')
  WVJBIframe.style.display = 'none'
  WVJBIframe.src = 'sp://__bridge_loaded__'
  document.documentElement.appendChild(WVJBIframe)
  setTimeout(function () {
    document.documentElement.removeChild(WVJBIframe)
  }, 0)
}
// eslint-disable-next-line prettier/prettier
setupWebViewJavascriptBridge(function () { })

/**
 * 启用webview动桥接设置
 * @param {String} cbName 注册的函数名
 * @param {Function} fn 回调函数
 */
function bridgeRegisterHandler(cbName, fn) {
  setupWebViewJavascriptBridge(function (bridge) {
    if (cbName && fn) {
      bridge.registerHandler(cbName, fn)
    }
  })
}

/**
 * APP功能暴露接口
 * @param {String} type 接口名称
 * @param params 接口参数
 * @param {*} cfn 设置成功后的回调函数
 *
 */

const isMock = process.env.VUE_APP_MODE === 'MOCK'
// eslint-disable-next-line prettier/prettier
function bridgeCallHandler(type, params, cfn = function () { }) {
  let wbB = window.WebViewJavascriptBridge

  if (isMock) {
    // mock模式下，将发给app的数据转发给mock服务器
    import('@/api/request').then(({ default: request }) => {
      request.post(`/app-mock/${type}`, params).then(res => {
        cfn(res.data)
      })
    })
  } else if (!wbB) {
    setTimeout(() => {
      wbB = window.WebViewJavascriptBridge
      //if (!wbB) console.error('仍然无法使用window.WebViewJavascriptBridge对象')
      wbB && wbB.callHandler(type, params, cfn)
    }, 0)
  } else {
    wbB.callHandler(type, params, cfn)
  }
}

// 设置浏览器标题
function appSetTitle(content) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('page/setTitle', { title: content })
  } else {
    bridgeCallHandler('setTitle', { title: content })
  }
}

// 关闭浏览器
function appClose() {
  if (!window.WebViewJavascriptBridge) {
    router.go(-1)
    return
  }

  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('page/close', undefined)
  } else {
    bridgeCallHandler('close', undefined)
  }
}

// 返回上一级（先返回webview历史栈，如果webview历史栈为空则关闭页面）
function appBack() {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('page/back', undefined)
  } else {
    bridgeCallHandler('back', undefined)
  }
}

// 请求协议网络
function appRequestCloud(params, cb) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('cloud/request', params, cb)
  } else {
    bridgeCallHandler('requestCloud', params, cb)
  }
}

// 请求设备信息
function appRequestDevice(params, cb) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('device/request', params, cb)
  } else {
    bridgeCallHandler('requestDevice', params, cb)
  }
}

// 请求Native
function appReqeustNative(params, cb) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    const { url } = params
    switch (url) {
      case 'USER_TOKEN_INFO':
        bridgeCallHandler('cloud/getToken', params.params, cb)
        break
      case 'APP_LOGOUT':
        bridgeCallHandler('app/logout', params.params, cb)
        break
      case 'APP_RELOGIN':
        bridgeCallHandler('app/relogin', params.params, cb)
        break
      case 'APP_REFRESH_TOKEN':
        bridgeCallHandler('cloud/refreshToken', params.params, cb)
        break
      default:
        bridgeCallHandler('requestNative', params, cb)
        break
    }
  } else {
    bridgeCallHandler('requestNative', params, cb)
  }
}

// 设置浏览器返回键为关闭键（包含安卓物理返回键）
function appSetBackToClose(flag) {
  const { bridgeType } = getParamsFromUserAgent()

  if (bridgeType === 'superMax') {
    bridgeCallHandler('page/setBackToClose', {})
  } else {
    bridgeCallHandler('setBackToClose', { setBackToCloseSwitch: flag })
  }
}

// 是否禁用系统返回键  -- max
function appSetWebBackEnable(flag) {
  bridgeCallHandler('page/setWebBackEnable', flag)
}

// 消息通知
function appOnNotification(params, cb) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('app/notification', params, cb)
  } else {
    bridgeCallHandler('onNotification', params, cb)
  }
}

// 消息通知APP onNotification   NoticeNameDeviceListChanged
function onNotificationToApp(name, params) {
  let req = {
    name: name,
    params: params ? params : {}
  }
  appOnNotification(req, function (res) {
    console.log(res, '消息通知app')
  })
}

// 扫码
function appOpenScan(callback) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('app/openScan', {}, callback)
  }
}

// 支付页面消失通知
function appPayDismiss(params) {
  const { bridgeType } = getParamsFromUserAgent()
  if (bridgeType === 'superMax') {
    bridgeCallHandler('pay/dismiss', params)
  }
}

// 打开H5
function openH5(params) {
  if (!window.WebViewJavascriptBridge) {
    router.push(params.url)
  } else {
    bridgeCallHandler('app/openH5', params)
  }
}

// 打开弹窗 在弹窗中加载H5
function openDialog(params) {
  bridgeCallHandler('page/openDialog', params)
}

// 关闭弹窗
function closeDialog(params) {
  bridgeCallHandler('page/closeDialog', params)
}

// 给H5传递数据
function postDataToH5(params) {
  bridgeCallHandler('page/postDataToH5', params)
}

// H5跳转app页面
function gotoPage(params) {
  bridgeCallHandler('app/gotoPage', params)
}

// 分享和转移接收设备后通知APP刷新设备列表
function receiveDevice(params) {
  bridgeCallHandler('app/receiveDevice', params)
}

// 布撤防后通知APP刷新设备列表布撤防状态
function refreshDefenseState(params) {
  bridgeCallHandler('app/refreshDefenseState', params)
}

// 回调提示接口 app给出提示
function showToast(params) {
  bridgeCallHandler('app/showToast', params)
}

// 在APP本地缓存中写入数据
function setCacheData(params) {
  bridgeCallHandler('cache/setData', params)
}

// 在APP本地缓存中读取数据
function getCacheData(key, callback) {
  bridgeCallHandler('cache/getData', key, callback)
}

// 从APP获取布撤防设备-superlive max有
function getChlList(params, cb) {
  bridgeCallHandler('device/getChlList', params, cb)
}

// 从APP获取布撤防设备-- superlive max有
function getSensorList(params, cb) {
  bridgeCallHandler('device/getSensorList', params, cb)
}

// 从APP获取当前用户是否为游客 -- superlive max有
function isGuest(cb) {
  bridgeCallHandler('app/isGuest', {}, cb)
}

// 通知APP打印日志  -- type 传值有log/error log/warn  log/info  log/debug
function appLog(type = 'log/info', string) {
  bridgeCallHandler(type, string)
}

// max android 显示loading
function showLoading(flag) {
  bridgeCallHandler('page/showLoading', flag)
}

// 添加至云端 请求设备
function appRequestDeviceList(params, cb) {
  bridgeCallHandler('deviceDebug/getDeviceDetails', params, cb)
}

// 绑定设备
function appRequestAddBindDevices(params, cb) {
  bridgeCallHandler('cloud/addBindDevices', params, cb)
}

// 重置设备密码
function resetPassword(params, cb) {
  bridgeCallHandler('device/resetPassword', params, cb)
}

//修改设备名称
function modifyDeviceName(params, cb) {
  bridgeCallHandler('deviceDebug/modifyDeviceName', params, cb)
}

// 修改密码后同步给app
function updateDevicePassword(params) {
  bridgeCallHandler('deviceDebug/updateDevicePassword', params)
}

// 手机区号选择
function appSelectCountry(params, cb) {
  console.log('调用user/selectCountry', params)
  bridgeCallHandler('user/selectCountry', params, cb)
}

export {
  bridgeRegisterHandler,
  bridgeCallHandler,
  appSetTitle,
  appBack,
  appClose,
  appRequestCloud,
  appRequestDevice,
  appReqeustNative,
  appSetBackToClose,
  onNotificationToApp,
  appOpenScan,
  appPayDismiss,
  openH5,
  openDialog,
  closeDialog,
  postDataToH5,
  gotoPage,
  receiveDevice,
  refreshDefenseState,
  showToast,
  setCacheData,
  getCacheData,
  getChlList,
  getSensorList,
  isGuest,
  appSetWebBackEnable,
  appLog,
  showLoading,
  appRequestDeviceList,
  appRequestAddBindDevices,
  resetPassword,
  modifyDeviceName,
  updateDevicePassword,
  appSelectCountry
}
