<template>
  <div class="ipc-upgrade-list">
    <nav-bar @clickLeft="back"></nav-bar>
    <!-- ipc升级 -->
    <div class="ipc-upgrade">
      <div class="title">
        {{ bridgeType === 'superMax' ? $t('cameraUpdate') : '' }}
      </div>
      <div class="container">
        <div class="title-text">
          <div class="left">
            <div class="title-text-img">
              <theme-image alt="camera" imageName="camera.png" />
            </div>
            <div class="title-text-title text-over-ellipsis">
              {{ ipcInfo.ipcName }}
            </div>
          </div>
          <!-- 最新版本则隐藏升级按钮 -->
          <div class="right">
            <div
              :class="['title-text-button', !isIpcNeedUpgrade ? 'title-button-disabled' : '']"
              v-if="isShowUpgrade"
              @click="upgrade"
            >
              {{ $t('updateNow') }}
            </div>
            <div
              :class="['title-text-button', !isIpcNeedUpgrade ? 'title-button-disabled' : '']"
              v-else
              @click="cancelCloudUpgrade"
            >
              {{ $t('cancel') }}
            </div>
          </div>
        </div>
        <div class="list-content" v-if="isIpcNeedUpgrade">
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('currentVersion') }}
            </div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              <span>{{ ipcInfo.version || '--' }}</span>
            </div>
          </div>
          <div class="list-content-row" v-if="ipcInfo.newVersion">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('latestVersion') }}
            </div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              <span>{{ ipcInfo.newVersion || '--' }}</span>
            </div>
          </div>
          <div class="list-content-row" v-if="isIpcNeedUpgrade">
            <div class="view-btn" @click="viewUpdateContent(ipcInfo.versionNote)">
              {{ $t('viewUpdateContent') }}
            </div>
          </div>
          <!-- ipc升级 状态 升级中、升级失败、离线-->
          <!-- <div class="list-content-row" v-if="ipcInfo.state">
            <div class="download-status">
              <div
                v-if="ipcInfo.showIpcStatusText"
                class="download-status-text"
                :style="`color: ${statusColor[ipcInfo.ipcStatusColor]}`"
              >
                {{ ipcInfo.ipcStatusText }}
              </div>
              <span
                class="progress"
                v-if="ipcInfo.progress"
                :style="`color: ${statusColor[ipcInfo.ipcStatusColor]}`"
                ><span>(</span>{{ ipcInfo.progress }}<span>)</span></span
              >
            </div>
          </div> -->
          <!-- 展示新的状态及进度条 -->
          <div class="download-status-box">
            <van-progress
              v-if="ipcInfo.showIpcStatusText && !['normal'].includes(ipcInfo.newState)"
              class="upgrade-progress"
              :percentage="Number(ipcInfo.newProgress || 0)"
              stroke-width="5"
              :show-pivot="false"
            >
            </van-progress>
            <div
              v-if="ipcInfo.showIpcStatusText"
              class="download-status-text"
              :style="`color: ${statusColor[ipcInfo.ipcStatusColor]}`"
            >
              {{ ipcInfo.newIpcStatusText }}
              <!-- 此标签为了触发上面van-progress同步渲染更新 -->
              <span style="visibility: hidden">{{ ipcInfo.progress }}</span>
            </div>
          </div>
          <!-- 提示 -->
          <div class="list-content-row" v-if="isShowUpgradeTip">
            <div class="download-tip">{{ $t('upgradeTip') }}</div>
          </div>
        </div>
        <!-- 摄像机 没有更新的 -->
        <div class="list-content" v-else>
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('currentVersion') }}
            </div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              <span>{{ ipcInfo.version }}</span>
            </div>
          </div>
          <div class="list-content-row">
            <div class="has-latest-version">{{ $t('hasLatestVersion') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 摄像机升级 -->
    <div class="footer">
      <div class="footer-btn" @click="checkVersion">
        {{ $t('handleCheck') }}
      </div>
    </div>
    <!-- 查看 更新内容弹框 -->
    <van-popup v-model="showUpdateContent" class="pop-dialog" :close-on-click-overlay="false">
      <div class="pop-div">
        <div class="dialog-title">{{ $t('updateNote') }}</div>
        <div class="dialog-close-img" @click="closePopup">
          <theme-image alt="close" imageName="close.png" />
        </div>
      </div>
      <div class="update-box">
        <div class="update-content">{{ updateContent }}</div>
      </div>
    </van-popup>
    <!-- 权限校验弹框 -->
    <check-pwd ref="checkPwd" @confirm="authPwd"></check-pwd>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import CheckPwd from './CheckPwd.vue'
import { appSetTitle, appBack, appRequestDevice, getCacheData, setCacheData } from '@/utils/appbridge'
import { transformXml, getParamsFromUserAgent, getUrlQuery, getMaxUrlQuery, debounce } from '@/utils/common'
import {
  getCloudUpgradeInfoXml,
  maxCheckVersionXml,
  checkVersionXml,
  cloudUpgradeXml,
  cancelCloudUpgradeXml
} from '@/api/ipcUpgrade'
import { STATUS_COLOR_THEME } from '@/utils/options.js'
import md5 from 'js-md5'
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'upgradeList',
  components: {
    NavBar,
    CheckPwd,
    ThemeImage
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      devId: '',
      bindState: 0, // SN和安全码 添加的   bindState是1     ip添加的 bindState是0  IP添加的设备升级是要输入账号密码
      isIpcNeedUpgrade: true,
      showUpdateContent: false,
      isShowUpgrade: true, // 升级 还是 取消按钮的选择
      updateContent: '',
      ipcInfo: {
        state: '',
        ipcName: '',
        version: '',
        newVersion: '',
        newVersionGUID: '',
        progress: '',
        versionNote: '',
        showIpcStatusText: false, //升级的状态展示
        ipcStatusColor: '',
        ipcStatusText: ''
      },
      timer: null,
      cloudUpgradeStateMap: {
        // normal: this.$t('online'), //正常状态 不处于升级过程，就是正常在线状态
        upgradePrepare: this.$t('inupgrade'), //升级准备中
        upgrading: this.$t('inupgrade'), //升级中
        upgradeFail: this.$t('upgradeFail'), //升级失败
        offLine: this.$t('offline') //app给的code判断的设备离线
      },
      errorCode: null, //10000 设备离线
      bridgeType: null, // APP类型，需要区分出superlive max
      appPlatform: null, // APP平台  Android /  IOS
      // 新的云升级状态映射 -- 待下载、升级中、重启中、升级成功
      newCloudUpgradeStateMap: {
        upgradePrepare: this.$t('inupgrade'), // 待下载 -- 对应升级中
        upgrading: this.$t('inupgrade'), // 升级中对应正在升级，包括下载和升级中
        restart: this.$t('inupgrade'), // 重启中  -- 需要记录之前的升级状态，如果有升级中则离线对应重启中，重启中也展示升级中
        offLine: this.$t('offline'), //app给的code判断的设备离线
        installSuccess: this.$t('upgradeSuccess'), // 升级成功  -- 需要记录之前的升级状态，如果有下载则最新版本对应升级成功
        installFail: this.$t('upgradeFail'), // 升级失败，转发失败的错误原因还未明确
        downloadFail: this.$t('downloadFail') // 下载失败，网络异常
      },
      // 记录设备及通道的云升级状态数组--为了正确的判断重启中和升级成功  key: sn, value: 云升级状态数组
      upgradeStatusObj: {},
      // 记录设备及通道的云升级进度数组--为了正确的判断下载的进度  key: sn, value: 进度
      upgradeProgressObj: {},
      // 当设备判断为重启中时，记录设备重启时间
      upgradeRestartTimeObj: {}
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
    // 存储云升级数据
    this.setUpgradeStatusStore({
      upgradeStatusObj: this.upgradeStatusObj,
      upgradeProgressObj: this.upgradeProgressObj,
      upgradeRestartTimeObj: this.upgradeRestartTimeObj
    })
  },
  async mounted() {
    appSetTitle(this.$t('upgrade'))
    const { bridgeType, appPlatform } = getParamsFromUserAgent()
    this.bridgeType = bridgeType
    this.appPlatform = appPlatform
    let json = {}
    if (bridgeType === 'superMax') {
      // superlive max走正常的网址解析
      json = getMaxUrlQuery(window.location.href)
    } else {
      json = getUrlQuery(window.location.href)
    }
    this.devId = json.devId
    this.bindState = json.bindState
    this.ipcInfo.ipcName = decodeURIComponent(decodeURIComponent(json.devName))
    console.log(this.devId, this.ipcInfo.ipcName, this.language, this.bindState)
    // 获取暂存的数据
    await this.getUpgradeStatusStore()
    if (bridgeType === 'superMax') {
      await this.initCheckVersion() // superlive max进入云升级页面先调用检测更新
    }
    // 之前superlive cloud等APP的逻辑
    this.getCloudUpgradeInfo()
    this.startTimer() //开启定时器
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    isShowUpgradeTip() {
      let stateList = ['upgradePrepare', 'upgrading', 'offLine']
      return !!stateList.includes(this.ipcInfo.state)
    },
    languageFlag() {
      return this.language == 'zh'
    },
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    statusColor() {
      return STATUS_COLOR_THEME(this.appStyleFlag, this.uiStyleFlag)
    }
  },
  methods: {
    back() {
      clearInterval(this.timer)
      this.timer = null
      // 存储云升级数据
      this.setUpgradeStatusStore({
        upgradeStatusObj: this.upgradeStatusObj,
        upgradeProgressObj: this.upgradeProgressObj,
        upgradeRestartTimeObj: this.upgradeRestartTimeObj
      })
      appBack()
    },
    // 查询IPC云升级信息
    getCloudUpgradeInfo() {
      let that = this
      const req = {
        devId: this.devId,
        url: 'getCloudUpgradeInfo',
        params: getCloudUpgradeInfoXml()
      }
      // console.log('查询云升级信息req', req)
      appRequestDevice(req, function (res) {
        // console.log('getCloudUpgradeInfo返回结果res', res)
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          let xmlObject = transformXml(resData.body)
          let xmlObjRes = xmlObject.config
          if (xmlObjRes) {
            that.ipcInfo.state = xmlObjRes.state ? xmlObjRes.state.__text : ''
            that.ipcInfo.version = xmlObjRes.version
              ? xmlObjRes.version['#cdata-section'] || xmlObjRes.version['__cdata']
              : ''
            that.ipcInfo.progress = xmlObjRes.progress ? xmlObjRes.progress.__text + '%' : ''
            if (xmlObjRes.newVersionInfo) {
              // that.isIpcNeedUpgrade = true
              that.ipcInfo.newVersionGUID =
                xmlObjRes.newVersionInfo.newVersionGUID['#cdata-section'] ||
                xmlObjRes.newVersionInfo.newVersionGUID['__cdata']
              that.ipcInfo.newVersion =
                xmlObjRes.newVersionInfo.newVersion['#cdata-section'] || xmlObjRes.newVersionInfo.newVersion['__cdata']
              that.ipcInfo.versionNote = xmlObjRes.newVersionInfo.newVersionNote
                ? xmlObjRes.newVersionInfo.newVersionNote['#cdata-section'] ||
                  xmlObjRes.newVersionInfo.newVersionNote['__cdata']
                : ''
            } else {
              // that.isIpcNeedUpgrade = false
            }
            // IPC升级状态提示
            that.ipcInfo.showIpcStatusText = !!that.ipcInfo.state
            that.ipcInfo.ipcStatusColor = that.ipcInfo.state || ''
            that.ipcInfo.ipcStatusText = that.ipcInfo.state ? that.cloudUpgradeStateMap[that.ipcInfo.state] : ''
            // 展示 升级按钮 还是取消升级按钮
            that.isShowUpgrade = !(that.ipcInfo.state === 'upgradePrepare' || that.ipcInfo.state === 'upgrading')
            // 新的设备状态提示
            const newState = that.dealDevState(that.devId, that.ipcInfo.state)
            that.ipcInfo.newState = newState
            that.ipcInfo.newIpcStatusText = that.newCloudUpgradeStateMap[newState] || ''
            // 新的设备进度
            const newProgress = that.dealDevProgress(
              that.devId,
              newState,
              xmlObjRes.progress ? parseInt(xmlObjRes.progress.__text) : ''
            )
            that.ipcInfo.newProgress = newProgress
            // console.log('newState', newState, 'xmlObjRes.newVersionInfo', xmlObjRes.newVersionInfo)
            // 使用新状态判断是否展示升级状态及进度
            if (newState == 'normal' && !xmlObjRes.newVersionInfo) {
              that.isIpcNeedUpgrade = false
            } else {
              that.isIpcNeedUpgrade = true
            }
            // 正常状态去除重启时间
            delete that.upgradeRestartTimeObj[that.devId]
          }
        } else if (resData.code == 10000 || resData.code == 604) {
          // 只是在升级过程中重启离线的情况会显示到升级状态里
          that.ipcInfo.showIpcStatusText = true
          that.ipcInfo.state = 'offLine'
          that.ipcInfo.ipcStatusColor = 'offLine'
          that.ipcInfo.ipcStatusText = that.cloudUpgradeStateMap['offLine']
          that.ipcInfo.progress = '' //展示离线的时候 不展示进度
          // 新的设备状态提示  升级过程中的离线定义为重启中
          const newState = that.dealDevState(that.devId, 'offLine')
          if (newState === 'restart') {
            // 重启中记录下重启时间
            if (!that.upgradeRestartTimeObj[that.devId]) {
              that.upgradeRestartTimeObj[that.devId] = new Date().getTime()
            }
          }
          that.ipcInfo.newState = newState
          that.ipcInfo.newIpcStatusText = that.newCloudUpgradeStateMap[newState] || ''
          // 新的设备进度
          that.ipcInfo.newProgress = that.dealDevProgress(that.devId, newState, '')
        }
      })
    },
    // 设备升级
    upgrade() {
      if (!this.isIpcNeedUpgrade) {
        this.$toast(this.$t('hasLatestVersion'))
      } else if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      } else if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      } else if (this.bindState == 0) {
        this.$refs.checkPwd.show = true // 用户名密码添加的设备
      } else {
        this.cloudUpgrade()
      }
    },
    authPwd(data) {
      this.$refs.checkPwd.show = false
      let dealPwd = md5(data.password).toUpperCase()
      this.cloudUpgrade(dealPwd)
    },
    // 兼容 样式的提示
    diffTip(word) {
      let tip = {
        className: 'upgrade-message',
        message: word,
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      if (this.style == 'UI1B') {
        tip['title'] = this.$t('tips')
      }
      return tip
    },
    // IPC 开始云升级
    cloudUpgrade(dealPwd) {
      // 已经是最新版本则不响应事件
      if (!this.isIpcNeedUpgrade) return
      let that = this
      let params = cloudUpgradeXml(dealPwd, that.ipcInfo.newVersionGUID)
      let tips = this.diffTip(this.$t('upgradeTip'))
      this.$dialog
        .confirm(tips)
        .then(() => {
          let req = {
            devId: this.devId,
            url: 'cloudUpgrade',
            params: params
          }
          that.$toast.loading({
            // superlive max显示加载中文字
            message: that.bridgeType === 'superMax' ? that.$t('loadingText') : '',
            className: 'upgrade-check-loading',
            forbidClick: true,
            position: 'middle',
            duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
          })
          appRequestDevice(req, function (res) {
            console.log(res, '开始云升级')
            that.$toast.clear()
            that.dealResult(res)
          })
        })
        .catch(() => {})
    },
    // 取消云升级
    cancelCloudUpgrade() {
      // 已经是最新版本则不响应事件
      if (!this.isIpcNeedUpgrade) return
      let that = this
      let req = {
        devId: this.devId,
        url: 'cancelCloudUpgrade',
        params: cancelCloudUpgradeXml(that.ipcInfo.newVersionGUID)
      }
      appRequestDevice(req, function (res) {
        console.log(res, '取消云升级')
        // 清除之前记录的状态
        if (this.upgradeStatusObj[this.devId]) {
          delete this.upgradeStatusObj[this.devId]
        }
        that.dealResult(res)
      })
    },
    // 初始化检测更新--用来在初始化进入页面时检查设备状态，有错误则提示
    initCheckVersion() {
      let that = this
      let req = {
        devId: this.devId,
        url: 'checkVersion',
        params: this.bridgeType === 'superMax' ? maxCheckVersionXml() : checkVersionXml()
      }
      appRequestDevice(req, function (res) {
        console.log(res, '初始化检测更新')
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        if (resData.code == 200) {
          let xmlObject = transformXml(resData.body)
          let xmlObjRes = xmlObject.config
          if (xmlObjRes._status !== 'success') {
            let errorCode = xmlObjRes._errorCode
            if (errorCode) {
              that.$toast(that.$t(`errorCode.ipc.${errorCode}`))
              return
            }
          }
        } else if (resData.code) {
          // 10000、604的 无法连接设备
          that.$toast(that.$t(`errorCode.${resData.code}`))
        }
      })
    },
    // 检测更新 先关闭定时器 检测完后再开启轮询
    checkVersion: debounce(function () {
      let that = this
      let req = {
        devId: this.devId,
        url: 'checkVersion',
        params: this.bridgeType === 'superMax' ? maxCheckVersionXml() : checkVersionXml()
      }
      if (that.timer) {
        clearInterval(that.timer)
        that.timer = null
      }
      that.$toast.loading({
        // superlive max显示加载中文字
        message: that.bridgeType === 'superMax' ? that.$t('loadingText') : '',
        className: 'upgrade-check-loading',
        forbidClick: true,
        position: 'middle',
        duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
      })
      appRequestDevice(req, function (res) {
        console.log(res, '检测更新')
        that.$toast.clear()
        that.startTimer() //开启定时器
        that.dealResult(res, that.$t('checkSuccess'), that.$t('checkFail'))
      })
    }, 100),
    // 查看更新内容
    viewUpdateContent(content) {
      this.showUpdateContent = true
      this.updateContent = content || this.$t('noData')
    },
    closePopup() {
      this.showUpdateContent = false
      setTimeout(() => {
        this.updateContent = ''
      }, 300) // 弹框动画api 默认是300ms 这里处理是滚动条位置初始化
    },
    // 查询云升级 设备和通道信息的定时器
    startTimer() {
      let that = this
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        that.getCloudUpgradeInfo()
      }, 3000)
    },
    // 公共的处理结果的方法
    dealResult(res, successInfo, failInfo) {
      let that = this
      let resData = res.replace(/\\t|\\n/g, '')
      resData = JSON.parse(resData)
      if (resData.code == 200) {
        let xmlObject = transformXml(resData.body)
        let xmlObjRes = xmlObject.config
        // console.log(xmlObjRes, 'xmlObjRes')
        if (xmlObjRes._status == 'success') {
          if (successInfo) {
            that.$toast(successInfo)
          }
          that.getCloudUpgradeInfo()
        } else {
          let errorCode = xmlObjRes._errorCode
          // 密码输入错误的提示单独处理 不是对应的正常错误码
          if (errorCode === 'pwdError') {
            let LoginFailNum = parseInt(xmlObjRes._LoginFailNum)
            if (LoginFailNum > 0) {
              that.$toast(that.$t('pwdError', [LoginFailNum]))
            } else {
              that.$toast(that.$t('pwdErrorLock'))
            }
            return
          } else if (errorCode === 'lockError') {
            that.$toast(that.$t('pwdErrorLock'))
          } else if (errorCode === 'permission_denied') {
            that.$toast(that.$t('noPermissions'))
            return
          } else if (errorCode) {
            that.$toast(that.$t(`errorCode.ipc.${errorCode}`))
            return
          } else {
            that.$toast(failInfo)
          }
        }
      } else if (resData.code) {
        // 10000、101001的 无法连接设备
        that.$toast(that.$t(`errorCode.${resData.code}`))
      }
    },
    // 处理设备/通道的新状态 -- sn 设备sn， state 设备状态  devInfo 设备详细信息
    dealDevState(sn, state) {
      // 针对离线和最新版本需要额外判断之前的状态
      let newState
      let statusSet = new Set(this.upgradeStatusObj[sn] || [])
      // console.log('this.upgradeStatusObj[sn]', this.upgradeStatusObj[sn], state)
      // 针对离线需要额外判断是否记录过重启时间，如果记录的重启时间跟当前时间超过5min则认为是离线
      let isOffLine = false
      const restartTime = this.upgradeRestartTimeObj[sn]
      if (restartTime) {
        const nowTime = new Date().getTime()
        const diffTime = nowTime - restartTime
        if (diffTime > 5 * 60 * 1000) {
          isOffLine = true
        }
      }
      switch (state) {
        case 'offLine':
          if (!isOffLine && statusSet.has('upgrading')) {
            // 之前有升级中，则离线可以认为是重启中
            newState = 'restart'
            statusSet.add('restart')
          } else {
            // 否则就是正常的离线
            newState = 'offLine'
            statusSet.add('offLine')
          }
          break
        case 'normal':
          if (statusSet.has('upgrading') && !statusSet.has('installSuccess')) {
            // 之前有待下载，且没有升级成功，则可以认为最新版本是升级成功
            newState = 'installSuccess'
            statusSet.add('installSuccess')
          } else {
            // 否则就是正常的已是最新版本
            newState = 'normal'
            // 已是最新版本则清空记录的状态
            statusSet = new Set()
          }
          break
        case 'upgradeFail':
        case 'installFailNodeDisconnect':
        case 'installFailNodeInvalid':
          if (statusSet.has('installFail')) {
            // 已经提示过升级失败则显示最新版本
            newState = 'normal'
            statusSet.add('normal')
          } else {
            newState = 'installFail'
            statusSet.add('installFail')
          }
          break
        default:
          newState = state
          statusSet.add(state)
          break
      }
      // 更新设备/通道的云升级状态
      this.upgradeStatusObj[sn] = Array.from(statusSet)
      return newState
    },
    // 处理设备的进度
    dealDevProgress(sn, state, progress) {
      // 进度条分为40-60-80-100分为四段
      // 进度条分为90-100分为两段
      // 下载中0-90 重启中 80 升级完成100
      let newProgress
      const upgradeProgressObj = { ...this.upgradeProgressObj }
      switch (state) {
        case 'upgradePrepare':
          // 升级准备中即是待下载
          newProgress = 0
          upgradeProgressObj[sn] = newProgress
          break
        case 'upgrading':
          if (progress) {
            // 有进度则是下载中，否则是升级中
            // 下载中根据下载进度乘以0.9
            newProgress = Math.min(parseInt(progress * 0.9), 90)
          } else {
            newProgress = 0
          }
          upgradeProgressObj[sn] = newProgress
          break
        case 'restart':
          // newProgress = 80
          // 重启中进度不变
          newProgress = upgradeProgressObj[sn] || 0 // 有记录的进度则用记录的进度 无记录则为0
          break
        case 'installSuccess':
          newProgress = 100
          // 升级完成则清除之前记录的进度
          delete upgradeProgressObj[sn]
          break
        default:
          newProgress = 0
          // 其余情况则清除之前记录的进度
          delete upgradeProgressObj[sn]
          break
      }
      this.upgradeProgressObj = upgradeProgressObj
      return newProgress
    },
    // 获取云升级暂存数据--点击云升级退出后保存的云升级状态数据
    async getUpgradeStatusStore() {
      await new Promise(resolve => {
        const callback = data => {
          if (data) {
            const obj = JSON.parse(data)
            // console.log(`${new Date()} 暂存数据返回, 结果是: ${data}`)
            if (obj && obj.body) {
              const { upgradeStatusObj, upgradeProgressObj, upgradeRestartTimeObj } = JSON.parse(obj.body) || {}
              this.upgradeStatusObj = upgradeStatusObj || {}
              this.upgradeProgressObj = upgradeProgressObj || {}
              this.upgradeRestartTimeObj = upgradeRestartTimeObj || {}
            }
            resolve()
          }
        }
        getCacheData('upgradeStatusStore', callback)
      })
    },
    // 云升级页面退出时暂存数据
    setUpgradeStatusStore(data) {
      setCacheData({ key: 'upgradeStatusStore', value: JSON.stringify(data) })
    }
  }
}
</script>
<style lang="scss" scoped>
.ipc-upgrade-list {
  height: calc(100% - 70px);
  overflow: auto;
}
.title-button-disabled {
  color: var(--brand-bg-color-disabled, #d6e4fe);
  border: 1px solid var(--brand-bg-color-disabled, #d6e4fe);
}
.download-tip {
  color: #ff3a39;
}
</style>
