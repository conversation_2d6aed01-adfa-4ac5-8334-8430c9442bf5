<template>
  <div class="add-defense-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="add-defense-content">
      <div class="add-defense-head">
        <div class="add-defense-title">{{ $t('name') }}</div>
        <div class="add-defense-text">
          <span class="defense-ellipsis-text">{{ defenseRecord.groupName }}</span>
          <img
            @click="handleEditName"
            class="arrow-img"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
          />
        </div>
      </div>
      <div class="add-defense-device">
        <div class="device-label">{{ $t('cameraSensor') }}</div>
        <div @click="addDevice"><van-icon class="vms-plus-icon" name="plus" /></div>
      </div>
      <div class="device-content">
        <div class="device-list-wrapper" v-if="showChannelList">
          <van-swipe-cell
            v-for="(item, index) in defenseRecord.channelList"
            :key="'item' + index"
            :name="`device-${index}`"
          >
            <div class="device-item-wrapper" @click.stop="editIPCLinkage(item, index)">
              <div class="device-item-left">
                <div class="device-title">{{ item.chlName }}</div>
                <!-- <div class="device-text">全部联动将不生效</div> -->
              </div>
              <div class="device-item-right">
                <img
                  class="arrow-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
                />
              </div>
            </div>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="() => deleteChannel(item, index)">
                <img
                  class="refuse-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/delete.png')"
                />
              </van-button>
            </template>
          </van-swipe-cell>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img :src="noDataImg" />
          </div>
          <div class="no-data-btn">
            <div class="add-device-btn" @click="addDevice">{{ $t('add') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="footer-btn" @click="confirmClick" v-if="type === 'add'">
        {{ $t('confirm') }}
      </div>
      <div class="footer-btn footer-delete-btn" @click="handleDelete" v-else>
        {{ $t('delete') }}
      </div>
    </div>
    <!-- 编辑区域名称 -->
    <edit-group-name ref="edirGroupName" :name="defenseRecord.groupName" @confirm="groupNameConfirm"></edit-group-name>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
import EditGroupName from './EditGroupName.vue'
import { transformXml } from '@/utils/common'
import { getDefenseGroupList, addDefenseGroup, deleteDefenseRecord, urlDefenseSwitchNodes } from '@/api/defense'
import { appRequestDevice } from '@/utils/appbridge'
export default {
  name: 'AddDefense',
  components: {
    NavBar,
    EditGroupName
  },
  data() {
    return {
      deviceList: [
        // { chlName: 'IPC', type: 0 },
        // { chlName: 'IPC', type: 0 },
        // { chlName: 'Sensor', type: 1 },
        // { chlName: 'IPC', type: 0 }
      ],
      id: null, // 记录的id 编辑才有
      type: 'add', // 新增还是编辑 0 新增 1 编辑
      confirmFn: this.$utils.debounceFun(this.handleConfirm)
    }
  },
  created() {
    if (this.defenseRecord && this.defenseRecord.id) {
      // 说明是编辑
      this.type = 'edit'
      this.$route.meta.title = this.$t('edit')
    } else {
      this.type = 'add'
      this.$route.meta.title = this.$t('add')
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('defense', ['noDataImg', 'channelList', 'defenseRecord']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    showChannelList() {
      return this.defenseRecord && this.defenseRecord.channelList && this.defenseRecord.channelList.length
    }
  },
  methods: {
    ...mapMutations('defense', [
      'SET_CHANNEL_RECORD',
      'SET_CHANNEL_LIST',
      'SET_DEFENSE_RECORD',
      'SET_DEFENSE_GROUP_LIST'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 查询布防撤防列表
    getDefenseList() {
      getDefenseGroupList({})
        .then(({ data }) => {
          this.$loading.hide()
          const resData = data || []
          console.log('布防data', data)
          // that.dataList = resData
          this.SET_DEFENSE_GROUP_LIST(resData)
        })
        .catch(error => {
          console.error(error)
        })
    },
    // 新增设备
    addDevice() {
      // 保存当前的通道
      // console.log('保存当前的通道 deviceList', [...this.deviceList])
      this.SET_CHANNEL_LIST([...this.deviceList])
      this.$router.push({ name: 'addChannel' })
    },
    confirmClick() {
      this.$loading.show()
      this.confirmFn()
    },
    // 添加分组
    async handleConfirm() {
      // 判断是否添加了通道
      const { groupName, channelList } = this.defenseRecord
      // 允许添加没有通道的空的分组
      // if (!channelList.length) {
      //   this.$toast(this.$t('pleaseAddCameraSensor'))
      //   return
      // }
      // 判断是否有不同设备下的通道
      const snSet = new Set(channelList.map(item => item.sn))
      if (snSet.size > 1) {
        this.$loading.hide()
        this.$toast(this.$t('onlySameDevice'))
        return
      }
      const params = {
        groupName,
        status: 0, // 默认是0
        details: channelList.map(item => {
          const { sn, chlIndex, extra } = item
          return {
            sn,
            chlIndex,
            extra: JSON.stringify(extra)
          }
        })
      }
      const that = this
      try {
        const callback = async msg => {
          this.$loading.hide()
          if (msg === 'SUCCESS') {
            await addDefenseGroup(params)
            that.back()
          } else {
            // this.$toast(this.$t('addFail'))
          }
        }
        this.updateDeviceStatus({ status: 0, channelList: params.details }, callback)
      } catch (err) {
        this.$loading.hide()
        console.error(err)
      }
      // this.$router.go(-1)
    },
    // position 为关闭时点击的位置
    beforeClose({ name, position, instance }) {
      // console.log('name', name, 'position', position, 'instance', instance)
      const index = name.split('-')[1]
      const tips = {
        message: this.$t('deleteDeviceConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      switch (position) {
        case 'left':
        case 'cell':
        case 'outside':
          instance.close()
          break
        case 'right':
          this.$dialog
            .confirm(tips)
            .then(() => {
              // on confirm
              const deviceList = this.deviceList.slice()
              deviceList.splice(index, 1)
              this.deviceList = deviceList
              instance.close()
            })
            .catch(() => {
              // on cancel
              //   instance.open()
            })
          break
      }
    },
    // 编辑IPC联动项 把信息存过去
    editIPCLinkage(record, index) {
      console.log('record', record)
      this.SET_CHANNEL_RECORD(record)
      this.$router.push({ name: 'ipcSetting', params: { index, from: 'edit', id: record.id || 0 } })
    },
    handleEditName() {
      this.$refs.edirGroupName.show = true // 编辑区域名称
    },
    async groupNameConfirm(val) {
      this.$refs.edirGroupName.show = false
      const { channelList } = this.defenseRecord
      if (this.type === 'edit') {
        // 编辑状态下的修改直接发送请求
        const params = {
          ...this.defenseRecord,
          groupName: val,
          details: channelList.map(item => {
            const { sn, chlIndex, extra } = item
            return {
              sn,
              chlIndex,
              extra: JSON.stringify(extra)
            }
          })
        }
        delete params.channelList
        this.$loading.show()
        try {
          await addDefenseGroup(params)
          this.SET_DEFENSE_RECORD({
            ...this.defenseRecord,
            groupName: val
          })
          await this.getDefenseList()
        } catch (err) {
          console.error(err)
        } finally {
          this.$loading.hide()
        }
      } else {
        // 新增的直接修改
        this.SET_DEFENSE_RECORD({
          groupName: val,
          channelList
        })
      }
    },
    // 删除分组
    async handleDelete() {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()
        // 删除当前分组
        const { id } = this.defenseRecord
        const params = {
          groupIdList: [id]
        }
        // console.log('defenseRecord', this.defenseRecord)
        // 将分组中关联的通道恢复到布防状态
        // 找到对应布防组的通道
        const callback = async msg => {
          this.$loading.hide()
          if (msg === 'SUCCESS') {
            await deleteDefenseRecord(params)
            await this.getDefenseList()
            this.back()
          } else {
            // that.$toast(that.$t('deleteFail'))
          }
        }
        // 发送协议到对应的设备--恢复到布防状态，默认为外出布防
        this.updateDeviceStatus({ ...this.defenseRecord, status: 1 }, callback)
      } catch (err) {
        console.log(err)
      } finally {
        this.$loading.hide()
      }
    },
    // 删除通道
    async deleteChannel(item, index) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      try {
        await this.$dialog.confirm(tips)
        const { channelList } = this.defenseRecord
        if (this.type === 'edit') {
          channelList.splice(index, 1)
          const { id } = item
          // 编辑状态下的删除直接发送请求
          const params = {
            detailIdList: [id]
          }
          this.$loading.show()
          try {
            await deleteDefenseRecord(params)
            this.SET_DEFENSE_RECORD({
              ...this.defenseRecord,
              channelList
            })
            await this.getDefenseList()
          } catch (err) {
            console.error(err)
          } finally {
            this.$loading.hide()
          }
        } else {
          // 新增的添加直接删除
          channelList.splice(index, 1)
          this.SET_DEFENSE_RECORD({
            ...this.defenseRecord,
            channelList
          })
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.$loading.hide()
      }
    },
    // 更新设备布撤防状态
    async updateDeviceStatus(item, callback) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 在家布防 2 外部布防
      const { status, channelList } = item
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }
      // 跟设备交互
      const { sn } = channelList[0]
      const req = {
        devId: sn,
        url: 'editNodeDefenseStatus',
        params: urlDefenseSwitchNodes(status, channelList)
      }
      const that = this
      // console.log('请求参数', req)
      appRequestDevice(req, function (res) {
        // console.log('返回结果', res)
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        const errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (xmlObject.response.status == 'success') {
            // 处理结果
            if (callback) callback('SUCCESS')
          } else {
            if (callback) callback('ERROR')
          }
        } else {
          if (errorCode === '550') {
            // 超时提示连接设备失败
            that.$toast(that.$t('deviceDisconnected'))
          } else {
            that.$toast(that.$t(`errorCode.${errorCode}`))
          }
          if (callback) callback('ERROR')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.add-defense-wrapper {
  position: relative;
  height: 100%;
  overflow: auto;
  .add-defense-content {
    // height: calc(100% - 44px);
    height: calc(100% - 120px);
    padding: 35px 16px 0px 16px;
    box-sizing: border-box;
    overflow: auto;
  }
  .add-defense-head {
    padding: 8px 15px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-defense-title {
      height: 24px;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      // line-height: 24px;
      white-space: nowrap;
    }
    .add-defense-text {
      display: flex;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      line-height: 24px;
      overflow: hidden;
      margin-left: 20px;
    }
    .defense-ellipsis-text {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .arrow-img {
      width: 24px;
      height: 24px;
    }
  }
  .add-defense-device {
    height: 32px;
    line-height: 32px;
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }
  .device-content {
    // min-height: 180px;
  }
  .device-list-wrapper {
    border-radius: 10px;
    overflow: hidden;
  }
  .device-item-wrapper {
    height: 46px;
    box-sizing: border-box;
    display: flex;
    padding: 12px 16px;
    .device-item-left {
      flex: 1;
      height: 100%;
      .device-title {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
        margin-bottom: 10px;
      }
      .device-text {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .device-item-right {
      width: 24px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 120px;
      img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-btn {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
    }
    .add-device-btn {
      width: 102px;
      height: 40px;
      border-radius: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
    }
  }
  .footer-btn {
    width: 343px;
    height: 46px;
    border-radius: 10px;
  }
}
.swipe-right-btn {
  height: 100%;
  .van-button__content .van-button__text {
    height: 24px !important;
  }
  .refuse-img {
    width: 24px;
    height: 24px;
  }
}
</style>
