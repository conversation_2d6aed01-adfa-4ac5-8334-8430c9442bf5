.ipc-upgrade-list {
  ::v-deep.van-popup--center {
    width: 320px;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 6px;
    right: 6px;
  }
  ::v-deep.van-popup {
    width: 300px;
  }
  .pop-dialog {
    width: 320px;
    border-radius: 8px 8px 8px 8px;
    .pop-div {
      position: relative;
    }
    .dialog-title {
      width: 300px;
      font-weight: 700;
      height: 54px;
      line-height: 54px;
      font-size: 15px;
      color: $black-color;
      text-align: center;
      border-bottom: 1px solid $light-gray-color;
    }
    .update-box {
      padding: 8px;
      .update-content {
        max-height: 394px;
        overflow-y: scroll;
        line-height: 22px;
        padding: 0 10px 18px 16px;
        word-break: break-all;
      }
    }
    .dialog-close-img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 13px;
      height: 13px;
      img {
        width: 100%;
        height: 100%；;
      }
    }
  }
  .title-text {
    height: 28px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title-text-img {
        width: 35px;
        height: 27px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .title-text-button {
    width: 70px;
    height: 25px;
    line-height: 25px;
    border-radius: 12.5px 12.5px 12.5px 12.5px;
    background: $white-color;
    text-align: center;
    border: 1px solid $--color-primary;
    color: $--color-primary;
  }
  .view-btn {
    color: $--color-primary;
    font-weight: 500;
    font-size: 12px;
  }
  .title {
    color: $gray-color;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    font-weight: 700;
  }
  .title-text-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 27px;
    margin-left: 15px;
    max-width: 190px;
  }
  .download-status {
    color: $--color-primary;
    font-size: 12px;
    display: flex;
    .download-status-text {
      margin-right: 8px;
    }
  }
  .download-status-box {
    color: $--color-primary;
    font-size: 12px;
    .download-status-text {
      margin-right: 8px;
    }
    .upgrade-progress {
      margin: 20px 0px 8px 0px;
    }
  }
  .ipc-upgrade {
    .container {
      background-color: $white-color;
      padding: 15px 15px;
    }
  }
  // 列表通用
  .list-content {
    padding-top: 20px;
    .list-content-row {
      display: flex;
      line-height: 24px;
      .label {
        width:36%;
        flex-shrink: 0;
        color: $gray-color;
      }
      .value {
        width:64%;
        flex: 1;
        word-wrap: break-word;
        white-space: pre-wrap;
        color: $black-color;
      }
      .zh-label{
        width:20%;
      }
      .zh-value{
        width:80%;
      }
    }
    .download-tip {
      line-height: 24px;
      font-size: 12px;
    }
    .has-latest-version {
      color: $green-color;
    }
  }
}