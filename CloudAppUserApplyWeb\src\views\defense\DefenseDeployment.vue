<template>
  <div class="defense-deployment-wrapper">
    <nav-bar
      @clickLeft="back"
      :showPlus="true"
      @showPlus="handleAdd"
      :showSetting="true"
      @showSetting="handleSetting"
    ></nav-bar>
    <div class="defense-deployment-content">
      <template v-if="!reqDefenseList || defenseGroupList.length">
        <tvt-better-scroll
          class="tvt-better-scroll"
          @pullingUp="pullingUp"
          @pullingDown="pullingDown"
          :pullingStatus="pullingStatus"
        >
          <defense-list :dataList="defenseGroupList" @handleClick="handleClick" @handleEdit="handleEdit" />
        </tvt-better-scroll>
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <img
            :src="noDataImg ? noDataImg : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')"
          />
        </div>
        <div class="no-data-btn">
          <div class="add-device-btn" @click="handleAdd">{{ $t('add') }}</div>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="footer-icon-list">
        <div class="footer-icon-item" @click="handleBtnClick(1)">
          <!-- <img class="footer-icon" :src="require('@/assets/img/common/defense/out_defense_icon.png')" /> -->
          <theme-image class="footer-icon" alt="out-defense-icon" imageName="out_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(2)">
          <!-- <img class="footer-icon" :src="require('@/assets/img/common/defense/home_defense_icon.png')" /> -->
          <theme-image class="footer-icon" alt="home-defense-icon" imageName="home_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(0)">
          <!-- <img class="footer-icon" :src="require('@/assets/img/common/defense/cancel_defense_icon.png')" /> -->
          <theme-image class="footer-icon" alt="cancel-defense-icon" imageName="cancel_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(3)">
          <!-- <img class="footer-icon" :src="require('@/assets/img/common/defense/cancel_warn_icon.png')" /> -->
          <theme-image class="footer-icon" alt="cancel-warn-icon" imageName="cancel_warn_icon.png" />
        </div>
      </div>
    </div>
    <!-- 一键布防/撤防 弹窗 -->
    <one-click-defense
      ref="oneClickDefense"
      :defenseType="defenseType"
      :reqStatusList="reqStatusList"
      @cancel="defenseCancel"
      @confirm="defenseConfirm"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import OneClickDefense from './OneClickDefense.vue'
import DefenseList from './DefenseList.vue'
import { mapState, mapMutations } from 'vuex'
import { transformXml } from '@/utils/common'
import { appBack, appRequestDevice } from '@/utils/appbridge'
import { IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import ThemeImage from '@/components/ThemeImage.vue'
import {
  defenseChannelList,
  getDefenseGroupList,
  getDefenseDetail,
  addDefenseGroup,
  urlDefenseSwitchNodes,
  urlDisalarmSwitchNodes
} from '@/api/defense'
// import { transferRequestList } from '@/api/transfer.js'
export default {
  name: 'ShareManage',
  components: {
    NavBar,
    OneClickDefense,
    DefenseList,
    ThemeImage
  },
  props: {},
  data() {
    return {
      pageNum: 1,
      pullingStatus: 0,
      defenseType: 0, // 布防/撤防 0为一键撤防 1表示外出布防 2表示在家布防 3表示一键消警
      reqStatusList: [], // 各个分组布防撤防状态
      reqDefenseList: false, // 是否请求数据
      ipcLinkageList: IPC_LINKAGE_LIST_FULLNAME()
    }
  },
  created() {},
  mounted() {
    this.getDefenseList() // 获取所有布防撤防列表
    if (!this.initStatus) {
      this.getChannelList() // 获取所有通道
      this.SET_INIT_STATUS(true)
    }
    // 将无数据图片储存起来
    const imgSrc = require('@/assets/img/common/defense/no_data.png')
    const callback = imgBase64 => {
      this.SET_NO_DATA_IMG(imgBase64)
    }
    this.converImageToBase64(imgSrc, callback)
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('defense', ['initStatus', 'noDataImg', 'defenseGroupList', 'groupChannelList', 'capabilityObj']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('defense', [
      'SET_INIT_STATUS',
      'SET_NO_DATA_IMG',
      'SET_ALL_CHANNEL_LIST',
      'SET_SITE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_DEFENSE_GROUP_LIST',
      'SET_GROUP_CHANNEL_LIST',
      'SET_DEFENSE_RECORD'
    ]),
    back() {
      appBack()
    },
    pullingUp(callback) {
      this.getDefenseList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.pageNum = 1
      this.getDefenseList({ type: 'down', callback })
    },
    // 图片转base64
    converImageToBase64(imgUrl, callback) {
      const image = new Image()
      image.crossOrigin = 'anonymous'
      image.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        canvas.height = image.naturalHeight
        canvas.width = image.naturalWidth
        ctx.drawImage(image, 0, 0)
        const dataUrl = canvas.toDataURL()
        callback && callback(dataUrl)
      }
      image.src = imgUrl
    },
    // 查询所有通道列表
    async getChannelList() {
      try {
        const res = await defenseChannelList({})
        const resData = res.data || []
        // console.log('通道data', resData)
        // 遍历所有的通道列表，构造成站点、设备、通道的树形结构
        this.createChannelTree(resData)
      } catch (error) {
        console.error(error)
      }
    },
    // 查询布防撤防列表
    async getDefenseList({ callback } = {}) {
      try {
        this.$loading.show()
        this.reqDefenseList = false
        const res = await getDefenseGroupList({})
        const resData = res.data || []
        // console.log('布防data', resData)
        // that.dataList = resData
        this.SET_DEFENSE_GROUP_LIST(resData)
        this.reqDefenseList = true
        // 遍历所有的分组，拿到groupIdList，请求所有的detail信息
        const ids = resData.map(item => item.id) || []
        if (ids.length) {
          this.getAllDetails(ids)
        }
      } catch (error) {
        this.reqDefenseList = true
        console.error(error)
        this.$loading.hide()
      } finally {
        this.$loading.hide()
        callback && callback()
      }
    },
    // 查询所有分组下面的通道
    async getAllDetails(ids) {
      const res = await getDefenseDetail({ ids })
      const resData = res.data || []
      this.SET_GROUP_CHANNEL_LIST(resData)
    },
    // 构建站点、设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const siteChannelList = []
      const siteIndexObj = {} // 记录站点Id及其在通道树中的索引
      const deviceIndexObj = {} // 记录站点Id——设备sn及其在通道树中的索引
      const channelObj = {} // 记录站点Id-设备sn-通道chlIndex及其对应的通道信息
      const capabilityObj = {} // 记录设备sn-通道chlIndex及其对应的能力集
      allChannelList.forEach(item => {
        const { siteName, siteId, sn, devName, chlIndex, capability } = item
        channelObj[`${siteId}~${sn}~${chlIndex}`] = { ...item }
        let temp = null
        // 判断站点
        if (siteIndexObj[siteId] !== undefined) {
          // 说明站点存在
          const index = siteIndexObj[siteId]
          temp = siteChannelList[index]
          temp.children = siteChannelList[index].children || []
          // 判断设备是否存在
          if (deviceIndexObj[`${siteId}~${sn}`] !== undefined) {
            // 说明设备存在
            const index2 = deviceIndexObj[`${siteId}~${sn}`]
            // 添加通道
            temp.children[index2].children.push({ ...item })
          } else {
            // 说明设备不存在
            const temp3 = { sn, devName, children: [{ ...item }] }
            deviceIndexObj[`${siteId}~${sn}`] = temp.children.length // 记录下设备的索引
            temp.children.push(temp3)
          }
        } else {
          // 说明站点不存在，直接添加
          temp = { siteName, siteId, children: [] }
          siteIndexObj[siteId] = siteChannelList.length // 记录下站点的索引
          // 继续添加设备和通道
          deviceIndexObj[`${siteId}~${sn}`] = temp.children.length
          temp.children.push({ sn, devName, children: [{ ...item }] })
          siteChannelList.push(temp)
        }
        // 找到能力集
        if (capability) {
          const { supportFun = [] } = JSON.parse(capability)
          capabilityObj[`${sn}~${chlIndex}`] = supportFun.slice()
        }
      })
      // console.log('capabilityObj', capabilityObj)
      this.SET_ALL_CHANNEL_LIST(allChannelList)
      this.SET_SITE_CHANNEL_LIST(siteChannelList)
      this.SET_CHANNEL_OBJ(channelObj)
      this.SET_CAPABILITY_OBJ(capabilityObj)
    },
    // 添加分组
    handleAdd() {
      // 判断分组数量是否超过50
      if (this.defenseGroupList && this.defenseGroupList.length >= 50) {
        this.$toast(this.$t('groupLimit', { limit: 50 }))
        return
      }
      this.SET_DEFENSE_RECORD({
        groupName: this.$t('areaGroup'),
        channelList: []
      })
      this.$utils.routerPush({
        path: '/defense/addEditDefense'
      })
    },
    // 进入设置页面
    handleSetting() {
      this.$utils.routerPush({
        path: '/defense/groupSetting'
      })
    },
    // 编辑分组
    handleEdit() {
      this.$utils.routerPush({
        path: '/defense/addEditDefense',
        query: { id: 1 }
      })
    },
    // 一键布防/撤防弹窗确认回调
    defenseConfirm() {
      this.$refs.oneClickDefense.show = false
      setTimeout(() => {
        this.defenseType = 0
      }, 300)
    },
    // 一键布防/撤防弹窗取消回调
    defenseCancel() {
      this.$refs.oneClickDefense.show = false
      setTimeout(() => {
        this.defenseType = 0
      }, 300)
    },
    // 根据设备能力集找到所有联动项
    getIpcLinkageList(sn, chlIndex) {
      const newIpcLinkageList = JSON.parse(JSON.stringify(this.ipcLinkageList))
      // 找到设备支持的能力集，过滤出支持的能力集选项
      const supportFun = this.capabilityObj[`${sn}~${chlIndex}`] || []
      // 根据能力集找到可以支持联动项
      const filterLinkageList = newIpcLinkageList.filter(item => supportFun.includes(item.value))
      return filterLinkageList
    },
    // 单个分组检查请求状态 0 未全部请求完 1 全部请求成功 2 有请求失败
    checkReqStatus(reqStatus, callback) {
      if (reqStatus.some(val => val.status === 2)) {
        // 有请求失败则回调失败
        callback && callback('ERROR', reqStatus)
      } else if (reqStatus.every(val => val.status === 1)) {
        // 全部请求成功才回调成功
        callback && callback('SUCCESS', reqStatus)
      }
    },
    // 点击分组切换布防撤防
    async handleClick(item) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 外部布防 2 在家布防
      const { id, status } = item
      const newStatus = status > 0 ? 0 : 1
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      const channelList = this.groupChannelList.filter(item => item.groupId === id)
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接更新状态
        await this.updateGroupStatus(item.id, newStatus)
        return
      }
      /* 新逻辑 */
      // 发送消息更新分组的布撤防状态
      this.$loading.show()
      const that = this
      const callback = (msg, reqStatus) => {
        if (msg === 'SUCCESS') {
          // 更新分组云后台的布撤防状态
          that.updateGroupStatus(id, newStatus, () => {
            that.$loading.hide()
          })
        } else {
          // 失败则提示失败
          // 找到失败且code不为200的
          const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
          if (reqItem) {
            const { code } = reqItem
            if (Number(code) === 550) {
              // 超时提示连接设备失败
              that.$toast(that.$t('deviceDisconnected'))
            } else {
              that.$toast(that.$t(`errorCode.${code}`))
            }
          } else {
            that.$toast(this.$t('editFail'))
          }
          that.$loading.hide()
        }
      }
      this.updateGroupDeviceStatus(id, newStatus, callback)
    },
    // 更新设备分组状态 -- 按照勾选和未勾选的联动项发送相反的指令
    updateGroupDeviceStatus(id, defenseStatus, callback) {
      // 找到对应分组下的通道
      const channelList = this.groupChannelList.filter(item => item.groupId === id)
      // 勾选的联动项和未勾选的联动项发送相反的指令
      // 遍历channelList，将每个channel拆分成两份：勾选的联动项和未勾选的联动项
      const checkChannelList = []
      const noChannelList = []
      channelList.forEach(item => {
        let { extra, sn, chlIndex } = item
        if (typeof extra === 'string') {
          extra = JSON.parse(extra)
        }
        const { bypassSwitch = 0, linkageList = [] } = extra
        // 根据当前设备能力集支持的所有联动项目找到未被勾选的联动项
        const ipcLinkageList = this.getIpcLinkageList(sn, chlIndex)
        const noLinkageList = ipcLinkageList
          .filter(item => linkageList.indexOf(item.value) === -1)
          .map(item => item.value)
        // 有已勾选的联动项
        if (linkageList.length) {
          checkChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: linkageList.slice()
            }
          })
        }
        // 有未勾选的联动项
        if (noLinkageList.length) {
          noChannelList.push({
            ...item,
            extra: {
              bypassSwitch,
              linkageList: noLinkageList.slice()
            }
          })
        }
      })
      const reqStatus = [
        { status: 0, code: 200 },
        { status: 0, code: 200 }
      ] // 请求状态：0 pending 1 success 2 fail
      const reqArr = [checkChannelList, noChannelList]
      const that = this
      reqArr.forEach((item, index) => {
        if (item.length) {
          const { sn } = item[0]
          const req = {
            devId: sn,
            url: 'editNodeDefenseStatus',
            params: urlDefenseSwitchNodes(defenseStatus, item, index) // 用index区分是勾选还是未勾选，对应的布撤防状态是相反的
          }
          // console.log('请求设备req', req)
          appRequestDevice(req, function (res) {
            let resData = res.replace(/\\t|\\n/g, '')
            resData = JSON.parse(resData)
            // const errorCode = resData.code
            // console.log('获取响应resData', resData)
            if (resData.code == 200) {
              const xmlObject = transformXml(resData.body)
              if (xmlObject.response.status == 'success') {
                // 处理结果
                reqStatus[index] = {
                  status: 1,
                  code: 200
                }
              } else {
                reqStatus[index] = {
                  status: 2,
                  code: 200
                }
              }
            } else {
              reqStatus[index] = {
                status: 2,
                code: resData.code
              }
            }
            // 检查单个分组请求都发送完
            that.checkReqStatus(reqStatus, callback)
          })
        } else {
          // 没有联动项默认不发送请求，直接默认成功
          reqStatus[index] = {
            status: 1,
            code: 200
          }
          // 检查单个分组请求都发送完
          that.checkReqStatus(reqStatus, callback)
        }
      })
    },
    // 一键切换布/撤防等回调函数
    reqStatusCallFn(msg, tag, index, code) {
      if (msg === 'success') {
        this.reqStatusList[index].reqStatus = 1
      } else {
        this.reqStatusList[index].reqStatus = 2
      }
      if (code) {
        this.reqStatusList[index].code = code
      }
      this.$nextTick(() => {
        // 检查是否全部请求完成
        if (this.reqStatusList.every(item => item.reqStatus !== 0)) {
          setTimeout(() => {
            // this.$refs.oneClickDefense.show = false // 弹窗自动关闭--延时关闭，避免接口返回较快时弹窗一闪而过
            this.defenseCancel()
          }, 1000)
          this.$loading.hide()
          // 判断一键消警的结果，成功返回成功，失败返回失败
          if (tag === 3) {
            if (this.reqStatusList.some(item => item.reqStatus === 2)) {
              // 有一个失败则提示失败
              this.$toast(this.$t('removalFail'))
            } else {
              // 全部成功才成功
              this.$toast(this.$t('removalSuccess'))
            }
          }
        }
      })
    },
    // 一键布撤防消警点击事件
    handleBtnClick(tag) {
      // tag 1 外出布防 2 在家布防 0 一键撤防 3 一键消警
      // 外出布防：组内通道/传感器将布防
      // 在家布防：组内开启旁路的通道/传感器将撤防（按撤防配置项取消联动），组内未开启旁路的通道/传感器将布防（按设备端参数配置布防）
      if (this.defenseGroupList.length === 0) {
        this.$toast(this.$t('pleaseAddGroup'))
        return
      }
      // 遍历所有分组，按照分组发送--因为同一分组只能有一个设备下的通道
      const allGroupIdSet = new Set()
      const groupObj = {} // 记录所有分组的信息 groupId为key value为分组信息
      this.defenseGroupList.forEach(item => {
        groupObj[item.id] = { ...item }
        allGroupIdSet.add(item.id)
      })
      const groupChannelObj = {}
      const groupIdSet = new Set()
      this.groupChannelList.forEach(item => {
        const { groupId } = item
        if (groupIdSet.has(groupId)) {
          groupChannelObj[groupId].push(item)
        } else {
          groupChannelObj[groupId] = [item]
        }
        groupIdSet.add(groupId)
      })
      const groupList = Array.from(allGroupIdSet)
      if (tag === 3) {
        // 点击一键消警时，需要判断所有分组是否带有通道，如果没有任何通道，则吐司提示“分组下未添加设备”，并返回
        const flag = groupList.every(groupId => {
          const channelList = groupChannelObj[groupId]
          return !(channelList && channelList.length)
        })
        if (flag) {
          this.$toast(this.$t('groupNoDevice'))
          return
        }
      } else {
        this.defenseType = tag
        this.$refs.oneClickDefense.show = true
      }
      // 判断所有分组是否带有
      // 循环不同分组依次发送消息
      // 生成每个分组的请求状态
      const reqStatusList = groupList.map(id => {
        const { groupName } = groupObj[id]
        return {
          id,
          groupName,
          reqStatus: 0 // 分组请求状态 0-pending 1-success 2-fail
        }
      })
      this.reqStatusList = reqStatusList
      // this.$loading.show()
      groupList.forEach((groupId, index) => {
        // 没有通道不需要发送设备请求直接切换状态
        const channelList = groupChannelObj[groupId] // 对应分组下的通道
        if (!(channelList && channelList.length)) {
          const callback = msg => {
            this.reqStatusCallFn(msg, tag, index)
          }
          if (tag !== 3) {
            // 发送消息更新分组云后台的布撤防状态
            this.updateGroupStatus(groupId, tag, callback)
          } else {
            // 一键消警中不带通道的分组默认成功
            this.reqStatusCallFn('success', tag, index)
          }
          return
        }
        // 一键消警走原来的逻辑
        if (tag === 3) {
          // 发送每个分组下设备的消息
          const { sn } = channelList[0]
          const req = {
            devId: sn,
            url: 'oneKeyDisalarm',
            params: urlDisalarmSwitchNodes(channelList)
          }
          const that = this
          console.log('一键消警请求req', req)
          appRequestDevice(req, function (res) {
            let resData = res.replace(/\\t|\\n/g, '')
            resData = JSON.parse(resData)
            console.log('一键消警返回res', res)
            const code = resData.code
            if (resData.code == 200) {
              const xmlObject = transformXml(resData.body)
              if (xmlObject.response.status == 'success') {
                // 处理结果
                that.reqStatusList[index].reqStatus = 1 // 请求成功
                that.reqStatusList[index].code = code
                // 更新所有分组请求状态
                that.reqStatusCallFn('success', tag, index, code)
              }
            } else {
              // 更新所有分组请求状态
              that.reqStatusCallFn('error', tag, index, code)
            }
          })
        } else {
          // 布防/撤防需要根据勾选的联动项和未勾选的联动项发送相反的指令
          const that = this
          // 更新分组请求状态
          const callback2 = (msg, code) => {
            // 更新所有分组请求状态
            that.reqStatusCallFn(msg, tag, index, code)
          }
          const callback = (msg, reqStatus) => {
            if (msg === 'SUCCESS') {
              // 更新分组云后台的布撤防状态
              that.updateGroupStatus(groupId, tag, callback2)
            } else {
              // 失败则提示失败
              // 找到失败且code不为200的
              const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
              if (reqItem) {
                const { code } = reqItem
                // 更新所有分组请求状态
                that.reqStatusCallFn('error', tag, index, code)
              }
            }
          }
          // 给设备发送布撤防状态切换请求
          this.updateGroupDeviceStatus(groupId, tag, callback)
        }
      })
    },
    // 更新某个分组的布撤防状态 -- 发送云后台的接口
    async updateGroupStatus(groupId, status, callback) {
      const idx = this.defenseGroupList.findIndex(item => item.id === groupId)
      if (idx === -1) {
        return
      }
      const groupItem = this.defenseGroupList[idx]
      const params = {
        ...groupItem,
        status
      }
      try {
        await addDefenseGroup(params)
        // 更新对应布防组的状态status
        // console.log('更新对应布防组状态', status)
        this.defenseGroupList[idx].status = status
        this.SET_DEFENSE_GROUP_LIST([...this.defenseGroupList])
        if (callback) callback('success')
      } catch (err) {
        console.error(err)
        // 操作失败则重新请求分组列表
        this.getDefenseList()
        if (callback) callback('error')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.defense-deployment-wrapper {
  height: 100%;
  overflow: hidden;
  .defense-deployment-content {
    height: calc(100% - 125px);
    overflow: auto;
    .tvt-better-scroll {
      height: 100%;
    }
  }
  .footer {
    width: 342px;
    height: 48px;
    left: calc(50% - 171px);
    bottom: 35px;
    margin: 0px auto;
    padding: 12px 0px;
    box-sizing: border-box;
    border-radius: 10px;
    .footer-icon-list {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .footer-icon-item {
        flex: 1;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 24px;
          height: 24px;
        }
      }
      & .footer-icon-item:not(:first-child) {
        border-left: 1px solid var(--bg-color-secondary, #eeeeee);
      }
    }
  }
  .no-data {
    width: 100%;
    height: calc(100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-btn {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
    }
    .add-device-btn {
      width: 102px;
      height: 40px;
      border-radius: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
    }
  }
}
</style>
