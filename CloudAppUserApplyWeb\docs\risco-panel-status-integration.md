# Risco系统面板状态集成文档

## 功能概述

基于Risco系统实际API返回数据，优化AlarmSystemStatus.vue组件的状态显示逻辑，正确解析布撤防状态和在线离线状态。

## 🔍 Risco API数据结构分析

### 关键数据字段

```json
{
  "result": 0,
  "status": 200,
  "response": {
    "state": {
      "isOnline": true,
      "media": 2,
      "status": {
        "partitions": [
          {
            "id": 0,
            "armedState": 3,  // 分区0：全布防
            "readyState": 2,
            "alarmState": 0
          },
          {
            "id": 1, 
            "armedState": 1,  // 分区1：离家布防
            "readyState": 2,
            "alarmState": 0
          }
        ],
        "systemStatus": 2,     // 系统整体状态
        "trouble": true,       // 故障状态
        "batteryLow": true,    // 电池低电
        "acLost": false        // 交流电丢失
      }
    }
  }
}
```

## 🔧 技术实现

### 1. 数据结构验证优化

```javascript
// 支持Pima和Risco两种不同的数据结构
export function isValidPanelStateData(data) {
  // Pima系统：data.state.status是数组
  if (data?.state?.status && Array.isArray(data.state.status)) {
    return data.state.status.length > 0
  }
  // Risco系统：data.state.status是对象，包含systemStatus
  if (data?.state?.status && typeof data.state.status === 'object') {
    return data.state.status.systemStatus !== undefined
  }
  return false
}
```

### 2. Risco状态解析逻辑

```javascript
// Risco系统直接使用系统状态
export function parsePanelStatus(data, systemType) {
  if (systemType === 'Risco') {
    // 直接使用系统状态
    const statusObj = data.state.status
    
    if (statusObj.systemStatus !== undefined) {
      return mapRiscoStatus(statusObj.systemStatus)
    }
    
    return 'UNKNOWN'
  }
}

// Risco系统状态码映射
export function mapRiscoStatus(statusCode) {
  const statusMap = {
    0: 'DISARMED',    // 撤防
    1: 'ARMED_AWAY',  // 离家布防
    2: 'ARMED_HOME',  // 在家布防
    3: 'ARMED_AWAY',  // 全布防
    // ... 其他状态码
  }
  return statusMap[statusCode] || 'UNKNOWN'
}
```

### 3. 在线状态解析

```javascript
export function parsePanelOnlineStatus(data, systemType) {
  if (!data?.state) {
    return false
  }

  // 所有系统都使用isOnline字段
  if (data.state.isOnline !== undefined) {
    return Boolean(data.state.isOnline)
  }

  // Risco系统额外检查media字段
  if (systemType === 'Risco' && data.state.media !== undefined) {
    return data.state.media > 0
  }

  return false
}
```

### 4. 故障状态解析

```javascript
export function parsePanelTroubleStatus(data, systemType) {
  const troubleInfo = {
    hasTrouble: false,
    troubleTypes: []
  }

  if (systemType === 'Risco') {
    const status = data.state.status
    
    // 检查各种故障类型
    if (status.trouble) {
      troubleInfo.hasTrouble = true
      troubleInfo.troubleTypes.push('general')
    }
    
    if (status.batteryLow) {
      troubleInfo.hasTrouble = true
      troubleInfo.troubleTypes.push('battery')
    }
    
    if (status.acLost) {
      troubleInfo.hasTrouble = true
      troubleInfo.troubleTypes.push('power')
    }
  }

  return troubleInfo
}
```

## 🎨 UI组件更新

### AlarmSystemStatus.vue 关键改进

#### 1. 状态文本显示
```javascript
displayStatus() {
  if (this.status) return this.status

  const parsedStatus = parsePanelStatus(this.panelState.data, this.systemType)
  if (parsedStatus) {
    // 获取国际化的状态文本
    const statusText = this.$t(getStatusI18nKey(parsedStatus))
    
    // 检查是否有故障状态
    const troubleInfo = parsePanelTroubleStatus(this.panelState.data, this.systemType)
    if (troubleInfo.hasTrouble) {
      // 如果有故障，在状态后添加故障标识
      return `${statusText} (${this.$t('faults')})`
    }
    return statusText
  }

  return this.$t('unknownEvent')
}
```

#### 2. 在线状态显示
```javascript
displayConnection() {
  if (this.isConnected !== null) return this.isConnected
  
  // 使用新的在线状态解析函数
  return parsePanelOnlineStatus(this.panelState.data, this.systemType)
}
```

#### 3. 状态图标选择
```javascript
statusIcon() {
  const status = this.displayStatus
  
  // 如果包含故障信息，使用故障图标
  if (status.includes('Faults') || status.includes('故障')) {
    return 'alarm-system/trouble.png'
  }
  
  // 提取基础状态（去除故障标识）
  const baseStatus = status.split(' (')[0]
  return getStatusIconName(baseStatus)
}
```

## 🌍 国际化支持

### 新增翻译内容

#### 中文翻译 (zh-CN.js)
```javascript
{
  home: '在家布防',
  away: '外出布防', 
  disarm: '撤防',
  mixed: '混合状态',
  faults: '故障',
  unknownEvent: '未知事件'
}
```

#### 英文翻译 (en-US.js)
```javascript
{
  home: 'Home',
  away: 'Away',
  disarm: 'Disarm', 
  mixed: 'Mixed',
  faults: 'Faults',
  unknownEvent: 'Unknown Event'
}
```

### 状态映射函数
```javascript
export function getStatusI18nKey(status) {
  const statusMap = {
    'DISARMED': 'disarm',
    'ARMED_HOME': 'home',
    'ARMED_AWAY': 'away', 
    'MIXED': 'mixed',
    'UNKNOWN': 'unknownEvent',
    'TROUBLE': 'faults'
  }
  return statusMap[status] || 'unknownEvent'
}
```

## 📊 状态映射表

### Risco系统状态码
| systemStatus | 含义 | 映射结果 | 中文显示 | 英文显示 |
|--------------|------|----------|----------|----------|
| 0 | 撤防 | DISARMED | 撤防 | Disarm |
| 1 | 离家布防 | ARMED_AWAY | 外出布防 | Away |
| 2 | 在家布防 | ARMED_HOME | 在家布防 | Home |
| 3 | 全布防 | ARMED_AWAY | 外出布防 | Away |

### 系统状态处理逻辑
1. **直接使用系统状态**：Risco系统直接使用`systemStatus`字段
2. **状态码映射**：通过`mapRiscoStatus`函数映射到统一状态
3. **简化处理**：避免复杂的分区状态分析，使用系统级状态

## 🔍 故障状态处理

### 故障类型检测
- **general**: `status.trouble = true`
- **battery**: `status.batteryLow = true`  
- **power**: `status.acLost = true`

### 显示逻辑
- 有故障时：`状态文本 (故障)`
- 无故障时：`状态文本`
- 故障图标：`alarm-system/trouble.png`

## 🧪 测试验证

### 测试用例

#### 1. 正常状态测试
```javascript
// 测试数据：系统撤防
const testData = {
  state: {
    isOnline: true,
    media: 2,
    status: {
      systemStatus: 0,  // 撤防
      trouble: false,
      batteryLow: false,
      acLost: false
    }
  }
}
// 预期结果：显示"撤防"，在线状态true
```

#### 2. 在家布防状态测试
```javascript
// 测试数据：在家布防（基于你提供的实际数据）
const testData = {
  state: {
    isOnline: true,
    media: 2,
    status: {
      systemStatus: 2,  // 在家布防
      trouble: false
    }
  }
}
// 预期结果：显示"在家布防"
```

#### 3. 故障状态测试
```javascript
// 测试数据：有故障（基于你提供的实际数据）
const testData = {
  state: {
    isOnline: true,
    media: 2,
    status: {
      systemStatus: 2,  // 在家布防
      trouble: true,
      batteryLow: true,
      acLost: false
    }
  }
}
// 预期结果：显示"在家布防 (故障)"，故障图标
```

#### 4. 离线状态测试
```javascript
// 测试数据：离线
const testData = {
  state: {
    isOnline: false,
    media: 0
  }
}
// 预期结果：在线状态false，显示离线图标
```

## 🎯 实现效果

### 用户体验改进
1. **准确的状态显示**：基于实际API数据正确显示布撤防状态
2. **故障状态提醒**：清晰显示系统故障信息
3. **国际化支持**：中英文状态文本自动切换
4. **视觉反馈**：不同状态对应不同图标

### 技术架构优势
1. **数据结构兼容**：同时支持Pima和Risco两种系统
2. **状态解析准确**：直接使用系统状态，简化处理逻辑
3. **错误处理完善**：数据异常时的降级处理
4. **代码可维护**：清晰的函数职责分离

## 📋 实际数据解析结果

基于你提供的Risco API数据：
```json
{
  "response": {
    "state": {
      "isOnline": true,
      "media": 2,
      "status": {
        "systemStatus": 2,
        "trouble": true,
        "batteryLow": true,
        "acLost": false
      }
    }
  }
}
```

**解析结果**：
- **系统状态**: `systemStatus: 2` → `ARMED_HOME` → 显示"在家布防"
- **在线状态**: `isOnline: true, media: 2` → 显示在线图标
- **故障状态**: `trouble: true, batteryLow: true` → 显示"在家布防 (故障)"
- **状态图标**: 使用故障图标 `alarm-system/trouble.png`

这个实现确保了AlarmSystemStatus.vue组件能够准确解析和显示Risco系统的面板状态，直接使用系统状态而不是复杂的分区状态分析，提供了简洁高效的解决方案。