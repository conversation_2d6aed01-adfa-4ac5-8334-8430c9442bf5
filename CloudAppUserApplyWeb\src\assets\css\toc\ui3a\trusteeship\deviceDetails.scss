.trusteeship-device-details {
  .device-details {
    background-color: $UI3A-light-background-color !important;
    color: $UI3A-50-white-color !important;
    .title {
      color: $UI3A-white-color !important;
    }
  }
  .configuration {
    color: $UI3A-white-color !important;
  }
  .container-ul{
    .device-configuration {
      background-color: $UI3A-light-background-color !important;
      border-bottom: 1px solid $UI3A-light-gray-color !important;
      &:last-child{
        border: 0 !important;
      }
      .configuration-item{
        color: $UI3A-white-color !important;
      }
    }
  }
  .v-li {
    background-color: $UI3A-light-background-color !important;
    .v-li-content {
      color: $UI3A-white-color !important;
    }
    .v-li-res {
      color: $UI3A-white-color !important;
    }
  }
}