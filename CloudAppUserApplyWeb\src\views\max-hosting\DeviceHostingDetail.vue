<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <div class="detail-content">
      <img src="@/assets/img/common/device.png" class="device-img" />
      <div class="device-name">
        {{ deviceDetail.deviceName }}
      </div>
      <div v-if="deviceDetail.trustStatus === SERVICE_STATUS.pending" class="time">
        {{ $t('trustTime') }}：{{ deviceDetail.hostingTimeTr }}
      </div>
      <div v-else class="time">{{ $t('residueTime') }}：<count-down :time="remainTime" @timeup="refreshDevice" /></div>
      <div class="device-status">{{ statusTr }}</div>
      <div class="detail-setting">
        <div class="detail-time black" @click="setTimeVisible = true">
          <span class="item-left">
            {{ $t('trustTime') }}
          </span>
          <span class="item-right">
            <span class="item-time">
              {{ deviceDetail.hostingTimeTr }}
            </span>
            <img src="@/assets/img/common/arrow_right.png" class="item-time-icon" />
          </span>
        </div>
        <div class="detail-time" @click="setPermissionVisible = true">
          <span class="item-left">
            {{ $t('trusteeshipPermissions') }}
          </span>
          <span class="item-right">
            <img src="@/assets/img/common/more.png" class="item-time-icon" />
          </span>
        </div>
        <div class="permission-list">
          <van-checkbox-group :value="deviceDetail.authList" ref="checkboxGroup">
            <van-checkbox
              v-for="item in DEVICE_CAPABILITY_LIST"
              class="permission-item"
              :key="item.value"
              :name="item.value"
              ref="checkboxes"
              label-position="left"
            >
              {{ item.label }}
              <template #icon="props">
                <img
                  class="img-icon"
                  :src="
                    props.checked
                      ? require('@/assets/img/common/check.png')
                      : require('@/assets/img/common/check_no.png')
                  "
                />
              </template>
            </van-checkbox>
          </van-checkbox-group>
        </div>
      </div>
    </div>
    <van-button class="footer-btn" type="primary" @click="handleHosting">
      <template v-if="deviceDetail.trustStatus === SERVICE_STATUS.pending">
        {{ $t('immediateTrusteeship') }}
      </template>
      <template v-else>
        {{ $t('cancelTrusteeship') }}
      </template>
    </van-button>
    <set-permission-popup-vue :device="deviceDetail" :visible.sync="setPermissionVisible" @submit="changePermission" />
    <set-time-popup :device="deviceDetail" :visible.sync="setTimeVisible" @submit="changeTime" />
    <cancel-device-hosting-popup ref="cancelRef" :deviceId="deviceId" :userId="userId" />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { showTimeStr2 } from '@/utils/common'
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'
import { mapState } from 'vuex'
import SetPermissionPopupVue from './components/SetPermissionPopup'
import SetTimePopup from './components/SetTimePopup'
import CancelDeviceHostingPopup from './components/CancelDeviceHostingPopup'
import CountDown from './components/CountDown'
import { serviceStatus } from './common'
import { updateDeviceHosting } from '@/api/maxHosting'

export default {
  name: 'DeviceHostingdeviceDetail',
  components: {
    NavBar,
    SetPermissionPopupVue,
    SetTimePopup,
    CancelDeviceHostingPopup,
    CountDown
  },
  data() {
    return {
      pullingStatus: 0,
      siteId: '',
      deviceId: '',
      DEVICE_CAPABILITY_LIST: DEVICE_CAPABILITY_LIST(),
      SERVICE_STATUS: serviceStatus,
      setPermissionVisible: false,
      setTimeVisible: false,
      remainTime: '',
      statusTr: ''
    }
  },
  computed: {
    ...mapState('maxHosting', ['deviceDetail', 'installerDetail'])
  },
  created() {
    const { userId, deviceId } = this.$route.params

    if (userId) {
      this.userId = userId
      this.deviceId = deviceId
      // 需要重新计算时间
      this.remainTime = this.translateRemainTime(this.deviceDetail)
      // 字符不同
      this.statusTr = this.translateStatus(this.deviceDetail)
    } else {
      this.userId = ''
      this.deviceId = ''
    }
  },
  methods: {
    clickLeft() {
      // 跳转到服务页面
      this.$router.go(-1)
    },
    handleHosting() {
      if (this.deviceDetail.trustStatus === this.SERVICE_STATUS.pending) {
        // 进入选择设备
        this.$router.push({
          path: '/max/hosting/device/select',
          query: {
            email: this.installerDetail.email,
            origin: 'device',
            installerUserId: this.userId
          }
        })
      } else {
        this.$refs.cancelRef.open()
      }
    },
    startChangeName() {
      this.sitePopupVisible = false
      this.nameInput = this.currentSite.siteName
      this.changeNameVisible = true
    },
    async changePermission(value) {
      try {
        await updateDeviceHosting(
          [
            {
              sn: this.deviceDetail.sn,
              authList: value
            }
          ],
          {
            autoErrorMsg: false
          }
        )
        this.setPermissionVisible = false
        this.deviceDetail.authList = value
        this.$toastSuccess(this.$t('settingSuccess'))
      } catch (error) {
        if (error.basic.code === 34209 || error.basic.code === 34204) {
          this.$toast(this.$t('noPermissions'))
        } else {
          this.$toast(this.$t(`errorCode.${error.basic.code}`))
        }
      }
    },
    async changeTime(value) {
      try {
        await updateDeviceHosting(
          [
            {
              sn: this.deviceDetail.sn,
              trustDuration: value
            }
          ],
          {
            autoErrorMsg: false
          }
        )
        this.deviceDetail.expireTime = Date.now() + value * 1000
        this.deviceDetail.trustDuration = value
        this.deviceDetail.hostingTimeTr = this.translateTime(this.deviceDetail)
        this.statusTr = this.translateStatus(this.deviceDetail)
        this.remainTime = this.translateRemainTime(this.deviceDetail)
        this.setTimeVisible = false
        this.$toastSuccess(this.$t('settingSuccess'))
      } catch (error) {
        if (error.basic.code === 34209 || error.basic.code === 34204) {
          this.$toast(this.$t('noPermissions'))
        } else {
          this.$toast(this.$t(`errorCode.${error.basic.code}`))
        }
      }
    },
    translateRemainTime(device) {
      if (device.trustStatus !== serviceStatus.serving) {
        return ''
      }

      let time = ''

      if (device.trustDuration === 0) {
        time = this.$t('forever')
      } else {
        const now = Date.now()

        time = device.expireTime - now
      }

      return time
    },
    translateTime(device) {
      if (device.trustDuration === 0) {
        return this.$t('forever')
      }

      return showTimeStr2(0, device.trustDuration * 1000)
    },
    translateStatus(device) {
      if (device.trustStatus === serviceStatus.pending) {
        return this.$t('waitTrustReceived')
      }
      return this.$t('duringTrust')
    },
    refreshDevice() {
      this.remainTime = '00:00:00'
      this.statusTr = this.$t('expired')
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  overflow: hidden;
  position: relative;
  .detail-content {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-direction: column;
    padding: 0 14px;

    .device-name {
      color: var(--text-color-primary, #1a1a1a);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      margin-bottom: 2px;
    }
    .time,
    .device-status {
      color: var(--icon-color-primary, #2b2b2b);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 3px;
    }
  }
  .detail-setting {
    width: 100%;
  }
  .detail-time {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--icon-color-secondary, #666666);
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
    &:first-child {
      margin-top: 20px;
    }
    &.black {
      color: var(--icon-color-primary, #2b2b2b);
    }

    .item-right {
      height: 60px;
      display: flex;
      align-items: center;
    }
    .item-time-icon {
      width: 24px;
      height: 24px;
    }
  }
  .permission-list {
    padding-left: 26px;

    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 60px;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);

      .img-icon {
        width: 24px;
        height: 24px;
      }
    }
  }

  .footer-btn {
    position: fixed;
    bottom: 10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
  }
}
</style>
<style lang="scss"></style>
