<template>
  <div class="share-success-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="share-success-content">
      <theme-image alt="avatar" class="avatar-icon" imageName="avatar.png" />
      <div class="share-success-title">{{ scanUserInfo.nick || $t('tourist') }}</div>
      <div class="share-success-text">
        <div class="share-success-desc" v-if="scanUserInfo.email">{{ scanUserInfo.email }}</div>
        <div class="share-success-desc" v-if="scanUserInfo.phone">{{ scanUserInfo.mobile }}</div>
      </div>
      <van-button v-if="showBtn" class="footer-btn" type="primary" @click="handleClick">
        {{ $t('nextStep') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { appClose } from '@/utils/appbridge'
import { mapState, mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'scanSharer',
  components: {
    NavBar,
    ThemeImage
  },
  props: {},
  data() {
    return {
      fromPage: 'h5' // 从哪里进入的页面   h5表示从H5页面进入，app表示从app扫码进入
    }
  },
  created() {
    // 从路由中获取扫码的信息
    const query = this.$route.query
    const scanInfo = {}
    if (query.email || query.phone) {
      scanInfo.nick = query.nick
      scanInfo.email = query.email
      scanInfo.phone = query.phone
      this.fromPage = 'app'
      this.SET_SCAN_USER_INFO(scanInfo)
    }
  },
  mounted() {
    console.log('scanUserInfo', this.scanUserInfo)
  },
  computed: {
    ...mapState('share', ['userInfo', 'shareUser', 'scanUserInfo']),
    showBtn() {
      return this.scanUserInfo.email || this.scanUserInfo.phone
    }
  },
  methods: {
    ...mapMutations('share', ['SET_SHARE_USER', 'SET_SCAN_USER_INFO']),
    back() {
      if (this.fromPage === 'app') {
        appClose()
      } else {
        this.$router.go(-1)
      }
    },
    handleClick() {
      // 判断邮箱是否为当前用户--不能分享给自己
      if (this.scanUserInfo && this.scanUserInfo.email === this.userInfo.email) {
        this.$toastFail(this.$t('notShareSelf'))
        return
      }
      // 将当前扫码的用户信息暂存为分享用户
      this.SET_SHARE_USER({
        active: this.scanUserInfo.email ? 'email' : 'mobile',
        email: this.scanUserInfo.email,
        mobile: this.scanUserInfo.phone
      })
      // 进入设备选择页面
      this.$utils.routerPush({
        path: '/share/chooseDevice'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.share-success-wrapper {
  height: 100%;
  overflow: hidden;
  .share-success-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 144px 24px 0px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .avatar-icon {
      width: 100px;
      height: 100px;
    }
    .share-success-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-h5-size, 18px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
    .share-success-text {
      width: 100%;
      margin: 10px 0px;
    }
    .share-success-desc {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .footer-btn {
      margin-top: 68px;
      width: 327px;
      border-radius: 23px;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
