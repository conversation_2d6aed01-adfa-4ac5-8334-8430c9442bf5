<template>
  <div class="trusteeship-permission">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="data-ul" v-if="isInit">
      <div class="v-li" v-for="(v, i) in authList" :key="'data' + i">
        <div class="v-li-label">{{ $t(v.label) }}</div>
        <van-checkbox v-model="v.flag">
          <template #icon="props">
            <img
              class="check-img"
              alt="check"
              :src="
                v.value === 'config'
                  ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_disable.png')
                  : props.checked
                  ? require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check.png')
                  : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/check_no.png')
              "
            />
          </template>
        </van-checkbox>
      </div>
    </div>
    <div class="footer">
      <div class="footer-btn" @click="confirm">
        {{ $t('confirm') }}
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appClose } from '@/utils/appbridge'
import { mapState, mapMutations } from 'vuex'
import { deepCopy } from '@/utils/common.js'
import { deviceTrusteeshipsUpdate } from '@/api/trusteeship.js'
export default {
  name: 'Permission',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      index: null,
      eidtId: null,
      isInit: false,
      from: null,
      initAuth: [],
      authList: []
    }
  },
  mounted() {
    this.index = this.$route.params.index
    this.from = this.$route.params.from
    this.eidtId = this.$route.params.id || ''
    this.initAuth = deepCopy(this.availableList[this.index].authList)
    this.authList = this.availableList[this.index].authList
    this.$nextTick(() => {
      setTimeout(() => {
        this.isInit = true
      }, 100)
    })
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('trusteeship', ['availableList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('trusteeship', ['MODIFY_AVAILABLE_LIST']),
    back(params) {
      //这个是正常的调接口返回 不要 进行判断拦截 不然会进入死循环
      if (params || !this.initAuth || !this.authList) {
        this.$router.go(-1)
        return
      }
      // 判断是否有修改  这里的修改来自于新增托管
      let isChange = this.arraysAreEqual(this.initAuth, this.authList)
      if (isChange) {
        let tips = {
          message: this.$t('isSaveModify'),
          cancelButtonText: this.$t('cancel'),
          confirmButtonText: this.$t('confirm')
        }
        if (this.style == 'UI1B') {
          tips['title'] = this.$t('tips')
        }
        this.$dialog
          .confirm(tips)
          .then(() => {
            this.confirm()
          })
          .catch(() => {
            // 默认绑定的时候会被改变 不想改变 赋回旧值
            let oldItem = { ...this.availableList[this.index], authList: this.initAuth }
            this.MODIFY_AVAILABLE_LIST({
              deleteIndex: this.index,
              data: oldItem
            })
            this.$router.go(-1)
          })
      } else {
        this.$router.go(-1)
      }
    },
    dealAuthList(list) {
      let newList = []
      if (list) {
        list.forEach(v => {
          if (v.flag) {
            newList.push(v.value)
          }
        })
      }
      return newList
    },
    updateApi() {
      let params = {
        id: this.eidtId,
        authList: this.dealAuthList(this.authList)
      }
      this.$loading.show()
      deviceTrusteeshipsUpdate(params)
        .then(() => {
          this.$loading.hide()
          this.$toast(this.$t('operationSuccess'))
          this.back('DoNotIntercept')
        })
        .catch(error => {
          this.$loading.hide()
          if (!error.basic) return
          if (error.basic.code === 32019 || error.basic.code === 32021) {
            this.back('DoNotIntercept')
          } else if (error.basic.code === 32018) {
            setTimeout(() => {
              appClose()
            }, 1000)
          }
        })
    },
    // 对比两个数组是否相同
    arraysAreEqual(oldVal, newVal) {
      let res = true
      oldVal.sort()
      newVal.sort()
      if (JSON.stringify(oldVal) === JSON.stringify(newVal)) {
        res = false
      }
      return res
    },
    confirm() {
      if (this.from === 'add') {
        this.confirmSave()
      } else if (this.from === 'edit') {
        // 从详情过来的编辑 直接调接口保存
        this.updateApi()
      }
    },
    confirmSave() {
      // 修改数据中的某一项
      let newItem = { ...this.availableList[this.index], authList: this.authList }
      this.MODIFY_AVAILABLE_LIST({
        deleteIndex: this.index,
        data: newItem
      })
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.trusteeship-permission {
  height: calc(100% - 70px);
  overflow: auto;
  .data-ul {
    background-color: var(--bg-color-white, #ffffff);
    border-top: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
    padding: 4px 0;
    font-size: var(--font-size-body2-size, 14px);
    color: var(--text-color-placeholder, #8f8e93);
    .v-li {
      height: 50px;
      line-height: 50px;
      padding: 6px 30px 6px 30px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      &:last-child {
        border: 0;
      }
    }
    .check-img {
      width: 20px;
      height: 20px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
