.defense-list-wrapper  .defense-item-wrapper {
    background-color: $vms-light-white;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-text {
    color: $gray-color;
}

.add-defense-wrapper .add-defense-head {
    background-color: $vms-light-white;
}

.add-defense-wrapper .add-defense-head .add-defense-text {
    color: $gray-color;
}

.add-defense-wrapper .add-defense-device {
    color: $gray-color;
}

.add-defense-wrapper .device-content .device-list-wrapper {
    background-color: $vms-light-white;
}

.device-content .no-data .add-device-btn {
    color: $vms-primary;
    border: 1px solid $vms-primary;
}

.device-content .device-list-wrapper .device-item-wrapper {
    border-bottom: 1px solid $border-color;
}

.ipc-setting-wrapper .ipc-setting-box {
    background-color: $vms-light-white;
}

.ipc-setting-wrapper .ipc-setting-desc {
    color: $gray-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-text {
    color: $gray-color;
}

.ipc-linkage-wrapper .ipc-configure {
    color: $gray-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list {
    background-color: $vms-light-white;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list .ipc-linkage-item {
    border-bottom: 1px solid $border-color;
}

.channel-wrapper .channel-content .channel-list {
    background-color: $vms-light-white;
}

.channel-wrapper .footer {
    background-color: transparent;
}

.add-defense-wrapper .footer {
    background-color: transparent;
}

.ipc-linkage-wrapper .footer {
    background-color: transparent;
}

.footer-delete-btn {
    background-color: transparent!important;
    border: 1px solid $vms-primary;
    color: $vms-primary!important;
}

.add-defense-content .device-list-wrapper .swipe-right-btn {
    background-color: $vms-delete-bg;
}

.defense-deployment-content .no-data .add-device-btn {
    color: $vms-primary;
    border: 1px solid $vms-primary;
}

.vms-plus-icon {
    color: $vms-primary;
}