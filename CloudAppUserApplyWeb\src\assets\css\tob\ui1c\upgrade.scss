.upgrade-list {
  ::v-deep.van-popup--center {
    width: 320px;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 6px;
    right: 6px;
  }
  
  ::v-deep.van-popup {
    width: 300px;
  }
  .pop-dialog {
    width: 320px;
    border-radius: 8px 8px 8px 8px;
    background-color: $UI1C-light-background-color!important;
    .pop-div {
      position: relative;
    }
    .dialog-title {
      width: 300px;
      font-weight: 700;
      height: 54px;
      line-height: 54px;
      font-size: 15px;
      color: $UI1C-white-color;
      text-align: center;
      border-bottom: 1px solid $UI1C-light-gray-color;
    }
    .update-box{
      padding: 8px;
      color: $UI1C-white-color;
      .update-content {
        max-height:394px;
        overflow-y: scroll;
        line-height: 22px;
        padding: 0 10px 18px 16px;
        word-break: break-all;
      }
    }
    .dialog-close-img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 13px;
      height: 13px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .title-text {
    height: 28px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title-text-img {
        width: 35px;
        height: 27px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .title-text-button {
    width: 70px;
    height: 25px;
    line-height: 25px;
    border-radius: 12.5px 12.5px 12.5px 12.5px;
    text-align: center;
    border: 1px solid $UI1C-color-primary;
    color: $UI1C-color-primary;
  }
  .view-btn {
    color: $UI1C-color-primary;
    font-weight: 500;
    font-size: 12px;
  }
  .title {
    color: $UI1C-50-white-color;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    font-weight: 700;
  }
  .title-text-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 27px;
    margin-left: 15px;
    max-width: 190px;
  }
  .download-status {
    color: $UI1C-color-primary;
    font-size: 12px;
    display: flex;
    .download-status-text {
      margin-right: 8px;
    }
  }
  .device-upgrade {
    .container {
      color: $UI1C-white-color;
      background-color: $UI1C-light-background-color;
      padding: 15px 15px;
    }
  }
  // 列表通用
  .list-content {
    padding-top: 20px;
    .list-content-row {
      display: flex;
      line-height: 24px;
      .label {
        width:36%;
        flex-shrink: 0;
        color: $UI1C-40-white-color;
      }
      .value {
        width:64%;
        flex: 1;
        word-wrap: break-word;
        white-space: pre-wrap;
        color: $UI1C-white-color;
      }
      .zh-label{
        width:20%;
      }
      .zh-value{
        width:80%;
      }
    }
    .download-tip {
      color: $UI1C-50-white-color;
      line-height: 24px;
      font-size: 12px;
    }
    .has-latest-version {
      color: $UI1C-green-color;
    }
  }
  .camera-upgrade {
    padding: 0 0 8px 0;
    .title-div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title-img {
        margin-right: 15px;
        width: 17px;
        height: 17px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .container-ul {
      background-color: $UI1C-light-background-color;
      padding: 2px 15px;
      .container-li {
        color: $UI1C-white-color;
        border-bottom: 1px solid $UI1C-light-gray-color;
        padding: 12px 0;
        &:last-child {
          border: 0;
        }
      }
    }
  }
  .camera-no-upgrade {
    margin: 0;
    padding-left: 15px;
    line-height: 28px;
    min-height: 60px;
    color: $UI1C-50-white-color;
    background-color: $UI1C-light-background-color;
  }
  .footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 40px;
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 345px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: $UI1C-button-background-color;
      border-radius: 20px;
      color: $UI1C-white-color;
    }
  }
}