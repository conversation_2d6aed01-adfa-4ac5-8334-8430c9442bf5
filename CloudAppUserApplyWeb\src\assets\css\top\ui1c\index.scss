@import './checkUserPwd.scss';
@import './upgrade.scss';
@import './navBar.scss';
@import './transfer/transferRequest.scss';
@import './share/shareManage.scss';
@import './defense/index.scss';
@import './line-bind/index.scss';

html,
body,
#app {
  background-color: $partner-background;
  color: $partner-black;
}

// 根据主题 全局 修改vant样式  VMS 
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $partner-primary;
}

.van-button--primary {
  color: $partner-white;
  background-color: $partner-primary;
  border: 1px solid $partner-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $partner-black;
}

.van-dialog__cancel{
  color:$partner-black;
}

.van-dialog__message{
  font-weight: 700;
  color:$partner-black;
  font-size: 14px;
} 

// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 40px;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .footer-btn {
    width: 296px;
    height: 46px;
    background: $partner-primary;
    border-radius: 23px;
    line-height: 46px;
    text-align: center;
    border-radius: 23px;
    color: $partner-white;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $partner-primary;
}


// Tab背景色
.van-tabs__nav {
  background-color: $partner-white;
}
.van-tab {
  color: $partner-gray;
}
.van-tab--active {
  color: $partner-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $partner-primary;
  border-color: $partner-primary;
}

// switch背景色
.van-switch {
  background-color: $partner-gray;
}
.van-switch--on {
  background-color: $partner-primary;
}