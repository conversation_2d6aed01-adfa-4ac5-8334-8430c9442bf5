<template>
  <div
    class="app-container"
    :class="{
      'top-safe-area': topSafeArea,
      'bottom-safe-area': bottomSafeArea
    }"
  >
    <router-view></router-view>
  </div>
</template>

<script>
export default {
  name: 'AppLayout',
  data() {
    return {
      topSafeArea: true,
      bottomSafeArea: true
    }
  },
  watch: {
    $route: {
      handler(to) {
        if (to?.meta?.hideTopArea) {
          this.topSafeArea = false
        } else {
          this.topSafeArea = true
        }

        if (to?.meta?.hideBottomArea) {
          this.bottomSafeArea = false
        } else {
          this.bottomSafeArea = true
        }
      },
      immediate: true
    }
  },
  components: {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.app-container {
  height: 100%;
  box-sizing: border-box;
  /* 安全区的范围 */
  &.top-safe-area {
    padding-top: var(--safeAreaTop);
  }
  &.bottom-safe-area {
    padding-bottom: var(--safeAreaBottom);
  }
}
</style>
