import request, { createCustomRequest } from '@/api/request'

// max 1.5.1
// 设备托管的接口
// 已托管的安装商列表
export const getTrustInstaller = data => request.post('/mobile_v1.0/resource/user/trust-installer/list', data)

// 查看已托管给指定安装商的站点和设备
export const getTrustSite = data => request.post('/mobile_v1.0/user/trusted-site/list', data)

// 更改站点信息
export const updateSite = data => request.post('/mobile_v1.0/user/site/update', data)

// 取消站点托管
export const cancelSite = data => request.post('/mobile_v1.0/user/trust/site/cancel', data)

// 查询安装商信息
export const getInstallerInfo = data => request.post('/mobile_v1.0/user/installer-info/get', data)

// 更新设备托管
export const updateDeviceHosting = (data, requestConfig = {}) => {
  const customRequest = createCustomRequest(requestConfig)
  return customRequest.post('/mobile_v1.0/user/trust/device/update', data)
}

// 查询安装商可托管的站点设备列表
export const getInstallerHostingSite = data => request.post('/mobile_v1.0/user/trustable-site/list', data)

// 创建托管申请
export const createHosting = data => request.post('/mobile_v1.0/user/trust/create', data)

// 查看已托管安装商列表
export const getHostedInstaller = data => request.post('/mobile_v1.0/resource/user/trust-installer/list', data)

// 查看托管工单业务详情
export const getTicketDetail = data => request.post('/mobile_v1.0/user/trust-ticket/get', data)

// 查看处理托管申请
export const handleTicket = data => request.post('/mobile_v1.0/user/trust/handle', data)

// 查询站点可托管的站点设备列表
export const getInstallerHostingDevice = data => request.post('/mobile_v1.0/user/trust/device/list', data)

// 取消设备托管
export const cancelDevice = (data, requestConfig = {}) => {
  const customRequest = createCustomRequest(requestConfig)
  return customRequest.post('/mobile_v1.0/user/trust/device/cancel', data)
}
