<template>
  <div class="appBridge-request-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="appBridge-request-content">
      <van-form @submit="onSubmit">
        <van-field
          v-model="formData.url"
          label="URL"
          placeholder="请输入URL"
          :rules="[{ required: true, message: '请输入URL' }]"
        />

        <!-- 添加参数类型选择 -->
        <van-field
          v-model="formData.paramType"
          label="参数类型"
          readonly
          clickable
          :value="formData.paramType"
          @click="showParamTypePicker = true"
        />
        <!-- <van-field v-model="formData.params" label="参数" type="textarea" rows="3" placeholder="请输入参数(JSON格式)" /> -->
        <van-popup v-model="showParamTypePicker" position="bottom">
          <van-picker
            :columns="paramTypeOptions"
            show-toolbar
            @confirm="onParamTypeConfirm"
            @cancel="showParamTypePicker = false"
          />
        </van-popup>

        <!-- 根据参数类型显示不同的输入框 -->
        <template v-if="formData.paramType === 'string'">
          <van-field v-model="formData.params" label="参数" type="text" placeholder="请输入字符串" />
        </template>

        <template v-else-if="formData.paramType === 'json'">
          <van-field v-model="formData.params" label="参数" type="textarea" rows="3" placeholder="请输入JSON格式数据" />
        </template>

        <template v-else-if="formData.paramType === 'boolean'">
          <van-field name="params" label="参数">
            <template #input>
              <van-switch v-model="formData.params" size="20" />
            </template>
          </van-field>
        </template>

        <template v-else-if="formData.paramType === 'int'">
          <van-field v-model="formData.params" label="参数" type="number" placeholder="请输入数字" />
        </template>

        <van-field
          v-model="formData.result"
          label="返回结果"
          type="textarea"
          rows="12"
          readonly
          class="result-field"
          :autosize="false"
        />

        <div class="submit-btn-wrap">
          <div class="btn-container">
            <van-button type="primary" native-type="submit" block>{{ $t('submit') }}</van-button>
            <van-button type="default" block @click="onReset">{{ $t('cancel') }}</van-button>
          </div>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appBack, bridgeCallHandler } from '@/utils/appbridge'
import { mapState } from 'vuex'

export default {
  name: 'AppBridge',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      showParamTypePicker: false,
      paramTypeOptions: ['string', 'json', 'boolean', 'int'],
      formData: {
        url: '',
        paramType: 'string', // 默认类型为字符串
        params: '',
        result: ''
        // error: ''
      }
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    back() {
      appBack()
    },
    onParamTypeConfirm(value) {
      this.formData.paramType = value
      // 重置参数值
      this.formData.params = this.getDefaultParamValue(value)
      this.showParamTypePicker = false
    },
    getDefaultParamValue(type) {
      switch (type) {
        case 'string':
          return ''
        case 'json':
          return ''
        case 'boolean':
          return false
        case 'int':
          return ''
        default:
          return ''
      }
    },
    async onSubmit() {
      this.loading = true
      this.formData.result = ''
      //   this.formData.error = ''
      try {
        // 解析参数
        const { url, params, paramType } = this.formData
        let processedParams = params

        // 根据参数类型处理参数
        switch (paramType) {
          case 'json':
            processedParams = params ? JSON.parse(JSON.stringify(params)) : null
            break
          case 'int':
            processedParams = parseInt(params, 10)
            if (isNaN(processedParams)) {
              throw new Error('请输入有效的数字')
            }
            break
          case 'boolean':
            // 处理 boolean 类型
            if (typeof params === 'boolean') {
              processedParams = params
            } else if (typeof params === 'string') {
              const lowercaseParams = params.toLowerCase().trim()
              if (lowercaseParams === 'true' || lowercaseParams === '1') {
                processedParams = true
              } else if (lowercaseParams === 'false' || lowercaseParams === '0') {
                processedParams = false
              } else {
                throw new Error('布尔值只能是 true/false 或者 1/0')
              }
            } else if (typeof params === 'number') {
              // 数值类型，1为true，0为false，其他抛出错误
              if (params === 1) {
                processedParams = true
              } else if (params === 0) {
                processedParams = false
              } else {
                throw new Error('数值类型的布尔值只能是 1 或 0')
              }
            } else {
              throw new Error('不支持的布尔值类型')
            }
            break
          default:
            processedParams = String(params)
        }

        if (!url) {
          this.formData.error = '请输入URL'
          return
        }

        const that = this
        const cb = response => {
          that.formData.result = typeof response === 'object' ? JSON.stringify(response, null, 2) : response
        }

        bridgeCallHandler(url, processedParams, cb)
      } catch (error) {
        console.log('error', error)
      } finally {
        this.loading = false
      }
    },
    onReset() {
      this.formData = {
        url: '',
        paramType: 'string',
        params: '',
        result: ''
      }
      // 重置表单校验状态
      this.$refs.form.resetValidation()
    }
  }
}
</script>
<style lang="scss" scoped>
.appBridge-request-wrapper {
  height: 100%;
  overflow: auto;
  .appBridge-request-content {
    height: calc(100% - 44px);
    padding: 10px 0px;
    box-sizing: border-box;

    // 添加返回结果输入框的样式
    :deep(.result-field) {
      .van-field__control {
        max-height: 400px; // 设置最大高度
        overflow-y: auto; // 启用垂直滚动
        font-family: monospace; // 使用等宽字体
        white-space: pre-wrap; // 保留换行和空格
        padding: 10px; // 添加内边距
        background-color: var(--bg-color-primary, #f5f5f5); // 添加背景色
      }
    }

    .submit-btn-wrap {
      margin-top: 20px;
      padding: 0 16px;

      .btn-container {
        display: flex;
        gap: 12px; // 按钮之间的间隔

        .van-button {
          flex: 1; // 按钮等宽
          margin: 0; // 清除原有的 margin
          border-radius: 23px;
        }
      }
    }
  }
}
</style>
