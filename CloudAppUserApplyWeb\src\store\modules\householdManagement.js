export default {
  namespaced: true,
  state: () => ({
    memberList: [], // 当前房间住户列表
    deviceList: [],
    buildingInfo: {}, // 当前楼栋信息 -- 用于新增/编辑楼栋信息
    roomInfo: {}, // 当前房间信息 -- 用于新增/编辑房间信息
    memberInfo: {}, // 当前住户信息 -- 用于新增/编辑住户信息
    residentInfo: {} // 人脸开门住户信息 -- 用于新增/编辑住户信息
  }),
  mutations: {
    SET_ROOM_INFO(state, data) {
      state.roomInfo = data
    },
    SET_MEMBER_LIST(state, data) {
      state.memberList = data
    },
    DEL_MEMBER(state, data) {
      state.memberList.splice(data, 1)
    },
    ADD_MEMBER(state, data) {
      state.memberList.push(data)
    },
    UPDATE_MEMBER(state, data) {
      const { oldVal, newVal } = data

      const index = state.memberList.findIndex(member => member.memberName === oldVal.memberName)

      if (index > -1) {
        state.memberList.splice(index, 1, newVal)
      } else {
        throw new Error('can not find member to update')
      }
    },
    SET_MEMBER_INFO(state, data) {
      state.memberInfo = data
    },
    SET_DEVICE_LIST(state, data) {
      if (!data) {
        state.deviceList = []
      } else {
        state.deviceList = data.sort((a, b) => b.createTime - a.createTime)
      }
    },
    UPDATE_DEVICE_LIST(state, data) {
      const index = state.deviceList.findIndex(device => device.sn === data.sn)

      if (index > -1) {
        state.deviceList.splice(index, 1, data)
      } else {
        throw new Error('can not find device to update')
      }
    },
    SET_BUILDING_INFO(state, data) {
      state.buildingInfo = data
    },
    SET_RESIDENT_INFO(state, data) {
      state.residentInfo = data
    }
  }
}
