<template>
  <div class="device-transfer-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="device-transfer-content">
      <div class="transfer-text-box">
        <div class="transfer-text">
          {{ $t('transferDesc') }}
        </div>
      </div>
      <user-card :installerInfo="transferInstallerInfo" />
      <div :class="['device-list-wrapper', isZh ? 'device-list-wrapper-zh' : 'device-list-wrapper-en']">
        <div class="device-list-title">{{ $t('transferDevice') }}</div>
        <div class="device-list-content">
          <tvt-better-scroll
            ref="scrollRef"
            class="tvt-better-scroll"
            @pullingUp="pullingUp"
            @pullingDown="pullingDown"
            :pullingStatus="pullingStatus"
          >
            <device-list :dataList="deviceData" />
          </tvt-better-scroll>
        </div>
      </div>
      <div class="device-check-line">
        <van-checkbox v-model="checked" :disabled="recordStatus !== 0">{{ $t('deviceTrusteeship') }}</van-checkbox>
      </div>
    </div>
    <div class="footer">
      <template v-if="recordStatus === 0 && deviceData.length > 0">
        <van-button plain class="footer-btn footer-btn-short" type="default" @click="handleReject">
          {{ $t('reject') }}
        </van-button>
        <van-button class="footer-btn footer-btn-short" type="primary" @click="handleTransfer">
          {{ bindInstaller ? $t('acceptTransfer') : $t('bindAcceptTransfer') }}
        </van-button>
      </template>
      <van-button v-if="recordStatus === 1" class="footer-btn" type="primary" @click="handleDetail">
        {{ $t('viewDetail') }}
      </van-button>
      <van-button v-if="recordStatus === 2" class="footer-btn" type="primary" :disabled="true">
        {{ $t('rejected') }}
      </van-button>
      <van-button v-if="recordStatus === 3" class="footer-btn" type="primary" :disabled="true">
        {{ $t('cancelled') }}
      </van-button>
      <van-button v-if="recordStatus === 4" class="footer-btn" type="primary" :disabled="true">
        {{ $t('expired') }}
      </van-button>
    </div>
    <!-- 去解绑或仅接受转移确定弹窗 -->
    <van-popup v-model="showAccept" round position="bottom" :style="{ height: '224px' }">
      <popup-confirm
        :title="$t('bindedInstaller')"
        :text="$t('bindedInstallerDesc')"
        :cancelText="$t('goUnBind')"
        :confirmText="$t('acceptTransfer')"
        @cancel="goUnBind"
        @confirm="confirmAccept"
      />
    </van-popup>
    <!-- 拒绝转移二次确认弹窗 -->
    <van-popup v-model="showReject" round position="bottom" :style="{ height: '224px' }">
      <popup-confirm
        :title="$t('rejectDeviceTransfer')"
        :text="$t('rejectTransfetDesc')"
        :cancelText="$t('notNow')"
        :confirmText="$t('reject')"
        @cancel="cancelReject"
        @confirm="confirmReject"
      />
    </van-popup>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import UserCard from './components/UserCard.vue'
import DeviceList from './components/DeviceList.vue'
import PopupConfirm from '@/components/PopupConfirm.vue'
import { appClose, receiveDevice, gotoPage } from '@/utils/appbridge'
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'
import { mapState } from 'vuex'
import { transferDetail, isPartner, bindPartner, transferHandle } from '@/api/transfer'
import { deviceTrusteeshipsCreate } from '@/api/trusteeship'

const queryType = {
  toOther: 1,
  toMe: 2
}

const transferStatus = {
  pendding: 0,
  received: 1,
  rejected: 2,
  deleted: 3
}

export default {
  name: 'TransferApply',
  components: {
    NavBar,
    UserCard,
    DeviceList,
    PopupConfirm
  },
  props: {},
  data() {
    return {
      transferId: 0, // 转移记录Id，用来从所有转移设备中过滤出对应转移记录的设备
      listParams: {
        pageNum: 0, // 查所有
        pageSize: 100,
        queryType: queryType.toMe,
        transferStatus: transferStatus.pendding,
        userName: null, //转移发起人或转移接受人的登录名，为空查所有
        userType: 2 // 用户类型 1:C用户, 2:安装商, 3:B-VMS用户，为空查所有
      },
      bindInstaller: false, // 是否绑定过安装商
      installerInfo: {}, // 绑定的安装商信息
      transferInstallerInfo: {}, // 转移设备的安装商信息
      bindFlag: false,
      deviceNum: 0, // 当前用户名下托管的设备数量
      deviceCapabilitys: DEVICE_CAPABILITY_LIST(), // 全量设备权限
      pullingStatus: 0,
      deviceData: [
        // {
        //   siteId: '1067490434814705664',
        //   siteName: 'Test1',
        //   sn: '969690A875AB46BE759DB3EFE6C9BFA0',
        //   devName: '3536C',
        //   snPlain: 'N44D20190726',
        //   chlIndex: 1,
        //   chlSn: null,
        //   chlName: 'IP頻道01',
        //   capability:
        //     '{"chlIndex":1,"verID":"A80B7109C822C3F426A06BF8B8EE6C64","version":"*******(6800)","model":"TD-9523A3-FR","manufacturer":"2","mac":"00:18:ae:a3:ab:51","protocol":259,"name":"IP頻道01","alarmInNum":1,"alarmOutNum":1,"supportFun":["osd","snp","cls"],"videoForm":["PAL","NTSC"],"stream":[],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
        //   onlineStatus: 2,
        //   version: '*******(6800)',
        //   enableStatus: 1,
        //   delStatus: 1,
        //   createTime: 1712583993000,
        //   siteCreateTime: 1668586086000,
        //   trusteeshipTime: 0, //  设备托管时长- 默认永久
        //   checkCapability: DEVICE_CAPABILITY_LIST(),
        //   status: 0
        // },
        // {
        //   siteId: '1215611930010583041',
        //   siteName: 'tesT',
        //   sn: '30EE927F9B11408B16540C210F014477',
        //   devName: 'N018AE006D80',
        //   snPlain: 'N018AE006D80',
        //   chlIndex: 17,
        //   chlSn: '306C25F1339597349CFA464A3C2E24D6',
        //   chlName: 'Camera_17',
        //   capability:
        //     '{"chlIndex":17,"verID":"4cd00bf062e3b09f548ed8feb9c78511","version":"5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta","model":"TD-9565S4-C","date":"","manufacturer":"2","chlSn":"306C25F1339597349CFA464A3C2E24D6","mac":"70:ab:49:83:7a:3e","coustomerID":"208","protocol":259,"name":"IP Camera 143","alarmInNum":0,"alarmOutNum":0,"supportFun":["a","d","ir","m","ma","mc","o","osd","p","ptz","snp","t","cls"],"videoForm":["PAL","NTSC"],"stream":[{"name":"mainCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"3200x1800"},{"fps":20,"value":"2688x1520"},{"fps":20,"value":"1920x1080"}]},{"name":"subCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"1280x720"},{"fps":20,"value":"704x480"},{"fps":20,"value":"640x480"},{"fps":20,"value":"352x240"}]},{"name":"aux1Caps","supEnct":["h264","h265","h265p"],"supEnctMode":["CBR"],"res":[{"fps":20,"value":"704x480"},{"fps":20,"value":"352x240"}]}],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
        //   onlineStatus: 2,
        //   version: '5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta',
        //   enableStatus: 1,
        //   delStatus: 1,
        //   createTime: 1711503964000,
        //   siteCreateTime: 1703073309000,
        //   trusteeshipTime: 0, //  设备托管时长- 默认永久
        //   checkCapability: DEVICE_CAPABILITY_LIST(),
        //   status: 1
        // }
      ],
      showAccept: false,
      recordStatus: 0, // 状态 -- 0:待接受 1：已接受 2：拒绝 3：删除 4：过期
      checked: true,
      transferRecordIDs: [], // 发起转移申请的用户邮箱
      showReject: false // 拒绝转移二次确认弹窗
    }
  },
  created() {
    // 从路由中获取转移记录的id
    const query = this.$route.query
    if (query.transferRecordIDs) {
      this.transferRecordIDs = JSON.parse(query.transferRecordIDs)
      console.log('query.transferRecordIDs', query.transferRecordIDs)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
      this.getInstallerInfo()
    })
  },
  computed: {
    ...mapState('share', ['shareUser']),
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    back() {
      appClose()
    },
    pullingUp(callback) {
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.listParams.pageNum = 1
      this.getList({ type: 'down', callback })
    },
    // 查询转移过来的的设备列表
    async getList({ callback, showToast = false } = {}) {
      try {
        // 没有对应id直接返回
        if (!this.transferRecordIDs.length) {
          return
        }
        this.$loading.show()
        const params = { ids: this.transferRecordIDs }
        const res = await transferDetail(params)
        console.log('res', res)
        const { data } = res
        const { installerUserId, installerCoName, logo, addr, mobile, email, transferList = [] } = data[0] || {}
        this.transferInstallerInfo = {
          installerUserId,
          installerCoName,
          logo,
          addr,
          mobile,
          email
        }
        this.deviceData = transferList
        // 判断详情中设备的状态--直接取第一个的状态  0:待接受 1：已接受 2：拒绝 3：删除 4：过期
        const { transferStatus } = transferList[0]
        this.recordStatus = Number(transferStatus)
        if (showToast) {
          switch (Number(transferStatus)) {
            case 1:
              this.$toastFail(this.$t('transferAcceptedDesc'))
              break
            case 2:
              this.$toastFail(this.$t('transferRejectedDesc'))
              break
            case 3:
              this.$toastFail(this.$t('applyCancelledDesc'))
              break
            case 4:
              this.$toastFail(this.$t('applyExpiredDesc'))
              break
            default:
              this.$toastFail(this.$t('errorCode.34006'))
          }
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        callback && callback()
      }
    },
    // 获取安装商信息
    async getInstallerInfo() {
      try {
        const res = await isPartner()
        // console.log(res, '查询安装商信息')
        if (res.basic.code == 200) {
          this.installerInfo = res.data
          // 根据请求到的安装商信息判断是否绑定过安装商
          this.bindInstaller = !!res.data.installerUserId
          return this.bindInstaller
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 取消解除绑定
    cancelUnBind() {
      this.showUnBind = false
    },
    // 确定解除绑定
    confirmUnBind() {
      this.showUnBind = false
      // installerUnbind().then(res => {
      //   if (res.basic.code == 200) {
      //     appBack()
      //   }
      // })
    },
    // 绑定
    handleBind() {
      this.$toast('进入绑定安装商页面')
    },
    // 进入托管页面
    handleTrusteeship() {
      this.$toast('进入托管页面')
      // 进入选择设备
      this.$utils.routerPush({
        path: '/maxTrusteeship/chooseDevice'
      })
    },
    async refresh(callback, refreshScroll = true) {
      this.queryParams.pageNum = 1
      //   await this.getList({ type: 'down', callback })
      callback && callback()
      if (refreshScroll && this.$refs.scrollRef) {
        this.$refs.scrollRef.refresh()
      }
    },
    // 接受转移
    async handleTransfer() {
      // 判断是否绑定过安装商
      const hasBindInstaller = await this.getInstallerInfo()
      if (hasBindInstaller) {
        // 绑定过安装商则判断当前转移申请记录发起者id是否与绑定安装商一致
        if (this.installerInfo && this.installerInfo.installerUserId !== this.transferInstallerInfo.installerUserId) {
          // 弹窗提示去解绑或者仅接受转移
          this.showAccept = true
        } else {
          // 直接接受
          this.acceptTransfer()
        }
      } else {
        // 未绑定安装商则进行绑定再接受
        // 从转移设备记录中获取安装商id进行绑定
        const { installerUserId } = this.transferInstallerInfo
        const res = await bindPartner({ installerUserId })
        // console.log('绑定res', res)
        if (res.basic.code === 200) {
          // 重新获取安装商信息
          this.bindInstaller = true
          this.getInstallerInfo()
          // 绑定成功之后再接收转移
          this.acceptTransfer()
        }
      }
    },
    // 接收转移设备
    async acceptTransfer(flag = true) {
      // flag表示是否需要后续托管设备
      this.$loading.show()
      try {
        // 待接受的设备
        const filterData = this.deviceData.filter(item => item.transferStatus === 0)
        const ids = filterData.map(item => item.id)
        const snList = filterData.map(item => item.sn)
        const result = await transferHandle({
          ids,
          accept: true
        })
        if (result) {
          this.$toastSuccess(this.$t('transferSuccess'))
          // 通知APP刷新设备列表
          const list = snList.map(sn => ({ devId: sn }))
          receiveDevice({ type: 'transfer', list })
        }
        // 接收转移设备后需要判断下是否勾选了托管设备，如果勾选了则需要发送托管设备的请求
        // console.log('this.bindInstaller', this.bindInstaller, 'this.checked', this.checked)
        if (flag && this.bindInstaller && this.checked) {
          // console.log('发起托管')
          this.truseeshipDevice()
        }
        // 修改当前设备状态为已接收
        this.recordStatus = 1
        const deviceData = this.deviceData.map(item => {
          const { id } = item
          if (ids.includes(id)) {
            return {
              ...item,
              status: 1
            }
          }
          return { ...item }
        })
        this.deviceData = deviceData
        this.$loading.hide()
      } catch (error) {
        this.showAccept = false
        console.log('error', error)
        console.error(error)
        this.$loading.hide()
        // 重新请求刷新列表--需要显示toast提示
        if (error.basic.code === 34006) {
          console.log('进入toast')
          this.getList({ showToast: true })
        } else {
          this.getList()
        }
      }
    },
    // 发起设备托管
    async truseeshipDevice() {
      const params = []
      this.deviceData.forEach(item => {
        const { sn } = item
        params.push({
          sn,
          authList: this.deviceCapabilitys.map(item2 => item2.value), // 默认所有权限
          effectiveTime: 0 // 设备托管时长，默认永久
        })
      })
      try {
        await deviceTrusteeshipsCreate(params)
      } catch (err) {
        console.error(err)
      }
    },
    // 拒绝转移
    handleReject() {
      // 弹窗二次确认
      this.showReject = true
    },
    cancelReject() {
      this.showReject = false
    },
    async confirmReject() {
      this.$loading.show()
      try {
        const ids = this.deviceData.filter(item => item.transferStatus === 0).map(item => item.id)
        const result = await transferHandle({
          ids,
          accept: false
        })
        if (result) {
          // 通知APP刷新设备列表
          receiveDevice({ type: 'transfer' })
        }
        this.showReject = false
        // 修改当前设备状态为已拒绝
        this.recordStatus = 2
        const deviceData = this.deviceData.map(item => {
          const { id } = item
          if (ids.includes(id)) {
            return {
              ...item,
              status: 2
            }
          }
          return { ...item }
        })
        this.deviceData = deviceData
        this.$loading.hide()
        this.$toastSuccess(this.$t('rejected'))
      } catch (error) {
        this.showReject = false
        console.error(error)
        this.$loading.hide()
        // 重新请求刷新列表--需要显示toast提示
        if (error.basic.code === 34006) {
          this.getList({ showToast: true })
        } else {
          this.getList()
        }
      }
    },
    // 弹窗点击去解绑
    goUnBind() {
      this.showAccept = false
      // this.$toast('去解绑')
      // 跳转到服务页面
      gotoPage({
        pageRoute: 'service/home'
      })
    },
    // 弹窗点击仅接受转移
    confirmAccept() {
      this.showAccept = false
      this.acceptTransfer(false)
    },
    // 查看详情
    handleDetail() {
      // this.$toast('跳转服务页面')
      // 跳转到服务页面
      gotoPage({
        pageRoute: 'service/home'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.device-transfer-wrapper {
  height: 100%;
  overflow: hidden;
  position: relative;
  .nav-bar {
    background-color: transparent;
  }
  .menu {
    position: absolute;
    top: 44px;
    right: 10px;
  }
  .device-transfer-content {
    width: 100%;
    height: calc(100% - 130px);
    overflow: auto;
    box-sizing: border-box;
    .transfer-text-box {
      width: 100%;
      min-height: 42px;
      padding: 10px 28px;
      box-sizing: border-box;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      text-align: center;
      .transfer-text {
        display: inline-block;
        text-align: left;
      }
    }
    .device-list-wrapper {
      width: 100%;
      height: calc(100% - 250px);
      .device-list-title {
        width: 100%;
        height: 60px;
        padding: 18px 14px 0 28px;
        box-sizing: border-box;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-list-content {
        width: 100%;
        height: calc(100% - 60px);
        overflow: auto;
      }
    }
    .device-list-wrapper-zh {
      height: calc(100% - 250px);
    }
    .device-list-wrapper-en {
      height: calc(100% - 275px);
    }
    .device-check-line {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.footer {
  .footer-btn-short {
    width: 150px;
    margin: 0px 20px;
  }
}
</style>
