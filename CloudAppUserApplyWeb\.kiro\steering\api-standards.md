---
inclusion: fileMatch
fileMatchPattern: ['**/api/**/*.js', '**/services/**/*.js', '**/utils/**/*.js']
---

# API Standards & Conventions

## HTTP Client Configuration
- Use Axios as the primary HTTP client (configured in `src/api/request.js`)
- Implement request/response interceptors for authentication and error handling
- Set appropriate timeout values for mobile network conditions
- Include proper headers for content type and authentication tokens

## API Service Structure
- One service file per feature domain (e.g., `alarmSystem.js`, `device.js`)
- Export named functions for each API endpoint
- Use consistent naming: `get*`, `create*`, `update*`, `delete*`, `list*`
- Group related endpoints in the same service file

## Request/Response Patterns
- Use async/await for all API calls
- Implement consistent error handling with try/catch blocks
- Return standardized response objects with `{ data, error, loading }` structure
- Handle network failures gracefully with user-friendly error messages

## Authentication & Security
- Include authentication tokens in request headers
- Implement token refresh logic for expired sessions
- Use HTTPS for all production API calls
- Sanitize user inputs before sending to API

## Error Handling
- Implement global error interceptors in request configuration
- Provide localized error messages for different languages
- Log errors appropriately for debugging without exposing sensitive data
- Handle common HTTP status codes (401, 403, 404, 500) consistently

## Data Transformation
- Transform API responses to match Vue component expectations
- Use consistent date/time formatting across all endpoints
- Implement data validation for critical API responses
- Cache frequently accessed data to reduce API calls

## Mobile Optimization
- Implement request debouncing for user input-triggered API calls
- Use appropriate loading states for better UX on slower connections
- Implement offline handling where applicable
- Optimize payload sizes for mobile bandwidth constraints

## Testing & Documentation
- Include JSDoc comments for all API functions
- Document expected request/response formats
- Implement error scenarios in development environment
- Use consistent parameter naming across all API functions