import request from '@/api/request'
import baseUrl from '@/api/index.js'

// 设备托管的接口
// superlive用户查询绑定的安装商信息
export const installerInfo = data => request.post(`${baseUrl}/user/installer/info`, data)
// superlive用户解绑安装商
export const installerUnbind = data => request.post(`${baseUrl}/user/installer/unbind`, data)
// vms查询安装商接口
export const vmsInstallersGet = data => request.post(`${baseUrl}/installers/get`, data)
// superlive  vms 查询托管设备列表 type 0: 可托管设备； 1：已发起托管设备
export const deviceTrusteeshipsList = data => request.post(`${baseUrl}/device-trusteeships/list`, data)
// superlive  vms 查询托管设备详情
export const deviceTrusteeshipsGet = data => request.post(`${baseUrl}/device-trusteeships/get`, data)
// superlive  vms 发起托管设备
export const deviceTrusteeshipsCreate = data => request.post(`${baseUrl}/device-trusteeships/create`, data)
// superlive  vms 取消托管设备
export const deviceTrusteeshipsDelete = data => request.post(`${baseUrl}/device-trusteeships/delete`, data)
// 修改托管的有效期和权限
export const deviceTrusteeshipsUpdate = data => request.post(`${baseUrl}/device-trusteeships/update`, data)
