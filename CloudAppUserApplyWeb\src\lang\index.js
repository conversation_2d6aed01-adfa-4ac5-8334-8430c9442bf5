import Vue from 'vue'
import VueI18n from 'vue-i18n'
import { Locale } from 'vant'
import { loadLocales, getRemoteBasePath } from '@/utils/common.js'
import { appLog } from '@/utils/appbridge'

// 引入Van2语言包
import enUS from 'vant/es/locale/lang/en-US'
import zhCN from 'vant/es/locale/lang/zh-CN'
import deDE from 'vant/es/locale/lang/de-DE'
import esES from 'vant/es/locale/lang/es-ES'
import frFR from 'vant/es/locale/lang/fr-FR'
import viVN from 'vant/es/locale/lang/vi-VN'

// import en from './common/en-US'
// import zh from './common/zh-CN'

Vue.use(VueI18n)

// 初始化语言包 引入Van2语言包,其余的通过远程加载
export const messages = {
  'zh-CN': {
    ...zhCN
    // ...zh
  },
  'en-US': {
    ...enUS
    // ...en
  },
  'de-DE': {
    ...deDE
  },
  'es-ES': {
    ...esES
  },
  'fr-FR': {
    ...frFR
  },
  'it-IT': {},
  'pt-PT': {},
  'vi-VN': {
    ...viVN
  }
}

// 获取当前语言
export const getLocale = () => {
  let locale = localStorage.getItem('LANUAGE_TYPE')
  return locale || 'en-US'
}
// 注册i8n实例并引入语言文件
const i18n = new VueI18n({
  legacy: false,
  locale: getLocale(),
  globalInjection: false,
  fallbackLocale: 'en-US',
  silentTranslationWarn: true,
  messages: messages // 默认中性的翻译
})

// 新增：合并远程语言包
export const mergeLocaleMessage = (locale, remoteMessages) => {
  // 深度合并现有语言包和远程语言包
  const merged = {
    ...messages[locale],
    ...remoteMessages
  }

  // 更新 vue-i18n 的语言包
  i18n.setLocaleMessage(locale, merged)

  // 更新 vant 的语言包
  Locale.use(locale, merged)

  return merged
}

// 远程加载国际化文件// 修改 initI18n 函数
export const initI18n = async (language, appType = 'TOC', UI = 'UI1A') => {
  // 根据appType和UI来确定远程CSS文件的路径

  try {
    const basePath = getRemoteBasePath(appType)
    let customLangData = null
    let commonLangData = null

    // 1. 首先加载通用语言文件
    try {
      const commonResponse = await loadLocales(`${basePath}/common/lang/i18n.json`)
      if (commonResponse) {
        commonLangData = commonResponse
      }
    } catch (error) {
      console.error('Failed to load common language file:', error)
      throw new Error('Common language file is required')
    }

    // 2. 如果指定了特定UI，尝试加载定制语言文件
    // console.log('appType', appType, 'UI', UI)
    if (appType && UI && window.langUiList.find(item => item.aType === appType && item.aUI === UI)) {
      try {
        const customResponse = await loadLocales(`${basePath}/${UI.toLowerCase()}/lang/i18n.json`)
        // console.log(`当前${appTypeUI}国际化语言对象`, customResponse)
        if (customResponse) {
          customLangData = customResponse
        }
      } catch (error) {
        console.warn(`Custom language file for ${UI} not found, using common only:`, error.message)
        // Custom language file is optional, continue with common language only
      }
    }

    // 3. 合并语言包
    let currentLocale
    if (language) {
      currentLocale = language
    } else {
      currentLocale = getLocale()
    }
    // console.log('log/info', `${new Date()} initI18n中语言 language ${language} currentLocale ${currentLocale}`)
    appLog('log/info', `${new Date()} initI18n中语言 language ${language} currentLocale ${currentLocale}`)
    let mergedMessages = {}

    if (customLangData) {
      // 如果有定制语言，进行三层合并：vant基础语言包 + common语言包 + 定制语言包
      const keys = new Set(Object.keys(commonLangData).concat(Object.keys(customLangData)))
      Array.from(keys).forEach(locale => {
        mergedMessages[locale] = {
          ...(messages[locale] || {}), // vant基础语言包
          ...(commonLangData[locale] || {}), // 远程common语言包
          ...(customLangData[locale] || {}) // 远程定制语言包
        }
      })
    } else {
      // 如果只有common语言，进行两层合并：vant基础语言包 + common语言包
      // console.log('只有common语言，进行两层合并：vant基础语言包 + common语言包', commonLangData)
      Object.keys(commonLangData).forEach(locale => {
        mergedMessages[locale] = {
          ...(messages[locale] || {}), // vant基础语言包
          ...(commonLangData[locale] || {}) // 远程common语言包
        }
      })
    }

    // 4. 更新 i18n 实例的语言包
    Object.keys(mergedMessages).forEach(locale => {
      i18n.setLocaleMessage(locale, mergedMessages[locale])
      // console.log('更新 i18n 实例的语言包', locale, mergedMessages[locale])
    })

    // 5. 更新当前语言的 vant 组件库语言包
    Locale.use(currentLocale, mergedMessages[currentLocale])

    // 6. 更新 i18n 实例的当前语言
    i18n.locale = currentLocale

    return mergedMessages
  } catch (error) {
    console.error('Failed to initialize i18n:', error)
    throw error
  }
}

const currentLocale = getLocale()
Locale.use(currentLocale, messages[currentLocale])

export default i18n
