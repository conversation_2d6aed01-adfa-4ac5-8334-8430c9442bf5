<template>
  <div class="share-list-wrapper">
    <div class="share-item-wrapper" v-for="(item, index) of dataList" :key="index" @click="clickRecord(item)">
      <div class="share-item-left">
        <div class="share-name">{{ item.loginName }} {{ item.userRemark ? `(${item.userRemark})` : '' }}</div>
        <div class="share-device-box">
          <div
            :class="[
              'ellipsis',
              item.deviceNum > 1 ? (isZh ? 'share-device-name-zh' : 'share-device-name-en') : 'share-device-name'
            ]"
          >
            {{ item.displayDeviceName }}
          </div>
          <div :class="isZh ? 'share-device-other-zh' : 'share-device-other-en'" v-if="item.deviceNum > 1">
            {{ $t('andOther') }}
          </div>
          <div class="separator-box"></div>
          <div :class="isZh ? 'share-device-num-zh' : 'share-device-num-en'">
            {{ $t('deviceNum', [item.deviceNum]) }}
          </div>
        </div>
      </div>
      <div class="share-item-right" v-if="showOperate">
        <template v-if="item.status === 0">
          <div class="operate-btn reject-btn" @click="e => handleClick(e, item, index, 2)">{{ $t('reject') }}</div>
          <div class="operate-btn accept-btn" @click="e => handleClick(e, item, index, 1)">{{ $t('accept') }}</div>
        </template>
        <div v-else class="share-device-status">
          {{ statusObj[item.status] }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'

export default {
  name: 'ConfirmShare',
  components: {},
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    showOperate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusObj: {
        1: this.$t('accepted'),
        2: this.$t('rejected')
      }
    }
  },
  created() {},
  mounted() {
    console.log('dataList', this.dataList)
  },
  computed: {
    ...mapState('share', ['shareUser']),
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    clickRecord(item) {
      this.$emit('click', item)
    },
    handleClick(e, ...args) {
      e.stopPropagation() // 阻止冒泡
      this.$emit('change', ...args)
    }
  }
}
</script>
<style lang="scss" scoped>
.share-list-wrapper {
  width: 100%;
  .share-item-wrapper {
    width: 100%;
    height: 60px;
    padding: 9px 18px;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .share-item-left {
      flex: 1;
      height: 100%;
      overflow: hidden;
      .share-name {
        width: 100%;
        height: 22px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        display: inline-flex;
        align-items: center;
      }
      .share-device-box {
        width: 100%;
        overflow: hidden;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        .ellipsis {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .share-device-name {
          max-width: calc(100% - 72px);
        }
        .share-device-name-zh {
          max-width: calc(100% - 102px);
        }
        .share-device-name-en {
          max-width: calc(100% - 132px);
        }
        .share-device-other-zh {
          min-width: 30px;
        }
        .share-device-other-en {
          min-width: 50px;
          padding-left: 4px;
          box-sizing: border-box;
        }
        .separator-box {
          display: inline-block;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          margin: 0px 4px;
        }
        .share-device-num-zh {
          min-width: 50px;
        }
        .share-device-num-en {
          min-width: 60px;
        }
      }
    }
    .share-item-right {
      width: max-content;
      height: 100%;
      line-height: 40px;
      .operate-btn {
        display: inline-flex;
        width: max-content;
        height: 24px;
        padding: 6px 14px;
        box-sizing: border-box;
        justify-content: space-between;
        align-items: center;
        border-radius: 12px;
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      .reject-btn {
        color: var(--error-bg-color-default, #ff3d3d);
        border: 1px solid var(--error-bg-color-default, #ff3d3d);
      }
      .accept-btn {
        color: var(--brand-bg-color-default, #3277fc);
        border: 1px solid var(--brand-bg-color-default, #3277fc);
        margin-left: 12px;
      }
      .share-device-status {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
      }
    }
  }
}
</style>
