<template>
  <div class="add-defense-wrapper">
    <nav-bar @clickLeft="back">
      <template #left>
        <div class="nav-bar-left-text" v-if="type === 'edit'" @click="handleCancel">{{ $t('cancel') }}</div>
        <van-icon v-else name="arrow-left" @click="back" />
      </template>
      <template #right>
        <div class="nav-bar-right-icon" v-if="type === 'detail'" @click="handleEdit">
          <theme-image alt="edit" imageName="edit.png" />
        </div>
        <div class="nav-bar-right-icon" v-if="type === 'edit'" @click="deleteGroup">
          <theme-image alt="delete" imageName="delete_black.png" />
        </div>
      </template>
    </nav-bar>
    <div :class="['add-defense-content', type === 'detail' ? 'add-defense-whhole-content' : '']">
      <div :class="['add-defense-head', type === 'add' ? 'add-defense-head-block' : '']">
        <div class="add-defense-title">{{ $t('name') }}</div>
        <template v-if="type === 'add' || type === 'edit'">
          <van-field
            v-model="groupName"
            label=""
            maxlength="32"
            @input="handleInput"
            @blur="handleBlur"
            :placeholder="$t('enterGroupName')"
          />
        </template>
        <template v-else>
          <div class="add-defense-text">
            <span class="defense-ellipsis-text">{{ groupName }}</span>
          </div>
        </template>
      </div>
      <div class="add-defense-device">
        <div class="device-label text-over-ellipsis">{{ $t('linkCameraSensor') }}</div>
        <div @click="addDevice" v-if="type === 'edit'"><van-icon class="add-defense-icon" name="add-o" /></div>
        <div @click="handleSelectAll(isSelectAll)" v-if="type === 'add'">
          <span class="defense-status">{{ isSelectAll ? $t('cancelSelectAll') : $t('selectAll') }}</span>
        </div>
      </div>
      <div :class="['device-content', type === 'add' ? 'device-content-add' : '']">
        <!-- 新增分组时展示所有待选的通道 -->
        <template v-if="type === 'add'">
          <div class="device-list-wrapper" v-if="showAllChannelList">
            <check-channel v-model="checkChannelList" />
          </div>
          <div class="no-data" v-else>
            <div class="no-data-img">
              <img alt="noData" :src="noDataImg" />
            </div>
            <div class="no-data-btn">
              <div class="add-device-btn" @click="addDevice">{{ $t('add') }}</div>
            </div>
          </div>
        </template>
        <!-- 分组详情时展示当前分组下的通道 -->
        <template v-if="type === 'detail'">
          <div class="device-list-wrapper" v-if="showChannelList">
            <detail-channel :channelList="curChannelList" @click="editIPCLinkage" />
          </div>
          <div class="no-data" v-else>
            <div class="no-data-img">
              <img alt="noData" :src="noDataImg" />
            </div>
            <div class="no-data-btn">
              <div class="add-device-btn" @click="addDevice">{{ $t('add') }}</div>
            </div>
          </div>
        </template>
        <!-- 分组编辑时展示当前分组下的通道 -->
        <template v-if="type === 'edit'">
          <div class="device-list-wrapper" v-if="showChannelList">
            <delete-channel :channelList="curChannelList" @click="handleDeleteChl" />
          </div>
          <div class="no-data" v-else>
            <div class="no-data-img">
              <img alt="noData" :src="noDataImg" />
            </div>
            <div class="no-data-btn">
              <div class="add-device-btn" @click="addDevice">{{ $t('add') }}</div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="footer" v-if="type !== 'detail'">
      <van-button
        class="footer-btn"
        type="primary"
        v-if="type === 'add'"
        :disabled="btnDisabled"
        :loading="addLoading"
        @click="confirmAdd"
      >
        {{ $t('confirm') }}
      </van-button>
      <van-button
        class="footer-btn"
        type="primary"
        v-if="type === 'edit'"
        :loading="editLoading"
        :disabled="editBtnDisabled"
        @click="confirmEdit"
      >
        {{ $t('saveModify') }}
      </van-button>
    </div>
    <!-- 编辑区域名称 -->
    <!-- <edit-group-name ref="editGroupName" :name="defenseRecord.groupName" @confirm="groupNameConfirm"></edit-group-name> -->
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
// import EditGroupName from '../defense/editGroupName.vue'
import CheckChannel from './components/CheckChannel.vue'
import DeleteChannel from './components/DeleteChannel.vue'
import DetailChannel from './components/DetailChannel.vue'
import { debounce } from '@/utils/common'
import { appSetWebBackEnable } from '@/utils/appbridge'
import { addDefenseGroup, deleteDefenseRecord, getDefenseDetail } from '@/api/maxDefense'
export default {
  name: 'AddDefense',
  components: {
    NavBar,
    // EditGroupName,
    CheckChannel,
    DeleteChannel,
    DetailChannel,
    ThemeImage
  },
  data() {
    return {
      groupName: '',
      id: null, // 记录的id 编辑才有
      type: 'add', // 新增、编辑、详情
      confirmAdd: this.$utils.debounceFun(this.addGroupConfirm),
      confirmEdit: this.$utils.debounceFun(this.editGroupConfirm),
      checkChannelList: [], // 分组选中的通道
      addLoading: false,
      editLoading: false,
      curChannelList: []
    }
  },
  created() {
    appSetWebBackEnable(true)
    // 从路由中获取设备sn和deviceId,用作请求设备的通道列表
    const query = this.$route.query
    if (query.type) {
      this.type = query.type
    } else if (this.defenseRecord && this.defenseRecord.id) {
      // 进入页面默认是详情
      this.type = 'detail'
    } else {
      this.type = 'add'
    }
    if (this.type === 'add') {
      this.$route.meta.title = this.$t('createGroup')
    } else {
      const { groupName, curGroupName } = this.defenseRecord
      // console.log('defenseRecord', this.defenseRecord)
      this.groupName = curGroupName || groupName
      this.$route.meta.title = this.$t('defenseAreaSetting')
    }
  },
  async mounted() {
    await this.getChannelList()
    if (this.type === 'add') {
      // 新增时直接使用所有站点通道
      this.curChannelList = this.allChannelList.slice()
    } else {
      // 编辑和详情使用暂存的通道
      this.curChannelList = this.channelList.slice()
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('maxDefense', [
      'addType',
      'noDataImg',
      'allChannelList',
      'channelList',
      'defenseRecord',
      'groupChannelList',
      'ipcLinkageList',
      'channelObj'
    ]),
    showAllChannelList() {
      return this.allChannelList && this.allChannelList.length
    },
    showChannelList() {
      return this.curChannelList && this.curChannelList.length
    },
    // 添加时按钮是否禁用
    btnDisabled() {
      // 有分组名称且选中了通道才能点击确定按钮
      return !(this.groupName && this.groupName.trim() && this.checkChannelList && this.checkChannelList.length)
    },
    // 编辑时按钮是否禁用
    editBtnDisabled() {
      // 有分组名称且选中了通道才能点击确定按钮
      return !(this.groupName && this.groupName.trim() && this.curChannelList && this.curChannelList.length)
    },
    // 新增时是否全部选中
    isSelectAll() {
      // 从全部通道中找到未禁用的
      const waitCheckChannelList = (this.allChannelList || []).filter(item => Number(item.onlineStatus) === 1)
      // 当前选择了通道且数目等于全部待勾选的通道
      return this.checkChannelList.length && this.checkChannelList.length === waitCheckChannelList.length
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getChannelList', 'updateDeviceStatus']),
    ...mapMutations('maxDefense', [
      'SET_ALL_CHANNEL_LIST',
      'SET_SITE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_CHANNEL_RECORD',
      'SET_CHANNEL_LIST',
      'SET_DEFENSE_RECORD',
      'SET_DEFENSE_GROUP_LIST'
    ]),
    back() {
      this.$utils.routerReplace({
        path: '/maxDefense/defenseDeployment'
      })
    },
    // 输入分组名称
    handleInput(value) {
      this.groupName = value
    },
    // 分组名称输入组件失焦事件
    handleBlur() {
      this.groupName = this.groupName && this.groupName.trim()
    },
    // 点击编辑按钮
    handleEdit() {
      console.log('进入编辑模式')
      this.type = 'edit'
    },
    // 点击取消
    handleCancel() {
      this.type = 'detail'
      const { groupName, channelList = [] } = this.defenseRecord
      // 恢复通道
      this.curChannelList = channelList.slice()
      this.SET_CHANNEL_LIST([...channelList])
      this.groupName = groupName
    },
    // 查询当前分组下面的通道
    async getDefenseDetails() {
      const { id } = this.defenseRecord
      const res = await getDefenseDetail({ ids: [id] })
      const resData = res.data || []
      const channelList = []
      resData.forEach(item => {
        const { chlIndex, chlId, sn, extra } = item
        // 从channelList中找到对应的记录
        const channelItem = this.allChannelList.find(
          item2 => item2.chlIndex === chlIndex && item2.chlId === chlId && item2.sn === sn
        )
        if (channelItem) {
          const temp = {
            ...item,
            extra: extra ? JSON.parse(extra) : null
          }
          channelList.push(temp)
        }
      })
      this.SET_DEFENSE_RECORD({
        ...this.defenseRecord,
        channelList
      })
    },
    // 新增设备
    addDevice() {
      // 保存当前的通道
      this.SET_CHANNEL_LIST([...this.curChannelList])
      // 暂存当前名称
      this.SET_DEFENSE_RECORD({
        ...this.defenseRecord,
        curGroupName: this.groupName
      })
      this.$router.push({ name: 'maxAddChannel' })
    },
    // 删除传感器
    handleDeleteChl(item, index) {
      const curChannelList = this.curChannelList.slice()
      curChannelList.splice(index, 1)
      this.curChannelList = curChannelList
    },
    // 添加分组
    async addGroupConfirm() {
      this.$loading.show()
      this.addLoading = true
      // 判断是否添加了通道
      const { channelList } = this.defenseRecord
      // 把添加的设备传入store
      const addChannelList = this.checkChannelList.map(key => this.channelObj[key])
      // 简化存储的信息
      const simpleChannelList = addChannelList.map(item => {
        const { chlIndex, chlId, name, sn, capability, nodeType } = item
        // BypassSwitch旁路开关，默认0关闭 linkageList 联动项
        let linkageList = []
        // 找到能力集
        if (capability) {
          // 根据能力集找到可以支持联动项
          linkageList = this.ipcLinkageList
            .filter(item => !item.filterAble || capability.includes(item.value))
            .map(item2 => item2.value)
        } else if (nodeType === 'sensor') {
          // 没有能力集则认为nodeType为sensor（传感器），没有声音和闪灯
          linkageList = this.ipcLinkageList.filter(item => !item.filterAble).map(item2 => item2.value)
        }
        return {
          chlIndex,
          chlId,
          sn,
          name,
          extra: { bypassSwitch: 0, linkageList }
        }
      })
      const deviceSet = new Set(channelList.map(item => `${item.sn}~${item.chlIndex}~${item.chlId}`))
      // 过滤掉重复的
      const filterChannelList = simpleChannelList.filter(
        item => !deviceSet.has(`${item.sn}~${item.chlIndex}~${item.chlId}`)
      )
      const newChannelList = [...channelList, ...filterChannelList]
      const params = {
        groupName: this.groupName,
        status: 0, // 默认是0
        details: newChannelList.map(item => {
          const { sn, chlIndex, chlId, extra } = item
          return {
            sn,
            chlIndex,
            chlId,
            extra: JSON.stringify(extra)
          }
        }),
        addMethod: this.addType === 'bind' ? 1 : 2
      }
      const that = this
      try {
        const callback = async (msg, code) => {
          that.$loading.hide()
          that.addLoading = false
          if (msg === 'SUCCESS') {
            await addDefenseGroup(params) // 通知新增分组给云后台
            // toast提示
            that.$toastSuccess(that.$t('createSuccess'))
            that.back()
          } else {
            if (code) {
              if (Number(code) === 550) {
                // 超时提示连接设备失败
                that.$toast(that.$t('deviceDisconnected'))
              } else {
                that.$toast(that.$t(`errorCode.${code}`))
              }
            } else {
              that.$toastFail(that.$t('createFail'))
            }
          }
        }
        // 通知设备
        this.updateDeviceStatus({
          record: { status: 0, channelList: params.details },
          callback
        })
      } catch (err) {
        this.$loading.hide()
        this.addLoading = false
        console.error(err)
      }
    },
    // 编辑分组
    async editGroupConfirm() {
      this.$loading.show()
      this.editLoading = true
      // 编辑布防组
      const { id, status } = this.defenseRecord
      // 当前通道
      const newChannelList = this.curChannelList.slice()
      const params = {
        id,
        status,
        groupName: this.groupName,
        details: newChannelList.map(item => {
          const { sn, chlIndex, chlId, extra } = item
          return {
            sn,
            chlIndex,
            chlId,
            extra: JSON.stringify(extra)
          }
        }),
        addMethod: this.addType === 'bind' ? 1 : 2
      }
      /************************TODO 考虑后续加上此逻辑**********************************/
      // 需要考虑删除的通道，这些通道需要恢复到布防状态，固定为外出布防
      // 找到删除的通道
      const { channelList = [] } = this.defenseRecord
      const curChannelKeys = new Set(this.curChannelList.map(item => `${item.sn}~${item.chlIndex}~${item.chlId}`))
      const deleteChannelKeys = new Set(
        channelList
          .filter(item => !curChannelKeys.has(`${item.sn}~${item.chlIndex}~${item.chlId}`))
          .map(item2 => `${item2.sn}~${item2.chlIndex}~${item2.chlId}`)
      )
      // 找到对应布防组的通道
      const deleteChannelList = this.groupChannelList.filter(item =>
        deleteChannelKeys.has(`${item.sn}~${item.chlIndex}~${item.chlId}`)
      )
      try {
        const curDefenseRecord = {
          ...this.defenseRecord,
          channelList: newChannelList
        }
        delete curDefenseRecord.curGroupName
        /************************TODO 考虑后续加上此逻辑**********************************/
        // 针对删除的通道发送协议，将通道恢复到布防状态
        if (deleteChannelList && deleteChannelList.length) {
          console.log('删除通道发送消息给设备', deleteChannelList)
          const callback2 = async msg => {
            if (msg === 'SUCCESS') {
              // 提示
              console.log('删除通道发送消息给设备成功')
            }
          }
          this.updateDeviceStatus({
            record: {
              status: 1,
              channelList: deleteChannelList
            },
            callback: callback2
          })
        }
        // 编辑布防组
        const that = this
        const callback = async (msg, code) => {
          if (msg === 'SUCCESS') {
            await addDefenseGroup(params)
            // 更新defenseRecord
            that.SET_DEFENSE_RECORD(curDefenseRecord)
            that.$loading.hide()
            that.editLoading = true
            // toast提示
            that.$toastSuccess(that.$t('saveSuccess'))
            // 回到布防撤防列表页
            that.$utils.routerPush({
              path: '/maxDefense/defenseDeployment'
            })
          } else {
            if (code) {
              if (Number(code) === 550) {
                // 超时提示连接设备失败
                that.$toast(that.$t('deviceDisconnected'))
              } else {
                that.$toast(that.$t(`errorCode.${code}`))
              }
            } else {
              that.$toastFail(that.$t('saveFail'))
            }
            that.$loading.hide()
            that.editLoading = false
          }
        }
        // 发送协议到对应的设备
        this.updateDeviceStatus({ record: curDefenseRecord, callback })
      } catch (err) {
        this.$loading.hide()
        this.editLoading = false
        console.error(err)
      }
    },
    // 编辑IPC联动项 把信息存过去
    editIPCLinkage(record, index) {
      this.SET_CHANNEL_RECORD(record)
      this.$router.push({ name: 'maxIpcSetting', params: { index, from: 'edit', id: record.id || 0 } })
    },
    // 点击组名拉起弹窗编辑
    // handleEditName() {
    //   if (this.type === 'edit') {
    //     this.$refs.editGroupName.show = true // 编辑区域名称
    //   }
    // },
    // 修改分组名称
    groupNameConfirm(val) {
      this.$refs.editGroupName.show = false
      this.groupName = val
    },
    // 删除分组
    deleteGroup: debounce(async function () {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      const { id } = this.defenseRecord
      // 找到对应布防组的通道
      const channelList = this.groupChannelList.filter(channelItem => channelItem.groupId === id)
      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()
        // 将分组中关联的通道恢复到布防状态
        const that = this
        const callback = async (msg, code) => {
          if (msg === 'SUCCESS') {
            // 删除当前分组
            const params = {
              groupIdList: [id]
            }
            try {
              await deleteDefenseRecord(params)
              // toast提示
              that.$toastSuccess(that.$t('deleteSuccess'))
              // 回到布防撤防列表页
              that.$utils.routerPush({
                path: '/maxDefense/defenseDeployment'
              })
            } catch (error) {
              that.$toastFail(that.$t('deleteFail'))
              console.error(error)
              that.$loading.hide()
            }
          } else {
            if (code) {
              if (Number(code) === 550) {
                // 超时提示连接设备失败
                that.$toast(that.$t('deviceDisconnected'))
              } else {
                that.$toast(that.$t(`errorCode.${code}`))
              }
            } else {
              that.$toast(that.$t('deleteFail'))
            }
            that.$loading.hide()
          }
        }
        // 发送协议到对应的设备 -- 删除时恢复布防状态，固定为外出布防
        this.updateDeviceStatus({
          record: { ...this.defenseRecord, status: 1, channelList },
          callback
        })
      } catch (err) {
        console.error(err)
        this.$loading.hide()
      }
    }, 500),
    // 全选
    handleSelectAll(flag) {
      console.log('flag', flag)
      if (flag) {
        // 已经全选了则全部取消
        this.checkChannelList = []
      } else {
        // 并未全选则全选
        const waitCheckChannelList = this.allChannelList.filter(item => Number(item.onlineStatus) === 1) // 在线的全部选上
        this.checkChannelList = waitCheckChannelList.map(item => `${item.sn}~${item.chlIndex}~${item.chlId}`)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.add-defense-wrapper {
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  position: relative;
  height: 100%;
  overflow: auto;
  .add-defense-content {
    // height: calc(100% - 44px);
    height: calc(100% - 130px);
    padding: 0px 20px;
    box-sizing: border-box;
    overflow: auto;
  }
  .add-defense-whhole-content {
    height: calc(100% - 44px);
  }
  .add-defense-head {
    width: 100%;
    height: 50px;
    padding: 14px 0px;
    box-sizing: border-box;
    // border: 1px solid red;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-defense-title {
      height: 24px;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
      // line-height: 24px;
      white-space: nowrap;
      margin-right: 40px;
    }
    .add-defense-text {
      flex: 1;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      line-height: 24px;
      overflow: hidden;
    }
    .defense-ellipsis-text {
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .add-defense-head-block {
    height: 104px;
    padding: 0px;
    display: block;
    border-bottom: none;
    .add-defense-title {
      height: 52px;
      padding: 15px 0px;
      box-sizing: border-box;
    }
    .defense-ellipsis-text {
      height: 52px;
      padding: 15px 0px;
      box-sizing: border-box;
    }
  }
  .add-defense-device {
    height: 32px;
    line-height: 32px;
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }
  .device-label {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .add-defense-icon {
    font-size: var(--font-size-h3-size, 24px);
  }
  .device-content {
    width: 100%;
    height: calc(100% - 100px);
    overflow: auto;
    .tvt-better-scroll {
      height: 100%;
    }
  }
  .device-content-add {
    height: calc(100% - 151px);
    box-sizing: border-box;
  }
  .device-list-wrapper {
    // border-radius: 10px;
    // overflow: hidden;
    height: 100%;
  }
  .device-item-wrapper {
    height: 46px;
    box-sizing: border-box;
    display: flex;
    padding: 12px 16px;
    .device-item-left {
      flex: 1;
      height: 100%;
      .device-title {
        width: 100%;
        height: 24px;
        font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
        margin-bottom: 10px;
      }
      .device-text {
        width: 100%;
        height: 24px;
        font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .device-item-right {
      width: 24px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 120px;
      img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-btn {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20px;
    }
    .add-device-btn {
      width: 102px;
      height: 40px;
      border-radius: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
    }
  }
  .footer-btn {
    width: 343px;
    height: 46px;
    border-radius: 23px;
  }
}
.swipe-right-btn {
  height: 100%;
  .van-button__content .van-button__text {
    height: 24px !important;
  }
  .refuse-img {
    width: 24px;
    height: 24px;
  }
}
</style>
