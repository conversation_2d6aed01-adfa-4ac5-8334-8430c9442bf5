<template>
  <div class="cruise-point-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="cruise-point-content">
      <div class="cruise-point-list">
        <div class="cruise-point-line">
          <div class="cruise-point-left">
            <div class="cruise-point-title">{{ $t('presetPointName') }}</div>
          </div>
          <div class="cruise-point-right">
            <div class="cruise-point-text">
              <span class="preset-ellipsis-text">{{ pointRecord.name }}</span>
              <img
                @click="handleEditPoint"
                class="arrow-img"
                :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
              />
            </div>
          </div>
        </div>
        <div class="cruise-point-line" v-if="!(devType === '1' && this.ipcType === 'yuyan')">
          <div class="cruise-point-left">
            <div class="cruise-point-title">{{ $t('speed') }}</div>
          </div>
          <div class="cruise-point-right">
            <div class="cruise-point-text preset-ellipsis-text">{{ pointRecord.speed }}</div>
            <img
              @click="handleEditSpeed"
              class="arrow-img"
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
            />
          </div>
        </div>
        <div class="cruise-point-line">
          <div class="cruise-point-left">
            <div class="cruise-point-title">{{ $t('holdTime') }}</div>
          </div>
          <div class="cruise-point-right">
            <div class="cruise-point-text preset-ellipsis-text">
              {{ pointRecord.holdTime | holdTimeLabel(holdTimeList) }}
            </div>
            <img
              @click="handleEdiHoldTime"
              class="arrow-img"
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
            />
          </div>
        </div>
      </div>
      <div class="cruise-point-footer">
        <div class="footer-btn" @click="handleConfirm">{{ $t('confirm') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
import { appRequestDevice } from '@/utils/appbridge'
import { transformXml } from '@/utils/common'
import { urlEditChlCruise, urlPtzModifyCruise } from '@/api/cruiseLine'
export default {
  name: 'addEditPersetPoint',
  components: {
    NavBar
  },
  data() {
    return {
      type: 'add',
      ipcType: null // 判断是鱼眼还是球机  yuyan  qiuji
    }
  },
  created() {
    const type = this.$route.params.type ? this.$route.params.type : 'add'
    this.type = type
    if (type === 'edit') {
      this.$route.meta.title = this.$t('edit')
    } else {
      this.$route.meta.title = this.$t('addPoint')
    }
    if (this.devType === '1') {
      // 是IPC设备
      if (this.chlCapability && this.chlCapability.supportFun) {
        if (this.chlCapability.supportFun.includes('intptz')) {
          // 说明是球机
          this.ipcType = 'qiuji'
        } else {
          // 说明是鱼眼
          this.ipcType = 'yuyan'
        }
      }
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'language', 'appType']),
    ...mapState('cruiseLine', [
      'cruiseIndex',
      'devId',
      'chlId',
      'devType',
      'chlCapability',
      'curiseRecord',
      'pointRecord',
      'holdTimeList'
    ]),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  filters: {
    // holdTime转国际化文字
    holdTimeLabel(val, holdTimeList) {
      if (val && holdTimeList) {
        const item = holdTimeList.find(item => item.value === Number(val))
        if (item) {
          return item.label
        }
      }
      return ''
    }
  },
  methods: {
    ...mapMutations('cruiseLine', ['SET_POINT_RECORD', 'SET_CRUISE_RECORD']),
    back() {
      this.$router.go(-1)
    },
    // 编辑预置点，将预置点信息传过去
    handleEditPoint() {
      this.$router.push({ name: 'chooseCruisePoint' })
    },
    // 编辑速度
    handleEditSpeed() {
      this.$router.push({ name: 'chooseSpeed' })
    },
    // 编辑持续时间
    handleEdiHoldTime() {
      this.$router.push({ name: 'chooseHoldTime' })
    },
    handleConfirm() {
      const { name, speed, holdTime } = this.pointRecord
      if (!name || !name.trim()) {
        this.$toast(this.$t('pleaseChoosePoint'))
        return false
      }
      if (!speed) {
        this.$toast(this.$t('pleaseChooseSpeed'))
        return false
      }
      if (!holdTime) {
        this.$toast(this.$t('pleaseChooseSpeed'))
        return false
      }
      // 判断是添加还是编辑
      if (this.type === 'edit') {
        // 编辑
        const { presetPointList = [] } = this.curiseRecord
        const newPointList = JSON.parse(JSON.stringify(presetPointList))
        const { name, index, speed, holdTime, pointIndex } = this.pointRecord
        newPointList.splice(pointIndex, 1, { name, index, speed, holdTime })
        const curiseRecord = {
          ...this.curiseRecord,
          presetPointList: newPointList
        }
        if (this.cruiseIndex !== null) {
          // 说明巡航线是编辑模式，直接发送请求固化下来
          const callback = status => {
            if (status === 'success') {
              this.SET_CRUISE_RECORD(curiseRecord)
              this.$toast(this.$t('editSuccess'))
              this.SET_POINT_RECORD({})
              this.back()
            } else {
              this.$toast(this.$t('editFail'))
            }
          }
          this.operateCruiseLine(newPointList, callback)
        } else {
          this.SET_CRUISE_RECORD(curiseRecord)
          this.$toast(this.$t('editSuccess'))
          this.SET_POINT_RECORD({})
          this.back()
        }
      } else {
        // 新增
        const { presetPointList = [] } = this.curiseRecord
        const newPointList = JSON.parse(JSON.stringify(presetPointList))
        newPointList.push({ ...this.pointRecord })
        const curiseRecord = {
          ...this.curiseRecord,
          presetPointList: newPointList
        }
        if (this.cruiseIndex !== null) {
          // 说明巡航线是编辑模式，直接发送请求固化下来
          const callback = status => {
            if (status === 'success') {
              this.SET_CRUISE_RECORD(curiseRecord)
              this.$toast(this.$t('addSuccess'))
              this.SET_POINT_RECORD({})
              this.back()
            } else {
              this.$toast(this.$t('addFail'))
            }
          }
          this.operateCruiseLine(newPointList, callback)
        } else {
          this.SET_CRUISE_RECORD(curiseRecord)
          this.$toast(this.$t('addSuccess'))
          this.SET_POINT_RECORD({})
          this.back()
        }
      }
    },
    operateCruiseLine(newPointList, callback) {
      const that = this
      // 编辑直接发送请求，固化下来
      const { name, index } = this.curiseRecord
      const req = {
        devId: this.devId,
        url: 'editChlCruise',
        params: urlEditChlCruise(this.chlId, index, name, newPointList)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzModifyCruise_I'
        req.params = urlPtzModifyCruise(index, name, newPointList)
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          let resFlag = false
          if (that.devType === '1') {
            // IPC设备
            if (xmlObject.config._status === 'success') {
              resFlag = true
            }
          } else {
            if (xmlObject.response.status == 'success') {
              resFlag = true
            }
          }
          if (resFlag) {
            if (callback) callback('success')
          } else {
            if (callback) callback('fail')
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.cruise-point-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.cruise-point-content {
  width: 100%;
  height: calc(100% - 125px);
  padding: 35px 16px;
  box-sizing: border-box;
  overflow-y: auto;
}

.cruise-point-list {
  width: 100%;
  border-radius: 10px;
  .cruise-point-line {
    width: 100%;
    height: 46px;
    box-sizing: border-box;
    padding: 11px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .cruise-point-left {
      height: 100%;
      display: flex;
      align-items: center;
      .cruise-point-title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
      }
    }
    .cruise-point-right {
      flex: 1;
      height: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      overflow: hidden;
      .cruise-point-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        text-align: right;
        line-height: 24px;
        margin-left: 20px;
      }
      .preset-ellipsis-text {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.cruise-point-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px 0;
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% - 32px);
    height: 46px;
    border-radius: 10px;
    text-align: center;
    color: var(--bg-color-white, #ffffff);
  }
}
</style>
<style lang="scss">
.cruise-point-wrapper .nav-bar .nav-bar-center {
  font-size: var(--font-size-body1-size, 16px) !important;
  font-weight: 600;
}
</style>
