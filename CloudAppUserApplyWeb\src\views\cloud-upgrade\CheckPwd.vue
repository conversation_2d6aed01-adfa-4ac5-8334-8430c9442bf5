<template>
  <div class="check-pwd">
    <van-dialog
      v-model="show"
      :title="$t('password')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="password-div">
          <input
            class="common-input"
            :type="passwordType"
            v-model="password"
            maxlength="16"
            :placeholder="$t('pleaseEnterPwd')"
          />
          <span class="password-close">
            <van-icon name="close" v-if="password" @click="password = ''" />
          </span>
          <span class="password-type" @click="switchPasswordType">
            <van-icon name="eye" v-if="passwordType === 'password'" />
            <van-icon name="closed-eye" v-else />
          </span>
        </div>
        <div class="tips">{{ $t('upgradeTip') }}</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      show: false,
      password: '',
      passwordType: 'password'
    }
  },
  mounted() {},
  methods: {
    handleConfirm() {
      // APP这边限制的长度是用户名  63个字符  密码16个；不校验格式 只校验是否为空
      if (!this.password || !this.password.trim()) {
        this.$toast(this.$t('pleaseEnterPwd'))
        return false
      }
      this.$emit('confirm', { password: this.password.trim() })
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.clearParam()
    },
    clearParam() {
      this.password = ''
    },
    switchPasswordType() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password'
    }
  }
}
</script>
<style lang="scss" scoped>
.check-pwd {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--icon-color-primary, #393939);
  }
  input {
    border: 0px;
    outline: none;
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .password-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-top: 20px;
    margin-bottom: 12px;
    border: 1px solid var(bg-color-secondary, #d9d9d9);
    padding: 4px 2px;
    border-radius: 6px;
  }
  .common-input {
    width: 250px;
    padding-left: 8px;
  }
  .password-div {
    position: relative;
    .password-type {
      position: absolute;
      right: 10px;
    }
    .password-close {
      position: absolute;
      right: 30px;
    }
  }
  .tips {
    font-size: var(--font-size-text-size, 12px);
    color: var(--text-color-placeholder, #8f8e93);
    margin-top: 14px;
    margin-bottom: 18px;
  }
}
</style>
