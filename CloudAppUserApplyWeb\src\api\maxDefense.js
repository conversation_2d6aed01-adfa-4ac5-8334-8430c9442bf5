import request from '@/api/request'
import baseUrl from '@/api/index.js'
import { MAX_LINKAGE_DEVIEC_OBJ } from '@/utils/options'
import { getChlGuid16 } from '@/utils/common'

// 获取所有可布撤防通道
export const defenseChannelList = data => request.post(`${baseUrl}/channel/arming-list`, data)

// 获取当前用户的所有布撤防组
export const getDefenseGroupList = data => request.post(`${baseUrl}/user/arming-group/list`, data)

// 创建布撤防组
export const addDefenseGroup = data => request.post(`${baseUrl}/user/arming-group/put`, data)

// 查询布撤防组详情 传布撤防组ID
export const getDefenseDetail = data => request.post(`${baseUrl}/user/arming-group/detail`, data)

// 删除布撤防组或详情
export const deleteDefenseRecord = data => request.post(`${baseUrl}/user/arming-group/delete`, data)

// 编辑节点布撤防状态
/**
 *
 * @param {*} status : 分组布撤防的状态 1 外出布防 2 在家布防 0 一键撤防 3 一键消警
 * @param {*} channelList ：通道列表
 * @param {*} unCheckFlag ： 0 表示是勾选的联动项 1 表示是未勾选的联动项  两者的flag最终需要取反
 * @returns
 */
export const urlDefenseSwitchNodes = (status, channelList, unCheckFlag) => {
  // status 1 外出布防 2 在家布防 0 一键撤防 3 一键消警
  // switch 开关 开 表明将以下联动项加入撤防 关表明将以下联动项取消撤防
  let itemList = ''
  channelList.forEach(item => {
    let flag = status === 0 // 布防 1、2(对应switch false)  撤防 0(对应switch true)
    let { extra } = item
    if (typeof extra === 'string') {
      extra = JSON.parse(extra)
    }
    const { bypassSwitch = 0, linkageList = [] } = extra
    // console.log('bypassSwitch', bypassSwitch, 'status', status)
    if (bypassSwitch && status === 2) {
      // 开启了旁路由，且有撤防联动项时，则在家布防时该通道/传感器将撤防（按撤防配置项取消联动）
      // 根据撤防联动项加一个撤防，然后加上撤防联动项
      if (linkageList.length) {
        flag = true
        // 添加撤防联动项
        let defenseStr = ''
        linkageList.forEach(item => {
          defenseStr += `<item>${MAX_LINKAGE_DEVIEC_OBJ[item]}</item>`
        })
        const defenseAttrs = `<defenseAttrs type="list">${defenseStr}</defenseAttrs>`
        itemList += `<item id="${item.chlId ? '{' + item.chlId + '}' : getChlGuid16(item.chlIndex)}">
          <switch>${unCheckFlag ? !flag : flag}</switch>
          <nodeType>${item.chlId ? 'sensor' : 'channel'}</nodeType>
          ${defenseAttrs}
        </item>`
      } else {
        flag = false
        // 否则是布防状态
        itemList += `<item id="${item.chlId ? '{' + item.chlId + '}' : getChlGuid16(item.chlIndex)}">
          <switch>${flag}</switch>
          <nodeType>${item.chlId ? 'sensor' : 'channel'}</nodeType>
        </item>`
      }
    } else {
      if (linkageList.length) {
        // 添加撤防联动项
        let defenseStr = ''
        linkageList.forEach(item => {
          defenseStr += `<item>${MAX_LINKAGE_DEVIEC_OBJ[item]}</item>`
        })
        const defenseAttrs = `<defenseAttrs type="list">${defenseStr}</defenseAttrs>`
        itemList += `<item id="${item.chlId ? '{' + item.chlId + '}' : getChlGuid16(item.chlIndex)}">
          <switch>${unCheckFlag ? !flag : flag}</switch>
          <nodeType>${item.chlId ? 'sensor' : 'channel'}</nodeType>
          ${defenseAttrs}
        </item>`
      } else {
        itemList += `<item id="${item.chlId ? '{' + item.chlId + '}' : getChlGuid16(item.chlIndex)}">
          <switch>${flag}</switch>
          <nodeType>${item.chlId ? 'sensor' : 'channel'}</nodeType>
        </item>`
      }
    }
  })
  const xml = `<content><defenseSwitchNodes type="list">${itemList}</defenseSwitchNodes></content>`
  return xml
}

// 一键消警 -- 目前仅支持声音、灯光、alarm-out
export const urlDisalarmSwitchNodes = channelList => {
  let itemList = ''
  channelList.forEach(item => {
    itemList += `<item id="${item.chlId ? '{' + item.chlId + '}' : getChlGuid16(item.chlIndex)}" />`
  })
  const itemStr = `<nodes type="list">${itemList}</nodes>`
  const xml = `<content><disalarmItems>
  <item>
    <triggerType type="triggerType">triggerAudio</triggerType>
    ${itemStr}
  </item>
  <item>
    <triggerType type="triggerType">triggerWhiteLight</triggerType>
    ${itemStr}
  </item>
  <item>
    <triggerType type="triggerType">alarmOut</triggerType>
  </item>
  </disalarmItems></content>`
  return xml
}
