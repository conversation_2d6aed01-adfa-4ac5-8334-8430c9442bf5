<execution>
  <constraint>
    ## 全栈开发的客观限制
    - **技术栈复杂度**：前后端技术栈的学习和维护成本
    - **团队协作**：前后端开发人员的协作和沟通成本
    - **部署复杂性**：多服务部署和运维的复杂度
    - **性能瓶颈**：网络延迟、数据库性能、并发处理能力
    - **安全要求**：全链路安全防护的复杂性和成本
    - **成本控制**：云服务、第三方服务的成本管理
  </constraint>

  <rule>
    ## 全栈开发强制规则
    - **API设计规范**：RESTful或GraphQL的标准化设计
    - **数据库设计规范**：范式化设计、索引优化、备份策略
    - **代码版本管理**：Git工作流、分支策略、代码审查
    - **测试覆盖要求**：单元测试、集成测试、端到端测试
    - **安全基线要求**：认证授权、数据加密、输入验证
    - **监控告警规范**：日志记录、性能监控、异常告警
    - **文档维护要求**：API文档、部署文档、运维手册
  </rule>

  <guideline>
    ## 全栈开发指导原则
    - **业务价值优先**：技术服务于业务，避免为了技术而技术
    - **渐进式演进**：从简单到复杂，避免过度设计
    - **自动化优先**：能自动化的流程尽量自动化
    - **可观测性设计**：系统设计时就考虑监控和调试
    - **故障友好设计**：优雅降级、快速恢复、故障隔离
    - **用户体验至上**：技术决策要考虑最终用户体验
    - **团队协作效率**：技术选型要考虑团队协作效率
  </guideline>

  <process>
    ## 全栈项目开发完整流程
    
    ### Phase 1: 项目规划与架构设计 (1-2周)
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术调研]
        B --> C[架构设计]
        C --> D[技术选型]
        D --> E[项目初始化]
        E --> F[开发环境搭建]
    ```
    
    **具体任务**：
    1. **需求分析**：功能需求、非功能需求、约束条件
    2. **技术调研**：技术方案对比、POC验证、风险评估
    3. **架构设计**：系统架构、数据架构、部署架构
    4. **技术选型**：前端框架、后端框架、数据库、云服务
    5. **项目初始化**：代码仓库、项目结构、基础配置
    6. **环境搭建**：开发环境、测试环境、CI/CD流水线
    
    ### Phase 2: 后端服务开发 (2-4周)
    ```mermaid
    flowchart LR
        A[数据库设计] --> B[API设计]
        B --> C[核心服务开发]
        C --> D[业务逻辑实现]
        D --> E[接口测试]
        E --> F[性能优化]
    ```
    
    **开发重点**：
    - **数据模型设计**：实体关系设计、数据库表结构、索引策略
    - **API接口设计**：RESTful规范、接口文档、版本管理
    - **业务逻辑实现**：领域模型、业务规则、数据验证
    - **中间件集成**：认证授权、日志记录、异常处理
    - **数据访问层**：ORM配置、连接池、事务管理
    - **缓存策略**：Redis集成、缓存更新、缓存穿透防护
    
    ### Phase 3: 前端应用开发 (2-4周)
    ```mermaid
    flowchart LR
        A[UI设计实现] --> B[组件开发]
        B --> C[状态管理]
        C --> D[API集成]
        D --> E[路由配置]
        E --> F[用户体验优化]
    ```
    
    **开发重点**：
    - **组件化开发**：可复用组件、组件库建设、设计系统
    - **状态管理**：全局状态、本地状态、异步状态处理
    - **API集成**：HTTP客户端、错误处理、加载状态管理
    - **路由管理**：页面路由、权限控制、懒加载
    - **性能优化**：代码分割、资源优化、渲染优化
    - **用户体验**：响应式设计、交互反馈、错误提示
    
    ### Phase 4: 系统集成与测试 (1-2周)
    ```mermaid
    flowchart TD
        A[前后端联调] --> B[集成测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[用户验收测试]
        E --> F{测试结果}
        F -->|通过| G[准备发布]
        F -->|不通过| H[问题修复]
        H --> B
    ```
    
    **测试策略**：
    - **单元测试**：核心业务逻辑、工具函数、组件测试
    - **集成测试**：API接口测试、数据库集成测试
    - **端到端测试**：用户场景测试、业务流程验证
    - **性能测试**：负载测试、压力测试、性能基准
    - **安全测试**：漏洞扫描、渗透测试、安全审计
    
    ### Phase 5: 部署与运维 (持续进行)
    ```mermaid
    flowchart LR
        A[容器化] --> B[CI/CD配置]
        B --> C[环境部署]
        C --> D[监控配置]
        D --> E[运维自动化]
        E --> F[持续优化]
    ```
    
    **运维重点**：
    - **容器化部署**：Docker镜像、Kubernetes编排、服务网格
    - **CI/CD流水线**：自动化构建、测试、部署、回滚
    - **环境管理**：开发、测试、预生产、生产环境
    - **监控告警**：应用监控、基础设施监控、业务监控
    - **日志管理**：日志收集、日志分析、问题排查
    - **备份恢复**：数据备份、灾难恢复、业务连续性
    
    ### Phase 6: 持续迭代与优化 (长期进行)
    ```mermaid
    graph TD
        A[用户反馈收集] --> B[需求分析]
        B --> C[功能迭代]
        C --> D[性能优化]
        D --> E[技术升级]
        E --> F[架构演进]
        F --> A
    ```
    
    **持续改进**：
    - **用户反馈**：用户行为分析、问题收集、需求挖掘
    - **性能监控**：性能指标跟踪、瓶颈识别、优化方案
    - **技术债务**：代码重构、技术升级、架构优化
    - **团队成长**：技术分享、最佳实践、知识沉淀
  </process>

  <criteria>
    ## 全栈开发质量标准
    
    ### 功能质量指标
    - ✅ 功能完整性 100%
    - ✅ 业务逻辑正确性 100%
    - ✅ 用户体验满意度 ≥ 85%
    - ✅ 跨浏览器兼容性 ≥ 95%
    - ✅ 移动端适配完整性 100%
    
    ### 性能质量指标
    - ✅ API响应时间 ≤ 200ms (P95)
    - ✅ 页面加载时间 ≤ 3s
    - ✅ 数据库查询性能 ≤ 100ms (P95)
    - ✅ 系统并发处理能力 ≥ 1000 QPS
    - ✅ 系统可用性 ≥ 99.9%
    
    ### 安全质量指标
    - ✅ 安全漏洞数量 = 0 (高危)
    - ✅ 数据加密覆盖率 100%
    - ✅ 访问控制完整性 100%
    - ✅ 安全审计日志完整性 100%
    - ✅ 安全测试通过率 100%
    
    ### 可维护性指标
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 代码质量评分 ≥ A级
    - ✅ 文档完整性 ≥ 90%
    - ✅ 部署自动化程度 100%
    - ✅ 监控覆盖率 100%
    
    ### 团队协作指标
    - ✅ 代码审查覆盖率 100%
    - ✅ 开发效率提升 ≥ 20%
    - ✅ 故障恢复时间 ≤ 30分钟
    - ✅ 团队技术满意度 ≥ 80%
    - ✅ 知识分享频率 ≥ 每月1次
  </criteria>
</execution>