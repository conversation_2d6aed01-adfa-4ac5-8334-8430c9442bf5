.trusteeship-device-details {
  .device-details {
    background-color: $UI1E-light-background-color !important;
    color: $UI1E-50-white-color !important;
    .title {
      color: $UI1E-white-color !important;
    }
  }
  .configuration {
    color: $UI1E-white-color !important;
  }
  .container-ul{
    .device-configuration {
      background-color: $UI1E-light-background-color !important;
      border-bottom: 1px solid $UI1E-light-gray-color !important;
      &:last-child{
        border: 0 !important;
      }
      .configuration-item{
        color: $UI1E-white-color !important;
      }
    }
  }
  .v-li {
    background-color: $UI1E-light-background-color !important;
    .v-li-content {
      color: $UI1E-white-color !important;
    }
    .v-li-res {
      color: $UI1E-white-color !important;
    }
  }
}