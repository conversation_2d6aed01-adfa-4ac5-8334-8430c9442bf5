<template>
  <div class="defense-list-wrapper">
    <div class="defense-item-wrapper" v-for="(item, index) of dataList" :key="index" @click="handleClick(item, index)">
      <!-- <div class="defense-item-head"><van-icon name="edit" @click.stop="handleEdit(item, index)" /></div> -->
      <div class="defense-item-content">
        <div class="defense-item-left">
          <div class="defense-status-img">
            <!-- <img
              class="bind-img"
              :src="require('@/assets/img/common/defense/' + statusImgObj[item.status] || 'cancel_defense.png')"
            /> -->
            <theme-image
              class="bind-img"
              :alt="statusImgObj[item.status] || 'cancel_defense.png'"
              :imageName="statusImgObj[item.status] || 'cancel_defense.png'"
            />
          </div>
        </div>
        <div class="defense-item-right">
          <div class="defense-item-title">{{ item.groupName }}</div>
          <div class="defense-item-text">{{ statusNameObj[item.status] }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'DefenseList',
  components: {
    ThemeImage
  },
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      //   dataList: [{}, {}, {}]
      statusImgObj: {
        0: 'cancel_defense.png',
        1: 'out_defense.png',
        2: 'home_defense.png'
      },
      statusNameObj: {
        0: this.$t('cancelDefense'),
        1: this.$t('outDefense'),
        2: this.$t('homeDefense')
      }
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleClick(item, index) {
      this.$emit('handleClick', item, index)
    },
    handleEdit(item, index) {
      this.$emit('handleEdit', item, index)
    }
  }
}
</script>
<style lang="scss" scoped>
.defense-list-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  padding-bottom: 16px;
  box-sizing: border-box;
}
.defense-item-wrapper {
  width: 44%;
  height: 82px;
  box-sizing: border-box;
  border-radius: 10px;
  margin-left: 4%;
  margin-top: 16px;
  padding: 16px;
  .defense-item-head {
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .defense-item-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .defense-item-left {
      width: 36px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .defense-status-img img {
        width: 36px;
        height: 36px;
      }
    }
    .defense-item-right {
      width: calc(100% - 36px);
      height: 100%;
      box-sizing: border-box;
      padding-left: 8px;
      .defense-item-title {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .defense-item-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
