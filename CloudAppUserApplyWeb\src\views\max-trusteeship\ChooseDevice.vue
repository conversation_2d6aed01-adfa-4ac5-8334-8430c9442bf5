<template>
  <div class="choose-device-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div :class="['choose-device-content', isInitReq && deviceList.length > 0 ? 'choose-device-body' : '']">
      <template v-if="!isInitReq || deviceList.length">
        <select-device v-model="checkDevices" :deviceList="deviceList" />
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('noTrusteeshipDevice') }}</div>
      </div>
    </div>
    <div class="footer" v-if="isInitReq && deviceList.length > 0">
      <van-button class="footer-btn" type="primary" :disabled="btnDisabled" @click="handleNextStep">
        {{ $t('nextStep') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import SelectDevice from './components/SelectDevice.vue'
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'
import { deviceTrusteeshipsList, deviceTrusteeshipsCreate } from '@/api/trusteeship'
import { gotoPage } from '@/utils/appbridge'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'ChooseDevice',
  components: {
    NavBar,
    SelectDevice,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isInitReq: false, // 是否请求过数据
      checkDevices: [], // 选中的通道
      deviceList: [], // 设备列表
      deviceCapabilitys: DEVICE_CAPABILITY_LIST() // 全量设备权限
    }
  },
  created() {
    // 首次进入清除选中的设备 --后续可能会调整
    this.checkDevices = []
    // 请求设备和通道
    this.getTrusteeshipDevices()
  },
  mounted() {},
  computed: {
    // 按钮是否可点击
    btnDisabled() {
      return this.checkDevices.length === 0
    }
  },
  methods: {
    back() {
      // 跳转到服务页面
      gotoPage({
        pageRoute: 'service/home'
      })
    },
    // 获取当前用户可托管设备
    async getTrusteeshipDevices() {
      try {
        // type 0: 可托管设备； 1：已发起托管设备
        const res = await deviceTrusteeshipsList({ type: 0 })
        if (res.basic.code === 200) {
          this.isInitReq = true
          const { data } = res
          this.deviceList = data.map(item => {
            const { authList, effectiveTime } = item
            const checkCapability = authList
              ? this.deviceCapabilitys.filter(item2 => authList.includes(item2.value))
              : this.deviceCapabilitys.slice()
            return {
              ...item,
              effectiveTime: effectiveTime || 0,
              checkCapability
            }
          })
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 下一步
    async handleNextStep() {
      try {
        // 发起托管
        // 校验 至少选择一个设备
        if (this.checkDevices.length === 0) {
          this.$toast(this.$t('leastChoose'))
          return false
        }
        // console.log('this.checkDevices', this.checkDevices)
        const params = []
        this.checkDevices.forEach(item => {
          const { sn, checkCapability, effectiveTime } = item
          params.push({
            sn,
            authList: checkCapability.map(item2 => item2.value),
            effectiveTime
          })
        })
        const res = await deviceTrusteeshipsCreate(params)
        // console.log('res', res)
        if (res.basic.code === 200) {
          this.checkDevices = [] // 重置当前选中通道
          // 进入托管发送成功页
          this.$utils.routerPush({
            path: '/maxTrusteeship/trusteeshipSuccess'
          })
        }
      } catch (err) {
        console.error(err)
        // 重新请求刷新设备
        this.checkDevices = [] // 清除选中的设备
        this.getTrusteeshipDevices()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-device-wrapper {
  height: 100%;
  overflow: hidden;
  .choose-device-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    box-sizing: border-box;
    .no-data {
      width: 100%;
      height: calc(100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      .no-data-img {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 120px;
          height: 123px;
        }
        .theme-image-container {
          width: 120px;
          height: 123px;
        }
      }
      .no-data-text {
        text-align: center;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-top: 20px;
      }
    }
  }
  .choose-device-body {
    height: calc(100% - 130px);
  }
}
</style>
