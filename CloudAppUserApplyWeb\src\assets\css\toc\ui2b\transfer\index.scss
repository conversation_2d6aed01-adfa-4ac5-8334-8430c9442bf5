.device-transfer-wrapper {
    .device-list-item {
        .van-cell {
          border-bottom: 1px solid $max-border;
        }
    }
} 

.trusteeship-check {
  .container-li {
    border-bottom: 1px solid $max-border;
    .li-title {
      border-bottom: 1px solid $max-border; 
    }
    .v-li:not(:last-child) {
      border-bottom: 1px solid $max-border;
    }
    .v-li-value {
      color: $max-gray;
    }
    .v-li .v-li-title {
      color: $max-gray;
    }
    .v-li-active .v-li-title {
      color: $max-black;
    }
  }
}
.trusteeship-site-box {
  border-bottom: 1px solid $max-border;
  .site-name {
    color: $max-gray;
  }
}

.device-transfer-wrapper {
  .devices-list-wrapper {
      .separator-box {
          background-color: $max-gray;
      }
      .device-capability {
          color: $max-gray;
      }
  }
}