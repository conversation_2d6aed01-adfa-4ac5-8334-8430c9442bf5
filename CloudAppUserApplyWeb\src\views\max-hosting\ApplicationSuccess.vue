<template>
  <div class="application-success">
    <span class="success-title">
      {{ $t('applicationSuccess') }}
    </span>
    <span class="success-info">
      {{ $t('applicationInfo', [email]) }}
    </span>
    <img src="@/assets/img/common/device_success.png" class="success-img" />
    <van-button class="footer-btn" type="primary" @click="toService">
      {{ $t('done') }}
    </van-button>
  </div>
</template>

<script>
import { appClose } from '@/utils/appbridge'
export default {
  name: 'MaxHostingApplicationSuccess',
  components: {},
  data() {
    return {
      email: ''
    }
  },
  created() {
    const { email } = this.$route.query

    if (email) {
      this.email = email
    } else {
      this.email = ''
    }
  },
  methods: {
    toService() {
      appClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.application-success {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .success-title {
    margin-top: 124px;
    color: var(--bg-color-black, #000000);
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }
  .success-info {
    margin: 16px 0;
    color: var(--icon-color-primary, #393939);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    width: calc(100% - 72px);
    min-width: 303px;
  }
  .success-img {
    width: calc(100% - 72px);
    min-width: 303px;
  }
  .footer-btn {
    position: fixed;
    bottom: 10px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
  }
}
</style>
<style lang="scss"></style>
