/**
 * Created by <PERSON><PERSON> on 19/06/04.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

// 从superlivePlus app 拿到
// app注册
export function validateEmail(str) {
  const EMAIL_REG = /^[-_.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
  return EMAIL_REG.test(str)
}

// 设备密码校验
export function validatePassword(str, level) {
  const PASSWORD_REG = /^(?![0-9]+$)(?![A-Z]+$)(?![a-z]+$)(?![^a-zA-Z0-9]+$)[0-9A-Za-z\W_]{8,16}$/
  // 强度密码-密码长度为8-16个字符，包含数字、小写字母、大写字母、特殊符号中三种或三种以上
  const STRONG_PASSWORD_REG =
    /^(?:(?=.*[a-z])(?=.*[A-Z])(?=.*\d)|(?=.*[a-z])(?=.*[A-Z])(?=.*\W)|(?=.*[a-z])(?=.*\d)(?=.*\W)|(?=.*[A-Z])(?=.*\d)(?=.*\W))[a-zA-Z\d\W]{8,16}$/
  // 更强密码-长度为9-16个字符，包含数字、小写字母、大写字母、特殊符号
  const STRONGER_PASSWORD_REG = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{9,16}$/
  switch (level) {
    case 'medium':
      return PASSWORD_REG.test(str)
    case 'strong':
      return STRONG_PASSWORD_REG.test(str)
    case 'stronger':
      return STRONGER_PASSWORD_REG.test(str)
    default:
      return false
  }
}

// 中国11位数字
// 其它国家最多20
// 最少2位
export function validateCommonPhone(str, locale) {
  if (!str || str.length < 2) {
    return false
  }

  if (locale === 'CN') {
    return str.length === 11
  }

  return str.length <= 20
}

export function validateSiteName(str) {
  if (typeof str !== 'string' || !str) {
    return false
  }

  if (/[\\\\/:*?"<>|]+/.test(str)) {
    return false
  }

  return true
}
