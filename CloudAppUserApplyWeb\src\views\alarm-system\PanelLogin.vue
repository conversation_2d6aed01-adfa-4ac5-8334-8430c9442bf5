<template>
  <div class="panel-login">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" />
    <div class="panel-login-content">
      <div class="panel-login-main">
        <!-- 解锁图标和标题 -->
        <div class="unlock-section">
          <div class="unlock-icon">
            <theme-image class="unlock-img" alt="unlock" imageName="alarm-system/lock.png" />
          </div>
          <div class="unlock-title">{{ $t('unlocking') }} {{ siteName }}</div>
        </div>
        <!-- 输入框区域 -->
        <div class="input-section">
          <common-input
            v-model="userCode"
            type="password"
            :placeholder="$t('enterUserCode')"
            :maxlength="20"
            borderType="bottom"
          />
        </div>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div class="panel-login-footer footer">
      <van-button
        class="footer-btn alarm-footer-btn"
        type="primary"
        @click="handleProceed"
        :disabled="!userCode.trim()"
        :loading="isLoading"
      >
        {{ $t('proceed') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { loginToSite, authenticatePanel } from '@/api/alarmSystem'
import CommonInput from './components/CommonInput.vue'

export default {
  name: 'PanelLogin',
  components: {
    CommonInput
  },

  data() {
    return {
      userCode: '',
      isLoading: false
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['systemType', 'isPimaSystem', 'siteId', 'siteName'])
  },
  methods: {
    ...mapActions('alarmSystem', ['saveSiteLoginInfo']),
    goBack() {
      this.$router.back()
    },
    // 处理登录按钮点击事件
    async handleProceed() {
      if (!this.userCode.trim()) {
        this.$toast(this.$t('pleaseEnterUserCode'))
        return
      }
      if (!this.siteId) {
        this.$toast(this.$t('missingSiteInformation'))
        return
      }
      this.isLoading = true
      try {
        const siteInfo = await this.loginToPanel()
        this.$toast.success(this.$t('loginSuccessful'))
        // 跳转到面板主页
        this.$router.push({
          path: '/alarmSystem/panelMain',
          query: {
            systemType: this.systemType,
            siteName: this.siteName,
            siteId: this.siteId,
            sessionId: siteInfo?.sessionId
          }
        })
      } catch (error) {
        this.$toast.fail(this.$t('loginPanelFailed'))
      } finally {
        this.isLoading = false
      }
    },
    // 面板登录统一入口  根据报警系统类型分发到具体的登录方法
    async loginToPanel() {
      if (this.isPimaSystem) {
        return await this.loginToPimaPanel()
      } else {
        return await this.loginToRiscoPanel()
      }
    },
    // Pima系统面板登录
    async loginToPimaPanel() {
      const reqData = {
        data: this.userCode // 用户密码
      }

      const response = await authenticatePanel(reqData)

      if (response && response.data) {
        // 准备站点登录信息
        const siteInfo = {
          siteId: this.siteId,
          siteName: this.siteName,
          sessionId: response.data.sessionToken, // 使用sessionToken
          webUserType: response.data.webUserType, // 用户类型
          systemType: 'Pima'
        }

        // 保存站点登录信息到store
        await this.saveSiteLoginInfo(siteInfo)

        return siteInfo
      } else {
        throw new Error('Login failed: Invalid Pima authentication response')
      }
    },
    // Risco/Tyco系统面板登录 调用loginToSite接口进行站点登录
    async loginToRiscoPanel() {
      const reqData = {
        pinCode: this.userCode,
        languageId: 'en',
        devicePlatform: 2 // WebBrowser
      }
      const response = await loginToSite(this.siteId, reqData)
      if (response && response.status === 200) {
        const { sessionId, expiresAt, cpId } = response.response
        // 准备站点登录信息
        const siteInfo = {
          siteId: this.siteId,
          siteName: this.siteName,
          sessionId,
          expiresAt,
          cpId,
          systemType: this.systemType
        }
        // 保存站点登录信息到store
        await this.saveSiteLoginInfo(siteInfo)
        return siteInfo
      } else {
        throw new Error('Login failed: Invalid response')
      }
    },
    // 创建Pima系统模拟站点信息
    createMockPimaSiteInfo() {
      return {
        siteId: this.siteId,
        siteName: this.siteName,
        sessionId: `dev-mock-pima-session-${Date.now()}`,
        webUserType: 'mock-user-type',
        systemType: 'Pima'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-login {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  &-main {
    flex: 1;
  }

  .unlock-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 195px;
    margin-bottom: 33px;

    .unlock-icon {
      margin-bottom: 28px;

      .unlock-img {
        width: 42px;
        height: 42px;
      }
    }

    .unlock-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 28px;
    }
  }

  &-footer {
    bottom: 56px;
  }
}
</style>
