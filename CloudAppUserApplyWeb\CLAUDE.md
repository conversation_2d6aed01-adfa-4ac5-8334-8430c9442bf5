# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 2.7 mobile web application for SuperLive cloud platform, providing device management, security features, and user interfaces for TOC (Consumer), TOB (Business), and TOP (Partner) applications. The app integrates with native mobile apps via WebViewJavascriptBridge.

## Development Commands

### Core Commands
```bash
# Install dependencies
npm install

# Development server (port 9020)
npm run serve
# or
npm run dev

# Production build
npm run build

# Staging build
npm run stage

# Lint code
npm run lint
```

### Environment Configuration
- Development: Uses proxy configuration in `vue.config.js` 
- Staging: `npm run stage` for staging environment
- Production: `npm run build` for production deployment

## Application Architecture

### Multi-App & Multi-UI System
The application supports multiple app types with different UI themes:
- **TOC**: Consumer apps (SuperLivePlus, SuperCamPlus, etc.) - UI variants: UI1A, UI1B, UI1C, UI1D, UI1E, UI1F, UI1G, UI1H, UI1I, UI1K, UI1M, UI1N, UI2A, UI2B, UI2C, UI3A
- **TOB**: Business/VMS apps - UI variants: UI1B, UI1C  
- **TOP**: Partner/Installer apps - UI variant: UI1C

App type and UI theme are determined from URL parameters passed by the native app.

### Dynamic Style Loading
Styles are loaded dynamically based on app type and UI:
```javascript
import('@/assets/css/' + type + '/' + UI + '/index.scss')
```

### Internationalization (i18n)
Complex multi-language support with:
- Common translations in `/src/lang/common/`
- App-specific translations in `/src/lang/{APP_TYPE}_{UI}/`
- Language selection based on `{appType}_{originalStyle}` pattern
- Vant UI component translations included

### State Management (Vuex)
Modular store structure with modules for each major feature:
- `app`: Core app configuration and theme management
- `trusteeship`: Device hosting/trusteeship functionality  
- `share`: Device sharing features
- `defense`: Security/alarm system management
- `presetPoint`: Camera preset points
- `cruiseLine`: Camera cruise line configuration
- `householdManagement`: Building/household management
- `targetFace`: Face recognition management
- `maxHosting`: Advanced hosting features
- `device`: Device management

### API Structure
API endpoints are determined by app type:
- **TOC**: `/mobile_v1.0/resource`
- **TOB**: `/vms` 
- **TOP**: `/partner-api`

### Native App Bridge
Communication with native mobile apps via `WebViewJavascriptBridge`:
- Bridge setup in `/src/utils/appbridge.js`
- Functions like `appClose()`, `showToast()`, etc.
- Mock mode support for web development

## Routing System

### Layout Structure
- Uses layout wrapper (`/src/layout/index.vue`) for safe area management
- Route-based safe area control via `meta.hideTopArea` and `meta.hideBottomArea`
- Chunk-based lazy loading with webpackChunkName comments

### Safe Area Handling
Dynamic safe area padding based on native app parameters:
- CSS variables: `--safeAreaTop`, `--safeAreaBottom`
- Responsive to route meta configuration

## Key Development Patterns

### Component Organization
- Shared components in `/src/components/`
- Feature-specific components in `/src/views/{feature}/components/`
- Theme-aware image loading via `themeImage` component

### Styling Approach
- SCSS with global mixins and variables
- Theme-specific styles in nested directories
- Mobile-first responsive design with `amfe-flexible`
- Vant UI component library integration

### Language File Management
When adding new translatable content:
1. Add keys to `/src/lang/common/` files for all supported languages
2. For app-specific translations, add to appropriate `/src/lang/{APP_TYPE}_{UI}/` directories
3. Use `this.$t('key')` in components for translation

### Adding New Features
1. Create route in `/src/router/router.config.js` with appropriate lazy loading
2. Add Vuex module if state management needed
3. Create API functions in `/src/api/` 
4. Add translations to language files
5. Follow existing component structure and naming conventions

## Build Configuration

### Webpack Optimizations
- Code splitting for vendor libraries (Vant, common components)
- Bundle analysis with `webpack-bundle-analyzer`
- CSS extraction in production
- Source map configuration per environment

### Asset Handling
- Static assets in `/public/static/`
- Dynamic imports for theme-specific styles
- Image optimization and theming support

## Testing and Quality

### Linting
- ESLint + Prettier configuration
- Pre-commit hooks via husky and lint-staged
- Vue-specific linting rules

### Browser Support
- IE compatibility via core-js and regenerator-runtime
- Mobile-optimized with viewport meta configuration
- Polyfills for modern JavaScript features