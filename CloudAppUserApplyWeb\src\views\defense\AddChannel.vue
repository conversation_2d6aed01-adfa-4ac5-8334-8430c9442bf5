<template>
  <div class="channel-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="channel-content">
      <div class="channel-list">
        <van-collapse v-model="activeNames">
          <!-- 一级：站点 -->
          <van-collapse-item
            v-for="(item, index) of curSiteChannelList"
            :title="item.siteName"
            :name="item.siteId"
            :key="'' + item.siteId + index"
          >
            <van-collapse v-model="activeNames">
              <!-- 二级：设备 -->
              <van-collapse-item
                v-for="(item2, index2) of item.children"
                :title="item2.devName"
                :name="item2.sn"
                :key="`${item.siteId}~${item2.sn}~${index2}`"
              >
                <!-- 二级：设备右侧勾选 -->
                <template #value>
                  <!-- <van-checkbox
                    v-model="deviceChecked[`${item.siteId}~${item2.sn}`]"
                    label-disabled
                    @click.native="e => toggleDevice(e, `${item.siteId}~${item2.sn}`)"
                    @change="checked => handleDevice(checked, `${item.siteId}~${item2.sn}`)"
                  ></van-checkbox> -->
                  <van-checkbox
                    :name="`${item.siteId}~${item2.sn}~device`"
                    :ref="`${item.siteId}~${item2.sn}~device`"
                    v-model="deviceChecked[`${item.siteId}~${item2.sn}`]"
                    label-disabled
                    @click.native="e => toggleDevice(e, `${item.siteId}~${item2.sn}`, item2)"
                  ></van-checkbox>
                </template>
                <!-- 三级：通道勾选 -->
                <van-checkbox-group
                  v-model="result[`${item.siteId}~${item2.sn}`]"
                  :ref="`${item.siteId}~${item2.sn}~checkboxGroup`"
                >
                  <van-cell-group>
                    <van-cell
                      v-for="(item3, index3) of item2.children"
                      clickable
                      :key="`${item.siteId}~${item2.sn}~${item3.chlIndex}`"
                      :title="`${item3.chlName}`"
                      :class="
                        channelCheckedSet.has[`${item.siteId}~${item2.sn}~${item3.chlIndex}}`]
                          ? 'van-cell-checked'
                          : 'van-cell-normal'
                      "
                      @click.stop.native="
                        e => toggle(e, `${item.siteId}~${item2.sn}~${item3.chlIndex}~${index3}`, item2, item3)
                      "
                    >
                      <template #right-icon>
                        <van-checkbox
                          :name="`${item.siteId}~${item2.sn}~${item3.chlIndex}`"
                          :ref="`${item.siteId}~${item2.sn}~${item3.chlIndex}~${index3}`"
                        />
                      </template>
                    </van-cell>
                  </van-cell-group>
                </van-checkbox-group>
              </van-collapse-item>
            </van-collapse>
          </van-collapse-item>
        </van-collapse>
      </div>
    </div>
    <div class="footer">
      <div class="footer-btn" @click="handleConfirm">
        {{ $t('confirm') }}
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
import { IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import { transformXml } from '@/utils/common'
import { defenseChannelList, addDefenseGroup, getDefenseDetail, urlDefenseSwitchNodes } from '@/api/defense'
import { appRequestDevice } from '@/utils/appbridge'
export default {
  name: 'AddChannel',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      borderFlag: false,
      channelList: [], // 站点通道列表
      activeNames: [], // 打开的折叠面板
      deviceChecked: {}, // 设备是否勾选
      channelCheckedSet: new Set(), // 通道勾选集合
      ipcLinkageList: IPC_LINKAGE_LIST_FULLNAME(),
      result: {},
      type: 'add', // 新增还是编辑 0 新增 1 编辑,
      curSiteChannelList: [] // 展示的站点、设备、通道树
    }
  },
  created() {
    if (this.defenseRecord && this.defenseRecord.id) {
      // 说明是编辑
      this.type = 'edit'
      this.$route.meta.title = this.$t('edit')
    } else {
      this.type = 'add'
      this.$route.meta.title = this.$t('add')
    }
  },
  async mounted() {
    // 进入页面则请求一次全部的通道，避免账号删除设备后展示的设备树不正确
    await this.getChannelList()
    const { channelList = [] } = this.defenseRecord
    if (channelList.length) {
      // 有通道需要过滤当前组已经添加的通道
      const channelKeySet = new Set(channelList.map(item => `${item.siteId}~${item.sn}~${item.chlIndex}`))
      // 过滤出未被当前组添加的通道
      const filterChannelList = this.allChannelList.filter(
        item => !channelKeySet.has(`${item.siteId}~${item.sn}~${item.chlIndex}`)
      )
      const curSiteChannelList = this.createChannelTree(filterChannelList)
      this.curSiteChannelList = curSiteChannelList
    } else {
      // 没有通道则直接使用所有站点通道
      this.curSiteChannelList = this.siteChannelList
    }
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('defense', [
      'allChannelList',
      'siteChannelList',
      'channelObj',
      'defenseRecord',
      'capabilityObj',
      'defenseGroupList'
    ]),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('defense', [
      'SET_ALL_CHANNEL_LIST',
      'SET_SITE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_ADD_CHANNEL_LIST',
      'SET_DEFENSE_RECORD'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 查询所有通道列表
    async getChannelList() {
      try {
        const res = await defenseChannelList({})
        const resData = res.data || []
        // 遍历所有的通道列表，构造成站点、设备、通道的树形结构
        this.initChanelTree(resData)
      } catch (error) {
        console.error(error)
      }
    },
    // 根据所有通道构建站点、设备、通道的树形结构
    initChanelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const siteChannelList = []
      const siteIndexObj = {} // 记录站点Id及其在通道树中的索引
      const deviceIndexObj = {} // 记录站点Id——设备sn及其在通道树中的索引
      const channelObj = {} // 记录站点Id-设备sn-通道chlIndex及其对应的通道信息
      const capabilityObj = {} // 记录设备sn-通道chlIndex及其对应的能力集
      allChannelList.forEach(item => {
        const { siteName, siteId, sn, devName, chlIndex, capability } = item
        channelObj[`${siteId}~${sn}~${chlIndex}`] = { ...item }
        let temp = null
        // 判断站点
        if (siteIndexObj[siteId] !== undefined) {
          // 说明站点存在
          const index = siteIndexObj[siteId]
          temp = siteChannelList[index]
          temp.children = siteChannelList[index].children || []
          // 判断设备是否存在
          if (deviceIndexObj[`${siteId}~${sn}`] !== undefined) {
            // 说明设备存在
            const index2 = deviceIndexObj[`${siteId}~${sn}`]
            // 添加通道
            temp.children[index2].children.push({ ...item })
          } else {
            // 说明设备不存在
            const temp3 = { sn, devName, children: [{ ...item }] }
            deviceIndexObj[`${siteId}~${sn}`] = temp.children.length // 记录下设备的索引
            temp.children.push(temp3)
          }
        } else {
          // 说明站点不存在，直接添加
          temp = { siteName, siteId, children: [] }
          siteIndexObj[siteId] = siteChannelList.length // 记录下站点的索引
          // 继续添加设备和通道
          deviceIndexObj[`${siteId}~${sn}`] = temp.children.length
          temp.children.push({ sn, devName, children: [{ ...item }] })
          siteChannelList.push(temp)
        }
        // 找到能力集
        if (capability) {
          const { supportFun = [] } = JSON.parse(capability)
          capabilityObj[`${sn}~${chlIndex}`] = supportFun.slice()
        }
      })
      // console.log('capabilityObj', capabilityObj)
      this.SET_ALL_CHANNEL_LIST(allChannelList)
      this.SET_SITE_CHANNEL_LIST(siteChannelList)
      this.SET_CHANNEL_OBJ(channelObj)
      this.SET_CAPABILITY_OBJ(capabilityObj)
    },
    // 根据当前分组的通道列表去构建站点、设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const siteChannelList = []
      const siteIndexObj = {} // 记录站点Id及其在通道树中的索引
      const deviceIndexObj = {} // 记录站点Id——设备sn及其在通道树中的索引
      const capabilityObj = {} // 记录设备sn-通道chlIndex及其对应的能力集
      allChannelList.forEach(item => {
        const { siteName, siteId, sn, devName, chlIndex, capability } = item
        let temp = null
        // 判断站点
        if (siteIndexObj[siteId] !== undefined) {
          // 说明站点存在
          const index = siteIndexObj[siteId]
          temp = siteChannelList[index]
          temp.children = siteChannelList[index].children || []
          // 判断设备是否存在
          if (deviceIndexObj[`${siteId}~${sn}`] !== undefined) {
            // 说明设备存在
            const index2 = deviceIndexObj[`${siteId}~${sn}`]
            // 添加通道
            temp.children[index2].children.push({ ...item })
          } else {
            // 说明设备不存在
            const temp3 = { sn, devName, children: [{ ...item }] }
            deviceIndexObj[`${siteId}~${sn}`] = temp.children.length // 记录下设备的索引
            temp.children.push(temp3)
          }
        } else {
          // 说明站点不存在，直接添加
          temp = { siteName, siteId, children: [] }
          siteIndexObj[siteId] = siteChannelList.length // 记录下站点的索引
          // 继续添加设备和通道
          deviceIndexObj[`${siteId}~${sn}`] = temp.children.length
          temp.children.push({ sn, devName, children: [{ ...item }] })
          siteChannelList.push(temp)
        }
        // 找到能力集
        if (capability) {
          const { supportFun = [] } = JSON.parse(capability)
          capabilityObj[`${sn}~${chlIndex}`] = supportFun.slice()
        }
      })
      return siteChannelList
    },
    // 查询当前分组下面的通道
    async getDefenseDetails(id) {
      const res = await getDefenseDetail({ ids: [id] })
      const resData = res.data || []
      const channelList = []
      resData.forEach(item => {
        const { chlIndex, sn, extra } = item
        // 从channelList中找到对应的记录
        const channelItem = this.allChannelList.find(item2 => item2.chlIndex === chlIndex && item2.sn === sn)
        if (channelItem) {
          const { chlSn, devName, siteId, siteName, snPlain, version } = channelItem
          const temp = {
            ...item,
            chlSn,
            devName,
            siteId,
            siteName,
            snPlain,
            version,
            extra: extra ? JSON.parse(extra) : null
          }
          channelList.push(temp)
        }
      })
      this.SET_DEFENSE_RECORD({
        ...this.defenseRecord,
        channelList
      })
    },
    // 设备勾选
    handleDevice(checked, key) {
      // 如果设备下的checkboxGroup已经展开并渲染了，直接调用方法
      if (this.$refs[`${key}~checkboxGroup`] && this.$refs[`${key}~checkboxGroup`][0]) {
        this.$refs[`${key}~checkboxGroup`][0].toggleAll(checked)
      }
    },
    // 设备勾选
    toggleDevice(e, key, deviceRecord) {
      // 如果设备下的checkboxGroup已经展开并渲染了，则阻止默认事件
      if (this.$refs[`${key}~checkboxGroup`] && this.$refs[`${key}~checkboxGroup`][0]) {
        e.stopPropagation()
      } else {
        // 否则往上传递
      }
      const flag = this.deviceChecked[key]
      // console.log('flag', flag, 'key', key)
      if (!flag) {
        // 表示是勾选
        // 判断当前设备是否与之前勾选的通道属于同一设备，不同设备下不允许勾选
        const siteDeviceArr = Array.from(this.channelCheckedSet).map(item => {
          const list = item.split('~')
          return `${list[0]}~${list[1]}`
        })
        const siteDeviceList = Array.from(new Set(siteDeviceArr))
        if (siteDeviceList.length > 1 || (siteDeviceList.length === 1 && siteDeviceList[0] !== key)) {
          this.deviceChecked = {
            ...this.deviceChecked,
            [key]: false
          }
          this.$refs[`${key}~device`][0].toggle(false)
          this.$toast(this.$t('onlySameDevice'))
          return
        }
      }
      const { children = [] } = deviceRecord
      const channelCheckedSet = new Set(Array.from(this.channelCheckedSet))
      // 判断是否全选
      if (this.deviceChecked[key]) {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: false
        }
        // 直接清空勾选
        this.result = {
          ...this.result,
          [key]: []
        }
        children.forEach(item => channelCheckedSet.delete(`${item.siteId}~${item.sn}~${item.chlIndex}`))
      } else {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: true
        }
        // 直接把所有都勾选上
        this.result = {
          ...this.result,
          [key]: children.map(item => `${item.siteId}~${item.sn}~${item.chlIndex}`)
        }
        children.forEach(item => channelCheckedSet.add(`${item.siteId}~${item.sn}~${item.chlIndex}`))
      }
      this.channelCheckedSet = channelCheckedSet
    },
    // 通道勾选
    toggle(e, refKey, deviceRecord, channelRecord) {
      // 阻止冒泡事件
      e.stopPropagation()
      // console.log('refKey', refKey, 'this.$refs', this.$refs, 'this.$refs[refKey]', this.$refs[refKey])
      const { children = [] } = deviceRecord
      const { siteId, sn, chlIndex } = channelRecord
      const checkSet = new Set(this.result[`${siteId}~${sn}`])
      const channelCheckedSet = new Set(Array.from(this.channelCheckedSet))
      if (checkSet.has(`${siteId}~${sn}~${chlIndex}`)) {
        this.$refs[refKey][0].toggle(false)
        checkSet.delete(`${siteId}~${sn}~${chlIndex}`)
        channelCheckedSet.delete(`${siteId}~${sn}~${chlIndex}`)
      } else {
        // 表示是勾选
        // 判断当前设备是否与之前勾选的通道属于同一设备，不同设备下不允许勾选
        const siteDeviceArr = Array.from(this.channelCheckedSet).map(item => {
          const list = item.split('~')
          return `${list[0]}~${list[1]}`
        })
        const siteDeviceList = Array.from(new Set(siteDeviceArr))
        if (siteDeviceList.length > 1 || (siteDeviceList.length === 1 && siteDeviceList[0] !== `${siteId}~${sn}`)) {
          this.$refs[refKey][0].toggle(false)
          this.$toast(this.$t('onlySameDevice'))
          return
        }
        this.$refs[refKey][0].toggle(true)
        checkSet.add(`${siteId}~${sn}~${chlIndex}`)
        channelCheckedSet.add(`${siteId}~${sn}~${chlIndex}`)
      }
      this.channelCheckedSet = channelCheckedSet
      this.$nextTick(() => {
        // console.log('result', JSON.stringify(this.result), `${siteId}~${sn}`, this.result[`${siteId}~${sn}`])
        // 判断是否全部勾选了
        if (children.every(item => checkSet.has(`${item.siteId}~${item.sn}~${item.chlIndex}`))) {
          // 全部在结果里面，则勾选上设备
          this.deviceChecked = {
            ...this.deviceChecked,
            [`${siteId}~${sn}`]: true
          }
        } else {
          // 去除设备勾选
          this.deviceChecked = {
            ...this.deviceChecked,
            [`${siteId}~${sn}`]: false
          }
        }
      })
    },
    async handleConfirm() {
      // console.log('result', this.result)
      const channelKeyList = Object.values(this.result).reduce((pre, next) => {
        pre = pre.concat(next)
        return pre
      }, [])
      if (channelKeyList.length === 0) {
        this.$toast(this.$t('pleaseChooseChannel'))
        return
      }
      // 把添加的设备传入store
      const addChannelList = channelKeyList.map(key => this.channelObj[key])
      // 简化存储的信息
      const simpleChannelList = addChannelList.map(item => {
        const { chlIndex, chlName, chlSn, devName, siteId, siteName, sn, snPlain, version, capability } = item
        // BypassSwitch旁路开关，默认0关闭 linkageList 联动项
        let linkageList = []
        // 找到能力集
        if (capability) {
          const { supportFun = [] } = JSON.parse(capability)
          // 根据能力集找到可以支持联动项
          linkageList = this.ipcLinkageList.filter(item => supportFun.includes(item.value)).map(item2 => item2.value)
        }
        return {
          chlIndex,
          sn,
          chlName,
          chlSn,
          devName,
          siteId,
          siteName,
          snPlain,
          version,
          extra: { bypassSwitch: 0, linkageList }
        }
      })
      const { id, channelList } = this.defenseRecord
      const deviceSet = new Set(channelList.map(item => `${item.sn}~${item.chlIndex}`))
      // 过滤掉重复的
      const filterChannelList = simpleChannelList.filter(item => !deviceSet.has(`${item.sn}~${item.chlIndex}`))
      if (filterChannelList.length) {
        const newChannelList = [...channelList, ...filterChannelList]
        // 判断是否有不同设备下的通道
        const snSet = new Set(newChannelList.map(item => item.sn))
        if (snSet.size > 1) {
          this.$toast(this.$t('onlySameDevice'))
          return
        }
        if (this.type === 'edit') {
          // 编辑状态下的添加直接发送请求
          const params = {
            ...this.defenseRecord,
            details: newChannelList.map(item => {
              const { extra } = item
              return {
                ...item,
                extra: JSON.stringify(extra)
              }
            })
          }
          delete params.channelList
          this.$loading.show()
          try {
            const curDefenseRecord = {
              ...this.defenseRecord,
              channelList: newChannelList
            }
            const that = this
            const callback = async msg => {
              if (msg === 'SUCCESS') {
                await addDefenseGroup(params)
                // 更新布撤防组状态
                that.SET_DEFENSE_RECORD(curDefenseRecord)
                await that.getDefenseDetails(id)
                that.$router.go(-1)
              } else {
                that.$toast(this.$t('editFail'))
              }
            }
            // 发送协议到对应的设备
            this.updateDeviceStatus(curDefenseRecord, callback)
          } catch (err) {
            console.error(err)
          } finally {
            this.$loading.hide()
          }
        } else {
          this.SET_DEFENSE_RECORD({
            ...this.defenseRecord,
            channelList: newChannelList
          })
          this.$router.go(-1)
        }
      } else {
        this.$router.go(-1)
      }
    },
    // 更新设备布撤防状态
    async updateDeviceStatus(item, callback) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 在家布防 2 外部布防
      const { status, channelList } = item
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }
      // 跟设备交互
      const { sn } = channelList[0]
      const req = {
        devId: sn,
        url: 'editNodeDefenseStatus',
        params: urlDefenseSwitchNodes(status, channelList)
      }
      // console.log('请求参数', req)
      appRequestDevice(req, function (res) {
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        // const errorCode = resData.code
        // console.log('返回结果', res)
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (xmlObject.response.status == 'success') {
            // 处理结果
            if (callback) callback('SUCCESS')
          } else {
            if (callback) callback('ERROR')
          }
        } else {
          if (callback) callback('ERROR')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-wrapper {
  height: 100%;
  overflow: hidden;
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
  // .van-hairline-unset--top-bottom::after {
  //   border-width: 0px;
  // }
  .van-cell {
    padding: 5px 12px;
  }
  .channel-content {
    height: calc(100% - 120px);
    overflow: auto;
    margin: 10px 0px;
    box-sizing: border-box;
  }
  .channel-list {
  }
  .channel-item {
    height: 40px;
    padding: 0px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:last-child {
      border: 0;
    }
    .check-img {
      width: 20px;
      height: 20px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .footer-btn {
    width: 343px;
    height: 46px;
    border-radius: 10px;
  }
}
</style>
<style lang="scss">
.van-collapse-item__content {
  padding: 2px 8px !important;
}
.van-cell {
  .van-cell__title {
    padding-left: 10px;
  }
  .van-cell__right-icon {
    left: calc(-100% + 10px);
  }
}
.van-cell__value {
  right: -15px;
  width: min-content;
  display: flex;
  justify-content: flex-end;
  .van-checkbox {
    justify-content: flex-end;
  }
}
</style>
