// 西班牙语(es)  客户提供了翻译就用客户的，没有提供的用英文
export default {
  upgrade: 'Cloud Upgrade',
  cancel: 'Cancelar',
  confirm: 'Confirmar',
  deviceUpdate: 'Dispositivo',
  cameraUpdate: 'Cámara',
  updateNow: 'Actualizar',
  currentVersion: 'Versión actual',
  latestVersion: 'La última versión',
  updateContent: 'Actualizar contenido',
  hasLatestVersion: 'Ya está la última versión',
  online: 'En línea',
  offline: 'Sin conexión',
  waitDownload: 'Esperando la descarga',
  inprogress: 'Descarga',
  downloadFail: 'Error en descarga',
  downloadFinished: 'Descarga completa',
  inupgrade: 'Actualización',
  upgradeFail: 'Actualización fallida',
  upgradeSuccess: '¡Actualice con éxito!',
  deviceUpgradeInfo:
    'Durante la actualización, el dispositivo se desconectará y se reiniciará automáticamente. ¿Estás seguro de actualizar?',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo:
    'Durante la actualización, la cámara se desconectará y se reiniciará automáticamente. ¿Estás seguro de actualizar?',
  pwdUserNameError: 'Error de nombre de usuario o contraseña',
  permissionAuth: 'Autenticación de la autoridad de super administrador',
  pleaseEnterUser: 'Por favor ingrese el nombre de usuario',
  pleaseEnterPwd: 'Introducir la contraseña',
  noCameraUpgrade: 'No se detectó una cámara actualizable',
  handleCheck: 'Buscar actualizaciones',
  done: 'Terminar',
  cloudStorage: 'Cloud storage',
  delete: 'Eliminar',
  operationSuccess: 'Acción exitosa',
  operationFail: 'Acción sin éxito',
  chooseDevice: 'Seleccione el dispositivo',
  details: 'Detalle',
  live: 'En vivo',
  rec: 'Reproducir',
  config: 'Configurar',
  unBind: 'Desvincular',
  checkSuccess: 'Comprobado con éxito',
  checkFail: 'Verificación fallida',
  viewUpdateContent: 'Ver contenido de actualización',
  deviceDisconnected: 'No se pudo conectar al dispositivo',
  updateNote: 'Nota de actualización',
  noData: 'Sin datos',
  tips: 'Consejos',
  password: 'Contraseña',
  pwdError: 'Error de contraseña. Puedes intentarlo {0} veces más',
  pwdErrorLock: 'El dispositivo está bloqueado debido a errores repetitivos. Por favor, inténtelo de nuevo más tarde',
  noPermissions: 'no permisos',
  permission: 'Permiso',
  validity: 'Validez',
  permissionValidity: 'Validez del permiso',
  isSaveModify: 'Confirmar guardando los cambios',
  manyMinutes: '{0}minuto',
  manyHours: '{0}h',
  manyDays: '{0}d',
  manyMinutesEn: '{0}minuto', // 缺少翻译
  manyHoursEn: '{0}h', // 缺少翻译
  manyDaysEn: '{0}d', // 缺少翻译
  oneWeek: '1 semana',
  forever: 'Para siempre',
  expired: 'Venció',
  residue: 'Residuo',
  bindSuccess: 'Vinculante exitoso',
  from: 'Desde',
  lineAuthBind: 'Line authorization binding',
  bindFail: 'Line binding failed',
  changeSuccessfully: 'Modificado con éxito',
  email: 'Correo electrónico',
  enterMemberEmail: 'Ingrese correo electrónico',
  mobile: 'Número de teléfono',
  enterMemberMobile: 'Introduzca el número de teléfono',
  emailNameError: 'Error del formato de email',
  emailNameNotEmpty: 'La dirección de e-mail no puede estar vacía!',
  allUpdate: 'Upgrade All',

  paySuccess: 'Payment succeeded',
  payFail: 'Payment failed',
  rePurchase: 'Repurchase',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  myInstaller: 'My installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Share with installer',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  cancelTrusteeship: 'Cancel sharing with installer',
  noAvaiableDevice: 'Showing devices supporting installer sharing and bound to your account.',
  leastChoose: 'Select at least one device',
  confirmTrusteeshipTip:
    'The sharing request has been sent to the installer. Please wait for the installer to process it',
  cancelTrusteeshipTip:
    'After canceling sharing the installer is unable to provide you with remote maintenance services. Please confirm',
  unBindTrusteeship: 'After unbinding all device sharing will be cancelled. Please confirm',
  trusteeshipPermissions: 'Sharing permissions',
  trusteeshipTime: 'Share time',
  serviceException: 'Service exception',
  pullingText: 'Pull down to Loading...',
  loosingText: 'Free to refresh...',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more',
  errorCode: {
    400: 'error de parametro',

    503: 'Excepción del servidor',
    1000: 'error de parametro',
    1005: 'Error de código de verificación de imagen',
    1007: 'Código de verificación de la imagen requerido',
    1009: 'Error de código de verificación',
    1012: 'API desconocida',
    1015: 'El usuario ya existe',
    4500: 'error de parametro',
    5000: 'Usted no tiene permisos para realizar esta operación',
    6001: 'Operación frecuente',
    7000: 'error de parametro',
    7001: 'el usuario no existe',
    7002: 'La contraseña anterior es incorrecta',
    7003: 'simbolo no valido',
    7006: 'El número de teléfono móvil ya existe',
    7010: 'Cuenta de administración no activada',
    7019: 'Nombre de usuario existente',
    7023: 'El correo electrónico ha sido unido',
    7081: 'Fallo al importar el archivo de configuración.',
    7082: 'Fallo al exportar',
    9004: 'Falló la operación de la base de datos',
    10000: 'Failed to connect device',
    12344: 'Error en conexión de red',
    12345: 'Tiempo de espera de conexión de red',
    32019: 'Acción sin éxito',
    34004: 'Autorización fallida',
    536870943: 'Parametro invalido',
    536870947: 'Nombre de usuario no existe.',
    536870948: 'Error del nombre del usuario o contraseña',
    536871039: 'Parametro invalido',
    536871060: 'No se puede conectar al dispositivo, verifique el estado del dispositivo',

    550: 'Request timeout', //app返回的超时
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    404: 'The requested resource (web page, etc.) does not exist',
    500: 'System exception',
    502: 'Server request failed',
    504: 'Server request timeout',
    32018: 'The data doesnt exist.',
    32022: 'Hosting service is not supported because {0} device and installer are not in the same country/region.',
    536870945: 'Operation failure, please check the device status', //'Device busy, please try again later',
    536871017: 'Operation failure, please check the device status', // 版本不匹配
    536871082: 'Operation failure, please check the device status', //'无新版本',
    536871083: 'Operation failure, please check the device status', //'云升级版本不存在'
    536870940: 'Operation failure, please check the device status', //云升级未开启
    *********: 'Operation failure, please check the device status',
    1008: 'The verification code has expired',
    1011: 'The parameter is not filled in correctly!',
    1013: 'The verification code failed to send',
    1027: 'Please enter the correct device serial number/security code',
    1028: 'The camera is enabled or disabled',
    5001: 'The current user has no permission',
    6000: 'The current business status does not support this operation',
    7004: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7005: 'Invalid signature',
    7007: 'The user is locked, please contact the administrator to unlock',
    7009: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7011: 'Account not activated',
    7021: 'Deletion failed! Please clear all hosts under this host group first',
    7028: 'The template has been used in the project and cannot be deleted!',
    7029: 'The template name already exists!',
    7030: 'The data already exists!',
    7032: 'The firmware package already exists!',
    7034: 'The firmware package has been released and cannot be deleted!',
    7042: 'There are other tasks in the startup state',
    7043: 'The task has not been approved!',
    7044: 'Operation failed. There are no devices eligible for upgrade!',
    7045: 'The task is not approved!',
    7056: 'This version has been included in the supporting compatibility management and cannot be deleted!',
    7057: 'Issuing document cannot be blank!',
    7061: 'Correction failed, cannot create correction again!',
    7066: 'The customer code already exists!',
    7068: 'The customer code does not exist!',
    7069: 'Too much data, please narrow the scope and search again!',
    7084: 'The customer country code already exists',
    7086: 'The operation is refused due to system exception',
    7087: 'The product already exists!',
    7088: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7090: 'Hello, your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7093: 'Image and text information is not configured!',
    7094: 'The service terms information does not exist!',
    9000: 'System exception!',
    9001: 'The protocol version is too low. The old version is no longer compatible and needs to be upgraded',
    9002: 'Protocol version error, unrecognized version field or error message',
    9003: 'Failed to send verification code',
    9005: 'The data does not exist',
    9006: 'The data already exists',
    9007: 'The data to be viewed does not exist',
    9008: 'The data does not exist',
    9009: 'Data exception',
    9500: 'System exception!',
    10001: 'System exception!',
    20021: 'This email has been used',
    20024: 'The account has been activated',
    20030: 'The link has expired',
    33001: 'No permission to operate this device',
    33002: 'No permission to operate this site',
    33003: 'The site does not exist',
    33004: 'The length of the device name must be between 0 and 32',
    33010: 'The device already exists',
    7072: 'The device already exists',
    // 托管错误码
    32021: 'Data does not exist',
    // ipc 云升级错误码
    ipc: {
      499: 'Error desconocido.',
      612: 'Operation failure, please check the device status',
      730: 'Operation failure, please check the device status', //检查新版本信息时，无新版本信息
      731: 'Operation failure, please check the device status', //云升级功能未使能  升级guid错误
      732: 'Operation failure, please check the device status', //升级任务已存在
      735: 'Operation failure, please check the device status'
    }
  }
}
