<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="isAdd ? $t('addBuilding') : $t('buildingDetail')" @clickLeft="back"></nav-bar>
    <tvt-better-scroll
      class="tvt-better-scroll"
      @scroll="onScroll"
      @pullingDown="pullingDown"
      :pullingStatus="pullingStatus"
    >
      <van-cell class="household-item" name="name" is-link @click="editName">
        <template #title> <span class="required-icon">*</span>{{ $t('buildingName') }} </template>

        <template #default>
          <span class="right-value text-over-ellipsis">{{ buildingName }}</span>
        </template>
      </van-cell>
      <van-cell class="household-item" name="num" is-link @click="editNum">
        <template #title> <span class="required-icon">*</span>{{ $t('buildingNum') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ buildingNo }}</span>
        </template>
      </van-cell>
      <van-cell class="household-item" name="device" is-link @click="editSevice">
        <template #title>
          {{ $t('relateDevice') }}
        </template>

        <template #default>
          <span class="right-value text-over-ellipsis">{{ deviceListInfo.join('、') }}</span>
        </template>
      </van-cell>

      <div v-if="!isAdd" class="room-container">
        <div class="room-header">
          <span class="room-title">
            {{ $t('room') }}
          </span>
          <span class="room-add" @click="addRoom">
            <van-icon name="plus" />
          </span>
        </div>
        <div class="room-list" :style="roomListStyle" v-if="needRequest || roomList.length">
          <van-swipe-cell v-for="item in visibleList" :key="item.roomId">
            <van-cell class="room-item" :name="item.roomId" is-link @click="editRoom(item)">
              <template #title>
                <span class="room-name text-over-ellipsis">{{ item.roomNo }}</span>
              </template>

              <template #default>
                <span class="room-member-name text-over-ellipsis">{{
                  item.members.map(member => formatPhoneNumber(member.memberName)).join('、')
                }}</span>
              </template>
            </van-cell>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="deleteRoom(item)">
                <theme-image class="refuse-img" alt="delete" imageName="refuse.png" />
              </van-button>
            </template>
          </van-swipe-cell>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <theme-image :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''" alt="noData" imageName="no_data.png" />
          </div>
          <div class="no-data-text">
            <van-button class="add-btn" type="primary" @click="addRoom">
              {{ $t('add') }}
            </van-button>
          </div>
        </div>
      </div>
    </tvt-better-scroll>

    <div class="footer">
      <van-button class="footer-btn" type="primary" :loading="addLoading" @click="handleClick">
        {{ isAdd ? $t('confirm') : $t('deleteBuilding') }}
      </van-button>
    </div>
    <edit-building-name ref="editBuildingName" :value="buildingName" @confirm="confirmName"></edit-building-name>
    <edit-building-num ref="editBuildingNum" :value="buildingNo" @confirm="confirmNum"></edit-building-num>
    <edit-room-no ref="editRoomNo" :value="roomNo" @confirm="confirmNo"></edit-room-no>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import EditBuildingName from './dialog/EditBuildingName.vue'
import EditBuildingNum from './dialog/EditBuildingNum.vue'
import EditRoomNo from './dialog/EditRoomNo.vue'
import { formatPhoneNumber } from '@/utils/common.js'

import {
  queryBuildingDetail,
  queryBuildingRooms,
  queryBuildingDevices,
  delBuildingRoom,
  eidtBuilding,
  deleteBuilding,
  createBuilding,
  updateBuildingDevices,
  addBuildingRoom
} from '@/api/householdManagement.js'
import { mapState, mapMutations } from 'vuex'

const keeps = 30
const buffers = Math.round(keeps / 3)
const itemHeight = 44

export default {
  name: 'addBuilding',
  components: {
    NavBar,
    ThemeImage,
    EditBuildingName,
    EditBuildingNum,
    EditRoomNo
  },
  props: {},
  data() {
    return {
      roomList: [],
      isAdd: true,
      pullingStatus: 0,
      buildingId: '',
      needRequest: true,
      range: {
        start: 0,
        end: keeps
      },
      buildingName: '',
      buildingNo: '',
      addLoading: false,
      roomNo: ''
    }
  },
  mounted() {
    if (this.$route.query.id) {
      this.isAdd = false
      this.buildingId = this.$route.query.id
      this.queryDetail()
    } else {
      const { buildingName, buildingNo } = this.buildingInfo
      this.buildingName = buildingName
      this.buildingNo = buildingNo
      this.isAdd = true
      this.buildingId = ''
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['deviceList', 'buildingInfo']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    deviceListInfo() {
      // 用来显示简介， 无需太多
      // 0 未关联 1 已关联

      return this.deviceList
        .filter(item => item.status === 1)
        .slice(0, 4)
        .map(item => item.devName)
    },
    visibleList() {
      return this.roomList.slice(this.range.start, this.range.end)
    },
    roomListStyle() {
      const paddingTop = this.range.start * itemHeight
      const paddingBottom = (this.roomList.length - this.range.end) * itemHeight

      return { padding: `${paddingTop}px 0px ${paddingBottom}px 0px` }
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['INIT_ROOM', 'SET_DEVICE_LIST', 'SET_BUILDING_INFO', 'SET_ROOM_INFO']),
    back() {
      this.SET_BUILDING_INFO({})
      this.$router.push('/household')
    },
    editName() {
      this.$refs.editBuildingName.show = true
    },
    editNum() {
      this.$refs.editBuildingNum.show = true
    },
    editSevice() {
      this.SET_BUILDING_INFO({
        ...this.buildingInfo,
        buildingName: this.buildingName,
        buildingNo: this.buildingNo
      })
      this.$router.push({
        path: '/household/chooseDevice',
        query: {
          buildingId: this.buildingId
        }
      })
    },
    onScroll({ y }) {
      const index = Math.round(-y / itemHeight)

      this.range.start = Math.max(index - buffers, 0)
      this.range.end = Math.min(index + keeps, this.roomList.length)
    },
    async handleClick() {
      if (this.isAdd) {
        // 新增
        try {
          if (!this.buildingName) {
            this.$toast(this.$t('enterBuildingName'))
            return false
          }

          if (!this.buildingNo) {
            this.$toast(this.$t('enterBuildingNum'))
            return false
          }
          this.addLoading = true
          // 先创建楼栋, 然后更新设备
          const {
            data: { id }
          } = await createBuilding({
            buildingName: this.buildingName,
            buildingNo: this.buildingNo
          })

          await updateBuildingDevices({
            id,
            snList: this.deviceList.filter(device => device.status === 1).map(device => device.sn)
          })

          this.back()
          this.$toast(this.$t('addSuccess'))
        } catch (error) {
          console.error(error)
        } finally {
          this.addLoading = false
        }
      } else {
        // 删除楼栋
        const tips = {
          message: this.$t('deleteConfirm'),
          cancelButtonText: this.$t('cancel'),
          confirmButtonText: this.$t('confirm')
        }

        try {
          await this.$dialog.confirm(tips)
          this.$loading.show()

          await deleteBuilding({
            id: this.buildingId
          })

          this.$router.push('/household/management')

          this.$toast(this.$t('deleteSuccess'))
        } catch (error) {
          console.error(error)
        } finally {
          this.$loading.hide()
        }
      }
    },
    addRoom() {
      // 新增房间直接弹窗输入房号,不需要打开addRoom页面
      this.$refs.editRoomNo.show = true
    },
    async queryRoomList() {
      // 获取房间列表
      const {
        data: { records }
      } = await queryBuildingRooms({ id: this.buildingId, pageSize: 1000 })
      this.roomList = records || []
    },
    async confirmNo(value) {
      try {
        this.$loading.show()
        this.addLoading = true

        if (!value) {
          this.$toast(this.$t('enterRoomName'))
          return
        }
        await addBuildingRoom({
          id: this.buildingId,
          roomNo: value
        })
        this.$refs.editRoomNo.show = false
        this.queryRoomList()
        this.$toast(this.$t('addSuccess'))
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.addLoading = false
      }
    },
    editRoom(room) {
      // 存储当前编辑的房间信息
      this.SET_ROOM_INFO({ ...room })
      this.$router.push({
        path: '/household/addRoom',
        query: {
          buildingId: this.buildingId,
          roomId: room.roomId
        }
      })
    },
    async deleteRoom(room) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()

        await delBuildingRoom({
          id: room.roomId
        })
        // 只有编辑楼栋有房间列表
        // 所以无需考虑id不存在
        this.queryRoomList()

        this.$toast(this.$t('deleteSuccess'))
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    },
    async confirmName(value) {
      this.$refs.editBuildingName.show = false

      if (!this.isAdd) {
        // 立即保存
        try {
          await eidtBuilding({
            id: this.buildingId,
            buildingName: value
          })
          this.$toast(this.$t('changeSuccessfully'))
          this.buildingName = value
          this.SET_BUILDING_INFO({ ...this.buildingInfo, buildingName: value })
        } catch (error) {
          console.error(error)
        }
      } else {
        this.buildingName = value
      }
    },
    async confirmNum(value) {
      this.$refs.editBuildingNum.show = false

      if (!this.isAdd) {
        // 立即保存
        try {
          await eidtBuilding({
            id: this.buildingId,
            buildingNo: value
          })
          this.buildingNo = value
          this.$toast(this.$t('changeSuccessfully'))
          this.SET_BUILDING_INFO({ ...this.buildingInfo, buildingNo: value })
        } catch (error) {
          console.error(error)
        }
      } else {
        this.buildingNo = value
      }
    },

    async pullingDown(callback) {
      if (this.isAdd) {
        callback && callback()
        return
      }
      await this.queryDetail()
      callback && callback()
    },
    async queryDetail() {
      try {
        this.$loading.show()
        await Promise.all([this.queryBuildingDetail(), this.queryBuildingRooms(), this.queryBuildingDevices()])
        this.needRequest = false
      } catch (error) {
        console.error(error)
        this.needRequest = true
      } finally {
        this.$loading.hide()
      }
    },
    async queryBuildingRooms() {
      if (!this.isAdd) {
        const { data } = await queryBuildingRooms({ id: this.buildingId, pageSize: 1000 })
        this.roomList = data.records || []
      }
    },
    async queryBuildingDevices() {
      const { data } = await queryBuildingDevices({ id: this.buildingId, pageNum: 1, pageSize: 1000 })
      this.SET_DEVICE_LIST(data.records || [])
    },
    async queryBuildingDetail() {
      const { data } = await queryBuildingDetail({ id: this.buildingId })

      this.SET_BUILDING_INFO({ ...data })
      this.buildingName = data.buildingName
      this.buildingNo = data.buildingNo
    },
    formatPhoneNumber
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 90px;
  box-sizing: border-box;

  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;

    .household-item {
      padding-top: 12px;
      padding-bottom: 12px;
      ::v-deep .van-cell__title {
        height: 24px;
      }

      ::v-deep .van-cell__value {
        height: 24px;
      }
      &::after {
        border: none;
      }

      .right-value {
        display: inline-block;
        width: 100%;
      }
    }

    .room-container {
      padding: 10px 0;

      .room-header {
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        padding: 10px 6px 10px 16px;
      }

      .room-add {
        padding: 0 10px;
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }
  .refuse-img {
    width: 24px;
    height: 24px;
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .add-btn {
        padding: 0 35px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
