<template>
  <div class="binded-card-wrapper">
    <van-swipe
      v-if="list.length > 1"
      class="my-swipe"
      :autoplay="0"
      :loop="false"
      width="330"
      indicator-color="white"
      @change="onChange"
    >
      <van-swipe-item v-for="data in list" :key="data.user.id">
        <div class="card multiple">
          <installer-info :data="formatInstallerInfo(data.user)" />
          <div class="card-bottom" @click="viewDetail(data.user.id)">
            <span class="device-num">
              {{ data.quantity == 0 ? $t('trustDeviceNone') : $t('trustDeviceCount', [data.quantity]) }}
            </span>
            <span class="view-detail">{{ $t('viewDetail') }}</span>
          </div>
        </div>
      </van-swipe-item>
      <template #indicator>
        <div class="custom-indicator">
          <div
            class="indicator-item"
            :class="{ active: current === index - 1 }"
            v-for="index in list.length"
            :key="index"
          ></div>
        </div>
      </template>
    </van-swipe>
    <div v-else-if="list.length === 1" class="card-container">
      <div class="card single">
        <installer-info :data="formatInstallerInfo(list[0].user)" />
        <div class="card-bottom" @click="viewDetail(list[0].user.id)">
          <span class="device-num">
            {{ list[0].quantity == 0 ? $t('trustDeviceNone') : $t('trustDeviceCount', [list[0].quantity]) }}
          </span>
          <span class="view-detail">{{ $t('viewDetail') }}</span>
        </div>
      </div>
    </div>

    <van-button class="footer-btn" :class="{ multiple: list.length > 1 }" type="primary" @click="handleClick">
      {{ $t('immediateTrusteeship') }}
    </van-button>
  </div>
</template>

<script>
import InstallerInfo from './InstallerInfo.vue'
import { openH5 } from '@/utils/appbridge'
import { formatInstallerInfo } from '../common'

export default {
  name: 'BindedCard',
  components: {
    InstallerInfo
  },
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultAvatar: require('@/assets/img/common/trusteeship/avatar.png'),
      current: 0
    }
  },
  mounted() {},
  methods: {
    handleClick() {
      // 进入添加安装商
      openH5({
        url: '/max/hosting/installer/add'
      })
    },
    formatInstallerInfo,
    viewDetail(id) {
      openH5({
        url: `/max/hosting/service-detail/${id}`
      })
    },
    onChange(index) {
      this.current = index
    }
  }
}
</script>

<style lang="scss" scoped>
.binded-card-wrapper {
  padding-top: 10px;
  text-align: center;
  width: 100%;

  .my-swipe {
    ::v-deep .van-swipe-item {
      &:first-child .card {
        margin-left: calc(100% / 375px * 8px);
      }
      &:last-child .card {
        margin-right: calc(100% / 375px * 8px);
      }
    }
  }
  .custom-indicator {
    display: flex;
    justify-content: center;
    margin: 10px 0;
    .indicator-item {
      width: 6px;
      height: 6px;
      background: var(--text-color-disabled, #d1d1d1);
      opacity: 0.5;
      border-radius: 6px;
      margin-right: 6px;
      &.active {
        width: 12px;
        height: 6px;
        background: #70a0fd;
        opacity: 1;
      }
    }
  }

  .card {
    background-color: var(--bg-color-white, #ffffff);
    flex-shrink: 0;
    border-radius: 12px;
    box-shadow: 0 2px 4px -1px #0000000f, 0 4px 5px 0 #0000000a;
    &.multiple {
      margin: 0 8px;
    }
    &.single {
      width: calc(100% - 16px);
      max-width: 359px;
      margin: auto;
    }
    .card-bottom {
      box-sizing: border-box;
      padding: 14px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .device-num {
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .view-detail {
      color: var(--text-color-primary, #82879b);
      text-align: right;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
  }
  .footer-btn {
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
    margin-top: 26px;
    &.multiple {
      margin-top: 0;
    }
  }
}
</style>
