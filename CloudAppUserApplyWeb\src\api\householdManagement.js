import request from '@/api/request'
// import baseUrl from '@/api/index.js'
const baseUrl = '/mobile_v1.0'

// 住户管理
// superLivePlus

// 查看楼栋列表
export const queryBuildingList = data => request.post(`${baseUrl}/user/building/list`, data)

// 查看楼栋详情
export const queryBuildingDetail = data => request.post(`${baseUrl}/user/building/detail`, data)

// 查看楼栋房间列表
export const queryBuildingRooms = data => request.post(`${baseUrl}/user/building/room/list`, data)

// 创建楼栋信息
export const createBuilding = data => request.post(`${baseUrl}/user/building/create`, data)

// 新增楼栋关联设备
export const addBuildingDevice = data => request.post(`${baseUrl}/user/building/device/create`, data)

// 删除楼栋关联设备
export const delBuildingDevice = data => request.post(`${baseUrl}/user/building/device/delete`, data)

// 编辑楼栋房间
export const editBuildingRoom = data => request.post(`${baseUrl}/user/building/room/update`, data)

// 删除楼栋房间
export const delBuildingRoom = data => request.post(`${baseUrl}/user/building/room/delete`, data)

// 创建楼栋房间
export const addBuildingRoom = data => request.post(`${baseUrl}/user/building/room/create`, data)

// 编辑楼栋
export const eidtBuilding = data => request.post(`${baseUrl}/user/building/update`, data)

// 删除楼栋
export const deleteBuilding = data => request.post(`${baseUrl}/user/building/delete`, data)

// 查询房间住户列表
export const queryRoomMembers = data => request.post(`${baseUrl}/user/building/member/list`, data)

// 房间删除住户
export const deleteRoomMember = data => request.post(`${baseUrl}/user/building/member/delete`, data)

// 房间增加住户
export const addRoomMembers = data => request.post(`${baseUrl}/user/building/member/create`, data)

// 房间更新住户
export const updateRoomMember = data => request.post(`${baseUrl}/user/building/member/update`, data)

// 查询楼栋设备
export const queryBuildingDevices = data => request.post(`${baseUrl}/user/building/device/available`, data)

// 更新楼栋关联设备
export const updateBuildingDevices = data => request.post(`${baseUrl}/user/building/device/update`, data)

// 上传人脸图片
export const uploadFaceImg = data => request.post(`${baseUrl}/user/building/member-img/upload`, data)

// 获取oss上传凭证接口
export const getOssAccess = data => request.post(`${baseUrl}/user/cloud-storage/access/get`, data)

// 获取人脸地址
export const getFaceImage = data => request.post(`${baseUrl}/user/building/member/face-image/get`, data)

// 批量获取人脸地址
export const getFaceImageBatch = data => request.post(`${baseUrl}/user/building/member/face-image/batch-get`, data)
