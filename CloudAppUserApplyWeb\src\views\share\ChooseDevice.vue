<template>
  <div class="choose-device-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="choose-device-content">
      <template v-if="!isInitReq || deviceChannelList.length">
        <choose-channel v-model="checkChannels" type="add" :deviceChannelList="deviceChannelList" />
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('noShareDevice') }}</div>
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :disabled="btnDisabled" @click="handleNextStep">
        {{ $t('nextStep') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import ChooseChannel from './components/ChooseChannel.vue'
import { CHANNEL_CAPABILITY_LIST } from '@/utils/options'
import { mapState, mapMutations } from 'vuex'
import { getAllDeviceList, getResourceDeviceList, getDeviceDetail } from '@/api/share'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'chooseDevice',
  components: {
    NavBar,
    ChooseChannel,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isInitReq: false, // 是否请求过数据
      checkChannels: [], // 选中的通道
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      channelSupportFunObj: {}, // 通道能力集
      devSupportFunObj: {} // 设备能力集
    }
  },
  async created() {
    // 首次进入清除选中的设备 --后续可能会调整
    this.SET_CHOOSE_CHANNEL_LIST([])
    this.checkChannels = []
    // 请求设备和通道
    const {
      data: { records }
    } = await getAllDeviceList()
    const snList = records.map(item => item.sn)
    await this.getAllChannelCapability(snList)
    const { active, email, mobile } = this.shareUser
    const targetUserName = active === 'email' ? email : mobile
    const params = {
      snList,
      targetUserName
    }
    await this.getShareDeviceList(params)
  },
  mounted() {},
  computed: {
    ...mapState('share', ['shareUser', 'deviceChannelList', 'initChooseChannel']),
    // 按钮是否可点击
    btnDisabled() {
      return this.checkChannels.length === 0
    }
  },
  methods: {
    ...mapMutations('share', [
      'SET_SHARE_USER',
      'SET_ALL_CHANNEL_LIST',
      'SET_DEVICE_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_CHOOSE_CHANNEL_LIST',
      'SET_DEV_SUPPORT_FUN_OBJ'
    ]),
    back() {
      this.$router.go(-1)
    },
    // 获取当前用户的所有设备
    async getDeviceList() {
      const { data } = getAllDeviceList()
      return data
    },
    // 获取可以分享给某用户的设备
    async getShareDeviceList(params) {
      try {
        const { snList } = params
        // snList无数据不用请求了
        if (snList.length === 0) {
          this.isInitReq = true
          return
        }
        const res = await getResourceDeviceList(params)
        this.isInitReq = true
        const { data = [] } = res
        // 请求所有设备通道的能力集--将通道顺序排序
        const channelList = data
          .sort((a, b) => a.chlIndex - b.chlIndex)
          .reduce((pre, next) => {
            const { sn, deviceName, chls = [] } = next
            chls.forEach(({ chlIndex, chlName }) => {
              pre.push({ sn, deviceName, chlIndex, chlName })
            })
            return pre
          }, [])
        // 根据结果构建通道数树
        this.createChannelTree(channelList)
      } catch (err) {
        console.error(err)
      }
    },
    // 获取所有设备的能力集
    async getAllChannelCapability(snList) {
      try {
        // 获取所有通道的详情
        const resArr = await Promise.all(snList.map(sn => getDeviceDetail({ sn, returnChl: true })))
        const capabilityObj = {}
        resArr.forEach(res => {
          const { data } = res
          const { chlInfos = [], devInfo = {} } = data

          this.devSupportFunObj[devInfo.sn] = devInfo?.capability?.supportFun || []
          chlInfos.forEach(chlItem => {
            const { sn, chlIndex, capability } = chlItem
            // // 找到能力集
            const supportFun = capability && capability.supportFun ? capability.supportFun : null
            if (supportFun) {
              capabilityObj[`${sn}~${chlIndex}`] = supportFun
            }
          })
        })
        this.channelSupportFunObj = capabilityObj
        this.SET_CAPABILITY_OBJ(capabilityObj)
        this.SET_DEV_SUPPORT_FUN_OBJ(this.devSupportFunObj)
      } catch (err) {
        console.error(err)
      }
    },
    // 构建设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照站点->设备->通道分类的结构
      const deviceChannelList = []
      const deviceIndexObj = {} // 记录设备sn及其在通道树中的索引
      const channelObj = {} // 记录设备sn-通道chlIndex及其对应的通道信息
      // const capabilityObj = {} // 记录设备sn-通道chlIndex及其对应的能力集
      allChannelList.forEach(item => {
        const { sn, deviceName, chlIndex } = item
        // 找到能力集
        const supportFun = this.channelSupportFunObj[`${sn}~${chlIndex}`] || []
        const capabilityOptions = this.channelCapabilitys.filter(item => {
          if (item.filterAble) {
            const authArr = item.supportAuth ? [item.supportAuth] : item.value.split(',') // 针对现场和回放这种多合一权限的
            return authArr.every(authItem => supportFun.includes(authItem))
          }
          return true
        })
        // 通道能力集选项及勾选的能力集--两者开始默认一样
        item.capabilityOptions = capabilityOptions.slice()
        item.checkCapability = capabilityOptions.slice()
        channelObj[`${sn}~${chlIndex}`] = { ...item }
        let temp = null
        // 判断设备
        if (deviceIndexObj[sn] !== undefined) {
          // 说明设备存在
          const index = deviceIndexObj[sn]
          temp = deviceChannelList[index]
          temp.children = deviceChannelList[index].children || []
          temp.children.push({ ...item })
        } else {
          // 说明设备不存在，直接添加
          temp = { sn, deviceName, children: [{ ...item }] }
          deviceIndexObj[sn] = deviceChannelList.length // 记录下站点的索引
          // 继续添加通道
          deviceChannelList.push(temp)
        }
      })
      // console.log('capabilityObj', capabilityObj)
      this.SET_ALL_CHANNEL_LIST(allChannelList)
      this.SET_DEVICE_CHANNEL_LIST(deviceChannelList)
      this.SET_CHANNEL_OBJ(channelObj)
      // 模拟默认选中第一个
      // this.checkChannels = [allChannelList[0]]
      // 判断是否需要默认勾选通道
      if (this.initChooseChannel.length) {
        const channelKeys = this.initChooseChannel.map(item => `${item.sn}~${item.chlIndex}`)
        const checkChannels = allChannelList.filter(item2 => channelKeys.includes(`${item2.sn}~${item2.chlIndex}`))
        this.checkChannels = checkChannels
      }
    },
    // 下一步
    handleNextStep() {
      // 将当前选中的通道（包括权限）暂存下来
      this.SET_CHOOSE_CHANNEL_LIST(JSON.parse(JSON.stringify(this.checkChannels)))
      this.checkChannels = [] // 重置当前选中通道
      // 进入选择权限
      this.$utils.routerPush({
        path: '/share/changeCapability'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-device-wrapper {
  height: 100%;
  overflow: hidden;
  .choose-device-content {
    width: 100%;
    height: calc(100% - 130px);
    overflow: auto;
    box-sizing: border-box;
    .no-data {
      width: 100%;
      height: calc(100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      .no-data-img {
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 120px;
          height: 123px;
        }
        .theme-image-container {
          width: 120px;
          height: 123px;
        }
      }
      .no-data-text {
        text-align: center;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-top: 20px;
      }
    }
  }
}
</style>
