.choose-share-content, .choose-device-wrapper, .confirm-share-wrapper, .share-success-wrapper, .scan-share-wrapper, .share-managment-wrapper {
    background-color: $max-background;
}

.share-history-box {
    .share-history-title {
        color: $max-light-black
    }
    .share-history-item {
        color: $max-gray;
    }
}
.footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
        width: 327px;
        border-radius: 23px;
        line-height: 40px;
        text-align: center;
    }
}

.channel-collapse-title .channel-capability {
    color: $max-gray;
}
.channel-capability .separator-box {
    background-color: $max-gray;
}

.device-camera-wrapper {
    .van-collapse-item__content {
        background-color: $max-light-white;
    }
    .van-collapse-item__content .van-cell {
        background-color: $max-light-white;
    }
    .van-collapse-item__content .van-cell:not(:last-child) {
        border-bottom: 1px solid $max-border;
    }

    .channel-disabeld-box {
        color: $max-disabled;
    }
}

.choose-capability-wrapper {
    .choose-capability-head {
        border-bottom: 1px solid $max-border;
    }
    .choose-capability-title {
        color: $max-black;
    }
}

.confirm-share-body .share-device-title {
    color: $max-light-black;
}

.confirm-share-head {
    .confirm-share-desc {
        color: $max-black;
    }
    .van-field {
        border-bottom: 1px solid $max-border;
    }
}


.share-success-content {
    .share-success-title {
        color: $max-black;
    }
    .share-success-desc {
        color: $max-black;
    }
}

.share-list-wrapper .share-item-wrapper {
    border-bottom: 1px solid $max-border;
    .share-name {
        color: $max-black;
    }
    .share-device-box {
        color: $max-gray;
    }
    .separator-box {
        background-color: $max-gray;
    }
    .share-device-status {
        color: $max-light-black;
    }
}

.popup-confirm-foot {
    .cancel-btn {
        color: $max-black;
    }
    .danger-btn {
        color: $max-red;
    }
}

.no-data-text {
    color: $max-light-gray
}

.share-apply-wrapper {
    background-color: $max-light-white;
    .share-apply-list-wrapper {
        background-color: $max-background;
        color: $max-black;
        & .device-camera-wrapper {
            .van-collapse-item__content {
                background-color: $max-background;
            }
            .van-collapse-item__content .van-cell {
                background-color: $max-background;
            }
            .van-collapse-item__content .van-cell:not(:last-child) {
                border-bottom: 1px solid $max-border;
            }
        }
    }
}

.choose-share-wrapper .input-error-msg {
    color: $max-red2!important;
}

.choose-share-content, .share-managment-content {
    .van-tab--active {
        color: $max-black;
        &::before {
            background-color: $max-primary;
        }
    }
    .van-tab__text {
        &::before {
            background-color: $max-primary;
        }
    }
}
