<template>
  <div class="trusteeship-detail-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="trusteeship-detail-content">
      <div class="trusteeship-img-box">
        <theme-image alt="device" imageName="device.png" />
      </div>
      <div class="trusteeship-detail-title">{{ detailData.devName || 0 }}</div>
      <!-- 待接收时展示托管时长 -->
      <div class="trusteeship-detail-time" v-if="detailData.status === 0">
        {{ $t('trustTime') }}：{{ getTimeLable(detailData.effectiveTime || 0) }}
      </div>
      <!-- 已接收时展示剩余时长 -->
      <div class="trusteeship-detail-time" v-else>{{ $t('residueTime') }}：{{ residueTimeStr }}</div>
      <div class="trusteeship-detail-status">
        {{ detailData.expiredStatus ? $t('expired') : statusObj[detailData.status] || '' }}
      </div>
      <div class="trusteeship-time-box">
        <van-cell>
          <template #title>
            <div class="detail-cell-title">{{ $t('trustTime') }}</div>
          </template>
          <template #right-icon>
            <div>{{ getTimeLable(detailData.effectiveTime || 0) }}</div>
            <div class="detail-icon-box" @click="handleTime">
              <theme-image class="arrow-icon" alt="arrowRight" imageName="arrow_right.png" />
            </div>
          </template>
        </van-cell>
      </div>
      <div class="trusteeship-auth-box">
        <van-cell>
          <template #title>
            <div class="detail-cell-title">{{ $t('trusteeshipPermissions') }}</div>
          </template>
          <template #right-icon>
            <div class="detail-icon-box" @click="handleCapability">
              <theme-image class="more-icon" alt="more" imageName="more.png" />
            </div>
          </template>
        </van-cell>
      </div>
      <div class="device-capability-list">
        <van-cell
          v-for="item of deviceCapabilitys.filter(item2 => (detailData.authList || []).includes(item2.value))"
          clickable
          :key="item.value"
          :title="item.label"
        >
          <template #right-icon>
            <theme-image alt="check" class="check-img" imageName="checked.png" />
          </template>
        </van-cell>
      </div>
    </div>
    <div class="footer" v-if="editType === 'edit'">
      <van-button class="footer-btn" type="primary" @click="handleTrusteeship">
        {{ $t('cancelTrusteeship') }}
      </van-button>
    </div>
    <!-- 选择托管权限弹窗 -->
    <van-popup v-model="showCapabilityOptions" round position="bottom">
      <choose-capability
        :title="$t('permissionSetting')"
        v-model="authList"
        :options="deviceCapabilitys"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
    </van-popup>
    <!-- 选择托管时长弹窗 -->
    <van-popup v-model="showTimeOptions" round position="bottom">
      <choose-time
        v-model="effectiveTime"
        :options="timeOptions"
        @cancel="handleCancelTime"
        @confirm="handleConfirmTime"
      />
    </van-popup>
    <!-- 取消托管确定弹窗 -->
    <van-popup v-model="showUnTrusteeship" round position="bottom" :style="{ height: '224px' }">
      <popup-confirm
        :title="$t('cancelTrusteeship')"
        :text="$t('unTrusteeshipDesc')"
        :cancelText="$t('notNow')"
        :confirmText="$t('stillCancel')"
        @cancel="cancelUnTrusteeship"
        @confirm="confirmUnTrusteeship"
      />
    </van-popup>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { DEVICE_CAPABILITY_LIST, TRUSTEESHIP_VALIDITY_LIST_FULLNAME } from '@/utils/options'
import { expireTimeMethod } from '@/utils/common'
import ChooseCapability from '../share/components/ChooseCapability.vue'
import ChooseTime from './components/ChooseTime.vue'
import PopupConfirm from '@/components/PopupConfirm.vue'
import { mapState, mapMutations } from 'vuex'
import { appClose, gotoPage } from '@/utils/appbridge'
import { deviceTrusteeshipsGet, deviceTrusteeshipsUpdate, deviceTrusteeshipsDelete } from '@/api/trusteeship'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'TrusteeshipDetail',
  components: {
    NavBar,
    ChooseCapability,
    ChooseTime,
    PopupConfirm,
    ThemeImage
  },
  props: {},
  data() {
    return {
      // 0:待接收 -- 等待托管, 1:已接收 -- 正在托管, 2:拒绝
      statusObj: {
        0: this.$t('waitTrust'),
        1: this.$t('duringTrust')
        // 2: this.$t('rejected')
      },
      editType: 'detail',
      deviceCapabilitys: DEVICE_CAPABILITY_LIST(), // 全量设备权限
      timeOptions: TRUSTEESHIP_VALIDITY_LIST_FULLNAME(), // 设备时间选项
      detailData: {},
      authList: ['live', 'rec', 'config'], // 托管权限
      effectiveTime: 0, //  设备托管时长- 默认永久
      showCapabilityOptions: false,
      showTimeOptions: false,
      showUnTrusteeship: false,
      btnLoading: false,
      trusteeshipId: null,
      expireTimeMethod: expireTimeMethod,
      expireTimer: null, // 过期定时器
      residueTimeStr: this.$t('forever')
    }
  },
  created() {
    // 从路由中托管记录id
    const query = this.$route.query
    if (query.id) {
      this.trusteeshipId = query.id
      this.$nextTick(() => {
        // 请求托管详情
        this.getTrusteeshipDetail()
      })
    }
  },
  mounted() {},
  computed: {
    ...mapState('maxTrusteeship', ['trusteeshipRecord'])
  },
  methods: {
    ...mapMutations('maxTrusteeship', ['SET_TRUSTEESHIP_RECORD']),
    back() {
      this.detailData = {}
      // 判断是否从托管列表页面跳转过来的，如果是则回退到托管列表页，否则关闭
      const query = this.$route.query
      if (query && query.from === 'list') {
        // 回到托管列表页面
        this.$router.go(-1)
      } else {
        appClose()
      }
    },
    // 请求托管详情
    async getTrusteeshipDetail() {
      const { data } = await deviceTrusteeshipsGet({ id: this.trusteeshipId })
      const { authList = [], effectiveTime = 0 } = data
      const checkCapability = this.deviceCapabilitys.filter(item => authList.includes(item.value))
      // console.log('authList', authList, checkCapability)
      this.authList = authList.slice()
      const record = {
        ...data,
        effectiveTime: Number(effectiveTime),
        checkCapability
      }
      // 判断已接受的记录是否过期--额外加个过期状态
      let expiredStatus = 0 // 0 表示未过期 1 表示过期
      const { expireTime = 0, status } = record
      if (Number(status) === 1) {
        const nowTimeStamp = Date.parse(new Date())
        if (Number(expireTime) === 0) {
          // expireTime为0表示永久
          expiredStatus = 0 // 未过期
        } else {
          if (parseInt(expireTime) - parseInt(nowTimeStamp) < 1000) {
            // 剩余时长小于1s
            expiredStatus = 1 // 过期
          } else {
            expiredStatus = 0
          }
        }
      }
      record.expiredStatus = expiredStatus
      // 根据状态判断是否可编辑
      // 待接收和已接受的都可编辑
      this.editType = 'edit'
      this.detailData = record
      this.SET_TRUSTEESHIP_RECORD(record)
      // 开启定时器，倒计时过期时间
      this.expireTimeInterval()
    },
    // 过期时间定时器
    expireTimeInterval() {
      if (this.expireTimer) {
        clearInterval(this.expireTimer)
        this.expireTimer = null
      }
      const { expireTime = 0 } = this.detailData
      this.residueTimeStr = this.expireTimeMethod((Number(expireTime) / 1000) * 1000)
      if (expireTime) {
        this.expireTimer = setInterval(() => {
          this.residueTimeStr = this.expireTimeMethod(expireTime)
        }, 1000)
      }
    },
    // 获取时长对应的名称
    getTimeLable(val) {
      const item = this.timeOptions.find(item => item.value === val)
      if (item) {
        return item.label
      } else {
        return null
      }
    },
    // 编辑权限
    handleCapability(e) {
      e.stopPropagation() // 阻止冒泡
      if (this.editType === 'edit') {
        this.showCapabilityOptions = true
      }
    },
    handleCancel() {
      this.showCapabilityOptions = false
      this.authList = this.detailData.authList.slice()
    },
    async handleConfirm() {
      this.showCapabilityOptions = false
      try {
        const { id } = this.detailData
        const res = await deviceTrusteeshipsUpdate({
          id,
          authList: this.authList
        })
        if (res.basic.code === 200) {
          this.$toastSuccess(this.$t('settingSuccess'))
          this.detailData = {
            ...this.detailData,
            authList: this.authList.slice()
          }
        }
      } catch (err) {
        this.authList = this.detailData.authList.slice()
        console.error(err)
      }
    },
    // 编辑时长
    handleTime(e) {
      e.stopPropagation() // 阻止冒泡
      if (this.editType === 'edit') {
        this.showTimeOptions = true
        this.effectiveTime = Number(this.detailData.effectiveTime)
      }
    },
    handleCancelTime() {
      this.showTimeOptions = false
      this.effectiveTime = this.detailData.effectiveTime
    },
    async handleConfirmTime() {
      this.showTimeOptions = false
      try {
        const { id } = this.detailData
        const params = {
          id,
          effectiveTime: this.effectiveTime
        }
        const res = await deviceTrusteeshipsUpdate(params)
        if (res.basic.code === 200) {
          this.$toastSuccess(this.$t('settingSuccess'))
          this.detailData = {
            ...this.detailData,
            effectiveTime: this.effectiveTime
          }
          // 重新请求详情数据
          this.getTrusteeshipDetail()
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 取消托管按钮
    handleTrusteeship() {
      this.showUnTrusteeship = true
    },
    cancelUnTrusteeship() {
      this.showUnTrusteeship = false
    },
    async confirmUnTrusteeship() {
      this.showUnTrusteeship = false
      try {
        const { sn } = this.detailData
        const res = await deviceTrusteeshipsDelete({ sn })
        if (res.basic.code === 200) {
          this.$toastSuccess(this.$t('cancelTrusteeship'))
          // 跳转到服务页面
          gotoPage({
            pageRoute: 'service/home'
          })
        }
      } catch (err) {
        console.error(err)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-detail-wrapper {
  height: 100%;
  overflow: hidden;
  .trusteeship-detail-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    .trusteeship-img-box {
      width: 100%;
      height: 140px;
      padding-top: 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      & img {
        width: 303px;
        height: 120px;
      }
    }
    .trusteeship-detail-title {
      width: 100%;
      box-sizing: border-box;
      padding: 0px 10px;
      overflow: hidden;
      word-break: break-all;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
    .trusteeship-detail-time {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin: 2px 0px;
    }
    .trusteeship-detail-status {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .trusteeship-time-box {
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      margin-top: 20px;
      padding: 0px 14px;
    }
    .trusteeship-auth-box {
      width: 100%;
      height: 60px;
      box-sizing: border-box;
      padding: 0px 14px;
    }
    .detail-icon-box {
      width: 24x;
      height: 24px;
      img {
        width: 24px;
        height: 24px;
      }
    }
    .device-capability-list {
      width: 100%;
      padding: 0px 14px 0px 40px;
      box-sizing: border-box;
    }
  }
}
</style>
<style lang="scss">
.trusteeship-detail-content {
  .van-cell {
    display: flex;
    align-items: center;
    padding: 10px 0px;
  }
}
</style>
