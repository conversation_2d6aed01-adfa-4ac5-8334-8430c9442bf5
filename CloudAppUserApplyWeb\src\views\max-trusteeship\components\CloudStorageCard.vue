<template>
  <div class="cloud-card-wrapper">
    <div class="cloud-card-content" @click="handleClickDetail">
      <div class="cloud-card-head">
        <div class="cloud-card-purchase-record" v-show="cloudStorageBuyChlNum" @click="handleClickRecord">
          {{ $t('purchaseRecords') }}
        </div>
        <div class="cloud-card-title">{{ titleText }}</div>
        <div class="cloud-card-text" :class="{ underlined: this.cloudStorageBuyChlNum }" @click="handleBoughtClick">
          {{ descrptionText }}
        </div>
      </div>
      <div class="cloud-card-body">
        <div class="cloud-card-btn">
          <van-button class="footer-btn" type="primary" @click="handleClickPurchase">
            {{ $t('immediateBuy') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { gotoPage, openDialog } from '@/utils/appbridge'
export default {
  name: 'CloudStorageCard',
  components: {},
  props: {
    cloudStorageBuyChlNum: {
      type: Number,
      default: 0
    },
    isGuest: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    titleText() {
      return this.cloudStorageBuyChlNum ? this.$t('cloudStorageInUse') : this.$t('cloudStorage')
    },
    descrptionText() {
      return this.cloudStorageBuyChlNum
        ? this.$t('serviceBought', [this.cloudStorageBuyChlNum])
        : this.$t('secureAndQuick')
    }
  },
  methods: {
    // 点击购买记录
    handleClickRecord(event) {
      event.stopPropagation()
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/orderRecords'
      })
    },
    //点击立刻购买
    handleClickPurchase(event) {
      event.stopPropagation()
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/purchase'
      })
    },
    // 点击卡片其它任意位置 进入详情页。
    handleClickDetail() {
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }
      gotoPage({
        pageRoute: 'cloudStorage/main'
      })
    },
    // 已购买跳转
    handleBoughtClick(event) {
      event.stopPropagation()
      if (this.cloudStorageBuyChlNum) {
        gotoPage({
          pageRoute: 'cloudStorage/purchasedChannels'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.cloud-card-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  .cloud-card-content {
    width: calc(100% - 16px);
    max-width: 359px;
    // width: 359px;
    border-radius: 10px;
    background-color: var(--bg-color-white, #ffffff);
    box-shadow: 0 2px 4px 0 #00000040;
  }
  .cloud-card-head {
    // width: 359px;
    width: 100%;
    height: 137px;
    background-image: url('@/assets/img/common/trusteeship/unbind_bg.png');
    background-repeat: no-repeat;
    background-position-y: 0px;
    background-position-x: center;
    background-size: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    .cloud-card-title {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-h4-size, 20px);
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
    }
    .cloud-card-text {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .underlined {
      text-decoration: underline;
    }
    .cloud-card-purchase-record {
      position: absolute;
      right: 12px;
      top: 12px;
      color: var(--brand-bg-color-default, #3277fc);
      font-size: var(--font-size-text-size, 12px);
    }
  }
  .cloud-card-body {
    // width: 359px;
    width: 100%;
    height: 67px;
    border-radius: 0px 0px 10px 10px;
    background-color: var(--bg-color-white, #ffffff);
    .cloud-card-line {
      width: 100%;
      height: 24px;
      padding: 0px 30px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 6px;
    }
    .trusteeship-line-text {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      margin-left: 8px;
    }
    .cloud-card-btn {
      padding-top: 3px;
      text-align: center;
      .footer-btn {
        width: 327px;
        height: 40px;
        border-radius: 23px;
        line-height: 40px;
        text-align: center;
      }
    }
  }
}
</style>
