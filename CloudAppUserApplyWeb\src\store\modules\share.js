export default {
  namespaced: true,
  state: () => ({
    shareUser: {
      active: 'emai', // 暂存分享当前激活的tab页
      email: '', // 暂存分享填写的邮箱
      mobile: '' // 暂存分享填写的手机号
    },
    allChannelList: [
      // {
      //   siteId: '1067490434814705664',
      //   siteName: 'Test1',
      //   sn: '969690A875AB46BE759DB3EFE6C9BFA0',
      //   devName: '3536C',
      //   snPlain: 'N44D20190726',
      //   chlIndex: 1,
      //   chlSn: null,
      //   chlName: 'IP頻道01',
      //   capability:
      //     '{"chlIndex":1,"verID":"A80B7109C822C3F426A06BF8B8EE6C64","version":"*******(6800)","model":"TD-9523A3-FR","manufacturer":"2","mac":"00:18:ae:a3:ab:51","protocol":259,"name":"IP頻道01","alarmInNum":1,"alarmOutNum":1,"supportFun":["osd","snp","cls"],"videoForm":["PAL","NTSC"],"stream":[],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 2,
      //   version: '*******(6800)',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1712583993000,
      //   siteCreateTime: 1668586086000
      // },
      // {
      //   siteId: '1215611930010583041',
      //   siteName: 'tesT',
      //   sn: '30EE927F9B11408B16540C210F014477',
      //   devName: 'N018AE006D80',
      //   snPlain: 'N018AE006D80',
      //   chlIndex: 17,
      //   chlSn: '306C25F1339597349CFA464A3C2E24D6',
      //   chlName: 'Camera_17',
      //   capability:
      //     '{"chlIndex":17,"verID":"4cd00bf062e3b09f548ed8feb9c78511","version":"5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta","model":"TD-9565S4-C","date":"","manufacturer":"2","chlSn":"306C25F1339597349CFA464A3C2E24D6","mac":"70:ab:49:83:7a:3e","coustomerID":"208","protocol":259,"name":"IP Camera 143","alarmInNum":0,"alarmOutNum":0,"supportFun":["a","d","ir","m","ma","mc","o","osd","p","ptz","snp","t","cls"],"videoForm":["PAL","NTSC"],"stream":[{"name":"mainCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"3200x1800"},{"fps":20,"value":"2688x1520"},{"fps":20,"value":"1920x1080"}]},{"name":"subCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"1280x720"},{"fps":20,"value":"704x480"},{"fps":20,"value":"640x480"},{"fps":20,"value":"352x240"}]},{"name":"aux1Caps","supEnct":["h264","h265","h265p"],"supEnctMode":["CBR"],"res":[{"fps":20,"value":"704x480"},{"fps":20,"value":"352x240"}]}],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 2,
      //   version: '5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1711503964000,
      //   siteCreateTime: 1703073309000
      // },
      // {
      //   siteId: '1215611930010583041',
      //   siteName: 'tesT',
      //   sn: '30EE927F9B11408B16540C210F014477',
      //   devName: 'N018AE006D80',
      //   snPlain: 'N018AE006D80',
      //   chlIndex: 18,
      //   chlSn: '',
      //   chlName: 'IP Camera 02',
      //   capability:
      //     '{"chlIndex":18,"verID":"163D52962EBF596310A269071B7975DB","version":"*******(45060)","model":"TD-8543IE3N","date":"","manufacturer":"2","chlSn":"","mac":"58:5b:69:07:89:e7","protocol":259,"name":"IP Camera 02","alarmInNum":1,"alarmOutNum":1,"supportFun":["a","at","cm","d","m","ma","mc","osd","p","plc","ptz","san","sal","snp","t","tb","cls"],"videoForm":["PAL","NTSC"],"stream":[{"name":"mainCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":25,"value":"2560x1440"},{"fps":25,"value":"2304x1296"},{"fps":25,"value":"1920x1080"},{"fps":25,"value":"1280x720"}]},{"name":"subCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":25,"value":"1280x720"},{"fps":25,"value":"704x576"},{"fps":25,"value":"640x480"},{"fps":25,"value":"352x288"}]},{"name":"aux1Caps","supEnct":["h264","h265","h265p"],"supEnctMode":["CBR"],"res":[{"fps":25,"value":"704x576"},{"fps":25,"value":"352x288"}]}],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 1,
      //   version: '*******(45060)',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1711503964000,
      //   siteCreateTime: 1703073309000
      // }
    ], // 站点通道列表
    deviceChannelList: [], // 设备通道树形结构
    channelObj: {}, // 设备sn-通道chlIndex及其对应的通道信息
    capabilityObj: {}, // 设备能力集  设备sn-通道chlIndex及其对应的能力集数组
    chooseChannelList: [], // 分享时勾选的通道（包含权限）
    shareReocrd: {}, // 我的分享中某条记录详情
    myShareList: [], // 我的分享列表
    otherShareList: [], // 他人分享列表
    userInfo: {}, // 当前登录用户信息
    scanUserInfo: {}, // 扫码得到的用户信息
    initChooseChannel: [], // 默认选中的通道 [{sn, chlIndex}]
    devSupportFunObj: {}, // 设备支持的功能
    devOperationObj: {}, // 设备勾选操作权限
    devList: [] // 设备列表(勾选了操作权限的列表)
  }),
  getters: {},
  mutations: {
    SET_SHARE_USER(state, data) {
      state.shareUser = data
    },
    SET_ALL_CHANNEL_LIST(state, data) {
      state.allChannelList = data
    },
    SET_DEVICE_CHANNEL_LIST(state, data) {
      state.deviceChannelList = data
    },
    SET_CHANNEL_OBJ(state, data) {
      state.channelObj = data
    },
    SET_CAPABILITY_OBJ(state, data) {
      state.capabilityObj = data
    },
    SET_DEV_SUPPORT_FUN_OBJ(state, data) {
      state.devSupportFunObj = data
    },
    SET_DEV_OPERATION_OBJ(state, data) {
      state.devOperationObj = data
    },
    SET_DEV_OPERATION_OBJ_BY_SN(state, { sn, operationChecked }) {
      state.devOperationObj[sn] = operationChecked
    },
    SET_DEV_LIST(state, data) {
      state.devList = data
    },
    SET_CHOOSE_CHANNEL_LIST(state, data) {
      state.chooseChannelList = data
    },
    SET_SHARE_RECORD(state, data) {
      state.shareReocrd = data
    },
    SET_MY_SHARE_LIST(state, data) {
      state.myShareList = data
    },
    SET_OTHER_SHARE_LIST(state, data) {
      state.otherShareList = data
    },
    SET_USER_INFO(state, data) {
      state.userInfo = data
    },
    SET_SCAN_USER_INFO(state, data) {
      state.scanUserInfo = data
    },
    SET_INIT_CHOOSE_CHANNEL(state, data) {
      state.initChooseChannel = data
    }
  },
  actions: {}
}
