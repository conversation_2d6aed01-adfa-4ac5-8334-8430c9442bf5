import { appRequestDevice } from '@/utils/appbridge'
import { transformXml } from '@/utils/common.js'

function requestDevice({ url, devId, xml }) {
  const req = {
    devId,
    url,
    params: xml
  }
  // console.log('app request device req url', url)
  // console.log('app request device req xml', xml)

  return new Promise(resolve => {
    appRequestDevice(req, function (res) {
      // console.log('app request device res', url)
      // console.log('app request device res xml', res)

      let resData = res.replace(/\\t|\\n/g, '')
      resData = JSON.parse(resData)

      if (resData.code == 200) {
        const body = transformXml(resData.body)
        // console.log('app request device res', body)

        resolve(body)
      }
    })
  })
}

// 人脸库管理
// superLivePlus

// 查看设备信息
export const queryDeviceInfo = ({ xml, devId }) =>
  requestDevice({
    url: 'GetDeviceInfo',
    xml,
    devId
  })

// 查看人员
export const queryPerson = ({ xml, devId }) =>
  requestDevice({
    url: 'GetTargetFace',
    xml,
    devId
  })

// 新增人员
export const addPerson = ({ xml, devId }) =>
  requestDevice({
    url: 'AddTargetFace',
    xml,
    devId
  })

// 修改人员
export const editPerson = ({ xml, devId }) =>
  requestDevice({
    url: 'EditTargetFace',
    xml,
    devId
  })

// 删除人员
export const delPerson = ({ xml, devId }) =>
  requestDevice({
    url: 'DeleteTargetFace',
    xml,
    devId
  })

// 查看人员属性信息
export const getPersonFields = ({ devId }) =>
  requestDevice({
    url: 'GetPersonFields',
    xml: '',
    devId
  })

// 查看门禁配置
export const getAccessControlConfig = ({ devId }) =>
  requestDevice({
    url: 'GetAccessControlConfig',
    xml: '',
    devId
  })
