<template>
  <div class="check-user-pwd">
    <van-dialog
      v-model="show"
      :title="$t('permissionAuth')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="username-div">
          <input
            type="text"
            class="common-input"
            v-model="username"
            maxlength="63"
            :placeholder="$t('pleaseEnterUser')"
          />
          <span class="username-close">
            <theme-image alt="inputClose" imageName="input_close.png" v-if="username" @click="username = ''" />
          </span>
        </div>
        <div class="password-div">
          <input
            class="common-input password-input"
            :type="passwordType"
            v-model="password"
            maxlength="16"
            :placeholder="$t('pleaseEnterPwd')"
          />
          <span class="password-close">
            <theme-image alt="inputClose" imageName="input_close.png" v-if="password" @click="password = ''" />
          </span>
          <span class="password-type" @click="switchPasswordType">
            <!-- 睁眼图 闭眼图 -->
            <theme-image v-if="passwordType === 'password'" alt="openEye" imageName="open_eye.png" />
            <theme-image v-else alt="closeEye" imageName="close_eye.png" />
          </span>
        </div>
        <div class="tips">{{ $t('upgradeTip') }}</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  components: { ThemeImage },
  data() {
    return {
      show: false,
      username: '',
      password: '',
      passwordType: 'password'
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      // APP这边限制的长度是用户名  63个字符  密码16个；不校验格式 只校验是否为空
      if (!this.username || !this.username.trim()) {
        this.$toast(this.$t('pleaseEnterUser'))
        return false
      }
      if (!this.password || !this.password.trim()) {
        this.$toast(this.$t('pleaseEnterPwd'))
        return false
      }
      this.$emit('devUpgrade', { username: this.username.trim(), password: this.password.trim() })
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.clearParam()
    },
    clearParam() {
      this.username = ''
      this.password = ''
    },
    switchPasswordType() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password'
    }
  }
}
</script>
