<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="detail-content">
        <div class="card">
          <installer-info :data="detail" />
        </div>
        <div class="site-list">
          <div class="site" v-for="site in siteList" :key="site.siteId">
            <div class="site-header">
              <div class="site-header-left">
                <img src="@/assets/img/common/trusteeship/site.png" class="site-icon" />
                {{ $t('site') }}
              </div>
              <div class="site-tool">
                <span class="site-name text-over-ellipsis">
                  {{ site.siteName }}
                </span>
                <img src="@/assets/img/common/more.png" class="site-tool-icon" @click="openSitePopup(site)" />
              </div>
            </div>
            <div class="site-content" v-for="device in site.trustVos" :key="device.id">
              <div class="device">
                <dir class="device-header" @click="toDeviceDetail(site, device)">
                  <div class="device-name text-over-ellipsis">
                    {{ device.deviceName }}
                  </div>
                  <div class="device-status">
                    {{ device.statusTr }}
                    <count-down :time="device.remainTime" @timeup="refreshDevice(site, device)" />
                  </div>
                </dir>
                <div v-if="device.trustStatus === SERVICE_STATUS.serving" class="device-content">
                  <div class="device-permission" @click="openPermissionPopup(site, device)">
                    <div>
                      <div class="device-permission-title">
                        {{ $t('trustPermission') }}
                      </div>
                      <div class="device-permission-info">
                        {{ device.permissionTr }}
                      </div>
                    </div>
                    <img src="@/assets/img/common/more.png" class="device-content-icon" />
                  </div>
                  <div class="device-permission" @click="openTimePopup(site, device)">
                    <div>
                      <div class="device-permission-title">
                        {{ $t('trustTime') }}
                      </div>
                      <div class="device-permission-info">
                        {{ device.hostingTimeTr }}
                      </div>
                    </div>
                    <img src="@/assets/img/common/more.png" class="device-content-icon" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </tvt-better-scroll>
    <div class="footer-btn-container">
      <van-button class="footer-btn" type="primary" @click="handleHosting">
        {{ $t('immediateTrusteeship') }}
      </van-button>
    </div>

    <van-popup v-model="sitePopupVisible" round :close-on-click-overlay="false" position="bottom" get-container="#app">
      <div class="service-detail-site-popup-container">
        <div class="site-change-item" @click="startChangeName">
          {{ $t('changeSiteName') }}
        </div>
        <div class="site-change-item" @click="startStopHost">
          {{ $t('cancelHosting') }}
        </div>
        <div class="site-change-item cancel-btn" @click="sitePopupVisible = false">
          {{ $t('cancel') }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="changeNameVisible" position="bottom" round :close-on-click-overlay="false" get-container="#app">
      <div class="service-detail-change-name-popup-container">
        <div class="header">
          <span class="cancel" @click="changeNameVisible = false">
            {{ $t('cancel') }}
          </span>
          <span class="title">
            {{ $t('changeSiteName') }}
          </span>
          <span class="confirm" :class="{ disabled: nameInput === '' }" @click="handleChangeName">
            {{ $t('confirm') }}
          </span>
        </div>
        <div class="content">
          <van-field
            class="content-input"
            v-model.trim="nameInput"
            clearable
            maxlength="32"
            clear-trigger="always"
            :placeholder="$t('enterSiteName')"
          />
        </div>
      </div>
    </van-popup>
    <cancel-hosting-popup
      :id="currentSite.siteId"
      :site-num="siteList.length"
      @refresh="refreshRequest"
      ref="cancelHostingRef"
    />
    <set-permission-popup-vue
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setPermissionVisible"
      @submit="changePermission"
      ref="setPermissionRef"
    />
    <set-time-popup
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setTimeVisible"
      @submit="changeTime"
      ref="setTimeRef"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import InstallerInfo from './components/InstallerInfo'
import { showTimeStr2 } from '@/utils/common'
import { mapMutations } from 'vuex'
import { cloneDeep } from 'lodash'

import { appClose } from '@/utils/appbridge'
import CancelHostingPopup from './components/CancelHostingPopup'
import SetPermissionPopupVue from './components/SetPermissionPopup'
import SetTimePopup from './components/SetTimePopup'
import CountDown from './components/CountDown'
import { getTrustSite, updateSite, getInstallerInfo, updateDeviceHosting } from '@/api/maxHosting'
import { serviceStatus, formatInstallerInfo } from './common'

export default {
  name: 'ServiceDetail',
  components: {
    NavBar,
    InstallerInfo,
    CancelHostingPopup,
    SetPermissionPopupVue,
    SetTimePopup,
    CountDown
  },
  data() {
    return {
      pullingStatus: 0,
      detail: {},
      siteList: {},
      id: '',
      SERVICE_STATUS: serviceStatus,
      sitePopupVisible: false,
      changeNameVisible: false,
      setPermissionVisible: false,
      setTimeVisible: false,
      nameInput: '',
      currentSite: {},
      currentDevice: {}
    }
  },
  created() {
    // 安装商id
    const { id } = this.$route.params

    if (id) {
      this.id = id
      this.refreshRequest()
    } else {
      this.id = ''
    }
  },
  methods: {
    ...mapMutations('maxHosting', ['SET_DEVICE_DETAIL', 'SET_INSTALLER_DETAIL']),
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.id
      })

      this.detail = formatInstallerInfo(detail)
    },
    async pullingDown(callback) {
      await this.refreshRequest()
      callback && callback()
    },
    // 刷新当前页面请求
    async refreshRequest() {
      this.getDetail()

      try {
        const { data: list } = await getTrustSite({
          installerUserId: this.id
        })

        this.siteList = list.map(item => {
          const newItem = { ...item }
          if (item.trustVos) {
            newItem.trustVos = item.trustVos
              .filter(
                device =>
                  device.trustStatus !== null &&
                  device.trustStatus !== serviceStatus.deleted &&
                  device.trustStatus !== serviceStatus.rejected
              )
              .map(device => {
                device.trustDuration = parseInt(device.trustDuration)
                device.acceptTime = parseInt(device.acceptTime)
                device.expireTime = parseInt(device.expireTime)

                if (device.authList === null) {
                  device.authList = ['config']
                }

                if (device.trustDuration === null) {
                  device.trustDuration = 0
                }

                return {
                  ...device,
                  statusTr: this.translateStatus(device),
                  permissionTr: this.translatePermission(device),
                  hostingTimeTr: this.translateTime(device),
                  remainTime: this.translateRemainTime(device)
                }
              })
          }

          return newItem
        })
      } catch (error) {
        console.error(error)
      }
    },
    translateTime(device) {
      if (device.trustDuration === 0) {
        return this.$t('forever')
      }

      return showTimeStr2(0, device.trustDuration * 1000)
    },
    translatePermission(device) {
      if (!Array.isArray(device.authList)) {
        return ''
      }
      const sorted = {
        config: 0,
        live: 1,
        rec: 2
      }
      const arr = []
      device.authList.forEach(item => {
        // 进行排序，后端数据顺序会变
        const index = sorted[item]
        if (index !== undefined) {
          arr[index] = item
        }
      })

      return arr
        .filter(item => item)
        .map(item => this.$t(item))
        .join(' · ')
    },
    translateRemainTime(device) {
      if (device.trustStatus !== serviceStatus.serving) {
        return ''
      }

      let time = ''

      if (device.trustDuration === 0) {
        time = this.$t('forever')
      } else {
        const now = Date.now()

        time = device.expireTime - now
      }

      return time
    },
    translateStatus(device) {
      if (device.trustStatus === serviceStatus.pending) {
        return this.$t('waitTrustReceived')
      }

      return `${this.$t('duringTrsutResidueTime')}: `
    },
    clickLeft() {
      // 跳转到服务页面
      appClose()
    },
    openSitePopup(site) {
      this.currentSite = site
      this.sitePopupVisible = true
    },
    handleHosting() {
      // 进入选择设备
      this.$router.push({
        path: '/max/hosting/device/select',
        query: {
          email: this.detail.email,
          installerUserId: this.id
        }
      })
    },
    startChangeName() {
      this.sitePopupVisible = false
      this.nameInput = this.currentSite.siteName
      this.changeNameVisible = true
    },
    async handleChangeName() {
      if (this.nameInput === '') {
        return
      }

      await updateSite({
        id: this.currentSite.siteId,
        siteName: this.nameInput
      })
      const index = this.siteList.findIndex(site => site.siteId === this.currentSite.siteId)
      const newSite = {
        ...this.currentSite,
        siteName: this.nameInput
      }
      if (index > -1) {
        this.$set(this.siteList, index, newSite)
      }
      this.changeNameVisible = false
      this.$toastSuccess(this.$t('changeSuccessfully'))
    },
    startStopHost() {
      this.sitePopupVisible = false
      this.$refs.cancelHostingRef.open()
    },
    openPermissionPopup(site, device) {
      this.currentSite = site
      this.currentDevice = device

      this.setPermissionVisible = true
    },
    async changePermission(value) {
      try {
        await updateDeviceHosting([
          {
            sn: this.currentDevice.sn,
            authList: value
          }
        ])

        this.currentDevice.authList = value
        this.currentDevice.permissionTr = this.translatePermission(this.currentDevice)

        const siteIndex = this.siteList.findIndex(site => site.siteId === this.currentSite.siteId)
        if (siteIndex > -1) {
          const deviceIndex = this.siteList[siteIndex].trustVos.findIndex(device => device.id === this.currentDevice.id)

          if (deviceIndex > -1) {
            this.$set(this.siteList, siteIndex, this.currentSite)
            this.setPermissionVisible = false
            this.$toastSuccess(this.$t('settingSuccess'))
            return
          }
          console.error('cannot find device')
        }
        console.error('cannot find site')
      } catch (error) {
        console.error(error)
      }
    },
    openTimePopup(site, device) {
      this.currentSite = site
      this.currentDevice = device

      this.setTimeVisible = true
    },
    async changeTime(value) {
      try {
        await updateDeviceHosting([
          {
            sn: this.currentDevice.sn,
            trustDuration: value
          }
        ])
      } catch (error) {
        console.error(error)
      }
      this.refreshRequest()
      this.setTimeVisible = false
      this.$toastSuccess(this.$t('settingSuccess'))
    },
    toDeviceDetail(site, device) {
      // 等待托管不让进设备详情
      if (device.trustStatus === serviceStatus.pending) {
        return
      }
      this.SET_INSTALLER_DETAIL(cloneDeep(this.detail))
      this.SET_DEVICE_DETAIL(cloneDeep(device))
      // 避免h5 点击穿透
      setTimeout(() => {
        this.$router.push(`/max/hosting/device-detail/${this.id}/${device.sn}`)
      }, 100)
    },
    refreshDevice(site, device) {
      // console.log('refreshDevice', site, device)
      // 托管时间到，变成未处理，需要过滤掉
      const deviceIndex = site.trustVos.findIndex(item => item.sn === device.sn)
      if (deviceIndex > -1) {
        site.trustVos.splice(deviceIndex, 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  overflow: hidden;
  position: relative;

  .tvt-better-scroll {
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 44px);
    padding-bottom: 80px;
    overflow: auto;
    box-sizing: border-box;
    margin-top: 10px;
    .card {
      width: calc(100% - 24px);
      margin: auto;
    }
  }
  .site-list {
    width: calc(100% - 24px);
    margin: 20px auto 0;
    .site-header {
      display: flex;
      align-items: center;
      height: 60px;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
    }
    .site-header-left {
      display: flex;
      align-items: center;
      width: 50%;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      .site-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
    }
    .site-tool {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 50%;
      .site-name {
        max-width: calc(100% - 30px);
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: right;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-right: 6px;
      }
      .site-tool-icon {
        width: 24px;
        height: 24px;
      }
    }
    .device {
      padding-left: 6px;
    }
    .device-header {
      box-sizing: border-box;
      width: 100%;
      height: 70px;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      flex-direction: column;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);

      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        width: 100%;
      }
      .device-status {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
    .device-content {
      padding-left: 24px;
      .device-permission {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        height: 40px;
        box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
      }
      .device-permission-title {
        align-self: stretch;
        color: var(--icon-color-primary, #2b2b2b);
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-permission-info {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
      }
      .device-content-icon {
        width: 24px;
        height: 24px;
      }
    }
  }
  .footer-btn-container {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    background-color: var(--bg-color-white, #ffffff);
  }
  .footer-btn {
    display: block;
    margin: 0 auto;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
  }
}
</style>
<style lang="scss">
.service-detail-site-popup-container {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);

  .site-change-item {
    height: 52px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    background: #ffffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-primary, #1a1a1a);
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    background-color: var(--bg-color-white, #ffffff);
  }
  .cancel-btn {
    margin-top: 8px;
  }
}
.service-detail-change-name-popup-container {
  height: 196px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 16px;
    background-color: var(--bg-color-white, #ffffff);
    .cancel {
      color: var(--text-color-secondary, #50546d);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
    .title {
      color: var(--text-color-primary, #101d34);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .confirm {
      color: var(--brand-bg-color-active, #1d71f3);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      &.disabled {
        color: var(--text-color-placeholder, #a3a3a3);
      }
    }
  }
  .content {
    width: 100%;
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content-input {
    width: 300px;
    padding: 14px 0;
    color: var(--text-color-primary, #101d34);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  }
}
</style>
