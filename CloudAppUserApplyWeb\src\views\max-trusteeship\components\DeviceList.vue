<template>
  <div class="devices-list-wrapper">
    <div v-for="item of dataList" :key="item.sn" class="device-list-item">
      <van-cell clickable :key="item.sn" :title="`${item.devName}`" @click.stop.native="clickRecord(item)">
        <template #title>
          <div :class="['device-item-box', isZh ? 'device-item-box-zh' : 'device-item-box-en']">
            <div class="device-icon">
              <theme-image alt="camera" imageName="nvr.png" />
            </div>
            <div class="device-item-title">
              <div class="device-title">{{ item.devName }}</div>
              <div class="device-capability" v-if="item.checkCapability">
                <span v-for="(item2, index2) of item.checkCapability" :key="item2.value" class="device-capability-item">
                  <span>{{ item2.label }}</span>
                  <span class="separator-box" v-if="index2 < item.checkCapability.length - 1"></span>
                </span>
              </div>
            </div>
          </div>
        </template>
        <template #right-icon>
          <div class="device-right-box">
            <div class="device-status-text">
              {{ item.expiredStatus ? $t('expired') : statusObj[item.status] }}
            </div>
          </div>
        </template>
      </van-cell>
    </div>
  </div>
</template>
<script>
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'DeviceList',
  components: {
    ThemeImage
  },
  props: {
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      statusObj: {
        0: this.$t('toAccept'),
        1: this.$t('accepted')
      }
    }
  },
  created() {},
  mounted() {},
  computed: {
    isZh() {
      return this.$i18n.locale === 'zh-CN' || this.$i18n.locale === 'zh'
    }
  },
  methods: {
    clickRecord(record) {
      this.$emit('click', record)
    }
  }
}
</script>
<style lang="scss" scoped>
.devices-list-wrapper {
  width: 100%;
  height: 100%;
  font-family: 'Source Han Sans CN', sans-serif;
  .device-list-item {
    width: 100%;
    height: 60px;
    padding: 0px 14px;
    box-sizing: border-box;
  }
  .device-item-box {
    width: calc(100vw - 80px);
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    overflow: hidden;
    .device-icon {
      width: 24px;
      height: 24px;
      margin: 0px 6px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .device-item-title {
      flex: 1;
      font-size: var(--font-size-body2-size, 14px);
      overflow: hidden;

      .device-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 22px;
      }
      .device-capability {
        width: 100%;
        overflow: hidden;
        font-size: var(--font-size-text-size, 12px);
        line-height: 18px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .device-capability-item {
        display: inline-flex;
        align-items: center;
      }
      .separator-box {
        display: inline-block;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        margin: 0px 4px;
      }
    }
  }
  .device-item-box-zh {
    width: calc(100vw - 80px);
  }
  .device-item-box-en {
    width: calc(100vw - 140px);
  }
  .device-right-box {
    display: inline-flex;
    align-items: center;
    .device-status-text {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      white-space: nowrap;
    }
  }
}
</style>
<style lang="scss">
.devices-list-wrapper {
  .van-collapse-item__content {
    padding: 2px 8px !important;
  }
  .van-cell {
    height: 60px;
    padding: 10px 0px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    background-color: transparent;
  }
  .van-cell__value {
    right: -15px;
    width: min-content;
    display: flex;
    justify-content: flex-end;
    .van-checkbox {
      justify-content: flex-end;
    }
  }
  .van-checkbox__icon {
    font-size: var(--font-size-body1-size, 16px);
  }
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
}
</style>
