<template>
  <div class="nav-bar">
    <div class="nav-bar-left">
      <van-icon name="arrow-left" @click="back" />
    </div>
    <div class="nav-bar-center">
      <van-search
        v-model="searchValue"
        shape="round"
        class="search-input-wrapper"
        :left-icon="require('@/assets/img/common/input_search.png')"
        :placeholder="$t('searchTargetFace')"
        :action-text="$t('cancel')"
        @search="search"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TargetFaceSearchName',
  props: {},
  data() {
    return {
      searchValue: ''
    }
  },
  methods: {
    search() {
      this.$emit('search', this.searchValue)
    },
    back() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.nav-bar {
  position: sticky;
  top: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px;
  line-height: 44px;
  .nav-bar-left {
    position: absolute;
    left: 0px;
    display: flex;
    align-items: center;
    padding-left: 12px;
    .van-icon {
      font-size: var(--font-size-h5-size, 18px);
    }
  }
  .nav-bar-right {
    position: absolute;
    right: 0px;
    display: flex;
    align-items: center;
    padding-right: 18px;
    .van-icon {
      font-size: var(--font-size-h3-size, 24px);
    }
  }
  ::v-deep .van-field__body .van-field__clear {
    color: var(--text-color-placeholder, #8f8e93);
    padding: 0;
    flex-basis: 35px;
  }
  .nav-bar-center {
    font-size: var(--font-size-body2-size, 14px);
    text-align: center;
    width: calc(100% - 90px);
    ::v-deep .search-input-wrapper {
      width: 100%;
      height: 26px;
      background-color: unset;
      padding: 0;
      .van-search__content {
        width: 100%;
        border-radius: 17px;
        line-height: 26px;
        padding-left: 4px;
        .van-cell {
          padding: 0;
          height: 26px;
        }
        .van-field__left-icon .van-icon {
          height: 26px;
          display: flex;
          align-items: center;
          .van-icon__image {
            width: 24px;
            height: 24px;
          }
        }
        .van-field__control {
          height: 26px;
        }
      }
    }
  }
}
</style>
