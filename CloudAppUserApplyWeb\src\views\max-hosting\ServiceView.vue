<template>
  <div class="service-wrapper">
    <div class="header-placeholder"></div>
    <div class="service-content">
      <tvt-better-scroll
        class="whole-tvt-better-scroll"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <!-- 未绑定安装商时展示 -->
        <unbind-card v-if="transferList.length === 0" @startHost="handleBind" />
        <binded-card v-else :list="transferList" />
        <cloud-storage-card
          v-if="isCloudStorageAvailable"
          :cloudStorageBuyChlNum="cloudStorageBuyChlNum"
          :isGuest="isGuest"
        />
        <cloud-disk-card v-if="cloudDiskStatus === 1" :cloudDiskData="cloudDiskData" />
      </tvt-better-scroll>
    </div>
  </div>
</template>

<script>
import UnbindCard from './components/UnbindCard'
import BindedCard from './components/BindedCard'
import CloudStorageCard from './components/CloudStorageCard'
import CloudDiskCard from './components/CloudDiskCard'
import { bridgeRegisterHandler, openDialog, isGuest, openH5 } from '@/utils/appbridge'
import { isCloudStorageAvailable, getCloudStorageChannels, getCloudDiskStatus } from '@/api/cloudStorage'
import { getHostedInstaller } from '@/api/maxHosting'
import { formatFileSize, dateFormat } from '@/utils/common'

export default {
  name: 'serviceView',
  components: {
    UnbindCard,
    CloudStorageCard,
    BindedCard,
    CloudDiskCard
  },
  data() {
    return {
      pullingStatus: 0,
      isCloudStorageAvailable: false, // 云存储是否可用
      cloudStorageBuyChlNum: 0, // 购买云存储通道的数量
      isGuest: null,
      transferList: [],
      cloudDiskStatus: 0, // 云盘状态 0-未开通 1-使用中 2-已过期
      cloudDiskData: {} // 云盘状态数据
    }
  },
  created() {
    this.refreshRequest()
    bridgeRegisterHandler('viewAppear', async () => {
      // console.log('viewAppear回调函数参数')
      this.refreshRequest()
    })
  },
  methods: {
    // 刷新当前页面请求
    async refreshRequest() {
      this.isUserGuest()
      this.getTransferList()
      // 获取云存储是否可用
      this.isCloudStorageAvailable = await this.isAvailable()
      // 可用就继续获取云存储购买状态
      this.isCloudStorageAvailable && this.getCloudStorageStatus()
      // 获取云盘状态
      this.getCloudDiskStatus()
    },
    // 判断当前用户是否为游客
    isUserGuest() {
      isGuest(res => {
        // console.log('isGuest返回结果', res)
        // 从APP获取当前用户是否为游客
        const resData = JSON.parse(res)
        this.isGuest = resData.body.isGuest
      })
    },
    // 查询当前用户托管站点的安装商
    async getTransferList() {
      try {
        const { data } = await getHostedInstaller()
        this.transferList = data
      } catch (error) {
        console.error(error)
      }
    },
    async isAvailable() {
      try {
        const res = await isCloudStorageAvailable({})
        return res.data?.some(item => item.applicationId === 1)
      } catch (error) {
        console.error(error)
      }
    },
    // 获取当前云存储是否购买，有几路通道购买
    getCloudStorageStatus() {
      getCloudStorageChannels({}).then(res => {
        if (res.basic.code === 200) {
          this.cloudStorageBuyChlNum = res?.data || 0
        }
      })
    },
    // 获取当前云盘状态
    async getCloudDiskStatus() {
      try {
        const res = await getCloudDiskStatus({})
        const temp = { ...res.data }
        Object.keys(temp).forEach(key => {
          temp[key] = parseInt(temp[key])
        })
        const { presentTotal = 0, presentExpireTime, currentUsage = 0, payTotal = 0 } = temp
        // 如果赠送空间为0则为未开通, 如果赠送空间不为0已到过期时间则为已过期
        let cloudDiskStatus = 0
        if (!presentTotal) {
          cloudDiskStatus = 0
        } else if (presentTotal && presentExpireTime < Date.now()) {
          cloudDiskStatus = 2
        } else {
          cloudDiskStatus = 1
        }
        this.cloudDiskStatus = cloudDiskStatus
        let usagePercentage = 0
        if (presentTotal + payTotal > 0) {
          usagePercentage = parseInt((currentUsage / (presentTotal + payTotal)) * 100)
        }
        this.cloudDiskData = {
          ...temp,
          usedSpace: formatFileSize(currentUsage),
          totalSpace: formatFileSize(presentTotal + payTotal),
          cloudDiskStatus,
          usagePercentage,
          expirationTime: dateFormat(presentExpireTime)
        }
      } catch (error) {
        console.error(error)
      }
    },
    async pullingUp(callback) {
      // 刷新
      this.refreshRequest()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      this.refreshRequest()

      if (callback) callback()
    },
    // 托管
    handleBind() {
      // 判断是否为游客，如果是游客拉起APP的弹窗
      if (this.isGuest) {
        openDialog({
          url: 'native/guestAlert'
        })
        return
      }

      // 进入添加安装商
      openH5({ url: '/max/hosting/installer/add' })
    }
  }
}
</script>

<style lang="scss" scoped>
.service-wrapper {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);
  height: 100%;
  overflow: hidden;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  .header-placeholder {
    height: 44px;
  }

  .service-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    box-sizing: border-box;
    .whole-tvt-better-scroll {
      height: calc(100% - 20px);
      overflow: auto;
      padding-bottom: 20px;
    }
  }
}
</style>
