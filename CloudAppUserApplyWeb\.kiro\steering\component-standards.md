---
inclusion: fileMatch
fileMatchPattern: ['**/*.vue', '**/components/**/*.js']
---

# Vue Component Standards

## Component Architecture
- Use Vue 2.7.8 composition patterns with Options API
- Implement single-file components (.vue) with clear separation of template, script, and style
- Follow PascalCase naming for component files and registration
- Use kebab-case for component usage in templates

## Component Structure
```vue
<template>
  <!-- Template content -->
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
/* Component-specific styles */
</style>
```

## Props and Data Flow
- Define props with explicit types and validation
- Use camelCase for prop names in JavaScript, kebab-case in templates
- Implement proper prop validation with required, type, and default values
- Emit events for parent-child communication using kebab-case event names

## Styling Guidelines
- Use scoped styles to prevent CSS leakage
- Leverage Sass/SCSS for advanced styling features
- Follow mobile-first responsive design principles
- Use Vant UI components as base building blocks
- Implement theme-aware styling with CSS custom properties

## State Management
- Use Vuex for global state, local component state for UI-specific data
- Access store through computed properties and mapGetters/mapActions
- Avoid direct store mutations, use actions for async operations

## Internationalization
- Use vue-i18n for all user-facing text
- Keep translation keys concise and avoid complex nesting, reuse existing translation content when possible
- Support Simplified Chinese (简体中文) as primary language with English fallbacks
- Follow GB/T 15834 standards for Chinese text processing
- Use proper Chinese punctuation: 。，！？：；""''（）【】
- Maintain consistent terminology across all Chinese translations
- Use formal tone (正式语调) for system messages and documentation
- Prefer concise expressions over verbose descriptions in Chinese
- Use Arabic numerals (1, 2, 3) instead of Chinese numerals for technical content
- Date format: YYYY年MM月DD日 or YYYY-MM-DD for Chinese locale
- Time format: 24-hour format (HH:mm)
- Consider character width differences in responsive layouts for Chinese text
- Ensure UTF-8 encoding for all Chinese text
- Test text overflow with longer Chinese translations
- Provide tooltips or help text for complex technical concepts in Chinese

## Mobile Optimization
- Implement touch-friendly interactions with appropriate touch targets
- Use better-scroll for smooth scrolling experiences
- Handle safe area insets for modern mobile devices
- Optimize for various screen sizes and orientations

## Performance Best Practices
- Use v-show for frequently toggled elements, v-if for conditional rendering
- Implement lazy loading for heavy components
- Use computed properties for expensive operations
- Avoid inline styles and functions in templates

## Error Handling
- Implement proper error boundaries and fallback UI
- Provide meaningful error messages in user's language
- Log errors appropriately for debugging
- Handle network failures gracefully with retry mechanisms