<template>
  <div class="add-defense-wrapper group-setting-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="group-setting-content">
      <div class="device-content">
        <div class="device-list-wrapper" v-if="defenseGroupList && defenseGroupList.length">
          <van-swipe-cell v-for="(item, index) in defenseGroupList" :key="'item' + index" :name="`device-${index}`">
            <div class="device-item-wrapper" @click.stop="editDefense(item, index)">
              <div class="device-item-left">
                <div class="device-title defense-ellipsis-text">{{ item.groupName }}</div>
                <!-- <div class="device-text">全部联动将不生效</div> -->
              </div>
              <div class="device-item-right">
                <img
                  class="arrow-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
                />
              </div>
            </div>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="() => deleteGroup(item, index)">
                <img
                  class="refuse-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/delete.png')"
                />
              </van-button>
            </template>
          </van-swipe-cell>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img
              :src="
                noDataImg ? noDataImg : require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')
              "
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations } from 'vuex'
import { transformXml } from '@/utils/common'
import { getDefenseDetail, deleteDefenseRecord, getDefenseGroupList, urlDefenseSwitchNodes } from '@/api/defense'
import { appRequestDevice } from '@/utils/appbridge'
export default {
  name: 'GroupSetting',
  components: {
    NavBar
  },
  data() {
    return {
      id: null, // 记录的id 编辑才有
      type: 'add' // 新增还是编辑 0 新增 1 编辑
    }
  },
  created() {},
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('defense', ['noDataImg', 'allChannelList', 'defenseGroupList', 'defenseRecord', 'groupChannelList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('defense', ['SET_DEFENSE_RECORD', 'SET_DEFENSE_GROUP_LIST']),
    back() {
      this.$router.go(-1)
    },
    // position 为关闭时点击的位置
    beforeClose({ name, position, instance }) {
      // console.log('name', name, 'position', position, 'instance', instance)
      const index = name.split('-')[1]
      const tips = {
        message: this.$t('deleteDeviceConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      switch (position) {
        case 'left':
        case 'cell':
        case 'outside':
          instance.close()
          break
        case 'right':
          this.$dialog
            .confirm(tips)
            .then(() => {
              // on confirm
              const deviceList = this.deviceList.slice()
              deviceList.splice(index, 1)
              this.deviceList = deviceList
              instance.close()
            })
            .catch(() => {
              // on cancel
              //   instance.open()
            })
          break
      }
    },
    // 查询布防撤防列表
    getDefenseList() {
      getDefenseGroupList({})
        .then(({ data }) => {
          this.$loading.hide()
          const resData = data || []
          // that.dataList = resData
          this.SET_DEFENSE_GROUP_LIST(resData)
        })
        .catch(error => {
          console.error(error)
        })
    },
    async editDefense(item) {
      // 获取布防组详情
      try {
        this.$loading.show()
        const res = await getDefenseDetail({ ids: [item.id] })
        const channelList = []
        const { data = [] } = res
        data.forEach(item => {
          const { chlIndex, sn, extra } = item
          // 从channelList中找到对应的记录
          const channelItem = this.allChannelList.find(item2 => item2.chlIndex === chlIndex && item2.sn === sn)
          if (channelItem) {
            const { chlSn, devName, siteId, siteName, snPlain, version } = channelItem
            const temp = {
              ...item,
              chlSn,
              devName,
              siteId,
              siteName,
              snPlain,
              version,
              extra: extra ? JSON.parse(extra) : null
            }
            channelList.push(temp)
          }
        })
        this.SET_DEFENSE_RECORD({
          ...item,
          channelList
        })
        this.$nextTick(() => {
          this.$utils.routerPush({
            path: '/defense/addEditDefense'
          })
        })
      } catch (err) {
        console.log(err)
      } finally {
        this.$loading.hide()
      }
    },
    async deleteGroup(item) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      // 找到对应布防组的通道
      const channelList = this.groupChannelList.filter(channelItem => channelItem.groupId === item.id)
      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()
        // 将分组中关联的通道恢复到布防状态
        const that = this
        const callback = async msg => {
          if (msg === 'SUCCESS') {
            // 删除当前分组
            const { id } = item
            const params = {
              groupIdList: [id]
            }
            try {
              await deleteDefenseRecord(params)
            } catch (error) {
              console.error(error)
            } finally {
              await that.getDefenseList()
            }
          } else {
            that.$toast(that.$t('deleteFail'))
          }
        }
        // 发送协议到对应的设备
        this.updateDeviceStatus({ ...item, status: 1, channelList }, callback)
      } catch (err) {
        console.error(err)
        // 操作失败刷新分组列表
        this.getDefenseList()
      } finally {
        this.$loading.hide()
      }
    },
    // 更新设备布撤防状态--删除时恢复布防状态，固定为外出布防
    async updateDeviceStatus(item, callback) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 在家布防 2 外部布防
      const { status, channelList } = item
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接回调
        if (callback) callback('SUCCESS')
        return
      }
      // 跟设备交互
      const { sn } = channelList[0]
      const req = {
        devId: sn,
        url: 'editNodeDefenseStatus',
        params: urlDefenseSwitchNodes(status, channelList)
      }
      // console.log('请求参数', req)
      appRequestDevice(req, function (res) {
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        // const errorCode = resData.code
        // console.log('返回结果', res)
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (xmlObject.response.status == 'success') {
            // 处理结果
            if (callback) callback('SUCCESS')
          } else {
            if (callback) callback('ERROR')
          }
        } else {
          if (callback) callback('ERROR')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.group-setting-wrapper {
  position: relative;
  height: 100%;
  overflow: auto;
  .group-setting-content {
    height: calc(100% - 44px);
    padding: 35px 16px 0px 16px;
    box-sizing: border-box;
    overflow: auto;
  }
  .add-defense-device {
    height: 32px;
    line-height: 32px;
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }
  .device-content {
    // min-height: 180px;
    width: 100%;
    height: calc(100% - 20px);
    overflow: auto;
    border-radius: 10px;
  }
  .device-list-wrapper {
    border-radius: 10px;
    overflow: hidden;
  }
  .device-item-wrapper {
    height: 46px;
    box-sizing: border-box;
    display: flex;
    padding: 12px 16px;
    .device-item-left {
      width: calc(100% - 20px);
      height: 100%;
      display: flex;
      align-items: center;
      .device-title {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        // line-height: 24px;
      }
      .defense-ellipsis-text {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .device-text {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .device-item-right {
      width: 24px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-btn {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .add-device-btn {
      width: 102px;
      height: 40px;
      border-radius: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
    }
  }
}
.swipe-right-btn {
  height: 100%;
}
</style>
