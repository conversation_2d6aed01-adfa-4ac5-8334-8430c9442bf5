<template>
  <div class="popup-confirm-wrapper">
    <div class="popup-confirm-head">
      <div class="popup-confirm-title">{{ title || $t('title') }}</div>
    </div>
    <div class="popup-confirm-body">
      <span class="popup-body-text">{{ text }}</span>
    </div>
    <div class="popup-confirm-foot">
      <van-button class="footer-btn cancel-btn" type="primary" @click="handleCancel">
        {{ cancelText || $t('cancel') }}
      </van-button>
      <van-button class="footer-btn danger-btn" type="primary" @click="handleClick">
        {{ confirmText || $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import { debounce } from '@/utils/common'
export default {
  name: 'PopupConfirm',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    cancelText: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: ''
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    confirm: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleClick: debounce(async function () {
      this.$emit('confirm')
    }, 100)
  }
}
</script>

<style lang="scss" scoped>
.popup-confirm-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  .popup-confirm-head {
    width: 100%;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 14px 12px;
    box-sizing: border-box;
    .popup-confirm-title {
      font-family: 'Source Han Sans CN', serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
  }
  .popup-confirm-body {
    width: 100%;
    height: calc(100% - 112px);
    padding: 24px;
    box-sizing: border-box;
    font-style: normal;
    font-weight: 400;
    text-align: center;
    line-height: 22px;
    .popup-body-text {
      display: inline-block;
      text-align: left;
    }
  }
  .popup-confirm-foot {
    width: 100%;
    height: 60px;
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    .footer-btn {
      width: 50%;
      height: 52px;
      line-height: 52px;
      text-align: center;
      background-color: transparent;
      border: none;
    }
  }
}
</style>
<style lang="scss">
.popup-confirm-body {
  .van-cell {
    height: 52px !important;
  }
}
</style>
