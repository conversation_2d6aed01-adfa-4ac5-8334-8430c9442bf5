<template>
  <van-popup
    :value="visible"
    @change="value => $emit('update:visible', value)"
    position="bottom"
    round
    :close-on-click-overlay="false"
    get-container="#app"
  >
    <div class="site-device-setting-permission-pop-container">
      <div class="header">
        <span class="title">
          {{ $t('permissionSetting') }}
        </span>
        <img
          class="close-btn"
          src="@/assets/img/common/trusteeship/close.png"
          @click="$emit('update:visible', false)"
        />
      </div>
      <div class="content">
        <van-checkbox-group v-model="selectedValue" ref="checkboxGroup">
          <van-checkbox
            v-for="item in DEVICE_CAPABILITY_LIST"
            class="permission-item"
            :key="item.value"
            :name="item.value"
            ref="checkboxes"
            label-position="left"
            :disabled="item.value === 'config' && selectedValue.includes('config')"
          >
            {{ item.label }}
            <template #icon="props">
              <img
                class="img-icon"
                :src="
                  props.checked ? require('@/assets/img/common/check.png') : require('@/assets/img/common/check_no.png')
                "
              />
            </template>
          </van-checkbox>
        </van-checkbox-group>
      </div>
      <van-button class="footer-btn" type="primary" @click="handleHosting">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </van-popup>
</template>

<script>
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'

export default {
  name: 'SetPermissionPopup',
  props: {
    site: {
      type: Object,
      default: () => ({})
    },
    device: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      DEVICE_CAPABILITY_LIST: DEVICE_CAPABILITY_LIST(),
      selectedValue: [],
      initialSelectedValue: []
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.selectedValue = [...this.device.authList]
        this.initialSelectedValue = [...this.device.authList]
      }
    }
  },
  methods: {
    handleHosting() {
      const newValue = this.selectedValue.sort((a, b) => a - b)
      const oldValue = this.initialSelectedValue.sort((a, b) => a - b)
      if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
        this.$emit('update:visible', false)
        return
      }
      this.$emit('submit', this.selectedValue)
    }
  }
}
</script>

<style lang="scss">
.site-device-setting-permission-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    height: 52px;
    padding: 12px;
    position: relative;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    .title {
      display: inline-block;
      width: 100%;
      color: var(--text-color-primary, #1a1a1a);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-btn {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 14px;
    }
  }
  .content {
    padding: 0 16px;
    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);

      .img-icon {
        width: 24px;
        height: 24px;
      }
    }
    .van-checkbox__icon--disabled {
      .img-icon {
        filter: opacity(0.5);
      }
    }
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 10px auto 0;
  }
}
</style>
