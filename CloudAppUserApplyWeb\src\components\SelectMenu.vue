<template>
  <div class="select-menu">
    <div class="triangle"></div>
    <div class="select-menu-ul">
      <div class="select-menu-li" v-for="(item, index) in options" :key="index">
        <div class="label-text" @click="item.disabled ? null : $emit('choose', item)">
          <!-- 兼容直接传图片和主题图片两种模式 -->
          <img v-if="item.icon" class="unbind-icon" alt="unbind" :src="item.icon" />
          <theme-image v-else alt="unbind" class="unbind-icon" :imageName="item.iconName" />
          <div :class="{ 'select-text': true, 'disabled-select-text': item.disabled }">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'SelectMenu',
  components: {
    ThemeImage
  },
  props: {
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageContent: navigator.userAgent
    }
  },
  mounted() {},
  computed: {},
  methods: {}
}
</script>
<style lang="scss" scoped>
.select-menu {
  position: relative;
  width: 140px;
  .triangle {
    position: absolute;
    right: 12px;
    width: 0;
    height: 0;
    border-bottom: 10px solid var(--bg-color-white, #ffffff);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
  }
  .select-menu-ul {
    position: absolute;
    top: 10px;
    right: 0px;
    padding: 10px 4px 10px 0px;
    background-color: var(--bg-color-white, #ffffff);
    border-radius: 5px 5px 5px 5px;
    min-width: 110px;
    .select-menu-li {
      width: 100%;
      .label-text {
        width: 100%;
        padding: 3px 0;
        height: 24px;
        line-height: 24px;
        display: flex;
        align-items: center;
        .unbind-icon {
          width: 22px;
          height: 22px;
          margin-left: 8px;
          margin-right: 8px;
        }
        .disabled-select-text {
          color: var(--text-color-disabled, #d1d1d1);
        }
      }
    }
  }
}
</style>
