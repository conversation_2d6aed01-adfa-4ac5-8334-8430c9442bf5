{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-31T01:06:12.144Z", "updatedAt": "2025-07-31T01:06:13.605Z", "resourceCount": 12}, "resources": [{"id": "fullstack-development-methodology", "source": "project", "protocol": "execution", "name": "Fullstack Development Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/fullstacksage/execution/fullstack-development-methodology.execution.md", "metadata": {"createdAt": "2025-07-31T01:06:12.394Z", "updatedAt": "2025-07-31T01:06:12.394Z", "scannedAt": "2025-07-31T01:06:12.394Z", "path": "role/fullstacksage/execution/fullstack-development-methodology.execution.md"}}, {"id": "fullstacksage", "source": "project", "protocol": "role", "name": "Fullstacksage 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstacksage/fullstacksage.role.md", "metadata": {"createdAt": "2025-07-31T01:06:12.806Z", "updatedAt": "2025-07-31T01:06:12.806Z", "scannedAt": "2025-07-31T01:06:12.806Z", "path": "role/fullstacksage/fullstacksage.role.md"}}, {"id": "fullstack-architecture-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Architecture Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstacksage/thought/fullstack-architecture-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:12.811Z", "updatedAt": "2025-07-31T01:06:12.811Z", "scannedAt": "2025-07-31T01:06:12.811Z", "path": "role/fullstacksage/thought/fullstack-architecture-thinking.thought.md"}}, {"id": "technology-integration-mastery", "source": "project", "protocol": "thought", "name": "Technology Integration Mastery 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/fullstacksage/thought/technology-integration-mastery.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:13.030Z", "updatedAt": "2025-07-31T01:06:13.030Z", "scannedAt": "2025-07-31T01:06:13.030Z", "path": "role/fullstacksage/thought/technology-integration-mastery.thought.md"}}, {"id": "vue-development-best-practices", "source": "project", "protocol": "execution", "name": "Vue Development Best Practices 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vuejs-expert/execution/vue-development-best-practices.execution.md", "metadata": {"createdAt": "2025-07-31T01:06:13.044Z", "updatedAt": "2025-07-31T01:06:13.044Z", "scannedAt": "2025-07-31T01:06:13.044Z", "path": "role/vuejs-expert/execution/vue-development-best-practices.execution.md"}}, {"id": "component-architecture-design", "source": "project", "protocol": "thought", "name": "Component Architecture Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vuejs-expert/thought/component-architecture-design.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:13.063Z", "updatedAt": "2025-07-31T01:06:13.063Z", "scannedAt": "2025-07-31T01:06:13.063Z", "path": "role/vuejs-expert/thought/component-architecture-design.thought.md"}}, {"id": "vue-mastery-thinking", "source": "project", "protocol": "thought", "name": "Vue Mastery Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vuejs-expert/thought/vue-mastery-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:13.065Z", "updatedAt": "2025-07-31T01:06:13.065Z", "scannedAt": "2025-07-31T01:06:13.065Z", "path": "role/vuejs-expert/thought/vue-mastery-thinking.thought.md"}}, {"id": "vue<PERSON>s-expert", "source": "project", "protocol": "role", "name": "<PERSON><PERSON><PERSON><PERSON> Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vuejs-expert/vuejs-expert.role.md", "metadata": {"createdAt": "2025-07-31T01:06:13.090Z", "updatedAt": "2025-07-31T01:06:13.090Z", "scannedAt": "2025-07-31T01:06:13.090Z", "path": "role/vuejs-expert/vuejs-expert.role.md"}}, {"id": "vue-development-workflow", "source": "project", "protocol": "execution", "name": "Vue Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/vuemaster/execution/vue-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-31T01:06:13.316Z", "updatedAt": "2025-07-31T01:06:13.316Z", "scannedAt": "2025-07-31T01:06:13.316Z", "path": "role/vuemaster/execution/vue-development-workflow.execution.md"}}, {"id": "component-design-mastery", "source": "project", "protocol": "thought", "name": "Component Design Mastery 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vuemaster/thought/component-design-mastery.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:13.587Z", "updatedAt": "2025-07-31T01:06:13.587Z", "scannedAt": "2025-07-31T01:06:13.587Z", "path": "role/vuemaster/thought/component-design-mastery.thought.md"}}, {"id": "vue-expert-thinking", "source": "project", "protocol": "thought", "name": "Vue Expert Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/vuemaster/thought/vue-expert-thinking.thought.md", "metadata": {"createdAt": "2025-07-31T01:06:13.590Z", "updatedAt": "2025-07-31T01:06:13.590Z", "scannedAt": "2025-07-31T01:06:13.590Z", "path": "role/vuemaster/thought/vue-expert-thinking.thought.md"}}, {"id": "vuemaster", "source": "project", "protocol": "role", "name": "Vue<PERSON> 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/vuemaster/vuemaster.role.md", "metadata": {"createdAt": "2025-07-31T01:06:13.605Z", "updatedAt": "2025-07-31T01:06:13.605Z", "scannedAt": "2025-07-31T01:06:13.605Z", "path": "role/vuemaster/vuemaster.role.md"}}], "stats": {"totalResources": 12, "byProtocol": {"execution": 3, "role": 3, "thought": 6}, "bySource": {"project": 12}}}