<template>
  <div class="other-share-wrapper">
    <div class="other-share-content">
      <template v-if="!isInitReq || otherShareList.length">
        <share-list :dataList="otherShareList" :showOperate="true" @change="handleChange" @click="handleClick" />
      </template>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('noShareDevice') }}</div>
      </div>
    </div>
    <div class="footer" v-if="isInitReq && otherShareList.length > 0">
      <van-button
        class="footer-btn"
        type="primary"
        :disabled="btnDisabled"
        :loading="btnLoading"
        @click="handleConfirm"
      >
        {{ $t('allAccept') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import { receiveDevice } from '@/utils/appbridge'
import { mapState, mapMutations } from 'vuex'
import ShareList from './components/ShareList.vue'
import { getUserShareList, updateShareStatus } from '@/api/share'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'otherShare',
  components: {
    ShareList,
    ThemeImage
  },
  props: {
    activeTab: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isInitReq: false, // 是否请求过数据
      queryParams: {
        queryType: 2 // 1 我分享给别人的  2 别人分享给我的
      },
      btnLoading: false
    }
  },
  computed: {
    ...mapState('share', ['otherShareList']),
    // 按钮是否可点击
    btnDisabled() {
      const receptDataList = this.otherShareList.filter(item => item.status === 0)
      return receptDataList.length === 0
    }
  },
  watch: {
    activeTab: {
      handler(newValue) {
        if (newValue === 'otherShare') {
          this.getList()
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    ...mapMutations('share', ['SET_SHARE_RECORD', 'SET_OTHER_SHARE_LIST']),
    async getList() {
      try {
        this.isInitReq = true
        this.$loading.show()
        let { data } = await getUserShareList(this.queryParams)
        const otherData = data.map(item => {
          return {
            ...item,
            status: item.shareList[0].status
          }
        })
        // 存储请求数据
        this.SET_OTHER_SHARE_LIST(otherData)
      } catch (error) {
        if (error.code && error.msg) {
          this.$toast(error.msg)
        }
      } finally {
        this.$loading.hide()
      }
    },
    // 单个记录接受/拒绝
    async handleChange(item, index, status) {
      try {
        const ownerIds = [item.userId]
        const operationType = status === 1 ? 0 : 1 // 0 接受 1 拒绝
        const params = { ownerIds, operationType }
        await updateShareStatus(params)
        const dataList = [...this.otherShareList]
        dataList[index].status = status
        // 存储请求数据
        this.SET_OTHER_SHARE_LIST(dataList)
        if (status === 1) {
          this.$toastSuccess(this.$t('acceptSuccMsg'))
          // 通知APP刷新设备列表
          const list = (dataList[index].shareList || []).map(item => {
            const { sn, chlIndex } = item
            return {
              devId: sn,
              channelIndex: chlIndex
            }
          })
          receiveDevice({ type: 'share', list })
          this.getList()
        } else {
          this.$toastSuccess(this.$t('rejectMsg'))
        }
      } catch (err) {
        console.error(err)
        if (err.code === 'ERR_NETWORK') {
          // 提示网络连接失败
          this.$toastFail(this.$t('errorCode.12344'))
        } else {
          this.$toastFail(status === 1 ? this.$t('acceptFail') : this.$t('rejectFail'))
        }
      }
    },
    // 点击他人分享记录
    handleClick(item) {
      if (item.status === 0) {
        // 待接受状态不可进入详情
        return
      }
      this.SET_SHARE_RECORD({ ...item })
      // 进入编辑通道页面
      this.$utils.routerPush({
        path: '/share/shareDetail?type=detail'
      })
    },
    // 全部接受
    async handleConfirm() {
      // 判断是否有待接受的数据
      const receptDataList = this.otherShareList.filter(item => item.status === 0)
      try {
        this.$loading.show()
        this.btnLoading = true
        const ownerIds = receptDataList.map(item => item.userId)
        const operationType = 0 // 0 接受 1 拒绝
        const params = { ownerIds, operationType }
        await updateShareStatus(params)
        const dataList = [...this.otherShareList]
        dataList.forEach(item => {
          if (ownerIds.includes(item.userId)) {
            item.status = 1
          }
        })
        // 存储请求数据
        this.SET_OTHER_SHARE_LIST(dataList)
        this.$loading.hide()
        this.btnLoading = false
        // 通知APP刷新设备列表
        const list = []
        dataList.forEach(data => {
          const { shareList = [] } = data
          shareList.forEach(item => {
            const { sn, chlIndex } = item
            list.push({
              devId: sn,
              channelIndex: chlIndex
            })
          })
        })
        receiveDevice({ type: 'share', list })
      } catch (err) {
        this.$loading.hide()
        this.btnLoading = false
        console.error(err)
        if (err.code === 'ERR_NETWORK') {
          // 提示网络连接失败
          this.$toastFail(this.$t('errorCode.12344'))
        } else {
          this.$toastFail(this.$t('acceptFail'))
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.other-share-wrapper {
  height: 100%;
  overflow: auto;
  border-top: 1px solid #e7e7e7;
  .other-share-content {
    height: calc(100vh - 190px);
    overflow: auto;
  }
  .no-data {
    width: 100%;
    height: calc(100%);
    overflow: hidden;
    .no-data-img {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 100px;
      img {
        width: 120px;
        height: 123px;
      }
      .theme-image-container {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-top: 20px;
    }
  }
  .footer {
    padding: 10px 0px 6px 0px;
  }
}
</style>
