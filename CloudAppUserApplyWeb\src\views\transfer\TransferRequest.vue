<template>
  <div class="transfer-request-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <tvt-better-scroll
      class="tvt-better-scroll"
      @pullingUp="pullingUp"
      @pullingDown="pullingDown"
      :pullingStatus="pullingStatus"
    >
      <div class="transfer-request-content" v-if="dataList.length">
        <template>
          <van-swipe-cell v-for="(item, index) in dataList" :key="item.id" :name="`transfer_swipe_${index}`">
            <div class="transfer-item-wrapper">
              <div class="transfer-item-left">
                <div class="transfer-title">{{ item.devName }}</div>
                <div class="transfer-text">
                  <div class="transfer-line-text">{{ $t('from') }}：{{ item.transferUserLoginName }}</div>
                  <div class="transfer-line-text">{{ timeFormat(Number(item.createTime)) }}</div>
                </div>
              </div>
              <div class="transfer-item-right">
                <div class="recive-transfer-btn" @click="() => handleReceive(item)">
                  {{ $t('acceptTransfer') }}
                </div>
              </div>
            </div>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="handleRefuse(item)">
                <img
                  alt="refuse"
                  class="refuse-img"
                  :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/refuse.png')"
                />
              </van-button>
            </template>
          </van-swipe-cell>
        </template>
      </div>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <img
            alt="noData"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/no_data.png')"
            :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''"
          />
        </div>
        <div class="no-data-text">{{ $t('noData') }}</div>
      </div>
    </tvt-better-scroll>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { transferRequestList, transferHandle, isPartner, bindPartner } from '@/api/transfer.js'
import { appBack } from '@/utils/appbridge'
import { dateFormat, doNothing } from '@/utils/common.js'
import { mapState } from 'vuex'

const queryType = {
  toOther: 1,
  toMe: 2
}

const transferStatus = {
  pendding: 0,
  received: 1,
  rejected: 2,
  deleted: 3
}

export default {
  name: 'TransferRequest',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      dataList: [],
      listParams: {
        pageNum: 0, // 查所有
        pageSize: 100,
        queryType: queryType.toMe,
        transferStatus: transferStatus.pendding
      },
      pullingStatus: 0,
      bindAccount: false // 是否绑定过账号
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getList()
    })
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    timeFormat: dateFormat,
    back() {
      appBack()
    },
    pullingUp(callback) {
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.listParams.pageNum = 1
      this.getList({ type: 'down', callback })
    },
    // 查询转移过来的的设备列表
    async getList({ callback } = {}) {
      try {
        this.$loading.show()
        const { data } = await transferRequestList(this.listParams)

        this.dataList = data.records
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        callback && callback()
      }
    },
    async queryBindPartner() {
      if (this.bindAccount) {
        return true
      }

      const { data } = await isPartner()

      this.bindAccount = Boolean(data.installerUserId)

      return this.bindAccount
    },
    async bindPartner({ transferUserLoginName, transferUserId }) {
      let tips = {
        message: this.$t('bindInstallerText', { account: transferUserLoginName }),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)

        await bindPartner({
          installerUserId: transferUserId
        })
        this.$toast(this.$t('bindSuccess'))

        return false
      } catch (error) {
        console.error(error)
        return false
      } finally {
        // 每次进入页面第一次提示
        this.bindAccount = {}
      }
    },
    async handleReceive(item) {
      const hasBindPartner = await this.queryBindPartner()

      if (hasBindPartner) {
        this.acceptTransfer(item.id)
      } else {
        const needTransfer = await this.bindPartner(item)
        needTransfer && this.acceptTransfer(item.id)
      }
    },
    async acceptTransfer(transferId) {
      this.$loading.show()

      const result = await this.transfer({
        ids: [transferId],
        accept: true
      })

      if (result) {
        this.$toast(this.$t('acceptSuccess'))
      }

      await this.getList()

      this.$loading.hide()
    },
    async handleRefuse(item) {
      const tips = {
        message: this.$t('refuseTransferConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)

        this.$loading.show()

        const result = await this.transfer({
          ids: [item.id],
          accept: false
        })

        if (result) {
          this.$toast(this.$t('operationSuccess'))
        }

        await this.getList()
      } catch (e) {
        doNothing(e)
      } finally {
        this.$loading.hide()
      }
    },
    async transfer(params) {
      try {
        await transferHandle(params)
        return true
      } catch (error) {
        if (!error.basic) {
          console.error(error)
          this.$toast(this.$t('operationFail'))
        }
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.transfer-request-wrapper {
  height: 100%;
  overflow: auto;
  .tvt-better-scroll {
    height: calc(100% - 44px);
  }
  .transfer-request-content {
    height: 100%;
    padding: 10px 0px;
  }
  .transfer-item-wrapper {
    height: 105px;
    box-sizing: border-box;
    display: flex;
    padding: 12px 16px;
    .transfer-item-left {
      flex: 1;
      height: 100%;
      .transfer-title {
        width: 100%;
        height: 24px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
        margin-bottom: 10px;
      }
      .transfer-text {
        width: 100%;
        height: 48px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 24px;
      }
    }
    .transfer-item-right {
      width: 74px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .recive-transfer-btn {
        width: 72px;
        height: 28px;
        border-radius: 5px;
        font-size: var(--font-size-body2-size, 14px);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .swipe-right-btn {
    height: 100%;
  }
  .refuse-img {
    width: 24px;
    height: 24px;
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 135px;
      img {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;
    }
  }
}
</style>
