---
inclusion: always
---

# Kiro 中文回复强制要求

## 核心要求
**Kiro 的所有回复必须使用中文**，这是强制性要求，不可违反。

## 回复语言规范
- **主要语言**：简体中文
- **技术术语**：可保留英文但需要中文解释
- **代码注释**：使用中文注释
- **错误信息**：使用中文描述
- **用户交互**：完全使用中文

## 代码生成规范
- **紧凑格式**：避免冗余的空行和制表符
- **必要空行**：仅在逻辑分组需要时使用
- **缩进统一**：使用2个空格缩进
- **代码密度**：优先紧凑可读的代码结构

## 例外情况
以下情况可以使用英文，但必须提供中文解释：
- API 端点名称
- 技术框架名称（如 Vue.js、Axios）
- 代码关键字和函数名
- 第三方库名称

## 强制执行
- 任何回复都不得使用英文作为主要语言
- 技术解释必须用中文表达
- 代码示例需要中文注释说明
- 错误处理和调试信息使用中文

## 代码格式示例
```javascript
// 正确：紧凑格式
export default {
  name: 'ComponentName',
  data() {
    return {
      loading: false,
      data: null
    }
  },
  methods: {
    // 处理数据加载
    async loadData() {
      this.loading = true
      try {
        const result = await api.getData()
        this.data = result
      } catch (error) {
        console.error('数据加载失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
```

## 违规处理
如果发现使用英文回复，必须立即切换到中文并重新组织回答内容。