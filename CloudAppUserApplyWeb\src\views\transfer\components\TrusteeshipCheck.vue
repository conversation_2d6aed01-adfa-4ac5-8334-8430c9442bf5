<template>
  <div class="trusteeship-check">
    <div class="container-ul">
      <div class="container-li" v-for="(item, index) in dataList" :key="'item' + index">
        <div class="li-title">
          <div class="li-title-left">
            <van-checkbox v-model="item.flag">
              <template #icon="props">
                <theme-image v-if="props.checked" class="check-img" alt="list-check" imageName="check.png" />
                <theme-image v-else class="check-img" alt="list-check" imageName="check_no.png" />
              </template>
            </van-checkbox>
            <div class="li-name text-over-ellipsis">{{ item.deviceName }}</div>
          </div>
        </div>
        <div :class="item.show ? 'data-ul' : ''">
          <div :class="['v-li', item.flag ? 'v-li-active' : '']">
            <div class="v-li-content">
              <div class="v-li-title">{{ $t('trustPermission') }}</div>
              <div class="v-li-value">
                <span v-for="(item2, index2) of item.checkCapability" :key="item2.value" class="device-capability-item">
                  <span>{{ item2.label }}</span>
                  <span class="separator-box" v-if="index2 < item.checkCapability.length - 1"></span>
                </span>
              </div>
            </div>
            <div class="v-li-icon check-img" @click="editPermission(item)">
              <theme-image class="more-icon" alt="more" imageName="more.png" />
            </div>
          </div>
          <div :class="['v-li', item.flag ? 'v-li-active' : '']">
            <div class="v-li-content">
              <div class="v-li-title">{{ $t('trustTime') }}</div>
              <div class="v-li-value">{{ resValidity(item) }}</div>
            </div>
            <div class="v-li-icon check-img" @click="editTime(item)">
              <theme-image class="more-icon" alt="more" imageName="more.png" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 编辑权限 -->
    <set-permission-popup
      v-model="permissionList"
      :devCapabilityList="type === 'share' ? shareDevCapabilityList : devCapabilityList"
      :visible.sync="setPermissionVisible"
      @cancel="cancelPermission"
      @submit="changePermission"
    />
    <!-- 编辑有效期 -->
    <set-time-popup v-model="trustDuration" :visible.sync="setTimeVisible" @cancel="cancelTime" @submit="changeTime" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { DEVICE_CAPABILITY_LIST, AUTH_LIST, HOSTING_VALIDITY_LIST } from '@/utils/options.js'
import ThemeImage from '@/components/ThemeImage.vue'
import SetPermissionPopup from '@/components/SetPermissionPopup'
import SetTimePopup from '@/components/SetTimePopup'
export default {
  name: 'check',
  components: {
    ThemeImage,
    SetPermissionPopup,
    SetTimePopup
  },
  props: {
    type: {
      type: String,
      default: 'transfer' // transfer 转移交付，share 分享交付： 分享时设备权限不展示配置
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      devCapabilityList: DEVICE_CAPABILITY_LIST(),
      shareDevCapabilityList: DEVICE_CAPABILITY_LIST().slice(1), // 分享时设备权限不展示配置
      authList: AUTH_LIST(),
      validityList: HOSTING_VALIDITY_LIST(),
      setPermissionVisible: false,
      record: {},
      permissionList: [],
      setTimeVisible: false,
      trustDuration: null
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    resAuth(item) {
      let authStr = ''
      if (item.authList && item.authList.length) {
        item.authList.forEach(o => {
          if (o.flag) {
            authStr += this.$t(o.label) + '、'
          }
        })
      }
      authStr = authStr.slice(0, authStr.length - 1)
      return authStr
    },
    resValidity(item) {
      let str = ''
      let i = this.validityList.findIndex(o => o.value == item.trustDuration)
      if (i !== -1) {
        str = this.validityList[i].label
      }
      return str
    },
    // 编辑权限
    editPermission(item) {
      if (!item.flag) {
        // 设备被勾选才允许编辑权限
        return
      }
      const { checkCapability } = item
      this.record = { ...item }
      this.permissionList = checkCapability.map(item => item.value)
      this.setPermissionVisible = true
    },
    // 取消权限编辑
    cancelPermission() {
      this.permissionList = []
      this.record = {}
    },
    // 确定权限编辑
    changePermission() {
      const { sn } = this.record
      const newCheckCapability = this.authList.filter(item => this.permissionList.includes(item.value))
      const idx = this.dataList.findIndex(item => item.sn === sn)
      if (idx > -1) {
        this.$set(this.dataList, idx, { ...this.dataList[idx], checkCapability: newCheckCapability.slice() })
      }
      this.record = {}
      this.permissionList = []
    },
    // 编辑有效期
    editTime(item) {
      if (!item.flag) {
        // 设备被勾选才允许编辑有效期
        return
      }
      const { trustDuration } = item
      this.record = { ...item }
      this.trustDuration = trustDuration
      this.setTimeVisible = true
    },
    // 取消有效期编辑
    cancelTime() {
      this.trustDuration = null
      this.record = {}
    },
    // 确定有效期编辑
    changeTime() {
      const { sn } = this.record
      const idx = this.dataList.findIndex(item => item.sn === sn)
      // console.log('this.trustDuration', this.trustDuration)
      if (idx > -1) {
        this.$set(this.dataList, idx, { ...this.dataList[idx], trustDuration: this.trustDuration })
      }
      this.record = {}
      this.trustDuration = null
    }
  }
}
</script>
<style lang="scss" scoped>
.trusteeship-check {
  .container-ul {
    background-color: var(--bg-color-white, #ffffff);
    padding: 2px 0px;
    .container-li {
      padding: 0px 15px;
      &:last-child {
        border: 0;
      }
      .check-img {
        width: 20px;
        height: 20px;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        i {
          position: absolute;
          transition: all ease 0.6s;
          top: 2px;
          width: 14px;
          height: 14px;
          display: inline-block;
          background-image: url('@/assets/img/common/arrow_up.png');
          background-repeat: no-repeat;
          background-size: 14px;
          background-position-y: center;
          background-position-x: center;
        }
      }
      .arrow-up {
        transform: rotate(0deg);
      }
      .arrow-down {
        transform: rotate(180deg);
      }
      .arrow-right {
        transform: rotate(90deg);
      }
      .li-title {
        display: flex;
        justify-content: space-between;
        height: 50px;
        align-items: center;
        .li-title-left {
          display: flex;
        }
        .li-name {
          margin-left: 10px;
          max-width: 280px;
          font-weight: 500;
        }
      }
    }
    .data-ul {
      border-top: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
      padding: 4px 0;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--text-color-placeholder, #8f8e93);
    }
    .v-li {
      padding: 6px 0 6px 30px;
      display: flex;
      justify-content: space-between;
      .v-li-content {
        flex: 1;
        height: 40px;
      }
      .v-li-icon {
        width: 24px;
        height: 40px;
        display: flex;
        align-items: center;
        margin: 0px 6px;
        img {
          width: 24px;
          height: 24px;
        }
      }
      .v-li-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 22px;
      }
      .v-li-value {
        width: 100%;
        overflow: hidden;
        font-size: var(--font-size-text-size, 12px);
        line-height: 18px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .device-capability-item {
        display: inline-flex;
        align-items: center;
      }
      .separator-box {
        display: inline-block;
        width: 4px;
        height: 4px;
        margin: 0px 4px;
        background: var(--text-color-placeholder, #a3a3a3);
        border-radius: 50%;
      }
    }
  }
}
</style>
