<template>
  <div class="household-manangement-wrapper">
    <van-field
      class="input-item room-member-input"
      v-model.trim="mobile"
      type="tel"
      :maxlength="countryLocale === 'CN' ? 11 : 20"
      :placeholder="$t('enterMemberMobile')"
      @input="handleInput"
      @blur="e => validateMobile()"
    >
      <template #label>
        <div class="country-select" @click="openOptions">
          <span class="country-locale">
            {{ countryLocale }}
          </span>
          <van-icon name="arrow-down" class="select-icon" />
          <span class="country-code"> +{{ countryCode }} </span>
        </div>
      </template>
      <template #right-icon>
        <span class="cruise-line-close">
          <theme-image alt="inputClose" imageName="input_close.png" v-if="mobile" @click="clearMobile" />
        </span>
      </template>
    </van-field>
    <span class="error-msg" v-show="errorMsg"> {{ errorMsg }} </span>
    <van-popup v-model="showCountryOptions" position="bottom">
      <van-picker
        ref="optionRef"
        :confirm-button-text="$t('confirm')"
        :cancel-button-text="$t('cancel')"
        show-toolbar
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showCountryOptions = false"
      />
    </van-popup>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
import { validateCommonPhone } from '@/utils/validate'
import { MOBILE_COUNTRY_CODE } from 'tvtcloudcountrycode'
import { mapState } from 'vuex'

export default {
  name: 'MobileInput',
  components: {
    ThemeImage
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      mobile: '',
      countryLocale: '',
      countryCode: '',
      showCountryOptions: false,
      errorMsg: ''
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['memberList']),

    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    isZh() {
      return this.$i18n.locale === 'zh-CN'
    },
    columns() {
      const sorted = this.isZh
        ? MOBILE_COUNTRY_CODE.sort((a, b) => a.zh.localeCompare(b.zh))
        : MOBILE_COUNTRY_CODE.sort((a, b) => (a.en < b.en ? -1 : 1))

      return sorted.map(item => {
        if (this.isZh) {
          return item.zh
        }
        return item.en
      })
    },
    countryMobile() {
      return `${this.countryCode}+${this.mobile}`
    }
  },
  watch: {
    countryCode() {
      this.$emit('input', this.countryMobile)
    },
    mobile() {
      this.$emit('input', this.countryMobile)
    },
    value() {
      this.resolveValue()
    }
  },
  mounted() {
    this.resolveValue()
    this.initCountry()
  },
  methods: {
    resolveValue() {
      if (this.value) {
        if (this.value.indexOf('+') === -1) {
          this.mobile = this.value
        } else {
          const [code, number] = this.value.split('+')
          const country = MOBILE_COUNTRY_CODE.find(country => country.code == code)
          this.mobile = number

          if (country) {
            this.countryCode = code
            this.countryLocale = country.locale
          }
        }
      }
    },
    validateMobile(validateEmpty = false, initialValue = '') {
      if (this.mobile === '') {
        if (validateEmpty) {
          this.errorMsg = this.$t('mobileNotEmpty')
          return false
        }

        this.errorMsg = ''
        return true
      }

      if (!validateCommonPhone(this.mobile, this.countryLocale)) {
        this.errorMsg = this.$t('mobileError')
        return false
      }

      const hasSame = this.memberList.find(
        member => member.memberName.toLowerCase() === this.countryMobile.toLowerCase()
      )

      if (initialValue) {
        if (hasSame && this.countryMobile.toLowerCase() !== initialValue) {
          this.errorMsg = this.$t('memberMobileRepeate')
          return false
        }
      } else {
        if (hasSame) {
          this.errorMsg = this.$t('memberMobileRepeate')
          return false
        }
      }

      this.errorMsg = ''

      return true
    },
    handleInput() {
      this.mobile = this.mobile.replace(/[^\d]/gi, '')
    },
    initCountry() {
      if (!this.countryCode) {
        const language = window.navigator.language.toLowerCase()
        let index = MOBILE_COUNTRY_CODE.findIndex(v => language.indexOf(v.locale.toLowerCase()) >= 0)

        if (index < 0) {
          index = MOBILE_COUNTRY_CODE.findIndex(v => v.locale === 'CN')
        }
        this.setCountry(index)
      }
    },
    clearMobile() {
      this.mobile = ''
      this.errorMsg = ''
    },
    onConfirm(value, index) {
      this.setCountry(index)
      this.showCountryOptions = false
      this.validateMobile()
    },
    setCountry(index) {
      this.countryLocale = MOBILE_COUNTRY_CODE[index].locale
      this.countryCode = MOBILE_COUNTRY_CODE[index].code
    },
    openOptions() {
      const country = MOBILE_COUNTRY_CODE.find(item => item.code == this.countryCode)

      this.showCountryOptions = true

      if (country) {
        this.$nextTick(() => {
          this.$refs.optionRef.setColumnValue(0, this.isZh ? country.zh : country.en)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.input-item {
  width: 80%;
  padding: 5px 0;
  font-size: var(--font-size-h5-size, 18px);
  margin: auto;

  &::v-deep .van-field__label {
    width: unset;
  }

  .cruise-line-close {
    display: flex;
    align-items: center;
    img {
      width: 24px;
      height: 24px;
    }
  }
}
.error-msg {
  display: block;
  width: 80%;
  padding-top: 4px;
  margin: auto;
}
.country-locale {
  display: inline-block;
}
.select-icon {
  margin: 0 6px 0 12px;
}
.country-code {
  display: inline-block;
}
</style>
