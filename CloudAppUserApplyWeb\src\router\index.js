import Vue from 'vue'
import Router from 'vue-router'
import { Dialog } from 'vant'
import i18n from '@/lang'
import { constantRouterMap } from './router.config.js'

// hack router push callback
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router)
const isDevelopment = process.env.NODE_ENV === 'development'
const createRouter = () =>
  new Router({
    // mode: 'history', // 如果你是 history模式 需要配置vue.config.js publicPath
    base: isDevelopment ? '/' : '/',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRouterMap
  })

const router = createRouter()

router.onError(error => {
  const { name, request } = error
  console.error(error)

  if (name === 'ChunkLoadError' && /(.js|.css)$/.test(request)) {
    Dialog.alert({
      title: i18n.t('pageUpdateTitle'),
      message: i18n.t('pageUpdateContent'),
      confirmButtonText: i18n.t('ok')
    }).then(() => {
      location.reload()
    })
  }
})

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
