.defense-list-wrapper  .defense-item-wrapper {
    background-color: $USE44-light-background-color;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-title {
    color: $USE44-white-color;
}

.defense-list-wrapper .defense-item-wrapper .defense-item-content .defense-item-right .defense-item-text {
    color: $gray-color;
}

.add-defense-wrapper .add-defense-head {
    background-color: $USE44-light-background-color;
}

.add-defense-wrapper .add-defense-head .add-defense-title {
    color: $USE44-white-color;
}

.add-defense-wrapper .add-defense-head .add-defense-text {
    color: $gray-color;
}

.add-defense-wrapper .add-defense-device {
    color: $gray-color;
}

.add-defense-wrapper .device-content .device-list-wrapper {
    background-color: $USE44-light-background-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-title {
    color: $USE44-white-color;
}

.add-defense-wrapper .device-item-wrapper .device-item-left .device-text {
    color: $gray-color;
}

.device-content .device-list-wrapper .device-item-wrapper {
    border-bottom: 1px solid $USE44-light-gray-color;
}

.ipc-setting-wrapper .ipc-setting-box {
    background-color: $USE44-light-background-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-title {
    color: $USE44-white-color;
}

.ipc-setting-wrapper .ipc-setting-desc {
    color: $USE44-font-color;
}

.ipc-setting-wrapper .ipc-setting-box .ipc-setting-text {
    color: $gray-color;
}

.ipc-linkage-wrapper .ipc-configure {
    color: $gray-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list {
    background-color: $USE44-light-background-color;
}

.ipc-linkage-wrapper .ipc-linkage-content .ipc-linkage-list .ipc-linkage-item {
    color: $USE44-white-color;
    border-bottom: 1px solid $USE44-light-gray-color;
}

.channel-wrapper .channel-content .channel-list {
    background-color: $USE44-light-background-color;
}

.area-name-wrapper .common-input {
    background: transparent!important;
}