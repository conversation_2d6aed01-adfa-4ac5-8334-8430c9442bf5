.trusteeship-device-details {
  .device-details {
    background-color: $UI1C-light-background-color !important;
    color: $UI1C-50-white-color !important;
    .title {
      color: $UI1C-white-color !important;
    }
  }
  .configuration {
    color: $UI1C-white-color !important;
  }
  .container-ul{
    .device-configuration {
      background-color: $UI1C-light-background-color !important;
      border-bottom: 1px solid $UI1C-light-gray-color !important;
      &:last-child{
        border: 0 !important;
      }
      .configuration-item{
        color: $UI1C-white-color !important;
      }
    }
  }
  .v-li {
    background-color: $UI1C-light-background-color !important;
    .v-li-content {
      color: $UI1C-white-color !important;
    }
    .v-li-res {
      color: $UI1C-white-color !important;
    }
  }
}