<template>
  <div class="edit-dialog-wrapper">
    <van-dialog
      v-model="show"
      :title="title"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="cruise-line-div input-div">
          <input
            type="text"
            class="common-input"
            v-model="inputValue"
            :maxlength="maxLength"
            :placeholder="placeholder || $t('pleaseEnter')"
            @input="handleInput"
          />
          <span class="cruise-line-close input-close">
            <theme-image alt="inputClose" imageName="input_close.png" v-if="inputValue" @click="inputValue = ''" />
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
import { mapState } from 'vuex'

export default {
  name: 'EditDialog',
  components: {
    ThemeImage
  },
  props: {
    // 弹窗标题
    title: {
      type: String,
      required: true
    },
    // 输入框的值
    value: {
      type: String,
      default: ''
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: ''
    },
    // 最大长度
    maxLength: {
      type: Number,
      default: 32
    },
    // 输入类型：text(默认)、number(仅数字)
    type: {
      type: String,
      default: 'text'
    },
    // 自定义输入验证规则
    validator: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      show: false,
      inputValue: ''
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val !== this.inputValue) {
          this.inputValue = val
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      if (!this.inputValue || !this.inputValue.trim()) {
        this.$toast(this.$t('pleaseEnter'))
        return false
      }

      if (this.inputValue.trim() === this.value.trim()) {
        // 值未改变，直接关闭
        this.cancel()
        return
      }

      // 执行自定义验证
      if (this.validator) {
        const validResult = this.validator(this.inputValue.trim())
        if (validResult !== true) {
          this.$toast(validResult || this.$t('invalidInput'))
          return false
        }
      }

      this.$emit('confirm', this.inputValue.trim())
    },
    handleInput(event) {
      if (this.type === 'number') {
        this.inputValue = event.target.value.replace(/[^\d]/g, '')
      }
    },
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel')
    },
    clearParam() {
      this.inputValue = this.value
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-dialog-wrapper {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .input-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
    padding: 4px 2px;
    border-radius: 6px;
    margin-top: 20px;
    position: relative;
    .input-close {
      position: absolute;
      right: 10px;
      top: 8px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .common-input {
    width: 220px;
    padding-left: 8px;
  }
}
</style>
