<template>
  <div class="alarm-box-wrapper alarm-system-status">
    <div class="status-header">
      <div class="status-label">{{ $t('status') }}</div>
      <div class="wifi-icon">
        <theme-image
          class="connection-icon"
          :imageName="displayConnection ? 'alarm-system/online.png' : 'alarm-system/offline.png'"
          alt="connection"
        />
      </div>
    </div>
    <div class="status-content">
      <div class="status-indicator">
        <theme-image class="status-icon" :imageName="statusIcon" alt="status" />
        <span class="status-text">{{ displayStatus }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import { getStatusIconName, parsePanelStatus, parsePanelOnlineStatus, getStatusI18nKey } from '@/utils/alarmSystem'

export default {
  name: 'AlarmSystemStatus',
  props: {
    status: {
      type: String,
      default: null
    },
    isConnected: {
      type: Boolean,
      default: null
    },
    autoFetch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isLoading: false
    }
  },
  watch: {
    siteLoginInfo: {
      handler(newInfo) {
        if (this.autoFetch && newInfo.siteId && newInfo.sessionId) {
          this.loadPanelState()
        }
      },
      deep: true
    }
  },
  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo', 'panelState']),
    ...mapGetters('alarmSystem', ['siteId', 'sessionId', 'systemType', 'isPimaSystem', 'canFetchPanelState']),

    // 显示的状态文本
    displayStatus() {
      if (this.status !== null) return this.status
      const parsedStatus = parsePanelStatus(this.panelState.data, this.systemType)
      if (parsedStatus) {
        // 获取国际化的状态文本
        const statusText = this.$t(getStatusI18nKey(parsedStatus))
        return statusText
      }
      return ''
    },
    // 显示的连接状态
    displayConnection() {
      if (this.isConnected !== null) return this.isConnected
      // 使用在线状态解析函数
      return parsePanelOnlineStatus(this.panelState.data, this.systemType)
    },
    // 状态图标
    statusIcon() {
      return getStatusIconName(this.displayStatus)
    }
  },
  mounted() {
    if (this.autoFetch && this.canFetchPanelState) {
      this.loadPanelState()
    }
  },
  methods: {
    ...mapActions('alarmSystem', ['fetchPanelState']),

    // 获取Panel状态
    async loadPanelState() {
      if (!this.canFetchPanelState) {
        console.warn('Missing siteId or sessionToken for panel state fetch')
        return
      }
      // 防重复请求
      if (this.isLoading) {
        return
      }
      this.isLoading = true
      try {
        const panelData = await this.fetchPanelState({
          siteId: this.siteId,
          systemType: this.systemType
        })
        // 发送事件给父组件
        this.$emit('panel-data-change', panelData)
      } catch (error) {
        console.error('Failed to fetch panel state:', error)
        // 发送错误事件
        this.$emit('panel-error', error)
        // 特殊错误处理
        if (error.message?.includes('No security system')) {
          this.$emit('no-security-system', null)
        }
      } finally {
        this.isLoading = false
      }
    },
    // 手动刷新状态
    refresh() {
      return this.loadPanelState()
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-status {
  min-height: 76px;
  padding: 10px 16px;
  box-sizing: border-box;
  position: relative;
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .status-label {
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
    .wifi-icon {
      .connection-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
  .status-content {
    .status-indicator,
    .no-security-system {
      display: flex;
      align-items: center;
      .status-icon {
        margin-right: 12px;
        width: 22px;
        height: 22px;
      }
      .status-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
}
</style>
