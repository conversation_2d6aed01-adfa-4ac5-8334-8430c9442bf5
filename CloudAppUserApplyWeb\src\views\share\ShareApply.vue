<template>
  <div class="share-apply-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <template v-if="initRequest && shareList.length === 0">
      <div class="no-data">
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data_max.png" />
        </div>
        <div class="no-data-text">{{ $t('msgExpired') }}</div>
      </div>
    </template>
    <template v-else>
      <div class="share-apply-content">
        <div class="shareApply-text-box">
          <div class="shareApply-text">
            {{ $t('shareApplyDesc', [loginName]) }}
          </div>
        </div>
        <div class="share-apply-list-wrapper">
          <div class="share-apply-list-title">{{ $t('chooseChanAuth') }}</div>
          <div class="share-apply-list-content">
            <!-- <tvt-better-scroll
              ref="scrollRef"
              class="tvt-better-scroll"
              @pullingUp="pullingUp"
              @pullingDown="pullingDown"
              :pullingStatus="pullingStatus"
            > -->
            <choose-channel
              v-model="checkChannels"
              type="apply"
              :shareOverTen="recordStatus === 0 && shareOverTen"
              :channelDisabled="recordStatus !== 0 || shareOverTen"
              :deviceChannelList="deviceChannelList"
              @change="handleChange"
            />
            <!-- </tvt-better-scroll> -->
          </div>
        </div>
      </div>
      <div class="footer">
        <template v-if="recordStatus === 0">
          <van-button plain class="footer-btn footer-btn-short" type="default" @click="handleReject">
            {{ $t('rejectAll') }}
          </van-button>
          <van-button class="footer-btn footer-btn-short" type="primary" :disabled="shareOverTen" @click="handleAgree">
            {{ $t('agreeShare') }}
          </van-button>
        </template>
        <van-button v-if="recordStatus === 1" class="footer-btn" type="primary" disabled>
          {{ $t('agreed') }}
        </van-button>
        <van-button v-if="recordStatus === 2" class="footer-btn" type="primary" disabled>
          {{ $t('rejected') }}
        </van-button>
      </div>
    </template>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import ChooseChannel from './components/ChooseChannel.vue'
import { CHANNEL_CAPABILITY_LIST } from '@/utils/options'
import { appClose } from '@/utils/appbridge'
import ThemeImage from '@/components/ThemeImage.vue'
import {
  // shareDetail,
  getUserShareList,
  getResourceDeviceList,
  getDeviceDetail,
  agreeRefuseShareChannel
} from '@/api/share'

export default {
  name: 'ShareApply',
  components: {
    NavBar,
    ChooseChannel,
    ThemeImage
  },
  props: {},
  data() {
    return {
      initRequest: false, // 是否初始化请求过
      channelCapabilitys: CHANNEL_CAPABILITY_LIST(), // 全量通道权限
      ids: [], // 分享通道id列表
      sns: [], // 分享设备sn列表
      recordStatus: 0, // 状态 -- 0:待接受 1：已接受 2 已拒绝
      shareList: [],
      checkChannels: [], // 选中的通道
      deviceChannelList: [], // 设备-通道列表
      channelObj: {},
      loginName: '',
      shareOverTen: false // 是否分享超过10个用户，超过则不可分享
    }
  },
  created() {
    // 从路由中获取分享申请记录的id
    const query = this.$route.query
    if (query.ids) {
      this.shareIds = JSON.parse(query.ids)
    }
    if (query.sns) {
      this.sns = JSON.parse(query.sns)
    }
    if (query.loginName) {
      this.loginName = query.loginName
    }
  },
  async mounted() {
    await this.getAllChannelCapability(this.sns)
    this.getShareList()
  },
  computed: {},
  methods: {
    back() {
      appClose()
    },
    // pullingUp(callback) {
    //   this.getShareList({ type: 'up', callback })
    // },
    // pullingDown(callback) {
    //   this.listParams.pageNum = 1
    //   this.getShareList({ type: 'down', callback })
    // },
    // 获取当前可分享通道
    // 获取可以分享给某用户的设备
    async getShareDeviceList(params) {
      try {
        const { snList } = params
        // snList无数据不用请求了
        if (snList.length === 0) {
          this.isInitReq = true
          return
        }
        const res = await getResourceDeviceList(params)
        // console.log('res', res)
        const { data = [] } = res
        if (data.length) {
          // 说明没有分享超过10个用户
          this.shareOverTen = false
        } else {
          this.shareOverTen = true
        }
      } catch (err) {
        console.error(err)
      }
    },
    // 查询申请分享的的设备列表
    async getShareList({ callback } = {}) {
      try {
        // 没有对应id直接返回
        if (!this.shareIds.length) {
          this.initRequest = true
          return
        }
        this.$loading.show()
        const params = { shareIds: this.shareIds, queryType: 1 }
        const res = await getUserShareList(params)
        const { shareList = [], loginName } = res.data[0] || {}
        this.initRequest = true
        this.shareList = shareList.slice()
        if (shareList.every(item => item.status === 0)) {
          this.recordStatus = 0 // 全部为待接收则是待接收
        } else if (shareList.every(item => item.status === 2)) {
          this.recordStatus = 2 // 全部拒绝才是拒绝
        } else {
          // 部分/全部接受为已接受
          this.recordStatus = 1
        }
        this.createChannelTree(shareList)
        const params2 = {
          snList: this.sns,
          targetUserName: loginName
        }
        if (shareList.length) {
          // 有分享记录才去查是否分享超过10个人
          this.getShareDeviceList(params2)
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        callback && callback()
      }
    },
    // 获取所有设备的能力集
    async getAllChannelCapability(snList) {
      try {
        // 获取所有通道的详情
        const resArr = await Promise.all(snList.map(sn => getDeviceDetail({ sn, returnChl: true })))
        const capabilityObj = {}
        resArr.forEach(res => {
          const { data } = res
          const { chlInfos = [] } = data
          chlInfos.forEach(chlItem => {
            const { sn, chlIndex, capability } = chlItem
            // // 找到能力集
            const supportFun = capability && capability.supportFun ? capability.supportFun : null
            if (supportFun) {
              capabilityObj[`${sn}~${chlIndex}`] = supportFun
            }
          })
        })
        this.channelSupportFunObj = capabilityObj
        // 根据结果构建
      } catch (err) {
        console.error(err)
      }
    },
    // 构建设备、通道的树形结构
    createChannelTree(allChannelList) {
      // 根据站点通道列表处理成按照设备->通道分类的结构
      const deviceChannelList = []
      const deviceIndexObj = {} // 记录设备sn及其在通道树中的索引
      const channelObj = {} // 记录设备sn-通道chlIndex及其对应的通道信息
      const checkChannels = []
      allChannelList.forEach(item => {
        const { sn, devName, chlIndex, auth } = item
        // 过滤出通道支持的能力
        const supportFun = this.channelSupportFunObj[`${sn}~${chlIndex}`] || []
        const capabilityOptions = this.channelCapabilitys.filter(item => {
          if (item.filterAble) {
            const authArr = item.supportAuth ? [item.supportAuth] : item.value.split(',') // 针对现场和回放这种多合一权限的
            return authArr.every(authItem => supportFun.includes(authItem))
          }
          return true
        })
        let checkCapability = capabilityOptions.slice()
        // 过滤出通道勾选的能力
        if (auth) {
          const authList = JSON.parse(auth)
          checkCapability = checkCapability.filter(item => {
            const authArr = item.value.split(',') // 针对现场和回放这种多合一权限的
            return authArr.every(authItem => authList.includes(authItem))
          })
          item.auth = authList
          item.authList = authList
        }
        // 通道能力集选项及勾选的能力集
        item.capabilityOptions = capabilityOptions.slice()
        item.checkCapability = checkCapability
        channelObj[`${sn}~${chlIndex}`] = { ...item }
        let temp = null
        // 判断设备
        if (deviceIndexObj[sn] !== undefined) {
          // 说明设备存在
          const index = deviceIndexObj[sn]
          temp = deviceChannelList[index]
          temp.children = deviceChannelList[index].children || []
          temp.children.push({ ...item })
        } else {
          // 说明设备不存在，直接添加
          temp = { sn, deviceName: devName, children: [{ ...item }] }
          deviceIndexObj[sn] = deviceChannelList.length // 记录下站点的索引
          // 继续添加通道
          deviceChannelList.push(temp)
        }
        if (this.recordStatus === 0 || this.recordStatus === 2) {
          // 表示待接收/已拒绝，默认全部勾选
          checkChannels.push({ ...item })
        } else if (this.recordStatus === 1 && item.status === 1) {
          // 已接受则只添加接受状态的
          checkChannels.push({ ...item })
        }
      })
      this.channelObj = channelObj
      this.deviceChannelList = deviceChannelList
      this.checkChannels = checkChannels
    },
    // 编辑权限
    async handleChange(record, callback) {
      // 修改设备的权限
      const { checkCapability = [] } = record
      const auth = checkCapability.reduce((pre, next) => {
        return pre.concat(next.value.split(','))
      }, [])
      // 设置成功
      record.auth = auth.slice()
      if (callback) callback('success')
    },
    // 全部拒绝
    async handleReject() {
      if (!this.deviceChannelList[0] || !this.deviceChannelList[0].children.length) {
        this.$toast(this.$t('channelEmpty'))
        return
      }
      this.$loading.show()
      try {
        const params = this.deviceChannelList[0].children.map(item => {
          console.log('item', item)
          // 直接使用默认权限
          const { ownerId, id, authList, recipientId } = item
          return {
            ownerId,
            shareId: id,
            auth: authList,
            status: 2,
            recipientId
          }
        })
        await agreeRefuseShareChannel(params)
        this.recordStatus = 2 // 拒绝
      } catch (err) {
        console.error(err)
      } finally {
        this.$loading.hide()
      }
    },
    // 同意分享
    async handleAgree() {
      // console.log('checkChannels', this.checkChannels)
      if (!this.deviceChannelList[0] || !this.deviceChannelList[0].children.length) {
        this.$toast(this.$t('channelEmpty'))
        return
      }
      if (!this.checkChannels.length) {
        this.$toast(this.$t('pleaseChooseChannel'))
        return
      }
      // console.log('this.checkChannels', this.checkChannels)
      this.$loading.show()
      try {
        // 过滤出勾选的
        const params = this.deviceChannelList[0].children.map(item => {
          const { ownerId, id, auth } = item
          const channelItem = this.checkChannels.find(item2 => item2.sn === item.sn && item2.chlIndex === item.chlIndex)
          if (channelItem) {
            const { ownerId, id, checkCapability, recipientId } = channelItem
            // 选中通道，接受并使用新的权限
            const newAuth = checkCapability.reduce((pre, next) => {
              return pre.concat(next.value.split(','))
            }, [])
            return {
              ownerId,
              shareId: id,
              auth: newAuth,
              status: 1,
              recipientId
            }
          }
          // 未选中的通道，使用默认auth数组，并传拒绝状态
          return {
            ownerId,
            shareId: id,
            auth,
            status: 2
          }
        })
        await agreeRefuseShareChannel(params)
        this.recordStatus = 1 // 接受
      } catch (err) {
        console.error(err)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.share-apply-wrapper {
  height: 100%;
  overflow: hidden;
  position: relative;
  .menu {
    position: absolute;
    top: 44px;
    right: 10px;
  }
  .share-apply-content {
    width: 100%;
    height: calc(100% - 130px);
    overflow: auto;
    box-sizing: border-box;
    .shareApply-text-box {
      width: 100%;
      height: 68px;
      padding: 10px 24px;
      box-sizing: border-box;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      text-align: center;
      display: flex;
      align-items: center;
      .shareApply-text {
        text-align: left;
        overflow: hidden; // 超出的文本隐藏
        text-overflow: ellipsis; // 溢出用省略号显示
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
    .share-apply-list-wrapper {
      width: 100%;
      height: calc(100% - 68px);
      .share-apply-list-title {
        width: 100%;
        height: 60px;
        padding: 18px 14px 0 28px;
        box-sizing: border-box;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: bold;
        line-height: 22px;
      }
      .share-apply-list-content {
        width: 100%;
        height: calc(100% - 60px);
        overflow: auto;
      }
    }
    .device-check-line {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .no-data {
    width: 100%;
    height: calc(100% - 70px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 120px;
        height: 123px;
      }
      .theme-image-container {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      margin-top: 20px;
    }
  }
}
.footer {
  .footer-btn-short {
    width: 150px;
    margin: 0px 20px;
  }
}
</style>
