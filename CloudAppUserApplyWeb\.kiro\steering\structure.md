# Project Structure

## Root Directory
```
├── src/                    # Source code
├── public/                 # Static assets
├── .kiro/                  # Kiro IDE configuration
├── .promptx/               # PromptX configuration
├── node_modules/           # Dependencies
├── package.json            # Project configuration
├── vue.config.js           # Vue CLI configuration
└── README.md               # Project documentation
```

## Source Code Organization (`src/`)

### Core Application Files
- `main.js` - Application entry point with initialization logic
- `App.vue` - Root Vue component

### Feature Directories
```
src/
├── api/                    # API service layer
├── assets/                 # Static assets (CSS, images, fonts)
├── components/             # Reusable Vue components
├── lang/                   # Internationalization files
├── layout/                 # Layout components
├── plugins/                # Vue plugins configuration
├── router/                 # Vue Router configuration
├── store/                  # Vuex state management
├── utils/                  # Utility functions and helpers
└── views/                  # Page-level Vue components
```

## Key Conventions

### API Layer (`src/api/`)
- One file per feature domain (e.g., `alarmSystem.js`, `device.js`)
- `request.js` contains HTTP client configuration
- `index.js` exports common API utilities

### Components (`src/components/`)
- Reusable UI components
- Global components registered in `main.js`
- Theme-aware components (ThemeImage, ThemeSvg)

### Views (`src/views/`)
- Organized by feature domains
- Each feature has its own subdirectory
- Components specific to a view stored in `components/` subdirectory

### Utilities (`src/utils/`)
- Domain-specific utilities (e.g., `alarmSystem.js`)
- Common helpers in `common.js`
- App bridge integration in `appbridge.js`

### Internationalization (`src/lang/`)
- Organized by app type and UI theme
- Common translations in `common/`
- Theme-specific translations in respective directories

### State Management (`src/store/`)
- Modular Vuex structure
- Feature modules in `modules/` directory
- Global getters in `getters.js`

## Naming Conventions
- **Files**: kebab-case for multi-word files
- **Components**: PascalCase for Vue components
- **Variables**: camelCase for JavaScript variables
- **Constants**: UPPER_SNAKE_CASE for constants

## Import Aliases
Configured in `vue.config.js`:
- `@` → `src/`
- `assets` → `src/assets/`
- `api` → `src/api/`
- `views` → `src/views/`
- `components` → `src/components/`