<!--云存储支付结果页-->
<template>
  <div class="pay-result-page">
    <!-- <nav-bar @clickLeft="back" v-show="isAndroid"></nav-bar> -->
    <van-loading class="loading-bg" type="spinner" size="24px" v-if="pageLoading" />
    <div v-else>
      <div class="main-content">
        <div class="result-box">
          <div class="result-icon" v-if="realPayStatus"><van-image :src="successDataImg" /></div>
          <div class="result-icon" v-else><van-image :src="failDataImg" /></div>
          <div class="result-label">{{ realPayStatus ? $t('paySuccess') : $t('payFail') }}</div>
        </div>
      </div>
      <div class="footer">
        <div class="operation-btn" @click="close">{{ realPayStatus ? $t('done') : $t('rePurchase') }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { appSetTitle, appBack, appClose, appSetBackToClose, appPayDismiss } from '@/utils/appbridge'
import { failCallback, successCallback } from '@/api/pay'
import { appLog } from '@/utils/appbridge'
export default {
  name: 'LicensePayRes',
  components: {},
  data() {
    return {
      query: {},
      pageLoading: false,
      showRes: false,
      realPayStatus: 0, //支付结果成功1 or 失败0
      // isAndroid: 0 //是否安卓
      successDataImg:
        'data:image/png;base64,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',
      failDataImg:
        'data:image/png;base64,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'
    }
  },
  mounted() {
    appSetTitle(this.$t('cloudStorage'))
    this.init()
  },
  methods: {
    init() {
      appSetBackToClose(1)
      this.handleCallback()
    },
    back() {
      appBack()
    },
    close() {
      appClose()
      appPayDismiss({ payResult: this.realPayStatus ? 'paySuccess' : 'payFailed' })
    },
    failTips(val) {
      if (val == 'INSTRUMENT_DECLINED') {
        this.$toast(this.$t('INSTRUMENT_DECLINED'))
      } else if (val == 'PAYER_ACCOUNT_LOCKED_OR_CLOSED') {
        this.$toast(this.$t('PAYER_ACCOUNT_LOCKED_OR_CLOSED'))
      } else if (val == 'PAYER_ACCOUNT_RESTRICTED') {
        this.$toast(this.$t('PAYER_ACCOUNT_RESTRICTED'))
      } else if (val == 'TRANSACTION_LIMIT_EXCEEDED') {
        this.$toast(this.$t('TRANSACTION_LIMIT_EXCEEDED'))
      } else if (val == 'TRANSACTION_RECEIVING_LIMIT_EXCEEDED') {
        this.$toast(this.$t('TRANSACTION_RECEIVING_LIMIT_EXCEEDED'))
      } else if (val == 'CARD_EXPIRED') {
        this.$toast(this.$t('errorCode.23024'))
      } else if (val == 'COMPLIANCE_VIOLATION') {
        this.$toast(this.$t('errorCode.23025'))
      } else if (val == 'fail ') {
        this.$toast(this.$t('fail'))
      }
    },
    handleCallback() {
      this.pageLoading = true
      this.query = this.$route.query
      const { payStatus, payType, localOrderNo } = this.query
      appLog('log/info', `${new Date()} 支付回调页面参数 ${JSON.stringify(this.query)}`)
      if (payStatus == 1) {
        successCallback({
          orderId: localOrderNo
        })
          .then(res => {
            this.pageLoading = false
            appLog('log/info', `${new Date()} 成功回调结果 ${JSON.stringify(res)}`)
            if (res && res.basic.code == 200) {
              this.realPayStatus = 1
            } else {
              if (payType == 'stripe') {
                this.failTips(res)
              }
            }
          })
          .catch(res => {
            this.pageLoading = false
            this.realPayStatus = 0
            if (res && res.basic.code == 200) {
              this.realPayStatus = 1
            } else {
              if (payType == 'stripe') {
                this.failTips(res)
              }
            }
          })
      } else {
        // 失败回调
        failCallback({
          orderId: localOrderNo
        })
          .then(res => {
            appLog('log/info', `${new Date()} 失败回调结果 ${JSON.stringify(res)}`)
            console.log('成功:', res)
          })
          .catch(res => {
            console.log('失败:', res)
          })
          .finally(() => {
            this.pageLoading = false
          })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.van-image {
  width: 90px;
  height: 90px;
}
.pay-result-page {
  width: 100%;
  position: relative;
  .main-content {
    position: relative;
    .result-box {
      width: 100%;
      position: absolute;
      top: 0%;
      left: 50%;
      transform: translate(-50%, 100%);
    }
    .result-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 23.5px;
    }
    .result-label {
      font-weight: 700;
      height: 20px;
      font-size: var(--font-size-h4-size, 20px);
      text-align: center;
      color: var(--icon-color-primary, #393939);
    }
  }
  .footer {
    position: fixed;
    bottom: 7%;
    width: 100%;
    height: 44px;
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .operation-btn {
      width: 320px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      background: var(--text-color-brand, #00baff);
      border-radius: 4px;
      color: var(--bg-color-white, #ffffff);
    }
  }
  .loading-bg {
    text-align: center;
    margin-top: 30%;
  }
}
</style>
