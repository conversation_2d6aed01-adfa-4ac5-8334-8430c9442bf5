<template>
  <div class="preset-point-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="preset-point-content">
      <div class="preset-point-list">
        <div class="preset-point-line">
          <div class="preset-point-left">
            <div class="preset-point-title">{{ $t('presetPoint') }}</div>
          </div>
          <div class="preset-point-right">
            <div class="preset-point-text">
              <span class="preset-ellipsis-text">{{ pointRecord.index }}</span>
              <img
                @click="handleEditPoint"
                class="arrow-img"
                :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
              />
            </div>
          </div>
        </div>
        <div class="preset-point-line">
          <div class="preset-point-left">
            <div class="preset-point-title">{{ $t('presetPointName') }}</div>
          </div>
          <div class="preset-point-right">
            <div class="preset-point-text preset-ellipsis-text">{{ pointRecord.name }}</div>
            <img
              @click="handleEditName"
              class="arrow-img"
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/arrow_right.png')"
            />
          </div>
        </div>
      </div>
      <div class="preset-point-footer">
        <div :class="['footer-btn', pointRecord.index === null ? 'footer-btn-disabled' : '']" @click="handleConfirm">
          {{ $t('confirm') }}
        </div>
      </div>
    </div>
    <!-- 编辑预置点名称 -->
    <edit-point-name ref="editPointName" :name="pointRecord.name" @confirm="pointNameConfirm"></edit-point-name>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import EditPointName from './EditPointName.vue'
import { mapState, mapMutations } from 'vuex'
import { appBack, onNotificationToApp, appRequestDevice } from '@/utils/appbridge'
import { transformXml, getUrlQuery } from '@/utils/common'
import { urlChlPresetList, urlCreateChlPreset, urlPtzGetPresets, urlPtzModifyPresetPosition } from '@/api/presetPoint'
export default {
  name: 'addEditPoint',
  components: {
    NavBar,
    EditPointName
  },
  data() {
    return {
      devId: '',
      chlId: null,
      bindState: 0 // SN和安全码 添加的   bindState是1     ip添加的 bindState是0  IP添加的设备升级是要输入账号密码
    }
  },
  async mounted() {
    const json = getUrlQuery(window.location.href)
    this.devId = json.devId
    this.bindState = json.bindState
    this.chlId = decodeURIComponent(decodeURIComponent(json.chlId))
    if (!this.initStatus) {
      this.SET_INIT_STATUS(true)
      this.SET_DEV_TYPE(json.devType)
      this.SET_CHL_INDEX(json.chlIndex)
      this.getChlPresetList()
    }
  },
  computed: {
    ...mapState('app', ['style', 'language', 'appType']),
    ...mapState('presetPoint', ['initStatus', 'devType', 'chlIndex', 'pointList', 'pointRecord']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('presetPoint', [
      'SET_INIT_STATUS',
      'SET_DEV_TYPE',
      'SET_CHL_INDEX',
      'SET_POINT_RECORD',
      'SET_POINT_LIST',
      'SET_POINT_OPTIONS'
    ]),
    back() {
      appBack()
    },
    // 获取当前通道下所有的预置点列表
    getChlPresetList() {
      const that = this
      const req = {
        devId: this.devId,
        url: 'queryChlPresetList',
        params: urlChlPresetList(this.chlId)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = 'PtzGetPresets_I'
        req.params = urlPtzGetPresets()
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          if (that.devType === '1') {
            // IPC设备--单独处理
            const config = xmlObject.config
            if (config && config.presetInfo) {
              const { item = [] } = config.presetInfo
              let presets = []
              if (Array.isArray(item)) {
                // 数组说明是多个
                presets = item
              } else if (typeof item === 'object' && item !== null) {
                //对象说明是单个对象
                presets = [item]
              }
              // 遍历presets，生成已有的预置点列表
              const presetPointList = []
              const hasPointSet = new Set()
              presets.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_id']
                }
                hasPointSet.add(item['_id'])
                presetPointList.push(temp)
              })
              that.SET_POINT_LIST(presetPointList)
              // 根据已有的预置点，生成待选的预置点
              const pointOptions = new Array(255)
                .fill(0)
                .map((item, index) => ({
                  label: index + 1,
                  value: String(index + 1)
                }))
                .filter(item2 => !hasPointSet.has(item2.value))
              that.SET_POINT_OPTIONS(pointOptions)
              if (pointOptions.length) {
                // 取预置点第一个选项作为默认新增的
                const { value } = pointOptions[0]
                const pointRecord = {
                  name: `PRESET${String(value).padStart(3, 0)}`,
                  index: value
                }
                that.SET_POINT_RECORD(pointRecord)
              } else {
                // 否则index不赋值
                const pointRecord = {
                  name: 'PRESET',
                  index: null
                }
                that.SET_POINT_RECORD(pointRecord)
              }
            }
          } else {
            if (xmlObject.response.status == 'success') {
              // 处理设备
              const content = xmlObject.response.content
              // console.log('已有预置点内容', content)
              let presets = []
              if (content && content.presets) {
                const { item = [] } = content.presets
                if (Array.isArray(item)) {
                  // 数组说明是多个
                  presets = item
                } else if (typeof item === 'object' && item !== null) {
                  //对象说明是单个对象
                  presets = [item]
                }
              }
              // 遍历presets，生成已有的预置点列表
              const presetPointList = []
              const hasPointSet = new Set()
              presets.forEach(item => {
                const temp = {
                  name: item['#cdata-section'],
                  index: item['_index']
                }
                hasPointSet.add(item['_index'])
                presetPointList.push(temp)
              })
              that.SET_POINT_LIST(presetPointList)
              // 根据已有的预置点，生成待选的预置点
              const pointOptions = new Array(255)
                .fill(0)
                .map((item, index) => ({
                  label: index + 1,
                  value: String(index + 1)
                }))
                .filter(item2 => !hasPointSet.has(item2.value))
              that.SET_POINT_OPTIONS(pointOptions)
              if (pointOptions.length) {
                // 取预置点第一个选项作为默认新增的
                const { value } = pointOptions[0]
                const pointRecord = {
                  name: `preset${value}`,
                  index: value
                }
                // IPC的预置点默认名称是PRESET001这种形式的
                if (that.devType === '1') {
                  pointRecord.name = `PRESET${String(value).padStart(3, 0)}`
                }
                that.SET_POINT_RECORD(pointRecord)
              } else {
                // 否则index不赋值
                const pointRecord = {
                  name: 'preset',
                  index: null
                }
                that.SET_POINT_RECORD(pointRecord)
              }
            }
          }
        }
      })
    },
    // 编辑预置点，将预置点信息传过去
    handleEditPoint() {
      this.SET_POINT_RECORD({ ...this.pointRecord })
      this.$router.push({ name: 'choosePoint' })
    },
    handleEditName() {
      if (this.devType === '1') {
        // IPC的预置点不能改名字
        return
      }
      this.$refs.editPointName.show = true // 编辑区域名称
    },
    pointNameConfirm(val) {
      this.$refs.editPointName.show = false
      const pointRecord = {
        ...this.pointRecord,
        name: val
      }
      this.SET_POINT_RECORD(pointRecord)
    },
    handleConfirm() {
      if (this.pointRecord.index === null) {
        return
      }
      const that = this
      const { name, index } = this.pointRecord
      // 不用考虑跟自己重名的问题
      const nameSet = new Set(
        this.pointList.filter(item => item.index !== this.pointRecord.index).map(item => item.name)
      )
      // 判断是否重名
      if (nameSet.has(name)) {
        this.$toast(this.$t('pointNameExist'))
        return false
      }
      const req = {
        devId: this.devId,
        url: 'createChlPreset',
        params: urlCreateChlPreset(index, name, this.chlId)
      }
      if (this.devType === '1') {
        // IPC设备
        req.url = `PtzModifyPresetPosition_I/${this.chlIndex}`
        req.params = urlPtzModifyPresetPosition(index)
      }
      appRequestDevice(req, function (res) {
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          let resFlag = false
          if (that.devType === '1') {
            // IPC设备
            if (xmlObject.config._status === 'success') {
              resFlag = true
            } else {
              that.$toast(that.$t('addFail'))
              return
            }
          } else {
            if (xmlObject.response.status == 'success') {
              resFlag = true
            } else {
              that.$toast(that.$t('addFail'))
              return
            }
          }
          if (resFlag) {
            // 消息提示的回调
            const closeFn = () => {
              // 先通知APP
              onNotificationToApp('kAddPointNotifycation')
              // 再走返回
              appBack()
            }
            // 添加成功后，返回
            that.$toast({
              message: that.$t('addSuccess'),
              forbidClick: true,
              onClose: closeFn
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.preset-point-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.preset-point-content {
  width: 100%;
  height: calc(100% - 125px);
  padding: 35px 16px;
  box-sizing: border-box;
  overflow-y: auto;
}

.preset-point-list {
  width: 100%;
  border-radius: 10px;
  .preset-point-line {
    width: 100%;
    height: 46px;
    box-sizing: border-box;
    padding: 11px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .preset-point-left {
      height: 100%;
      display: flex;
      align-items: center;
      .preset-point-title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 24px;
      }
    }
    .preset-point-right {
      flex: 1;
      height: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      overflow: hidden;
      .preset-point-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: var(--font-size-body1-size, 16px);
        text-align: right;
        line-height: 24px;
        margin-left: 20px;
      }
      .preset-ellipsis-text {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.preset-point-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px 0;
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: calc(100% - 32px);
    height: 46px;
    border-radius: 10px;
    text-align: center;
    color: var(--bg-color-white, #ffffff);
  }
}
</style>
<style lang="scss">
.preset-point-wrapper .nav-bar .nav-bar-center {
  font-size: 16px !important;
  font-weight: 600;
}
</style>
