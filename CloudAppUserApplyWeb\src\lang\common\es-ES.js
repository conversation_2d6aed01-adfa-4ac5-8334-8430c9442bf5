// 西班牙语
export default {
  upgrade: 'Actualización del cloud',
  cancel: 'Cancelar',
  confirm: 'Aplicar',
  deviceUpdate: 'Dispositivo',
  cameraUpdate: 'Cá<PERSON>',
  allUpdate: 'Actualizar todo',
  updateNow: 'Actualizar',
  currentVersion: 'Versión actual',
  latestVersion: 'Última versión',
  updateContent: 'Información de la actualización',
  hasLatestVersion: 'Última versión',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Esperando descarga',
  inprogress: 'Descargando',
  downloadFail: 'Error en descarga',
  downloadFinished: 'Descarga completa',
  inupgrade: 'Actualizando',
  upgradeFail: 'Actualización fallida',
  upgradeSuccess: 'Actualización exitosa',
  deviceUpgradeInfo:
    'Durante la actualización, el dispositivo se desconectará y automáticamente se reiniciará. ¿Seguro que desea actualizar?',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo:
    'Durante la actualización, el dispositivo se desconectará y automáticamente se reiniciará. ¿Seguro que desea actualizar?',
  pwdUserNameError: 'Error de nombre de usuario o contraseña',
  permissionAuth: 'Autenticación de superadministrador',
  pleaseEnterUser: 'Por favor ingrese el nombre de usuario',
  pleaseEnterPwd: 'Introduzca contraseña',
  noCameraUpgrade: 'No hay ninguna cámara pendiente de actualizar',
  handleCheck: 'Actualizar detección',
  done: 'Terminar',
  cloudStorage: 'Almacenamiento en la nube',
  myInstaller: 'Mi instalador',
  trusteeshipDevice: 'Dispositivo vinculado',
  addTrusteeship: 'Añadir vinculación',
  waitReceived: 'Para ser recibido',
  delete: 'Eliminar',
  operationSuccess: 'Operación realizada con éxito',
  operationFail: 'Operación fallida',
  cancelTrusteeship: 'Cancelar permanencia',
  chooseDevice: 'Seleccionar dispositivo',
  noAvaiableDevice:
    'La versión del dispositivo admite la función de vinculación y sólo puede asignarse añadiendo dispositivos mediante la vinculación',
  leastChoose: 'Seleccione al menos un dispositivo',
  details: 'Detalle',
  live: 'Directo',
  rec: 'Playback',
  config: 'Configuración',
  confirmTrusteeshipTip:
    'La solicitud de vinculación ha sido enviada al instalador, por favor espere a que el instalador la procese.',
  cancelTrusteeshipTip:
    'Después de cancelar la vinculación, el instalador no puede proporcionarle servicios de mantenimiento remoto. ¿Está seguro de cancelar?',
  unBindTrusteeship:
    'Tras la desvinculación, se cancelará la vinculación de todos los dispositivos. Estás seguro de que quieres desvincularte?',
  trusteeshipPermissions: 'Permisos gestionados',
  trusteeshipTime: 'Tiempo de vinculación: ',
  unBind: 'Desvincular',
  loosing: 'Refrescar...',
  checkSuccess: 'Comprobado con éxito',
  checkFail: 'Comprobación fallida',
  viewUpdateContent: 'Ver contenido actualizado',
  deviceDisconnected: 'Error al conectar el dispositivo',
  updateNote: 'Nota de actualización',
  noData: 'Sin datos',
  paySuccess: 'Pago completado',
  payFail: 'Pago fallido',
  rePurchase: 'Volver a comprar',
  INSTRUMENT_DECLINED: 'La transacción supera el límite de la tarjeta',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'La cuenta del pagador no puede utilizarse para esta transacción',
  PAYER_ACCOUNT_RESTRICTED: 'La cuenta del pagador está restringida',
  TRANSACTION_LIMIT_EXCEEDED: 'El pago total supera el límite de la transacción',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'La transacción supera el límite de recepción del destinatario',
  // 以下暂由公司人员翻译 下次客户翻译再更改
  received: 'Recibido',
  refuse: 'Rechazo',
  serviceException: 'Excepción de servicio',
  pullingText: 'Liberar para cargar',
  loosingText: 'Liberar para refrescar...',
  loadingText: 'Cargando...',
  refreshComplete: 'Actualizar con éxito',
  noMore: 'No más',
  errorCode: {
    *********: 'Operación fallida, por favor compruebe el estado del dispositivo', //'设备忙，请稍后重试', //设备忙（互斥）关系的错误码和对应的词条
    *********: 'Operación fallida, por favor compruebe el estado del dispositivo', //'版本不匹配', // APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令告诉了一个更新的版本。此时APP触发升级NVR的话，就会返回
    536871082: 'Operación fallida, por favor compruebe el estado del dispositivo', //'无新版本',
    536871083: 'Operación fallida, por favor compruebe el estado del dispositivo', //'云升级版本不存在', //APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令取消掉了版本。此时APP触发升级NVR的话，就会返回
    536870940: 'Operación fallida, por favor compruebe el estado del dispositivo', //'设备未开启云升级功能', //云升级未开启
    536870934: 'Operación fallida, por favor compruebe el estado del dispositivo', //统一的模糊的提示
    536871060: 'Operación fallida, por favor compruebe el estado del dispositivo',
    536871030: 'Sin disco duro',
    10000: 'Error al conectar el dispositivo',
    12344: 'Error en conexión de red',
    12345: 'Tiempo de espera de conexión de red',
    400: 'Error de parametro',
    32018: 'Los datos no existen',
    32021: 'Los datos no existen',
    32022: '{0} el dispositivo y el instalador no se encuentran en el mismo país/región y no admiten la vinculación',
    // 以下暂由公司人员翻译 下次客户翻译再更改
    536870947: 'Nombre de usuario no existe',
    536870948: 'Error de nombre de usuario o contraseña',
    550: 'Tiempo de solicitud expirado',
    23024: 'La tarjeta de pago proporcionada ha caducado',
    23025: 'La transacción ha sido rechazada debido a una infracción',
    404: 'El recurso solicitado (página web, etc.) no existe.',
    500: '¡Excepción del sistema!',
    502: 'Error en la solicitud al servidor',
    503: 'Excepción de servidor',
    504: 'Tiempo de solicitud del servidor expirado'
  }
}
