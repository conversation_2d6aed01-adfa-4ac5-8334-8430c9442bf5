export const OCX_XML_Header = "<?xml version='1.0' encoding='utf-8'?>"

// 查询NVR云升级信息 getCloudUpgradeInfo
export const urlGetCloudUpgradeInfo = () => {
  return ''
}

//设备升级  包括 用户名密码添加的设备进行云升级 cloudUpgrade
export const urlCloudUpgrade = (versionGUID, auth, userName, password) => {
  var result = ''
  result += '<condition>'
  result += '<versionGUID>' + versionGUID + '</versionGUID>'
  result += '</condition>'
  if (auth) {
    result += '<auth>'
    result += '<userName>' + userName + '</userName>'
    result += '<password>' + password + '</password>'
    result += '</auth>'
  }
  return result
}

//云升级IPC  cloudUpgradeNode
export const urlCloudUpgradeNode = list => {
  var result = ''
  result += '<condition><chls>'
  let itemStr = ''
  list.forEach(v => {
    itemStr += '<item id="' + v._id + '">' + v.newVersionGUID + '</item>'
  })
  result += itemStr
  result += '</chls></condition>'
  return result
}

// 检测更新 checkVersion
export const urlCheckVersion = () => {
  var result = ''
  result += '<condition><dev>true</dev><chls>'
  result += '</chls></condition>'
  return result
}

// 云后台-云升级
import request from '@/api/request'
// import baseUrl from '@/api/index.js'
const baseUrl = '/vms'
// 云升级版本检查接口
export const getCloudUpgradeInfo = data => request.post(`${baseUrl}/device-upgrade-version/get`, data)

// 执行云升级
export const cloudUpgrade = data => request.post(`${baseUrl}/device/upgrade`, data)

// 云升级结果查询
export const getUpgradeResult = data => request.post(`${baseUrl}/device-upgrade-result/get`, data)

// 获取设备通道信息
export const getChanneList = data => request.post(`${baseUrl}/channel/list/detail`, data)
