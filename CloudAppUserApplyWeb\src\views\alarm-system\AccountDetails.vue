<template>
  <div class="account-details">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" />
    <div class="account-details-content">
      <div class="account-details-header">
        <div class="account-title">{{ $t('accountDetails') }}</div>
      </div>

      <div class="account-info">
        <div class="user-email">{{ userEmail }}</div>
        <div class="account-actions">
          <van-button type="primary" class="alarm-action-btn" @click="handleLogOut">
            {{ $t('logout') }}
          </van-button>
          <van-button type="primary" class="alarm-action-btn" @click="handlePanelList">
            {{ $t('panelList') }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <van-dialog
      v-model="showLogoutDialog"
      :title="$t('confirmLogout')"
      :message="$t('confirmLogoutTips')"
      show-cancel-button
      @confirm="confirmLogout"
      @cancel="showLogoutDialog = false"
    />
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  name: 'AccountDetails',
  data() {
    return {
      showLogoutDialog: false
    }
  },
  computed: {
    ...mapState('alarmSystem', ['systemType', 'userInfo']),
    ...mapGetters('alarmSystem', ['isRiscoSystem']),
    userEmail() {
      return this.userInfo?.userName || ''
    }
  },
  methods: {
    ...mapActions('alarmSystem', ['clearRiscoLoginInfo', 'clearAllPanelData']),
    goBack() {
      this.$router.push({
        path: '/alarmSystem/alarmLogin',
        query: {
          systemType: this.systemType
        }
      })
    },
    handleLogOut() {
      this.showLogoutDialog = true
    },
    handlePanelList() {
      // 跳转到面板列表页面
      this.$router.push({
        path: '/alarmSystem/panelList',
        query: {
          systemType: this.systemType
        }
      })
    },
    async confirmLogout() {
      this.showLogoutDialog = false

      try {
        this.$loading.show()

        if (this.isRiscoSystem) {
          // Risco系统：直接清除登录信息，无需调用API
          await this.performRiscoLogout()
        } else {
          // 其他系统：只清除本地数据
          await this.clearAllPanelData()
        }

        this.$loading.hide()
        this.$toast.success(this.$t('loggedOutSuccess'))

        // 跳转到选择系统界面
        this.$router.replace(`/alarmSystem/chooseSystem`)
      } catch (error) {
        this.$loading.hide()
        console.error('Logout error:', error)
        this.$toast.fail(this.$t('loginFailed'))
      }
    },
    /**
     * 执行Risco系统退出登录
     * 直接清除store中的认证信息，不调用API
     */
    async performRiscoLogout() {
      try {
        // 清除Risco登录信息
        await this.clearRiscoLoginInfo()

        // 清除所有面板相关数据
        await this.clearAllPanelData()

        console.log('✅ Risco用户退出登录成功')
      } catch (error) {
        console.error('Failed to clear Risco login info:', error)
        // 即使清除失败，也要确保跳转到登录页
        throw error
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-details {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }

  &-header {
    margin-bottom: 8px;

    .account-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
    }
  }

  .account-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-email {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    flex: 1;
  }

  .account-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-left: 20px;

    .van-button {
      width: 106px;
      height: 32px;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 400;
      border: none;
      min-width: 100px;
      padding: 0 16px;
    }
  }
}

// 自定义对话框样式
::v-deep .van-dialog {
  .van-dialog__header {
    color: #333;
    font-weight: 600;
  }

  .van-dialog__message {
    color: #666;
    text-align: center;
  }

  .van-dialog__footer {
    .van-button {
      border: none;

      &.van-button--default {
        color: #666;
      }

      &.van-button--primary {
        background-color: #1989fa;
      }
    }
  }
}
</style>
