<template>
  <div class="defense-deployment-wrapper">
    <nav-bar @clickLeft="back">
      <template #right>
        <!-- 分组超过50则添加按钮置灰 -->
        <div class="nav-bar-right-icon" @click="handleAdd">
          <van-icon name="plus" :class="addDisabled ? 'icon-disabled' : ''" />
        </div>
      </template>
    </nav-bar>
    <div class="defense-deployment-content">
      <tvt-better-scroll
        class="tvt-better-scroll"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <defense-list
          v-if="!reqDefenseList || defenseGroupList.length"
          :dataList="defenseGroupList"
          @handleClick="handleClick"
          @handleEdit="handleEdit"
          @handleDelete="handleDelete"
        />
        <div class="no-data" v-else>
          <div class="no-data-img">
            <img alt="noData" :src="require('@/assets/img/common/no_defense_data.png')" />
          </div>
          <div class="no-data-text">{{ $t('noGroupDesc') }}</div>
        </div>
      </tvt-better-scroll>
    </div>
    <div class="footer" v-if="!reqDefenseList || defenseGroupList.length">
      <div class="footer-icon-list">
        <div class="footer-icon-item" @click="handleBtnClick(2)">
          <theme-image class="footer-icon" alt="home-defense-icon" imageName="home_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(1)">
          <theme-image class="footer-icon" alt="out-defense-icon" imageName="out_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(0)">
          <theme-image class="footer-icon" alt="cancel-defense-icon" imageName="cancel_defense_icon.png" />
        </div>
        <div class="footer-icon-item" @click="handleBtnClick(3)">
          <theme-image class="footer-icon" alt="cancel-warn-icon" imageName="cancel_warn_icon.png" />
        </div>
      </div>
    </div>
    <div class="footer2" v-else>
      <van-button class="footer-btn" type="primary" @click="handleAdd">
        {{ $t('add') }}
      </van-button>
    </div>
    <!-- 一键布防/撤防 弹窗 -->
    <one-click-defense
      ref="oneClickDefense"
      :defenseType="defenseType"
      :reqStatusList="reqStatusList"
      @cancel="defenseCancel"
      @confirm="defenseConfirm"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import OneClickDefense from '../defense/OneClickDefense.vue'
import DefenseList from './components/DefenseList.vue'
import ThemeImage from '@/components/ThemeImage.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import { transformXml, debounce, converImageToBase64 } from '@/utils/common'
import { appClose, appRequestDevice, appSetWebBackEnable, refreshDefenseState } from '@/utils/appbridge'
import {
  getDefenseGroupList,
  getDefenseDetail,
  addDefenseGroup,
  urlDisalarmSwitchNodes,
  deleteDefenseRecord
} from '@/api/maxDefense'
export default {
  name: 'ShareManage',
  components: {
    NavBar,
    OneClickDefense,
    DefenseList,
    ThemeImage
  },
  props: {},
  data() {
    return {
      pageNum: 1,
      pullingStatus: 0,
      defenseType: 0, // 布防/撤防 0为一键撤防 1表示外出布防 2表示在家布防 3表示一键消警
      reqStatusList: [], // 各个分组布防撤防状态
      reqDefenseList: false // 是否初始化请求过数据
    }
  },
  created() {
    appSetWebBackEnable(false)
    // 从路由中获取设备sn和deviceId,用作请求设备的通道列表
    const query = this.$route.query
    // console.log('query', query)
    if (query.sn) {
      const { sn, devId, addType } = query
      this.SET_SN(sn)
      this.SET_DEVID(devId)
      this.SET_ADD_TYPE(addType)
    }
  },
  beforeDestroy() {
    // 页面退出时通知APP刷新设备布撤防状态
    refreshDefenseState()
  },
  mounted() {
    if (!this.initStatus) {
      this.getChannelList() // 获取所有通道
      this.SET_INIT_STATUS(true)
    }
    this.getDefenseList() // 获取所有布防撤防列表
    this.loadNoDataImg()
  },
  computed: {
    ...mapState('maxDefense', [
      'initStatus',
      'noDataImg',
      'sn',
      'devId',
      'addType',
      'defenseGroupList',
      'groupChannelList',
      'capabilityObj',
      'ipcLinkageList',
      'allChannelList'
    ]),
    addDisabled() {
      // 判断分组数量是否大于50
      return this.defenseGroupList && this.defenseGroupList.length >= 50
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getChannelList', 'updateDeviceStatus', 'updateDeviceMutexStatus']),
    ...mapMutations('maxDefense', [
      'SET_INIT_STATUS',
      'SET_SN',
      'SET_DEVID',
      'SET_ADD_TYPE',
      'SET_NO_DATA_IMG',
      'SET_ALL_CHANNEL_LIST',
      'SET_CHANNEL_OBJ',
      'SET_CAPABILITY_OBJ',
      'SET_DEFENSE_GROUP_LIST',
      'SET_GROUP_CHANNEL_LIST',
      'SET_DEFENSE_RECORD',
      'SET_CHANNEL_LIST'
    ]),
    back() {
      // 页面退出时通知APP刷新设备布撤防状态
      refreshDefenseState()
      appClose()
    },
    pullingUp(callback) {
      this.getDefenseList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.pageNum = 1
      this.getDefenseList({ type: 'down', callback })
    },
    // 加载无数据图片
    loadNoDataImg() {
      // 将无数据图片储存起来
      const imgSrc = require('@/assets/img/common/no_defense_data.png')
      const callback = imgBase64 => {
        // console.log('imgBase64', imgBase64)
        this.SET_NO_DATA_IMG(imgBase64)
      }
      converImageToBase64(imgSrc, callback)
    },
    // 查询布防撤防列表
    async getDefenseList({ callback } = {}) {
      try {
        this.$loading.show()
        const res = await getDefenseGroupList({ sn: this.sn })
        const resData = res.data || []
        this.$loading.hide()
        // console.log('布防撤防列表', resData)
        // that.dataList = resData
        this.SET_DEFENSE_GROUP_LIST(resData)
        this.reqDefenseList = true
        // 遍历所有的分组，拿到groupIdList，请求所有的detail信息
        const ids = resData.map(item => item.id) || []
        if (ids.length) {
          this.getAllDetails(ids)
        } else {
          this.SET_GROUP_CHANNEL_LIST([])
        }
      } catch (error) {
        this.reqDefenseList = true
        this.$loading.hide()
        console.error(error)
      } finally {
        callback && callback()
      }
    },
    // 查询所有分组下面的通道
    async getAllDetails(ids) {
      const res = await getDefenseDetail({ ids })
      const resData = res.data || []
      this.SET_GROUP_CHANNEL_LIST(resData)
    },
    // 添加分组
    handleAdd() {
      // 判断分组数量是否超过50
      if (this.defenseGroupList && this.defenseGroupList.length >= 50) {
        this.$toast(this.$t('groupLimit', { limit: 50 }))
        return
      }
      this.SET_DEFENSE_RECORD({
        groupName: this.$t('areaGroup'),
        channelList: []
      })
      this.SET_CHANNEL_LIST([])
      this.$utils.routerPush({
        path: '/maxDefense/addEditDefense',
        query: { type: 'add' }
      })
    },
    // 编辑分组
    async handleEdit(item) {
      // 获取布防组详情
      try {
        this.$loading.show()
        const res = await getDefenseDetail({ ids: [item.id] })
        const channelList = []
        const { data = [] } = res
        data.forEach(item => {
          const { chlIndex, chlId, sn, extra } = item
          // 从channelList中找到对应的记录
          const channelItem = this.allChannelList.find(
            item2 => item2.sn === sn && item2.chlIndex === chlIndex && item2.chlId === chlId
          )
          if (channelItem) {
            const temp = {
              ...channelItem,
              ...item,
              extra: extra ? JSON.parse(extra) : null
            }
            channelList.push(temp)
          }
        })
        this.SET_DEFENSE_RECORD({
          ...item,
          channelList: channelList.slice()
        })
        this.SET_CHANNEL_LIST(channelList.slice())
        this.$nextTick(() => {
          this.$utils.routerPush({
            path: '/maxDefense/addEditDefense',
            query: { type: 'detail' }
          })
        })
      } catch (err) {
        console.log(err)
      } finally {
        this.$loading.hide()
      }
    },
    // 删除分组
    handleDelete: debounce(async function (item) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      const { id } = item
      // 找到对应布防组的通道
      const channelList = this.groupChannelList.filter(channelItem => channelItem.groupId === id)
      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()
        // 将分组中关联的通道恢复到布防状态
        const that = this
        const callback = async (msg, code) => {
          if (msg === 'SUCCESS') {
            // 删除当前分组
            const params = {
              groupIdList: [id]
            }
            try {
              await deleteDefenseRecord(params)
              // toast提示
              that.$toastSuccess(that.$t('deleteSuccess'))
              // 刷新布撤防列表
              that.getDefenseList()
            } catch (error) {
              console.error(error)
              that.$toast(that.$t('deleteFail'))
              that.$loading.hide()
              // 删除失败则重新请求分组列表
              that.getDefenseList()
            }
          } else {
            if (code) {
              if (Number(code) === 550) {
                // 超时提示连接设备失败
                that.$toast(that.$t('deviceDisconnected'))
              } else {
                that.$toast(that.$t(`errorCode.${code}`))
              }
            } else {
              that.$toast(that.$t('deleteFail'))
            }
            that.$loading.hide()
          }
        }
        // 发送协议到对应的设备 -- 删除时恢复布防状态，固定为外出布防
        this.updateDeviceStatus({
          record: { ...item, status: 1, channelList },
          callback
        })
      } catch (err) {
        console.error(err)
        this.$loading.hide()
      }
    }, 500),
    // 一键布防/撤防弹窗确认回调
    defenseConfirm() {
      this.$refs.oneClickDefense.show = false
      setTimeout(() => {
        this.defenseType = 0
      }, 300)
    },
    // 一键布防/撤防弹窗取消回调
    defenseCancel() {
      this.$refs.oneClickDefense.show = false
      setTimeout(() => {
        this.defenseType = 0
      }, 300)
    },
    // 根据设备能力集找到所有联动项
    getIpcLinkageList(sn, chlIndex, chlId) {
      const newIpcLinkageList = JSON.parse(JSON.stringify(this.ipcLinkageList))
      // 找到设备支持的能力集，过滤出支持的能力集选项
      const supportFun = this.capabilityObj[`${sn}~${chlIndex}~${chlId}`] || []
      // 根据能力集找到可以支持联动项
      const filterLinkageList = newIpcLinkageList.filter(item => !item.filterAble || supportFun.includes(item.value))
      return filterLinkageList
    },
    // 单个分组检查请求状态 0 未全部请求完 1 全部请求成功 2 有请求失败
    checkReqStatus(reqStatus, callback) {
      if (reqStatus.some(val => val.status === 2)) {
        // 有请求失败则回调失败
        callback && callback('ERROR', reqStatus)
      } else if (reqStatus.every(val => val.status === 1)) {
        // 全部请求成功才回调成功
        callback && callback('SUCCESS', reqStatus)
      }
    },
    // 点击分组切换布防撤防
    async handleClick(item) {
      // 在外出和在家布防状态下支持点击切换为撤防
      // 在撤防状态下支持点击分组支持切换为外出布防
      // 0 撤防 1 外部布防 2 在家布防
      const { id, status } = item
      const newStatus = status > 0 ? 0 : 1
      // 发送每个分组下设备的消息
      // 找到对应分组下的通道
      const channelList = this.groupChannelList.filter(item => item.groupId === id)
      if (channelList.length === 0) {
        // 该分组下没有通道不需要跟设备交互，直接更新状态
        await this.updateGroupStatus(item.id, newStatus)
        return
      }
      /* 新逻辑 */
      // 发送消息更新分组的布撤防状态
      this.$loading.show()
      const that = this
      const callback = (msg, reqStatus) => {
        if (msg === 'SUCCESS') {
          // 更新分组云后台的布撤防状态
          that.updateGroupStatus(id, newStatus, msg2 => {
            that.$loading.hide()
            if (msg2 === 'error') {
              // 操作失败则重新请求分组列表
              that.getDefenseList()
            }
          })
        } else {
          // 失败则提示失败
          // 找到失败且code不为200的
          const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
          if (reqItem) {
            const { code } = reqItem
            if (Number(code) === 550) {
              // 超时提示连接设备失败
              that.$toast(that.$t('deviceDisconnected'))
            } else {
              that.$toast(that.$t(`errorCode.${code}`))
            }
          } else {
            that.$toast(this.$t('editFail'))
          }
          that.$loading.hide()
        }
      }
      this.updateDeviceMutexStatus({
        record: { status: newStatus, channelList },
        callback
      })
    },
    // 一键切换布/撤防等回调函数
    reqStatusCallFn(msg, tag, index, code) {
      if (msg === 'success') {
        this.reqStatusList[index].reqStatus = 1
      } else {
        this.reqStatusList[index].reqStatus = 2
      }
      if (code) {
        this.reqStatusList[index].code = code
      }
      this.$nextTick(() => {
        // 检查是否全部请求完成
        if (this.reqStatusList.every(item => item.reqStatus !== 0)) {
          setTimeout(() => {
            // this.$refs.oneClickDefense.show = false // 弹窗自动关闭--延时关闭，避免接口返回较快时弹窗一闪而过
            this.defenseCancel()
          }, 1000)
          this.$loading.hide()
          // 判断一键消警的结果，成功返回成功，失败返回失败
          if (tag === 3) {
            if (this.reqStatusList.some(item => item.reqStatus === 2)) {
              // 有一个失败则提示失败
              this.$toast(this.$t('removalFail'))
            } else {
              // 全部成功才成功
              this.$toast(this.$t('removalSuccess'))
            }
          } else {
            // 一键布撤防有错误码则提示一个错误信息
            const item = this.reqStatusList.find(item => item.reqStatus === 2)
            if (item && item.code) {
              this.$toast(this.$t(`errorCode.${item.code}`))
            }
          }
          if (this.reqStatusList.some(item => item.reqStatus === 2)) {
            // 操作失败则重新请求分组列表
            this.getDefenseList()
          }
        }
      })
    },
    // 一键布撤防消警点击事件
    handleBtnClick(tag) {
      // tag 1 外出布防 2 在家布防 0 一键撤防 3 一键消警
      // 外出布防：组内通道/传感器将布防
      // 在家布防：组内开启旁路的通道/传感器将撤防（按撤防配置项取消联动），组内未开启旁路的通道/传感器将布防（按设备端参数配置布防）
      if (this.defenseGroupList.length === 0) {
        this.$toast(this.$t('pleaseAddGroup'))
        return
      }
      // 遍历所有分组，按照分组发送--因为同一分组只能有一个设备下的通道
      const allGroupIdSet = new Set()
      const groupObj = {} // 记录所有分组的信息 groupId为key value为分组信息
      this.defenseGroupList.forEach(item => {
        groupObj[item.id] = { ...item }
        allGroupIdSet.add(item.id)
      })
      const groupChannelObj = {}
      const groupIdSet = new Set()
      this.groupChannelList.forEach(item => {
        const { groupId } = item
        if (groupIdSet.has(groupId)) {
          groupChannelObj[groupId].push(item)
        } else {
          groupChannelObj[groupId] = [item]
        }
        groupIdSet.add(groupId)
      })
      const groupList = Array.from(allGroupIdSet)
      if (tag === 3) {
        // 点击一键消警时，需要判断所有分组是否带有通道，如果没有任何通道，则吐司提示“分组下未添加设备”，并返回
        const flag = groupList.every(groupId => {
          const channelList = groupChannelObj[groupId]
          return !(channelList && channelList.length)
        })
        if (flag) {
          this.$toast(this.$t('groupNoDevice'))
          return
        }
      } else {
        this.defenseType = tag
        this.$refs.oneClickDefense.show = true
      }
      // 生成每个分组的请求状态
      const reqStatusList = groupList.map(id => {
        const { groupName } = groupObj[id]
        return {
          id,
          groupName,
          reqStatus: 0 // 分组请求状态 0-pending 1-success 2-fail
        }
      })
      this.reqStatusList = reqStatusList
      // 循环不同分组依次发送消息
      groupList.forEach((groupId, index) => {
        // 没有通道不需要发送设备请求直接切换状态
        const channelList = groupChannelObj[groupId] // 对应分组下的通道
        if (!(channelList && channelList.length)) {
          const callback = msg => {
            this.reqStatusCallFn(msg, tag, index)
          }
          if (tag !== 3) {
            // 发送消息更新分组云后台的布撤防状态
            this.updateGroupStatus(groupId, tag, callback)
          } else {
            // 一键消警中不带通道的分组默认成功
            this.reqStatusCallFn('success', tag, index)
          }
          return
        }
        // 一键消警走原来的逻辑
        if (tag === 3) {
          // 发送每个分组下设备的消息
          const req = {
            devId: this.devId,
            url: 'oneKeyDisalarm',
            params: urlDisalarmSwitchNodes(channelList)
          }
          const that = this
          // console.log('一键消警请求req', req)
          appRequestDevice(req, function (res) {
            let resData = res.replace(/\\t|\\n/g, '')
            resData = JSON.parse(resData)
            // console.log('一键消警返回res', res)
            const code = resData.code
            if (resData.code == 200) {
              const xmlObject = transformXml(resData.body)
              if (xmlObject.response.status == 'success') {
                // 处理结果
                that.reqStatusList[index].reqStatus = 1 // 请求成功
                that.reqStatusList[index].code = code
                // 更新所有分组请求状态
                that.reqStatusCallFn('success', tag, index, code)
              }
            } else {
              // 更新所有分组请求状态
              that.reqStatusCallFn('error', tag, index, code)
            }
          })
        } else {
          // 布防/撤防需要根据勾选的联动项和未勾选的联动项发送相反的指令
          const that = this
          // 更新分组请求状态
          const callback2 = (msg, code) => {
            // 更新所有分组请求状态
            that.reqStatusCallFn(msg, tag, index, code)
          }
          const callback = (msg, reqStatus) => {
            if (msg === 'SUCCESS') {
              // 更新分组云后台的布撤防状态
              that.updateGroupStatus(groupId, tag, callback2)
            } else {
              // 失败则提示失败
              // 找到失败且code不为200的
              const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
              if (reqItem) {
                const { code } = reqItem
                // 更新所有分组请求状态
                that.reqStatusCallFn('error', tag, index, code)
              }
            }
          }
          // 给设备发送布撤防状态切换请求
          this.updateDeviceMutexStatus({
            record: { status: tag, channelList },
            callback
          })
        }
      })
    },
    // 更新某个分组的布撤防状态--跟云后台交互
    async updateGroupStatus(groupId, status, callback) {
      const idx = this.defenseGroupList.findIndex(item => item.id === groupId)
      if (idx === -1) {
        return
      }
      const groupItem = this.defenseGroupList[idx]
      const params = {
        ...groupItem,
        status,
        addMethod: this.addType === 'bind' ? 1 : 2
      }
      try {
        await addDefenseGroup(params)
        // 更新对应布防组的状态status
        // console.log('更新对应布防组状态', status)
        this.defenseGroupList[idx].status = status
        this.SET_DEFENSE_GROUP_LIST([...this.defenseGroupList])
        if (callback) callback('success')
      } catch (err) {
        console.error(err)
        if (callback) callback('error')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.defense-deployment-wrapper {
  height: 100%;
  overflow: hidden;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  .defense-deployment-content {
    height: calc(100% - 125px);
    overflow: auto;
    .tvt-better-scroll {
      height: 100%;
    }
  }
  .footer {
    width: 342px;
    height: 48px;
    left: calc(50% - 171px);
    bottom: 35px;
    margin: 0px auto;
    padding: 12px 0px;
    box-sizing: border-box;
    border-radius: 10px;
    .footer-icon-list {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .footer-icon-item {
        flex: 1;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        .theme-image-container {
          width: 24px;
          height: 24px;
        }
      }
      & .footer-icon-item:not(:first-child) {
        border-left: 1px solid var(--bg-color-secondary, #eeeeee);
      }
    }
  }
  .footer2 {
    width: 100%;
    height: 83px;
    margin: 0px auto;
    display: flex;
    justify-content: center;
    .footer-btn {
      width: 343px;
      height: 46px;
      border-radius: 23px;
    }
  }
  .no-data {
    width: 100%;
    height: calc(100%);
    overflow: hidden;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 80px;
      img {
        width: 180px;
        height: 216px;
      }
    }
    .no-data-text {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
    }
    .add-device-btn {
      width: 102px;
      height: 40px;
      border-radius: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      font-size: var(--font-size-body1-size, 16px);
    }
  }
}
</style>
