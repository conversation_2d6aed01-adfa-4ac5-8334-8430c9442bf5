<template>
  <div class="area-name-wrapper">
    <van-dialog
      v-model="show"
      :title="$t('name')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="areaname-div">
          <input type="text" class="common-input" v-model="areaname" maxlength="32" :placeholder="$t('pleaseEnter')" />
          <span class="areaname-close">
            <theme-image v-if="areaname" @click="areaname = ''" alt="delete" imageName="input_close.png" />
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'editAreaName',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  components: {
    ThemeImage
  },
  data() {
    return {
      show: false,
      areaname: ''
    }
  },
  watch: {
    name: {
      handler(val) {
        this.areaname = val
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      if (!this.areaname || !this.areaname.trim()) {
        this.$toast(this.$t('pleaseEnterName'))
        return false
      }
      this.$emit('confirm', this.areaname.trim())
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel', {})
    },
    // 清除数据
    clearParam() {
      this.areaName = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.area-name-wrapper {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--icon-color-primary, #393939);
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .areaname-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
    border: 1px solid var(bg-color-secondary, #d9d9d9);
    padding: 4px 2px;
    border-radius: 6px;
  }
  .common-input {
    width: 220px;
    padding-left: 8px;
  }
  .areaname-div {
    margin-top: 20px;
    position: relative;
    .areaname-close {
      position: absolute;
      right: 10px;
      top: 8px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
