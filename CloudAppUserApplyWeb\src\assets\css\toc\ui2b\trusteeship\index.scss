.unbind-card-head {
   color: $max-middle-black;
   .unbind-card-tag {
    background-color: $max-tag-color;
   }
}

.bind-card-wrapper {
    .bind-card-head {
        color: $max-black;
     }
     .bind-card-body {
        background-color: $max-background;
     }
}

.device-list-wrapper {
   color: $max-light-black;
   .device-capability .device-capability-item {
      color: $max-gray;
   }
   .device-capability-item .separator-box {
      background-color: $max-gray;
   }
}
.device-status-text{
   color: $max-light-black;
}
.devices-list-wrapper {
   .device-list-item:first-child {
      .van-cell {
         border-top: 1px solid $max-border;
      }
   }
   .device-list-item {
      .van-cell {
         border-bottom: 1px solid $max-border;
      }
      
   }
}

.trusteeship-detail-wrapper {
   background-color: $max-background;
   color: $max-black;
}

.trusteeship-detail-content {
   .van-cell {
      border-bottom: 1px solid $max-border;
   }
}

.device-transfer-wrapper {
   background-color: $max-background;
}

.device-transfer-content .transfer-text-box {
   color: $max-high-black;
}

.trusteeship-success-wrapper {
   background-color: $max-background;
}

.transfer-device-content {
   .transfer-device-title {
      color: $max-black;
   }
   .transfer-device-text {
      color: $max-light-black;
   }
} 