import request from '@/api/request'
import baseUrl from '@/api/index.js'
const baseUrl2 = '/mobile_v1.0/'
// 站点未交付时重置密码
export const resetDevCredentials = data => request.post(`${baseUrl2}/device/target-value-set/credential/get`, data)

// 根据sn查询设备信息
export const getDevDetail = data => request.post(`${baseUrl}/device/detail`, data)

const xmlHeader = '<?xml version="1.0" encoding="utf-8" ?>'
export function getRequestContent() {
  return `${xmlHeader}<request version="1.0"  systemType="NVMS-9000" clientType="MOBILE"></request>`
}
