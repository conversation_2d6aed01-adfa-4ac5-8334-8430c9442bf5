<template>
  <div class="target-face-detail-wrapper">
    <nav-bar @clickLeft="back" :title="$t(title)" class="target-face-nav-bar"> </nav-bar>
    <van-form @submit="submit">
      <van-cell
        class="target-item margin-top"
        :title="$t('personType')"
        :value="$t(targetFaceDetail.listType)"
        is-link
        @click="showPersonType = true"
      >
      </van-cell>
      <van-cell
        class="target-item img"
        :class="{
          required: requiredFiled.includes('faceImg')
        }"
        :title="$t('personFace')"
        is-link
        @click="uploadImage"
      >
        <img ref="faceImageRef" :src="targetFaceDetail.faceImg || defaultFaceImg" class="target-face" />
      </van-cell>
      <van-cell
        class="target-item"
        :class="{
          required: requiredFiled.includes('jobNumber')
        }"
        :title="$t('jobNumber')"
      >
        <van-field
          v-model="targetFaceDetail.jobNumber"
          :maxlength="personFileds.jobNumber.maxLen"
          @input="handleInputJobNum"
          class="target-face-input"
        />
      </van-cell>
      <van-cell class="target-item" :title="$t('targetName')">
        <van-field
          v-model="targetFaceDetail.name"
          :maxlength="personFileds.name.maxLen"
          @input="handleInputName"
          class="target-face-input"
        />
      </van-cell>
      <template v-if="targetFaceDetail.listType !== 'strangerList'">
        <van-cell
          key="gender"
          class="target-item"
          :title="$t('gender')"
          :value="$t(targetFaceDetail.sex)"
          is-link
          @click="showGenderType = true"
        >
        </van-cell>
        <van-cell key="age" class="target-item" :title="$t('age')">
          <van-field v-model="targetFaceDetail.age" type="number" @input="handleInputAge" class="target-face-input" />
        </van-cell>
        <van-cell key="telephone" class="target-item" :title="$t('telephone')">
          <van-field
            v-model="targetFaceDetail.telephone"
            :maxlength="personFileds.telephone.maxLen"
            type="number"
            @input="handleInputTel"
            class="target-face-input"
          />
        </van-cell>
      </template>
      <van-cell key="floor" class="target-item" :title="$t('floor')">
        <van-field v-model="targetFaceDetail.floor" type="number" @input="handleInputFloor" class="target-face-input" />
      </van-cell>
      <van-cell
        v-if="targetFaceDetail.listType !== 'strangerList'"
        key="roomName"
        class="target-item margin-bottom"
        :title="$t('roomName')"
      >
        <van-field v-model="targetFaceDetail.room" type="number" @input="handleInputRoom" class="target-face-input" />
      </van-cell>
      <van-cell
        class="target-item margin-top"
        key="accessType"
        :title="$t('verificationMethod')"
        :value="$t(targetFaceDetail.accessType)"
        is-link
        @click="showAccessType = true"
      >
      </van-cell>
      <van-cell
        class="target-item ellipsis"
        :title="$t('cardId')"
        :class="{
          required: requiredFiled.includes('cardIds')
        }"
        :value="targetFaceDetail.cardIds.join('、')"
        is-link
        @click="editCardId"
      >
      </van-cell>
      <van-cell
        class="target-item"
        key="password"
        :class="{
          required: requiredFiled.includes('password')
        }"
        :title="$t('Password')"
      >
        <van-field
          v-model="targetFaceDetail.password"
          :type="showPwdContent ? 'text' : 'password'"
          right-icon="warning-o"
          :inputmode="personFileds.password.input === 'digit' ? 'numeric' : 'text'"
          :maxlength="personFileds.password.maxPassLen"
          @focus="handlePwdFocus"
          @input="handleInputPwd"
          :placeholder="displayEncrytPwd ? '********' : ''"
          class="target-face-input"
        >
          <template #right-icon>
            <img
              v-if="showPwdContent"
              src="@/assets/img/common/open_eye.png"
              class="pwd-right-icon"
              @click="showPwdContent = false"
            />
            <img v-else src="@/assets/img/common/close_eye.png" class="pwd-right-icon" @click="showPwdContent = true" />
          </template>
        </van-field>
      </van-cell>
      <van-cell
        class="target-item ellipsis required"
        key="lockIdArr"
        :title="$t('lockPermission')"
        :value="formatLockIds(targetFaceDetail.lockIdArr)"
        is-link
        @click="openLockIds"
      >
      </van-cell>
      <template v-if="targetFaceDetail.listType === 'strangerList'">
        <div class="small-title">{{ $t('termOfValidity') }}</div>
        <van-cell
          class="target-item"
          :title="$t('startTime')"
          :value="dateFormat(targetFaceDetail.startTime)"
          is-link
          @click="editTime(targetFaceDetail.startTime, 'start')"
        >
        </van-cell>
        <van-cell
          class="target-item margin-bottom"
          :title="$t('endTime')"
          :value="dateFormat(targetFaceDetail.endTime)"
          is-link
          @click="editTime(targetFaceDetail.endTime, 'end')"
        >
        </van-cell>
      </template>
      <template v-else>
        <van-cell
          class="target-item margin-bottom margin-top"
          :title="$t('termOfValidity')"
          :value="$t(targetFaceDetail.termOfValidity)"
          is-link
          @click="showTermOfValidity = true"
        >
        </van-cell>
        <van-cell
          v-if="targetFaceDetail.termOfValidity === 'custom'"
          class="target-item"
          :title="$t('startTime')"
          :value="dateFormat(targetFaceDetail.startTime)"
          is-link
          @click="editTime(targetFaceDetail.startTime, 'start')"
        >
        </van-cell>
        <van-cell
          v-if="targetFaceDetail.termOfValidity === 'custom'"
          class="target-item margin-bottom"
          :title="$t('endTime')"
          :value="dateFormat(targetFaceDetail.endTime)"
          is-link
          @click="editTime(targetFaceDetail.endTime, 'end')"
        >
        </van-cell>
      </template>

      <van-cell class="target-item remark" :title="$t('remark')">
        <van-field
          v-model="targetFaceDetail.comment"
          :maxlength="personFileds.comment.maxLen"
          @input="handleInputComment"
          class="target-face-input"
        />
      </van-cell>
      <div class="submit-wrapper">
        <div class="submit-btn" @click.prevent="submit">{{ $t('confirm') }}</div>
      </div>
    </van-form>
    <van-popup v-model="showPersonType" position="bottom">
      <div class="target-face-type-list">
        <div class="target-face-type disabled">{{ $t('personType') }}</div>
        <div
          v-for="item in personFileds.listType"
          :key="item"
          class="target-face-type"
          :class="{ selected: item === targetFaceDetail.listType }"
          @click="selectPersonType(item)"
        >
          {{ $t(item) }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showGenderType" position="bottom">
      <div class="target-face-type-list">
        <div class="target-face-type disabled">{{ $t('gender') }}</div>
        <div
          v-for="item in personFileds.sexType"
          :key="item"
          class="target-face-type"
          :class="{ selected: item === targetFaceDetail.sex }"
          @click="selectGender(item)"
        >
          {{ $t(item) }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showAccessType" position="bottom">
      <div class="target-face-type-list">
        <div class="target-face-type disabled">{{ $t('verificationMethod') }}</div>
        <div
          v-for="item in personFileds.accessType"
          :key="item"
          class="target-face-type"
          :class="{ selected: item === targetFaceDetail.accessType }"
          @click="selectAccessType(item)"
        >
          {{ $t(item) }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showLockIDs" position="bottom">
      <div class="target-face-type-list no-padding">
        <div class="target-id-type disabled">
          <span class="door-lock-title">
            {{ $t('lockPermission') }}
          </span>
          <van-checkbox
            v-model="isLockIdsSelected"
            :disabled="doorLockDisabled"
            @click="toggleLockIds"
            class="door-lock-checkbox"
          ></van-checkbox>
        </div>
        <van-checkbox-group v-model="lockIdArrTemp" ref="lockIdsRef">
          <div class="target-id-type" v-for="item in doorLockList" :key="item.id">
            <span class="lock-id-name"> {{ item.doorName }} </span>
            <van-checkbox
              :name="item.id"
              :disabled="doorLockDisabled"
              @click="updateSelectLockId"
              class="door-lock-checkbox"
            >
            </van-checkbox>
          </div>
        </van-checkbox-group>
      </div>
      <div class="popup-bottom">
        <div class="popup-bottom-btn" @click="showLockIDs = false">
          {{ $t('cancel') }}
        </div>
        <div class="popup-bottom-btn" @click="confirmLockIds">
          {{ $t('confirm') }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showTermOfValidity" position="bottom">
      <div class="target-face-type-list">
        <div class="target-face-type disabled">{{ $t('termOfValidity') }}</div>
        <div
          class="target-face-type"
          @click="selectTermOfValidity('foreverValid')"
          :class="{ selected: targetFaceDetail.termOfValidity === 'foreverValid' }"
        >
          {{ $t('foreverValid') }}
        </div>
        <div
          class="target-face-type"
          @click="selectTermOfValidity('custom')"
          :class="{ selected: targetFaceDetail.termOfValidity === 'custom' }"
        >
          {{ $t('custom') }}
        </div>
      </div>
    </van-popup>
    <van-popup v-model="showDateTime" position="bottom">
      <van-datetime-picker
        class="target-face-date"
        v-model="dateTime"
        type="datetime"
        :min-date="minDateTime"
        :max-date="maxDateTime"
        :show-toolbar="false"
      />
      <div class="popup-bottom">
        <div class="popup-bottom-btn" @click="showDateTime = false">
          {{ $t('cancel') }}
        </div>
        <div class="popup-bottom-btn" @click="confirmDateTime">
          {{ $t('confirm') }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapActions, mapMutations } from 'vuex'
import { dateFormat, getUrlQuery } from '@/utils/common.js'
import { appReqeustNative, appLog } from '@/utils/appbridge.js'

export default {
  name: 'TargetFaceDetail',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      title: 'addPerson',
      showPersonType: false,
      showGenderType: false,
      showAccessType: false,
      showLockIDs: false,
      showTermOfValidity: false,
      showDateTime: false,
      dateTime: new Date(),
      minDateTime: undefined,
      maxDateTime: undefined,
      timeType: 'start',
      lockIdArrTemp: [],
      showPwdContent: false,
      isLockIdsSelected: false,
      defaultFaceImg: require('@/assets/img/common/default_avatar.png'),
      doorLockDisabled: false
    }
  },
  async created() {
    const json = getUrlQuery(window.location.href)
    let { devId } = json

    this.SET_DEV_ID(devId)
    const isDetail = this.$route.query.personId !== '-1'

    if (isDetail) {
      this.title = 'details'
    } else {
      this.title = 'addPerson'
    }

    const maxDateTime = this.$moment().add(30, 'years').endOf('days').valueOf()
    this.maxDateTime = new Date(maxDateTime)

    if (this.queryDetail) {
      if (isDetail) {
        this.$loading.show()
      }

      this.INIT_TARGET_FACE_DETAIL()
      this.updateDoorLock()
      this.queryPersonFields()

      await this.queryTargetFace({ personID: this.$route.query.personId })

      if (isDetail) {
        this.$loading.hide()
      }
    } else {
      this.SET_QUERY_DETAIL(true)
    }
  },
  mounted() {
    if (this.title === 'addPerson') {
      this.initTimePeriod()
    }
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('targetFace', ['targetFaceDetail', 'doorLockList', 'queryDetail', 'personFileds', 'displayEncrytPwd']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    requiredFiled() {
      const defaultFileds = ['lockIdArr']
      let addedFileds = []
      switch (this.targetFaceDetail.accessType) {
        case 'Password':
          addedFileds = ['jobNumber', 'password']
          break
        case 'FaceMatch':
          addedFileds = ['faceImg']
          break
        case 'SwipingCard':
          addedFileds = ['cardIds']
          break
        case 'MatchAndPass':
          addedFileds = ['jobNumber', 'password']
          break
        case 'MatchAndCard':
          addedFileds = ['cardIds']
          break
        case 'MatchorCard':
          addedFileds = []
          break
        case 'AllType':
          addedFileds = []
          break
        default:
          break
      }
      return [...defaultFileds, ...addedFileds]
    }
  },
  methods: {
    ...mapActions('targetFace', [
      'queryTargetFace',
      'queryAccessConfig',
      'addTargetFace',
      'editTargetFace',
      'queryPersonFields'
    ]),
    ...mapMutations('targetFace', [
      'SET_QUERY_DETAIL',
      'SET_DEV_ID',
      'INIT_TARGET_FACE_DETAIL',
      'SET_DISPLAY_ENCRYPT_PWD'
    ]),
    back() {
      this.$router.go(-1)
    },
    selectPersonType(item) {
      this.targetFaceDetail.listType = item
      this.showPersonType = false
    },
    selectGender(item) {
      this.targetFaceDetail.sex = item
      this.showGenderType = false
    },
    selectAccessType(item) {
      this.targetFaceDetail.accessType = item
      this.showAccessType = false
    },
    selectTermOfValidity(item) {
      this.targetFaceDetail.termOfValidity = item
      this.showTermOfValidity = false

      if (item === 'custom' && !this.targetFaceDetail.startTime) {
        this.initTimePeriod()
        return
      }

      if (item === 'foreverValid') {
        this.targetFaceDetail.startTime = ''
        this.targetFaceDetail.endTime = ''
      }
    },
    openLockIds() {
      this.lockIdArrTemp = this.targetFaceDetail.lockIdArr.slice()
      this.updateSelectLockId()
      this.showLockIDs = true
    },
    confirmLockIds() {
      if (this.lockIdArrTemp.length === 0) {
        this.$toast({
          message: this.$t('doorLockTip'),
          position: 'middle'
        })
      } else {
        this.targetFaceDetail.lockIdArr = this.lockIdArrTemp.slice()
        this.showLockIDs = false
      }
    },
    formatLockIds(ids) {
      const arr = this.doorLockList.filter(info => ids.includes(info.id)).map(info => info.doorName)

      return arr.join('、')
    },
    toggleLockIds() {
      this.$refs.lockIdsRef.toggleAll(this.isLockIdsSelected)
    },
    updateSelectLockId() {
      if (this.lockIdArrTemp.length >= this.doorLockList.length) {
        this.isLockIdsSelected = true
      } else {
        this.isLockIdsSelected = false
      }
    },
    editTime(time, timeType) {
      if (!time) {
        if (timeType === 'start') {
          const startTime = this.$moment()
          startTime.startOf('day')
          this.dateTime = startTime.toDate()
        } else {
          const endTime = this.$moment()
          endTime.add(10, 'years')
          endTime.subtract(1, 's')
          this.dateTime = endTime.toDate()
        }
      } else {
        this.dateTime = time
      }

      this.timeType = timeType
      this.showDateTime = true
    },
    confirmDateTime() {
      if (this.timeType === 'start') {
        if (this.targetFaceDetail.endTime && this.dateTime.getTime() >= this.targetFaceDetail.endTime.getTime()) {
          this.$toast({
            message: this.$t('timeRangeTip'),
            position: 'middle'
          })
          return
        } else {
          this.targetFaceDetail.startTime = this.dateTime
        }
      } else {
        if (this.targetFaceDetail.startTime && this.dateTime.getTime() <= this.targetFaceDetail.startTime.getTime()) {
          this.$toast({
            message: this.$t('timeRangeTip'),
            position: 'middle'
          })
          return
        } else {
          this.targetFaceDetail.endTime = this.dateTime
        }
      }
      this.showDateTime = false
    },
    uploadImage() {
      const that = this
      appReqeustNative(
        {
          url: 'REQUEST_PLUGIN_CAMERA_FACE',
          params: '',
          timeout: 3000
        },
        res => {
          const data = JSON.parse(res)

          if (parseInt(data.code) === 200) {
            that.targetFaceDetail.faceImg = `data:image/jpg;base64,${data.body}`
          } else if (parseInt(data.code) === 101) {
            this.$toast({
              message: this.$t('personFace.sizeTip'),
              position: 'middle'
            })
          }
        }
      )
    },
    editCardId() {
      this.$router.push({
        name: 'targetFaceCardId'
      })
    },
    dateFormat(time) {
      if (time) {
        return dateFormat(time)
      }
      return time
    },
    initTimePeriod() {
      const startTime = this.$moment()
      this.targetFaceDetail.startTime = startTime.toDate()

      const endTime = startTime.clone()
      endTime.add(24, 'hours')
      this.targetFaceDetail.endTime = endTime.toDate()
    },
    // 计算字符串字节长度的函数
    getByteLength(str) {
      let byteLength = 0
      for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i)
        if (charCode >= 0 && charCode <= 128) {
          byteLength += 1
        } else {
          byteLength += 3 // 假设中文字符占3个字节
        }
      }
      return byteLength
    },
    handleInputJobNum(value) {
      // this.targetFaceDetail.jobNumber = value.replace(/[&<>"'/|$]/g, '')
      // 先过滤特殊字符
      const filteredValue = value.replace(/[&<>"'/|$]/g, '')
      // 获取最大长度限制
      const maxByteLength = this.personFileds.jobNumber.maxLen
      // 按字节长度处理
      if (this.getByteLength(filteredValue) > maxByteLength) {
        // 逐字符检查并截取到不超过字节限制的最大长度
        let result = ''
        let currentByteLength = 0
        for (let i = 0; i < filteredValue.length; i++) {
          const char = filteredValue.charAt(i)
          const charByteLength = this.getByteLength(char)
          if (currentByteLength + charByteLength <= maxByteLength) {
            result += char
            currentByteLength += charByteLength
          } else {
            break
          }
        }
        this.targetFaceDetail.jobNumber = result
      } else {
        this.targetFaceDetail.jobNumber = filteredValue
      }
    },
    handleInputName(value) {
      // 先过滤特殊字符
      const filteredValue = value.replace(/[&<>"'/|$]/g, '')
      // 获取最大长度限制
      const maxByteLength = this.personFileds.name.maxLen

      // 判断是按字节长度处理还是按字符长度处理
      // 如果为32则认为是字符长度，大于32则认为是字节长度
      if (maxByteLength > 32) {
        // 按字节长度处理
        if (this.getByteLength(filteredValue) > maxByteLength) {
          // 逐字符检查并截取到不超过字节限制的最大长度
          let result = ''
          let currentByteLength = 0
          for (let i = 0; i < filteredValue.length; i++) {
            const char = filteredValue.charAt(i)
            const charByteLength = this.getByteLength(char)
            if (currentByteLength + charByteLength <= maxByteLength) {
              result += char
              currentByteLength += charByteLength
            } else {
              break
            }
          }
          this.targetFaceDetail.name = result
        } else {
          this.targetFaceDetail.name = filteredValue
        }
      } else {
        // 按字符长度处理
        if (filteredValue.length > maxByteLength) {
          this.targetFaceDetail.name = filteredValue.slice(0, maxByteLength)
        } else {
          this.targetFaceDetail.name = filteredValue
        }
      }
    },
    handleInputAge(value) {
      const str = value.replace(/[^0-9]/g, '')

      if (!str) {
        this.targetFaceDetail.age = ''
        return
      }

      const age = parseInt(str)

      if (age < this.personFileds.age.min || age > this.personFileds.age.max) {
        this.$toast({
          message: this.$t('ageRangeTip', { max: this.personFileds.age.max, min: this.personFileds.age.min }),
          position: 'middle'
        })
        this.targetFaceDetail.age = str.slice(0, str.length - 1)
      } else {
        this.targetFaceDetail.age = String(age)
      }
    },
    handleInputTel(value) {
      this.targetFaceDetail.telephone = value.replace(/[^0-9]/g, '')
    },
    handleInputFloor(value) {
      const str = value.replace(/[^0-9]/g, '')

      if (!str) {
        this.targetFaceDetail.floor = ''
        return
      }

      const floor = parseInt(str)

      if (floor < this.personFileds.floor.min || floor > this.personFileds.floor.max) {
        this.$toast({
          message: this.$t('floorRangeTip', { max: this.personFileds.floor.max, min: this.personFileds.floor.min }),
          position: 'middle'
        })
        this.targetFaceDetail.floor = str.slice(0, str.length - 1)
      } else {
        this.targetFaceDetail.floor = String(floor)
      }
    },
    handleInputRoom(value) {
      const str = value.replace(/[^0-9]/g, '')

      if (!str) {
        this.targetFaceDetail.room = ''
        return
      }

      const room = parseInt(str)

      if (room < this.personFileds.room.min || room > this.personFileds.room.max) {
        this.$toast({
          message: this.$t('floorRangeTip', { max: this.personFileds.room.max, min: this.personFileds.room.min }),
          position: 'middle'
        })
        this.targetFaceDetail.room = str.slice(0, str.length - 1)
      } else {
        this.targetFaceDetail.room = String(room)
      }
    },
    handleInputPwd(value) {
      if (this.personFileds.password.input === 'digit') {
        this.targetFaceDetail.password = value.replace(/[^0-9]/g, '')
      }
    },
    handlePwdFocus() {
      if (this.displayEncrytPwd) {
        this.SET_DISPLAY_ENCRYPT_PWD(false)
      }
    },
    handleInputComment(value) {
      this.targetFaceDetail.comment = value.replace(/[&<>"'/|$]/g, '')
    },
    async updateDoorLock() {
      this.doorLockDisabled = false
      await this.queryAccessConfig()

      // 新增人员默认选中所有门锁
      if (this.title === 'addPerson') {
        this.targetFaceDetail.lockIdArr = this.doorLockList.map(info => info.id)
        if (this.targetFaceDetail.lockIdArr.length === 1) {
          this.doorLockDisabled = true
        }
      }
    },
    async submit() {
      if (this.requiredFiled.includes('faceImg') && !this.targetFaceDetail.faceImg) {
        this.$toast({
          message: this.$t('personFace.uploadTip'),
          position: 'middle'
        })
        return
      }

      if (this.requiredFiled.includes('jobNumber') && !this.targetFaceDetail.jobNumber) {
        this.$toast({
          message: this.$t('jobNumberTip'),
          position: 'middle'
        })
        return
      }

      const cardIds = this.targetFaceDetail.cardIds.filter(id => id)

      if (this.requiredFiled.includes('cardIds') && cardIds.length === 0) {
        this.$toast({
          message: this.$t('cardIdsTip'),
          position: 'middle'
        })
        return
      }

      if (!this.displayEncrytPwd && this.targetFaceDetail.password.length < this.personFileds.password.minPassLen) {
        if (this.personFileds.password.minPassLen === this.personFileds.password.maxPassLen) {
          this.$toast({
            message: this.$t('passwordLengthTip', { length: this.personFileds.password.minPassLen }),
            position: 'middle'
          })
        } else {
          this.$toast({
            message: this.$t('passwordRangeTip', {
              min: this.personFileds.password.minPassLen,
              max: this.personFileds.password.maxPassLen
            }),
            position: 'middle'
          })
        }
        return
      }

      if (!this.displayEncrytPwd && this.requiredFiled.includes('password') && !this.targetFaceDetail.password) {
        this.$toast({
          message: this.$t('pleaseEnterPwd'),
          position: 'middle'
        })
        return
      }

      const lockIdArr = this.targetFaceDetail.lockIdArr.filter(id => id)

      if (this.requiredFiled.includes('lockIdArr') && lockIdArr.length === 0) {
        this.$toast({
          message: this.$t('doorLockTip'),
          position: 'middle'
        })
        return
      }
      try {
        this.$loading.show()
        // 设置超时定时器
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('timeout'))
          }, 30000) // 30秒超时
        })
        if (this.title === 'addPerson') {
          if (this.targetFaceDetail.listType === 'strangerList') {
            this.targetFaceDetail.termOfValidity = 'custom'
          }
          appLog('log/info', `${new Date()} addTargetFace 请求发送`)
          // const {
          //   config: { addFace, _status: status, _errorCode: outterCode }
          // } = await this.addTargetFace(this.targetFaceDetail)
          try {
            // 使用 Promise.race 实现超时控制
            const result = await Promise.race([this.addTargetFace(this.targetFaceDetail), timeoutPromise])
            const {
              config: { addFace, _status: status, _errorCode: outterCode }
            } = result
            appLog(
              'log/info',
              `${new Date()} addTargetFace 请求返回 ${JSON.stringify({
                addFace,
                _status: status,
                _errorCode: outterCode
              })}`
            )
            if (status === 'success') {
              this.$toast({
                message: this.$t('addSuccess'),
                position: 'middle'
              })
              this.back()
            } else {
              let errorCode = ''

              if (outterCode === '499') {
                errorCode = outterCode
              } else {
                ;({
                  resultInfo: {
                    item: {
                      status: { __text: errorCode }
                    }
                  }
                } = addFace)
              }

              const key = `faceMatchErrorCode.${errorCode}`

              const errorMsg = this.$t(key)
              if (errorMsg !== key) {
                this.$toast({
                  message: errorMsg,
                  position: 'middle'
                })
              } else {
                this.$toast({
                  message: this.$t('addFail'),
                  position: 'middle'
                })
              }
            }
          } catch (error) {
            appLog('log/info', `${new Date()} addTargetFace 请求报错 ${JSON.stringify(error)}`)
            if (error.message === 'timeout') {
              this.$toast({
                message: this.$t('errorCode.550'),
                position: 'middle'
              })
            } else {
              this.$toast({
                message: this.$t('addFail'),
                position: 'middle'
              })
            }
          }
        } else {
          if (this.targetFaceDetail.listType === 'strangerList') {
            this.targetFaceDetail.termOfValidity = 'custom'
          }
          appLog('log/info', `${new Date()} editTargetFace 请求发送`)
          // const {
          //   config: { _errorCode: errorCode, _status: status }
          // } = await this.editTargetFace(this.targetFaceDetail)
          try {
            // 使用 Promise.race 实现超时控制
            const result = await Promise.race([this.editTargetFace(this.targetFaceDetail), timeoutPromise])
            const {
              config: { _errorCode: errorCode, _status: status }
            } = result
            appLog(
              'log/info',
              `${new Date()} editTargetFace 请求返回 ${JSON.stringify({ _status: status, _errorCode: errorCode })}`
            )
            if (status === 'success') {
              this.$toast({
                message: this.$t('editSuccess'),
                position: 'middle'
              })
              this.back()
            } else {
              const key = `faceMatchErrorCode.${errorCode}`

              const errorMsg = this.$t(key)
              if (errorMsg !== key) {
                this.$toast({
                  message: errorMsg,
                  position: 'middle'
                })
              } else {
                this.$toast({
                  message: this.$t('editFail'),
                  position: 'middle'
                })
              }
            }
          } catch (error) {
            appLog('log/info', `${new Date()} editTargetFace 请求报错 ${JSON.stringify(error)}`)
            if (error.message === 'timeout') {
              this.$toast({
                message: this.$t('errorCode.550'),
                position: 'middle'
              })
            } else {
              this.$toast({
                message: this.$t('editFail'),
                position: 'middle'
              })
            }
          }
        }
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.target-face {
  width: 51px;
  height: 68px;
}
.target-face-detail-wrapper {
  ::v-deep .target-face-nav-bar {
    .nav-bar-left .van-icon-arrow-left {
      font-size: var(--font-size-h5-size, 18px);
    }
    .nav-bar-center {
      font-size: var(--font-size-body1-size, 16px);
    }
    .nav-bar-right {
      padding-right: 10px;
    }
  }
  .margin-top {
    margin-top: 10px;
  }
  .margin-bottom {
    margin-bottom: 10px;
  }
  ::v-deep .target-item {
    line-height: 30px;
    &.img {
      line-height: 0;
    }
    &.remark {
      flex-direction: column;
      .van-cell__value {
        margin-top: 4px;
      }
      .target-face-input {
        height: 80px;
      }
    }
    &.ellipsis {
      .van-cell__value {
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &.required {
      .van-cell__title::after {
        content: '*';
        color: var(--error-bg-color-default, #ff5656);
      }
    }
    .van-cell__title {
      display: flex;
      align-items: center;
    }
    .van-cell__value {
      flex-grow: 1.6;
    }
    .van-field__body {
      height: 30px;
    }
    .van-cell__right-icon {
      margin-left: 10px;
      padding-top: 3px;
    }
    .target-face-input {
      height: 30px;
      padding: 0;
      border: 1px solid var(--outline-color-primary, #ebebeb);
      border-radius: 1px;
    }
    .van-field__right-icon {
      display: flex;
      align-items: center;
      height: 30px;
      margin: 0;
      padding: 0 4px 0 0;
    }
    .pwd-right-icon {
      width: 24px;
      height: 24px;
    }
    .van-field__control {
      line-height: 30px;
    }
  }
  .small-title {
    margin: 10px auto 4px 12px;
    width: 36px;
    height: 18px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: var(--font-size-text-size, 12px);
    color: var(--bg-color-dialogs, #00000066);
    line-height: 18px;
  }
  .submit-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  .submit-btn {
    width: 343px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: var(--font-size-body2-size, 14px);
    color: var(--bg-color-white, #ffffff);
    text-align: center;
    background: var(--text-color-brand, #00baff);
    border-radius: 4px;
    margin-bottom: 16px;
  }
  .target-face-type-list {
    padding-bottom: 23px;
    &.no-padding {
      padding: 0;
    }
    .target-face-type {
      height: 46px;
      box-shadow: inset 0 -1px 0 0 var(--outline-color-primary, #ebebeb);
      line-height: 46px;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--icon-color-primary, #393939);
      text-align: center;

      &.disabled {
        background: #f5f8fe;
      }
      &.selected {
        color: var(--text-color-brand, #00baff);
      }
    }
    .target-id-type {
      display: flex;
      justify-content: space-between;
      height: 46px;
      line-height: 46px;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--icon-color-primary, #393939);
      padding: 0 16px;
      &.disabled {
        justify-content: center;
        background: #f5f8fe;
        .door-lock-title {
          margin: auto;
        }
      }
    }
  }
  .popup-bottom {
    height: 46px;
    box-shadow: inset 0 1px 0 0 var(--outline-color-primary, #ebebeb);
    display: flex;
    .popup-bottom-btn {
      &:first-child {
        box-shadow: inset -1px 0 0 0 var(--outline-color-primary, #ebebeb);
      }
      flex: 1;
      text-align: center;
      line-height: 46px;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--icon-color-primary, #393939);
    }
  }
  .door-lock-checkbox {
    font-size: 10px;
    ::v-deep .van-checkbox__icon--disabled {
      .van-icon {
        background-color: var(--outline-color-primary, #ebedf0) !important;
        border-color: var(--outline-color-primary, #ebedf0) !important;
      }
    }
  }
  ::v-deep .target-face-date {
    .van-picker-column__item {
      color: var(--text-color-brand, #00baff);
    }
  }
}
</style>
