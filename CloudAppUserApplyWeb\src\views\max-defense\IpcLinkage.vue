<template>
  <div class="ipc-linkage-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="ipc-linkage-content">
      <div class="ipc-linkage-list-wrapper">
        <div class="ipc-linkage-list" v-if="ipcLinkageList && ipcLinkageList.length">
          <div class="ipc-linkage-item" v-for="(item, index) in ipcLinkageList" :key="'item.value' + index">
            <div class="ipc-linkage-item-line">
              <van-checkbox v-model="item.flag">
                <template #icon="props">
                  <theme-image
                    alt="check"
                    class="check-img"
                    :imageName="props.checked ? 'check.png' : 'check_no.png'"
                  />
                </template>
              </van-checkbox>
              <div class="ipc-linkage-name text-over-ellipsis">{{ item.label }}</div>
            </div>
          </div>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <theme-image alt="noData" imageName="no_data.png" />
          </div>
        </div>
      </div>
    </div>
    <div class="footer" v-if="ipcLinkageList && ipcLinkageList.length">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import { MAX_IPC_LINKAGE_LIST_FULLNAME } from '@/utils/options'
import { mapState, mapMutations, mapActions } from 'vuex'
import { addDefenseGroup } from '@/api/maxDefense'
import { appSetWebBackEnable } from '@/utils/appbridge'
export default {
  name: 'IpcLinkage',
  components: {
    NavBar,
    ThemeImage
  },
  props: {},
  data() {
    return {
      ipcLinkageList: MAX_IPC_LINKAGE_LIST_FULLNAME(),
      linkageList: []
    }
  },
  created() {
    appSetWebBackEnable(true)
    if (this.channelRecord && this.channelRecord.name) {
      this.$route.meta.title = this.channelRecord.name
    }
  },
  mounted() {
    if (this.channelRecord) {
      const { sn, chlIndex, chlId, extra } = this.channelRecord
      const { linkageList = [] } = extra
      this.linkageList = linkageList
      // console.log('linkageList', linkageList)
      const newIpcLinkageList = JSON.parse(JSON.stringify(this.ipcLinkageList))
      // 找到设备支持的能力集，过滤出支持的能力集选项
      const supportFun = this.capabilityObj[`${sn}~${chlIndex}~${chlId}`] || []
      // 根据能力集找到可以支持联动项
      const filterLinkageList = newIpcLinkageList.filter(item => !item.filterAble || supportFun.includes(item.value))
      // 遍历ipcLinkageList给flag属性赋值
      filterLinkageList.forEach(item => {
        if (linkageList.includes(item.value)) {
          item.flag = 1
        } else {
          item.flag = 0
        }
      })
      this.ipcLinkageList = filterLinkageList
    }
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('maxDefense', ['addType', 'channelRecord', 'defenseRecord', 'capabilityObj']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getDefenseList', 'updateDeviceMutexStatus']),
    ...mapMutations('maxDefense', [
      'SET_CHANNEL_RECORD',
      'SET_DEFENSE_RECORD',
      'SET_CHANNEL_LIST',
      'SET_DEFENSE_GROUP_LIST'
    ]),
    back() {
      this.$router.go(-1)
    },
    async handleConfirm() {
      // 筛选出勾选和未勾选的项保存下来
      const checkIPCList = this.ipcLinkageList.filter(item => item.flag).map(item2 => item2.value)
      // 允许不勾选
      // if (checkIPCList.length === 0) {
      //   this.$toast(this.$t('pleaseChooseLinkage'))
      //   return
      // }
      const { sn, chlIndex, chlId, extra } = this.channelRecord
      // 编辑布防组
      const { id, groupName, channelList, status } = this.defenseRecord
      const newChannelList = JSON.parse(JSON.stringify(channelList))
      // 找到channelList中对应的channelRecord
      const channelItem = newChannelList.find(
        item => item.sn === sn && item.chlIndex === chlIndex && item.chlId === chlId
      )
      console.log('this.channelRecord', this.channelRecord, 'channelItem', channelItem)
      if (channelItem) {
        channelItem.extra = {
          ...extra,
          linkageList: checkIPCList
        }
      }
      // 编辑时发送请求
      if (id) {
        // 构造传参
        const params = {
          id,
          groupName,
          status,
          details: newChannelList.map(item => {
            const { sn, chlIndex, chlId, extra } = item
            return {
              sn,
              chlIndex,
              chlId,
              extra: JSON.stringify(extra)
            }
          }),
          addMethod: this.addType === 'bind' ? 1 : 2
        }
        this.$loading.show()
        try {
          const curDefenseRecord = {
            ...this.defenseRecord,
            channelList: newChannelList
          }
          const that = this
          const callback = async (msg, reqStatus) => {
            if (msg === 'SUCCESS') {
              await addDefenseGroup(params)
              // toast提示
              that.$toastSuccess(that.$t('saveSuccess'))
              // 更新defenseRecord
              that.SET_DEFENSE_RECORD(curDefenseRecord)
              // 更新channelList
              that.SET_CHANNEL_LIST(newChannelList.slice())
              // 更新当前的channelRecord
              const newChannelRecord = {
                ...that.channelRecord,
                extra: {
                  ...that.channelRecord.extra,
                  linkageList: checkIPCList
                }
              }
              that.SET_CHANNEL_RECORD(newChannelRecord)
              const cb = () => {
                that.$loading.hide()
              }
              await that.getDefenseList({ callback: cb })
              that.back()
            } else {
              // 找到失败且code不为200的
              const reqItem = reqStatus.find(item => Number(item.status) === 2 && Number(item.code) !== 200)
              if (reqItem) {
                const { code } = reqItem
                if (Number(code) === 550) {
                  // 超时提示连接设备失败
                  that.$toast(that.$t('deviceDisconnected'))
                } else {
                  that.$toast(that.$t(`errorCode.${code}`))
                }
              } else {
                that.$toastFail(that.$t('saveFail'))
              }
            }
          }
          // 发送协议到对应的设备
          this.updateDeviceMutexStatus({ record: curDefenseRecord, callback })
        } catch (err) {
          console.error(err)
        } finally {
          this.$loading.hide()
        }
      } else {
        // 新增时不用发送请求直接更新记录
        // 更新defenseRecord
        this.SET_DEFENSE_RECORD({
          ...this.defenseRecord,
          channelList: newChannelList
        })
        // 更新当前的channelRecord
        const newChannelRecord = {
          ...this.channelRecord,
          extra: {
            ...this.channelRecord.extra,
            linkageList: checkIPCList
          }
        }
        this.SET_CHANNEL_RECORD(newChannelRecord)
        this.back()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ipc-linkage-wrapper {
  height: 100%;
  overflow: hidden;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  .ipc-linkage-content {
    height: calc(100% - 120px);
    overflow: auto;
    padding: 10px 0px;
    box-sizing: border-box;
  }
  .ipc-configure,
  .ipc-desc {
    height: 32px;
    line-height: 32px;
    padding-left: 15px;
    padding-right: 15px;
    display: flex;
    justify-content: space-between;
  }
  .ipc-linkage-item {
    height: 40px;
    padding: 0px 15px;
    .ipc-linkage-item-line {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .van-checkbox {
      margin-right: 10px;
    }
    &:last-child {
      border: 0;
    }
    .check-img {
      width: 20px;
      height: 20px;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      img {
        width: 120px;
        height: 123px;
      }
      .theme-image-container {
        width: 120px;
        height: 123px;
      }
    }
  }
  .footer-btn {
    width: 343px;
    height: 46px;
    border-radius: 23px;
  }
}
</style>
