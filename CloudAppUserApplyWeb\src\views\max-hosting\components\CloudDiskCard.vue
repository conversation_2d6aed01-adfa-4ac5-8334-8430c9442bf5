<template>
  <div class="cloud-disk-wrapper">
    <div class="cloud-disk-content">
      <div class="cloud-disk-head">
        <div class="cloud-disk-title">{{ $t('cloudDiskUse') }}</div>
        <div class="cloud-disk-head-tag">{{ $t('gift') }}</div>
      </div>
      <div class="cloud-disk-usage">
        <div class="usage-info">
          <div class="usage-info-space">
            <div class="usage-info-space-label">{{ $t('capacity') }}</div>
            <div class="usage-info-space-value">{{ cloudDiskData.usedSpace }}/{{ cloudDiskData.totalSpace }}</div>
          </div>
          <div class="usage-info-files">
            <div class="usage-info-files-label">{{ $t('totalFiles', [cloudDiskData.fileNum]) }}</div>
          </div>
        </div>
        <div class="usage-process">
          <div class="progress-bar">
            <div class="progress" :style="{ width: cloudDiskData.usagePercentage + '%' }"></div>
          </div>
        </div>
      </div>
      <div class="cloud-disk-expired">
        <div class="cloud-disk-expired-label">{{ $t('expirationTime') }}</div>
        <div class="cloud-disk-expired-time">{{ cloudDiskData.expirationTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CloudDiskCard',
  props: {
    cloudDiskData: {
      type: Object,
      default: () => ({
        totalSpace: '',
        usedSpace: '',
        usagePercentage: 0,
        fileNum: 0,
        expirationTime: ''
      })
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.cloud-disk-wrapper {
  width: 100%;
  margin-top: 10px;
  font-family: 'PingFang SC', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  .cloud-disk-content {
    width: calc(100% - 16px);
    height: 144px;
    border-radius: 10px;
    background: var(--bg-color-white, #ffffff);
    box-shadow: 0 2px 4px 0 #0000000d;
    padding: 16px 24px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
  }

  .cloud-disk-head {
    height: 26px;
    max-width: 80%;
    .cloud-disk-title {
      color: var(--text-color-primary, #1a1a1a);
      font-size: var(--font-size-h5-size, 18px);
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
    }

    .cloud-disk-head-tag {
      width: 36px;
      height: 22px;
      position: absolute;
      top: 0px;
      right: 0px;
      background: var(--brand-bg-color-default, #3277fc);
      border-radius: 0px 12px 0px 6px;
      color: var(--bg-color-white, #ffffff);
      font-size: var(--font-size-text-size, 12px);
      font-weight: 500;
      line-height: 22px;
      text-align: center;
    }
  }

  .cloud-disk-usage {
    width: 100%;
    height: 42px;
    margin: 12px 0px;
    .usage-info {
      width: 100%;
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .usage-info-space {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .usage-info-space-label {
          color: var(--text-color-placeholder, #9a9ca2);
          font-size: var(--font-size-body2-size, 14px);
          font-weight: 400;
          line-height: 22px;
          text-align: left;
          margin-right: 4px;
        }
        .usage-info-space-value {
          color: var(--text-color-primary, #1a1a1a);
          font-size: var(--font-size-body2-size, 14px);
          font-weight: 600;
          line-height: 22px;
          text-align: left;
        }
      }
      .usage-info-files {
        .usage-info-files-label {
          color: var(--text-color-placeholder, #9a9ca2);
          font-size: var(--font-size-body2-size, 14px);
          font-weight: 400;
          line-height: 22px;
          text-align: left;
          margin-right: 4px;
        }
      }
    }
    .usage-process {
      width: 100%;
      height: 8px;
      margin-top: 8px;
      .progress-bar {
        width: 100%;
        height: 8px;
        background: #e8e8e8;
        border-radius: 2px;

        .progress {
          height: 100%;
          background: var(--brand-bg-color-default, #3277fc);
          border-radius: 2px;
          transition: width 0.3s ease;
        }
      }
    }
  }

  .cloud-disk-expired {
    width: 100%;
    height: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: var(--font-size-text-size, 12px);
    color: var(--text-color-placeholder, #9a9ca2);
    .cloud-disk-expired-label {
      margin-right: 6px;
    }
  }
}
</style>
