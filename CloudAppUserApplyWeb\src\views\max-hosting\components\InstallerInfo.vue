<template>
  <div class="installer-card">
    <div class="installer-info">
      <template v-if="isInstaller">
        <span class="info-item name installer">
          {{ data.loginName }}
        </span>
        <span class="info-item margin">
          {{ data.installerCoName }}
        </span>
      </template>
      <span v-else class="info-item name visitor">
        {{ data.loginName }}
      </span>
      <span class="info-item">
        <img src="@/assets/img/common/trusteeship/email_gray.svg" class="info-icon email" />
        <span>
          {{ data.email }}
        </span>
      </span>
      <span class="info-item">
        <img src="@/assets/img/common/trusteeship/phone_gray.svg" class="info-icon" />
        <span>
          {{ data.mobile }}
        </span>
      </span>
      <span class="info-item">
        <img src="@/assets/img/common/trusteeship/address_gray.svg" class="info-icon" />
        <span>
          {{ data.addr }}
        </span>
      </span>
    </div>
    <div class="installer-avatar">
      <van-image
        width="60"
        height="60"
        round
        :src="data.logo"
        :loading-icon="defaultAvatar"
        :error-icon="defaultAvatar"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'InstallerInfo',
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    isInstaller() {
      return this.data.installerCoName !== ''
    }
  },
  data() {
    return {
      defaultAvatar: require('@/assets/img/common/trusteeship/avatar.png')
    }
  }
}
</script>

<style lang="scss" scoped>
.installer-card {
  box-sizing: border-box;
  width: 100%;
  border-radius: 6px;
  background: linear-gradient(90deg, #d2e1f8 0%, #f0f1f7 100%);

  background-size: cover;
  display: flex;
  justify-content: flex-start;

  height: 142;
  padding-top: 16px;
  padding-right: 20px;
  padding-bottom: 12px;
  padding-left: 20px;
  gap: 8px;

  .installer-avatar {
    width: 72px;
    height: 72px;
    display: flex;
    flex-basis: 72px;
    align-items: center;
    justify-content: center;

    ::v-deep .van-image__error-icon .van-icon__image {
      width: 72px;
      height: 72px;
    }
    ::v-deep .van-image__loading-icon .van-icon__image {
      width: 72px;
      height: 72px;
    }
  }
  .installer-info {
    flex: 1;
    display: flex;
    width: 0;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;

    .info-icon {
      width: 14px;
      height: 14px;
      margin-right: 6px;
      margin-top: 3px;
      &.email {
        vertical-align: -3px;
      }
    }

    .info-item {
      color: var(--text-color-primary, #101d34);
      font-family: 'PingFang SC';
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      max-width: 100%;

      display: flex;
      align-items: flex-start;
      &.name {
        font-family: PingFang SC;
        font-weight: 600;
        font-size: var(--font-size-h5-size, 18px);
        line-height: 26px;
        vertical-align: middle;
      }
      &.visitor {
        margin-bottom: 11px;
      }
      &.margin {
        margin-bottom: 16px;
      }
      span {
        word-break: break-all;
        text-align: left;
      }
    }
  }
}
</style>
