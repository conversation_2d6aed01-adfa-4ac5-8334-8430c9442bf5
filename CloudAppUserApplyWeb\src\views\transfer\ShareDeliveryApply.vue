<template>
  <div class="device-transfer-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <!-- 转移申请时展示的内容 -->
    <template v-if="recordStatus === 0">
      <div class="device-transfer-content">
        <div :class="['step-box', `step-box-${step}`]" v-if="hasTrustApply">
          <common-step v-model="step" :stepList="stepList" />
        </div>
        <!-- 设备转移步骤时展示的内容 -->
        <div :class="['transfer-content', hasTrustApply ? '' : 'nostep-transfer-content']" v-show="step === 1">
          <div class="installer-info-box">
            <installer-info :data="transferInstallerInfo" />
          </div>
          <div class="trusteeship-desc text-over-ellipsis2">
            {{ $t('deviceShareTip') }}
          </div>
          <div class="trusteeship-site-box">
            <site-info-box :siteName="siteInfo.siteName" @help="handleHelp" />
          </div>
          <div class="device-list-wrapper">
            <div class="device-list-title">{{ $t('shareDevice') }}</div>
            <div class="device-list-content">
              <template v-if="transferDevList.length === 0">
                <no-data :text="$t('noDeviceTransfer')" />
              </template>
              <template v-else>
                <div class="device-list-box">
                  <device-list :dataList="transferDevList" />
                  <van-field
                    v-model="remark"
                    rows="6"
                    disabled
                    autosize
                    type="textarea"
                    class="host-remark"
                    :placeholder="$t('remarkPlaceholder')"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>
        <!-- 托管权限申请步骤时展示的内容 -->
        <div :class="['trusteeship-content', hasTrustApply ? '' : 'nostep-trusteepship-content']" v-show="step === 2">
          <div class="trusteeship-desc text-over-ellipsis2">
            {{ $t('shareApplyTip') }}
          </div>
          <div class="trusteeship-site-box">
            <site-info-box :siteName="siteInfo.siteName" @help="handleHelp" />
          </div>
          <div class="trusteeship-list-wrapper">
            <template v-if="initTrustReq && trusteeshipDevList.length === 0">
              <no-data :text="$t('noDeviceHosting')" />
            </template>
            <template v-else>
              <div class="trusteeship-list-box">
                <trusteeship-check type="share" :dataList="trusteeshipDevList" :authList="authList"></trusteeship-check>
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>
    <!-- 申请详情时展示的内容 -->
    <template v-if="[1, 2, 3, 4].includes(recordStatus)">
      <div class="device-transfer-content">
        <div class="installer-info-box">
          <installer-info :data="transferInstallerInfo" />
        </div>
        <div class="trusteeship-desc text-over-ellipsis2">
          {{ $t('deviceShareTip') }}
        </div>
        <!-- 站点信息 -->
        <div class="trusteeship-site-box">
          <site-info-box :siteName="siteInfo.siteName" @help="handleHelp" />
        </div>
        <div class="device-list-wrapper">
          <div class="device-list-title">{{ $t('shareDevice') }}</div>
          <div :class="['detail-device-list-content', hasTrustApply ? '' : 'nostep-detail-device-list-content']">
            <template v-if="initReq && deviceData.length === 0">
              <no-data :text="$t('noDeviceShare')" />
            </template>
            <template v-else>
              <div class="detail-device-list-box">
                <!-- 有托管申请且转移申请同意后才展示托管的权限和状态 -->
                <device-list :dataList="deviceData" :showAuthTime="!!trustTicketId" />
                <van-field
                  v-model="remark"
                  rows="6"
                  disabled
                  autosize
                  type="textarea"
                  class="host-remark"
                  :placeholder="$t('remarkPlaceholder')"
                />
              </div>
            </template>
          </div>
        </div>
      </div>
    </template>
    <div class="footer">
      <template v-if="recordStatus === 0">
        <van-button plain class="footer-btn footer-btn-short" type="default" @click="handleReject">
          {{ $t('rejectAll') }}
        </van-button>
        <van-button class="footer-btn footer-btn-short" type="primary" @click="handleTransfer">
          {{ step === 1 ? $t('accept') : $t('agree') }}
        </van-button>
      </template>
      <template v-if="[1, 2, 3, 4].includes(recordStatus)">
        <van-button v-if="recordStatus === 1" class="footer-btn" type="primary" :disabled="true">
          {{ $t('agreed') }}
        </van-button>
        <van-button v-if="recordStatus === 2" class="footer-btn" type="primary" :disabled="true">
          {{ $t('rejected') }}
        </van-button>
        <van-button v-if="recordStatus === 3" class="footer-btn" type="primary" :disabled="true">
          {{ $t('cancelled') }}
        </van-button>
        <van-button v-if="transferStatus === 4" class="footer-btn" type="primary" :disabled="true">
          {{ $t('expired') }}
        </van-button>
      </template>
    </div>
    <!-- 站点说明弹窗 -->
    <site-desc-popup :visible.sync="siteDescVisible" />
    <!-- 拒绝转移二次确认弹窗 -->
    <van-popup v-model="showReject" round position="bottom" :style="{ height: '224px' }">
      <popup-confirm
        :title="$t('rejectDeviceShare')"
        :text="$t('rejectShareDesc')"
        :cancelText="$t('cancel')"
        :confirmText="$t('stillReject')"
        @cancel="cancelReject"
        @confirm="confirmReject"
      />
    </van-popup>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import { appClose, receiveDevice, appLog, getCacheData, setCacheData } from '@/utils/appbridge'
import { AUTH_LIST, DEVICE_CAPABILITY_LIST } from '@/utils/options.js'
import { getTransferInfo, confirmTransfer, getTrusteeshipInfo, dealTrusteeshipApply } from '@/api/transfer'
import CommonStep from './components/CommonStep.vue'
import InstallerInfo from '../max-hosting/components/InstallerInfo.vue'
import DeviceList from './components/DeviceList.vue'
import TrusteeshipCheck from './components/trusteeshipCheck'
import PopupConfirm from '@/components/PopupConfirm.vue'
import SiteDescPopup from '@/views/max-hosting/components/SiteDescPopup.vue'
import NoData from '@/views/transfer/components/NoData.vue'
import SiteInfoBox from './components/SiteInfoBox.vue'
import { getInstallerInfo } from '@/api/maxHosting'
import { formatInstallerInfo } from '../max-hosting/common'

const STATUS = {
  PENDING: 0,
  ACCEPTED: 1,
  REJECTED: 2,
  CANCELLED: 3,
  EXPIRED: 4
}

export default {
  name: 'TransferManagApply',
  components: {
    NavBar,
    CommonStep,
    InstallerInfo,
    DeviceList,
    TrusteeshipCheck,
    PopupConfirm,
    SiteDescPopup,
    NoData,
    SiteInfoBox
  },
  props: {},
  data() {
    return {
      authList: AUTH_LIST().slice(1), // 不带配置权限
      siteId: null,
      transferTicketId: null, // 分享交付记录id
      trustTicketId: null, // 站点托管id
      deviceTrustTicketId: null, // 设备托管id
      initReq: false, // 是否初始化请求过
      initTrustReq: false, // 托管详情是否请求过
      hasTrustApply: false, // 分享交付时是否有托管申请
      step: 1,
      transferInstallerInfo: {}, // 安装商信息
      siteInfo: {}, // 站点信息
      transferStatus: STATUS.ACCEPTED, // 转移状态 -- 0：待处理 1：已接受  2：已拒绝 3：已取消 4：已过期
      trusteeshipStatus: STATUS.ACCEPTED, // 托管状态 -- 0：待接收 1、已接受 2、已拒绝 3、已取消 4：已过期
      deviceCapabilitys: DEVICE_CAPABILITY_LIST(), // 全量设备权限
      transferDevList: [],
      trusteeshipDevList: [],
      deviceData: [],
      installerUserId: null, // 托管的安装商ID
      storeDeviceData: [], // 设备转移暂存数据
      showReject: false, // 拒绝转移二次确认弹窗
      siteDescVisible: false, // 站点说明弹窗
      transferStoreObj: {}, // 转移申请暂存数据
      recordStatus: null, // 转移和托管最终状态--两个申请的组合状态
      // 0: 待处理（转移或托管有一个）
      // 1: 已接受（转移申请接受、托管申请接受/拒绝/过期情况下整体状态展示已接受）
      // 2: 已拒绝（转移申请拒绝，托管申请同时默认为拒绝）
      // 3: 已取消（站点删除等特殊情况下整体状态为已取消）
      // 4: 已过期（转移申请过期，注意：转移申请接受后，托管申请过期时等同于转移接受托管拒绝）
      remark: '' // 备注信息
    }
  },
  computed: {
    stepList() {
      return [
        {
          step: 1,
          title: this.$t('deviceShare')
        },
        {
          step: 2,
          title: this.$t('trusteeshipAuthApply')
        }
      ]
    }
  },
  async created() {
    // 从路由中获取type,用作默认
    const query = this.$route.query
    if (query.installerUserId && query.installerUserId !== 'null') {
      this.installerUserId = query.installerUserId
    }
    if (query.siteId) {
      this.siteId = query.siteId
    }
    if (query.transferTicketId) {
      this.transferTicketId = query.transferTicketId
    }
    // 有托管id则说明有托管申请
    if (query.trustTicketId && query.trustTicketId !== 'null') {
      this.trustTicketId = query.trustTicketId
      this.hasTrustApply = true
    } else {
      this.hasTrustApply = false
    }
    if (query.deviceTrustTicketId && query.deviceTrustTicketId !== 'null') {
      this.deviceTrustTicketId = query.deviceTrustTicketId
    }
    this.refreshData()
    // 模拟数据构造--后续删除
    this.initMockData(1, 1)
  },
  methods: {
    back() {
      // 如果转移申请进入步骤2，则需要将对应步骤、设备勾选和权限变更都记录下来，以便再次进入时还原
      if (this.step === 2) {
        // 获取勾选设备
        const deviceData = this.trusteeshipDevList.map(item => {
          const { sn, flag, checkCapability, trustDuration } = item
          return {
            sn,
            flag,
            checkCapability,
            trustDuration
          }
        })
        this.transferStoreObj[`${this.siteId}_${this.transferTicketId}`] = {
          step: this.step,
          deviceData
        }
        this.setTransferData(this.transferStoreObj)
      }
      appClose()
    },
    initMockData(type = 0, step = 1) {
      /**
       * type： 0 待审批（仅仅分享申请，不含托管）
       * type： 1 待审批（有分享和托管）
       * type： 2 已接受（仅分享）
       * type： 3 已接受（有分享和托管）
       * type： 4 已拒绝（仅分享或者有分享和托管时拒绝分享）
       * type： 5 已拒绝（有分享和托管时同意分享拒绝托管）
       * type： 6 已取消（仅分享）
       * type： 7 已取消（有分享和托管）
       */
      /**
       * step： 1 当前处于设备分享审批步骤
       * step： 2 当前处于托管权限申请步骤
       */
      // 模拟数据构造--后续删除
      this.transferInstallerInfo = {
        // 模拟数据-后续删除
        logo: null,
        installerCoName: '企业名称111',
        loginName: null,
        email: 'Mike <EMAIL>',
        mobile: '+86 6627836628',
        addr: 'United States'
      } // 安装商信息
      if (type === 0 || type === 1) {
        this.recordStatus = STATUS.PENDING // 分享和托管待审批
        this.hasTrustApply = type === 1
        this.trustTicketId = type === 1 ? '123456789' : null
      } else if (type === 2) {
        this.recordStatus = STATUS.ACCEPTED // 仅分享已接受
      } else if (type === 3) {
        this.recordStatus = STATUS.ACCEPTED // 分享和托管已接受
        this.trustTicketId = '123456789'
        this.hasTrustApply = true
      } else if (type === 4 || type === 5) {
        this.recordStatus = STATUS.REJECTED // 分享已拒绝
      } else if (type === 6) {
        this.recordStatus = STATUS.CANCELLED // 仅分享已取消
      } else if (type === 7) {
        this.recordStatus = STATUS.EXPIRED // 分享和托管已取消
        this.trustTicketId = '123456789'
        this.hasTrustApply = true
      }
      this.step = step // 当前步骤 1 设备分享 2 托管权限申请
      // 站点信息
      this.siteInfo = {
        siteName: '站点名称1',
        siteAddr: 'United States'
      }
      // 分享申请设备设备列表
      this.transferDevList = [
        { deviceName: '设备名称1', checkCapability: this.deviceCapabilitys.slice(), trustDuration: 0 },
        { deviceName: '设备名称2', checkCapability: this.deviceCapabilitys.slice(), trustDuration: 0 },
        { deviceName: '设备名称3', checkCapability: this.deviceCapabilitys.slice(), trustDuration: 0 }
      ]
      // 托管申请设备列表
      this.trusteeshipDevList = [
        { deviceName: '设备名称1', flag: true, checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 },
        { deviceName: '设备名称2', flag: true, checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 },
        { deviceName: '设备名称3', flag: true, checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 }
      ]
      // 托管详情设备列表
      this.deviceData = [
        { deviceName: '设备名称1', checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 },
        { deviceName: '设备名称2', checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 },
        { deviceName: '设备名称3', checkCapability: this.deviceCapabilitys.slice(1), trustDuration: 0 }
      ]
    },
    // 刷新数据
    async refreshData() {
      // 获取安装商信息
      if (this.installerUserId) {
        this.getInstallerInfo()
      }
      // 获取转移详情
      if (this.transferTicketId) {
        this.getTransferInfo()
      }
      if (this.trustTicketId) {
        // 先获取设备托管暂存的数据
        await this.getTransferInfoStore()
        // 获取托管详情
        this.getTrusteeshipInfo()
      }
    },
    // 获取安装商信息
    async getInstallerInfo() {
      try {
        const { data: detail } = await getInstallerInfo({
          userId: this.installerUserId
        })
        this.transferInstallerInfo = formatInstallerInfo(detail)
      } catch (error) {
        console.error(error)
      }
    },
    handleError(error, operation) {
      console.error(`${operation}失败:`, error)
      appLog('log/error', `${operation}失败: ${error.message}`)
    },
    // 获取转移申请暂存数据--操作到托管权限申请退出时保存的数据
    async getTransferInfoStore() {
      try {
        const data = await new Promise(resolve => {
          getCacheData('transferData', resolve)
        })

        if (!data) return

        const transferData = this.parseTransferData(data)
        if (!transferData) return

        this.updateTransferState(transferData)
      } catch (error) {
        this.handleError(error, '获取缓存数据')
      }
    },
    parseTransferData(data) {
      try {
        const obj = JSON.parse(data)
        if (!obj?.body) return null
        return JSON.parse(obj.body)
      } catch (error) {
        this.handleError(error, '解析缓存数据')
        return null
      }
    },
    updateTransferState(transferStoreObj) {
      this.transferStoreObj = transferStoreObj
      const transferData = transferStoreObj[`${this.siteId}_${this.transferTicketId}`]
      if (transferData) {
        const { step, deviceData } = transferData
        this.step = step
        this.storeDeviceData = deviceData
      }
    },
    setTransferData(data) {
      setCacheData({ key: 'transferData', value: JSON.stringify(data) })
    },
    // 清除当前设备转移的暂存数据，拒绝或同意成功后
    clearTransferData() {
      const transferStoreObj = this.transferStoreObj
      delete transferStoreObj[`${this.siteId}_${this.transferTicketId}`]
      this.setTransferData(transferStoreObj)
    },
    // 查询转移详情
    async getTransferInfo({ callback } = {}) {
      try {
        // 没有对应id直接返回
        if (!this.siteId || !this.transferTicketId) {
          return
        }
        const params = { siteId: this.siteId, ticketId: this.transferTicketId }
        const res = await getTransferInfo(params)
        const { data } = res
        const { site, deviceTransferOutVos = [], serviceTicket } = data
        this.siteInfo = { ...site }
        this.transferDevList = deviceTransferOutVos
        // 判断站点转移的状态--  0：待处理 1：已接受  2：已拒绝 3：已取消 4：已过期
        const { status = 0, remark } = serviceTicket || {}
        this.transferStatus = Number(status)
        this.remark = remark
        if (!this.trustTicketId) {
          // 没有托管记录则直接确定状态
          this.recordStatus = Number(status)
          if (Number(status) >= 1) {
            // 展示详情
            this.deviceData = deviceTransferOutVos
          }
        } else {
          this.transferStatus = Number(status)
        }
        if (Number(status) > 0) {
          this.step = 2
        }
        this.initReq = true
      } catch (error) {
        console.error(error)
      } finally {
        callback && callback()
      }
    },
    // 查询托管详情
    async getTrusteeshipInfo() {
      try {
        if (!this.trustTicketId) {
          return
        }
        const ticketIds = []
        if (this.trustTicketId) {
          ticketIds.push(this.trustTicketId)
        }
        if (this.deviceTrustTicketId) {
          ticketIds.push(this.deviceTrustTicketId)
        }
        const params = { siteId: this.siteId, ticketIds }
        const res = await getTrusteeshipInfo(params)
        const { deviceTrusts, status } = res.data
        this.trusteeshipDevList = (deviceTrusts || []).map(item => {
          const deviceItem = this.storeDeviceData.find(item2 => item2.sn === item.sn)
          if (deviceItem) {
            // 找到暂存的则用暂存的
            return { ...item, ...deviceItem }
          } else {
            return {
              ...item,
              flag: true, // 默认勾选
              checkCapability: this.deviceCapabilitys.filter(o => item.authList && item.authList.includes(o.value)),
              trustDuration: item.trustDuration === null ? null : Number(item.trustDuration)
            }
          }
        })
        this.trusteeshipStatus = Number(status)
        // 0：待处理 1：已接受  2：已拒绝 3：已取消 4：已过期
        switch (Number(status)) {
          case 0:
            this.recordStatus = 0
            break
          case 1:
            this.recordStatus = 1
            break
          case 2:
            this.recordStatus = 2
            break
          case 3:
            this.recordStatus = 3
            break
          case 4:
            if (this.transferStatus === 1) {
              // 转移已经接受，但是托管过期，则等通过转移同意，托管拒绝状态
              this.recordStatus = 1
              this.trusteeshipStatus = 2
            }
            break
          default:
            this.recordStatus = Number(status)
            break
        }
        if (this.recordStatus > 0) {
          // 如果托管不是待接收，则更新详情中的设备列表数据
          this.deviceData = this.trusteeshipDevList.slice()
        }
        this.initTrustReq = true
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    },
    // 点击站点帮助
    handleHelp() {
      this.siteDescVisible = true
    },
    // 拒绝或同意托管
    async dealTrusteeship(flag, callback) {
      if (!this.trustTicketId) {
        return
      }
      try {
        const ticketIds = []
        if (this.trustTicketId) {
          ticketIds.push(this.trustTicketId)
        }
        if (this.deviceTrustTicketId) {
          ticketIds.push(this.deviceTrustTicketId)
        }
        const params = {
          ticketIds,
          trusts: {
            installerUserId: this.installerUserId,
            siteId: this.siteId
          }
        }
        // 遍历托管设备，构造传参
        // 勾选的设备才传
        let operateType = flag ? 1 : 2 //  1 同意 2 拒绝
        const deviceTrusts = this.trusteeshipDevList
          .filter(record => record.flag)
          .map(item => {
            const { sn, checkCapability, trustDuration } = item
            return {
              sn,
              authList: checkCapability.map(item2 => item2.value),
              trustDuration
            }
          })
        // 全部设备均未勾选时点击同意相当于全部拒绝
        if (flag && deviceTrusts.length === 0) {
          operateType = 2
        }
        if (operateType === 2) {
          // 拒绝则不展示权限和时长
          this.trusteeshipDevList = this.trusteeshipDevList.map(item => ({
            ...item,
            authList: [],
            checkCapability: [],
            trustDuration: null
          }))
        }
        params.trusts.deviceTrusts = deviceTrusts
        params.operateType = operateType
        await dealTrusteeshipApply(params)
        if (callback) {
          callback('SUCCESS', operateType)
        }
      } catch (err) {
        console.error(err)
        this.$loading.hide()
        // 重新请求刷新详情
        this.refreshData()
      }
    },
    // 点击全部拒绝
    async handleReject() {
      // 有托管时
      if (this.trustTicketId) {
        if (this.step === 1) {
          // 弹窗二次确认
          this.showReject = true
        } else {
          // 托管审核点击拒绝不展示二次确认
          const callback = msg => {
            this.$loading.hide()
            if (msg === 'SUCCESS') {
              this.recordStatus = 2
              this.trusteeshipStatus = 2
              this.deviceData = this.trusteeshipDevList.slice()
              this.$toast({
                className: 'max-wide-custom-toast',
                message: this.$t('rejectTrusteeshipDesc', [this.$t('service')]),
                icon: require('@/assets/img/common/success_icon.png')
              })
              this.clearTransferData()
            }
          }
          this.$loading.show()
          this.dealTrusteeship(false, callback)
        }
      } else if (this.transferTicketId) {
        // 拒绝转移
        const callback = msg => {
          if (msg === 'SUCCESS') {
            this.recordStatus = 2
            this.transferStatus = 2
            this.deviceData = this.transferDevList.slice()
          }
        }
        this.dealTransfer(false, callback)
      }
    },
    // 取消拒绝
    cancelReject() {
      this.showReject = false
    },
    // 拒绝或同意转移
    async dealTransfer(accept, callback) {
      // 接受转移
      try {
        this.$loading.show()
        const result = await confirmTransfer({ siteId: this.siteId, ticketId: this.transferTicketId, accept })
        if (result) {
          // 通知APP刷新设备列表
          receiveDevice({ type: 'transfer' })
        }
        if (!accept) {
          this.showReject = false
          // 修改转移记录状态为拒绝
          this.transferStatus = 2
          this.$loading.hide()
          this.$toastSuccess(this.$t('rejected'))
        } else {
          this.$loading.hide()
          this.$toastSuccess(this.$t('agreed'))
        }
        if (callback) {
          callback('SUCCESS')
        }
      } catch (error) {
        console.error(error)
        if (!accept) {
          this.showReject = false
        }
        this.$loading.hide()
        // 重新请求刷新详情--需要显示toast提示
        this.refreshData()
      }
    },
    // 拒绝转移弹窗确认
    async confirmReject() {
      // 拒绝转移
      this.dealTransfer(false)
      // 有托管申请则同步拒绝托管
      if (this.trustTicketId) {
        const callback = msg => {
          if (msg === 'SUCCESS') {
            this.recordStatus = 2
            this.transferStatus = 2
            this.deviceData = this.trusteeshipDevList.slice()
          }
        }
        this.dealTrusteeship(false, callback)
      }
    },
    // 接受转移/托管按钮点击
    async handleTransfer() {
      if (this.step === 1) {
        const callback = async msg => {
          if (msg === 'SUCCESS') {
            if (this.trustTicketId) {
              // 有托管则进入下一步
              // 刷新托管详情
              await this.getTrusteeshipInfo()
              this.step = 2
            } else {
              // 展示转移设备
              this.deviceData = this.transferDevList.slice()
              // 修改当前状态为接受
              this.transferStatus = 1
              this.recordStatus = 1
            }
          }
        }
        this.dealTransfer(true, callback)
      } else {
        // 接受托管时，如果站点下无设备或者有设备但是没有勾选时，同意等同于全部拒绝，所以回调函数中需要额外判断下最终是拒绝还是同意
        const callback = (msg, operateType) => {
          if (msg === 'SUCCESS') {
            this.$loading.hide()
            if (operateType === 1) {
              // 同意托管
              this.recordStatus = 1
              this.trusteeshipStatus = 1
              this.deviceData = this.trusteeshipDevList.slice()
              this.$toast({
                className: 'max-wide-custom-toast',
                message: this.$t('agreeTrusteeshipDesc', [this.$t('service')]),
                icon: require('@/assets/img/common/success_icon.png')
              })
            } else {
              // 拒绝托管
              this.recordStatus = 2
              this.trusteeshipStatus = 2
              this.deviceData = this.trusteeshipDevList.slice()
              this.$toast({
                className: 'max-wide-custom-toast',
                message: this.$t('rejectTrusteeshipDesc', [this.$t('service')]),
                icon: require('@/assets/img/common/success_icon.png')
              })
            }
            this.clearTransferData()
          }
        }
        this.$loading.show()
        this.dealTrusteeship(true, callback)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.device-transfer-wrapper {
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  .nav-bar {
    background-color: transparent;
  }
  .menu {
    position: absolute;
    top: 44px;
    right: 10px;
  }
  .device-transfer-content {
    width: 100%;
    height: calc(100% - 130px);
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .transfer-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      overflow: hidden;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }
    .nostep-transfer-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      box-sizing: border-box;
    }
    .step-box {
      width: 100%;
      height: 100px;
      display: flex;
      align-items: center;
    }
    .step-box-2 {
      height: 80px;
      padding: 10px 0px;
      box-sizing: border-box;
    }
    .installer-info-box {
      width: 100%;
      height: 150px;
      padding: 10px 10px;
      box-sizing: border-box;
    }
    .device-list-wrapper {
      width: 100%;
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .device-list-title {
        width: 100%;
        height: 40px;
        padding: 0px 14px 0 28px;
        box-sizing: border-box;
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
        color: var(--text-color-primary, #82879b);
      }
      .device-list-content {
        width: 100%;
        flex: 1;
        overflow: auto;
        box-sizing: border-box;
      }
      .detail-device-list-content {
        width: 100%;
        flex: 1;
        overflow: auto;
        box-sizing: border-box;
      }
      .nostep-detail-device-list-content {
        width: 100%;
        flex: 1;
        overflow: auto;
        box-sizing: border-box;
      }
    }
    .trusteeship-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;
    }
    .trusteeship-desc {
      min-height: 22px;
      height: auto; // 允许自动增长
      // height: 44px;
      padding: 0px 24px;
      color: var(--text-color-primary, #82879b);
      font-size: var(--font-size-body2-size, 14px);
      line-height: 22px;
      margin-bottom: 10px;
    }
    .trusteeship-site-box {
      width: 100%;
      height: 52px;
      padding: 14px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      .site-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 100%;
        img {
          width: 24px;
          height: 24px;
        }
        .help-icon {
          width: 16px;
          height: 16px;
        }
        .site-title {
          font-size: var(--font-size-body1-size, 16px);
          margin: 0px 6px;
        }
      }
      .site-right {
        flex: 1;
        height: 100%;
        .site-name {
          width: 100%;
          text-align: right;
        }
      }
    }
    .trusteeship-list-wrapper {
      width: 100%;
      flex: 1;
      overflow: auto;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }
    .trusteeship-list-box {
      box-sizing: border-box;
      flex: 1;
      overflow: auto;
    }
  }
  .host-remark {
    width: calc(100% - 32px);
    box-sizing: border-box;
    margin: 20px auto 0;
    background: var(--brand-bg-color-light-disabled, #f2f4f8);
    border: 1px solid var(--bg-color-secondary, #eeeeee);
    border-radius: 8px;
  }
}
.footer {
  .footer-btn-short {
    width: 150px;
    margin: 0px 20px;
  }
}
.installer-card {
  background-image: url('@/assets/img/common/trusteeship/bind_bg.png');
  background-repeat: no-repeat;
  background-position-y: 0px;
  background-position-x: center;
  background-size: 100%;
}
</style>
