<template>
  <!-- 用户反馈提交弹窗 -->
  <van-popup v-model="show" round position="bottom" @click-overlay="cancel" class="pop-dialog">
    <div class="pop-div">
      <div class="dialog-title">{{ $t('userFeedback') }}</div>
      <div class="dialog-close-img" @click="cancel">
        <theme-image alt="close" imageName="close_2.png" />
      </div>
    </div>
    <div class="feedback-info">{{ $t('feedBackInfo') }}</div>
    <stars-choose @change="starsChange" class="choose"></stars-choose>
    <div class="description">
      <van-field
        v-model="description"
        rows="5"
        autosize
        type="textarea"
        maxlength="200"
        :placeholder="$t('feedBackPlaceholder2')"
        show-word-limit
      />
    </div>
    <van-button :class="['footer-btn', score && description ? '' : 'button-disabled']" @click="submit" type="primary">
      {{ $t('submit') }}
    </van-button>
  </van-popup>
</template>
<script>
import StarsChoose from '@/views/user-feedback/StarsChoose'
import { closeDialog, showToast } from '@/utils/appbridge'
import ThemeImage from '@/components/ThemeImage.vue'
import { feedbackAdd } from '@/api/feedback.js'
import { mapState } from 'vuex'
export default {
  name: 'feedbackPopup',
  components: {
    StarsChoose,
    ThemeImage
  },
  props: {},
  data() {
    return {
      show: true,
      score: 0,
      description: ''
    }
  },
  created() {},
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'appId'])
  },
  methods: {
    initParams() {
      this.score = 0
      this.description = ''
    },
    // 清除数据 关闭弹框
    cancel() {
      this.initParams()
      showToast({
        msg: this.$t('feedBackFinish'),
        style: 0
      })
      closeDialog()
    },
    // 提交数据 关闭弹框
    async submit() {
      if (this.score === 0) return
      if (!this.description) return
      // 调用接口
      this.$loading.show()
      let params = {
        score: this.score,
        description: this.description,
        appId: this.appId,
        version: this.version
      }
      try {
        let res = await feedbackAdd(params)
        this.$loading.hide()
        if (res.basic.code === 200) {
          showToast({
            msg: this.$t('feedBackSuccess'),
            style: 0
          })
        }
      } catch (err) {
        this.$loading.hide()
        console.error(err)
      }
      closeDialog()
    },
    // 选中的星星数
    starsChange(data) {
      this.score = data
    }
  }
}
</script>
<style lang="scss" scoped>
.pop-dialog {
  width: 100%;
  border-radius: 12px 12px 0px 0px;
  .pop-div {
    position: relative;
  }
  .dialog-title {
    font-weight: 600;
    height: 52px;
    line-height: 52px;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--text-color-primary, #1a1a1a);
    text-align: center;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  }
  .dialog-close-img {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    img {
      width: 100%;
      height: 100%；;
    }
  }
  .choose {
    ::v-deep .stars-note {
      margin: 10px 16px 10px 16px;
    }
  }
  .feedback-info {
    font-size: var(--font-size-body1-size, 16px);
    margin: 30px 32px 20px 32px;
  }
  .description {
    margin: 10px 16px;
    ::v-deep .van-field__control {
      max-height: 140px; /* 设置最大高度 */
      padding-left: 2px;
      padding-right: 2px;
    }
  }
  .van-cell {
    border-radius: 6px;
  }
  ::v-deep .van-field__word-limit {
    color: var(--text-color-placeholder, #a3a3a3);
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 20px auto 40px auto;
    color: #ffffffe6;
  }
  ::v-deep .van-button--primary {
    border: 0;
  }
}
</style>
<style lang="scss">
html body #app:has(.app-container .pop-dialog) {
  background-color: var(--bg-color-dialogs, #0000001a);
}
.app-container:has(.pop-dialog) {
  background-color: transparent;
}
</style>
