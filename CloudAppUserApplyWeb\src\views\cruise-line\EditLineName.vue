<template>
  <div class="cruise-line-wrapper">
    <van-dialog
      v-model="show"
      :title="$t('cruiseLineName')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="cruise-line-div">
          <input
            type="text"
            class="common-input"
            v-model="linename"
            maxlength="63"
            :placeholder="$t('pleaseEnterLine')"
          />
          <span class="cruise-line-close">
            <img
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/input_close.png')"
              alt=""
              v-if="linename"
              @click="linename = ''"
            />
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'editLineName',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  components: {},
  data() {
    return {
      show: false,
      linename: ''
    }
  },
  watch: {
    name: {
      handler(val) {
        this.linename = val
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('cruiseLine', ['devType', 'curiseRecord', 'cruisesList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      if (this.devType !== '1') {
        // IPC设备可以不要名字，NVR则需要填写名称
        if (!this.linename || !this.linename.trim()) {
          this.$toast(this.$t('pleaseEnterLine'))
          return false
        }
      }
      const linename = this.linename ? this.linename.trim() : null
      // 不用考虑跟自己重名的问题
      const nameSet = new Set(
        this.cruisesList.filter(item => item.index !== this.curiseRecord.index).map(item => item.name)
      )
      if (nameSet.has(linename)) {
        this.$toast(this.$t('lineNameExist'))
        return false
      }
      const name = this.name ? this.name.trim() : null
      if (linename === name) {
        // 说明没有改动，不用调接口
        this.cancel()
        return
      }
      this.$emit('confirm', linename)
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel')
    },
    // 清除数据
    clearParam() {
      this.pointname = this.name
    }
  }
}
</script>
<style lang="scss" scoped>
.cruise-line-wrapper {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--icon-color-primary, #393939);
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .cruise-line-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
    border: 1px solid var(bg-color-secondary, #d9d9d9);
    padding: 4px 2px;
    border-radius: 6px;
    margin-top: 20px;
    position: relative;
    .cruise-line-close {
      position: absolute;
      right: 10px;
      top: 8px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
  .common-input {
    width: 220px;
    padding-left: 8px;
  }
}
</style>
