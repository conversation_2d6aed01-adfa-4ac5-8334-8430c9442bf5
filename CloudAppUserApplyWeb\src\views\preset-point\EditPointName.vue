<template>
  <div class="point-name-wrapper">
    <van-dialog
      v-model="show"
      :title="$t('presetPointName')"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="handleConfirm"
      @cancel="cancel"
      @open="clearParam"
      :cancelButtonText="$t('cancel')"
      :confirmButtonText="$t('confirm')"
    >
      <div class="content-div">
        <div class="point-name-div">
          <input
            type="text"
            class="common-input"
            v-model="pointname"
            maxlength="63"
            :placeholder="$t('pleaseEnterPoint')"
          />
          <span class="point-name-close">
            <img
              :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/input_close.png')"
              v-if="pointname"
              @click="pointname = ''"
            />
          </span>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'editPointName',
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  components: {},
  data() {
    return {
      show: false,
      pointname: ''
    }
  },
  watch: {
    name: {
      handler(val) {
        this.pointname = val
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('presetPoint', ['pointList', 'pointRecord']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    handleConfirm() {
      if (!this.pointname || !this.pointname.trim()) {
        this.$toast(this.$t('pleaseEnterPoint'))
        return false
      }
      const name = this.pointname.trim()
      // 不用考虑跟自己重名的问题
      const nameSet = new Set(
        this.pointList.filter(item => item.index !== this.pointRecord.index).map(item => item.name)
      )
      if (nameSet.has(name)) {
        this.$toast(this.$t('pointNameExist'))
        return false
      }
      this.$emit('confirm', this.pointname.trim())
    },
    // confirm 确认按钮；before-close控制关闭前的回调
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done(true)
      }
    },
    cancel() {
      this.show = false
      this.$emit('cancel', {})
    },
    // 清除数据
    clearParam() {
      this.pointname = this.name
    }
  }
}
</script>
<style lang="scss" scoped>
.point-name-wrapper {
  ::v-deep.van-dialog__header {
    font-weight: 700;
    font-size: var(--font-size-body1-size, 16px);
    color: var(--icon-color-primary, #393939);
  }
  .content-div {
    width: 264px;
    margin: auto;
  }
  .point-name-div {
    width: 260px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 12px;
    border: 1px solid var(bg-color-secondary, #d9d9d9);
    padding: 4px 2px;
    border-radius: 6px;
  }
  .common-input {
    width: 220px;
    padding-left: 8px;
  }
  .point-name-div {
    margin-top: 20px;
    position: relative;
    .point-name-close {
      position: absolute;
      right: 10px;
      top: 8px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
