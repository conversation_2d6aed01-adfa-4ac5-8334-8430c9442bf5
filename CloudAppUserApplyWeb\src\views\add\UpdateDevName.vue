<template>
  <div class="update-device-name">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="update-content-box">
      <div class="add-success-img">
        <van-image width="303px" height="120px" :src="devIcon" />
      </div>
      <div class="update-name-box">
        <van-field
          v-model="devName"
          clearable
          maxlength="18"
          :placeholder="$t('pleaseEnterDevName')"
          @input="filterEmojis"
        />
        <div :class="['update-tips', showInvalidTip ? 'update-tips-warn' : '']">
          {{ showInvalidTip ? $t('updateWarnTips', ['`$&|;?/:"<>*\\']) : $t('updateTips') }}
        </div>
      </div>
    </div>
    <div class="bottom-fixed-box">
      <van-button class="bottom-btn" round block :disabled="!devName" @click="onSave">{{ $t('confirm') }}</van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { modifyDeviceName, appBack, appRequestDeviceList } from '@/utils/appbridge'
import emojiRegex from 'emoji-regex'
export default {
  name: 'updateDevName',
  components: { NavBar },
  data() {
    return {
      devName: '',
      devIcon: null,
      showInvalidTip: false
    }
  },
  created() {
    this.getDevInfo()
  },
  methods: {
    validateInput(value) {
      // 检查特殊字符
      const specialChars = /[`$&|;?/:"<>*\\]/
      return specialChars.test(value)
    },
    filterEmojis(event) {
      const rawValue = event
      // 使用emoji-regex过滤掉表情符号
      const filteredValue = rawValue.replace(emojiRegex(), '')
      this.devName = filteredValue
      // 验证输入字符
      this.showInvalidTip = this.validateInput(filteredValue)
    },
    onSave() {
      if (this.validateInput(this.devName)) {
        this.$toastFail(this.$t('updateWarnTips', ['`$&|;?/:"<>*\\']))
        return
      }
      const devObj = JSON.parse(JSON.stringify(this.$route.query))
      const { deviceId } = devObj
      // 来自app
      const params = JSON.stringify({ deviceId, deviceName: this.devName })
      const callback = data => {
        console.log(data)
        if (data) {
          const obj = JSON.parse(data)
          if (obj && obj.body) {
            const res = JSON.parse(obj.body)
            if (res.errorCode === 200) {
              this.$toastSuccess(this.$t('changeSuccessfully'))
              appBack()
            } else if (res.errorCode === 209) {
              this.$toastFail(this.$t('errRenameTips'))
            } else {
              this.$toastFail(this.$t(`errorCode.${res.errorCode}`))
            }
          }
          console.log(obj)
        }
      }
      modifyDeviceName(params, callback)
    },
    getDevInfo() {
      // 获取设备信息
      const deviceId = this.$route.query?.deviceId
      if (deviceId) {
        const params = JSON.stringify({ idList: deviceId })
        appRequestDeviceList(params, res => {
          try {
            const deviceList = JSON.parse(JSON.parse(res)?.body).deviceList
            const devInfo = deviceList[0] || {}
            const { type, devName } = devInfo
            if (type === 1) {
              this.devIcon = require('@/assets/img/common/update_ipc_icon.png')
            } else {
              this.devIcon = require('@/assets/img/common/update_dev_icon.png')
            }
            // 特殊字符中的'进行了html硬编码，需要进行解析后显示
            const encodedString = devName
            const parser = new DOMParser()
            const doc = parser.parseFromString(encodedString, 'text/html')
            const decodeDevName = doc.documentElement.textContent
            this.devName = decodeDevName
            // 初始化时进行字符验证
            this.showInvalidTip = this.validateInput(decodeDevName)
          } catch (error) {
            console.log(error)
          }
        })
      }
    },
    back() {
      appBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.update-device-name {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color-white, #ffffff);
  .update-content-box {
    display: flex;
    flex-direction: column;
    padding: 0 36px;
    .add-success-img {
      margin: 40px 0;
    }
    .update-name-box {
      .van-cell {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 10px;
        padding: 14px 0;
        &::after {
          left: 0;
          right: 0;
        }
        .van-cell__value--alone {
          color: var(--text-color-primary, #1a1a1a);
        }
      }
      .update-tips {
        color: var(--text-color-placeholder, #a3a3a3);
        font-size: var(--font-size-text-size, 12px);
        font-weight: 400;
        line-height: 20px;
        text-align: center;
      }
      .update-tips-warn {
        color: var(--error-bg-color-default, #ff4545);
      }
    }
  }
}
.bottom-fixed-box {
  position: fixed;
  width: 100%;
  height: 80px;
  bottom: 0;
  left: 0;
  background-color: var(--bg-color-white, #ffffff);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px 32px 12px;
  box-sizing: border-box;
  .bottom-btn {
    background-color: var(--brand-bg-color-default, #3277fc);
    color: var(--anti-color-11, #ffffffe6);
    height: 40px;
    .van-button__text {
      font-weight: 500;
      font-size: var(--font-size-body1-size, 16px);
      line-height: 24px;
    }
  }
  .van-button--disabled {
    background-color: var(--brand-bg-color-disabled, #d6e4fe);
  }
}
</style>
