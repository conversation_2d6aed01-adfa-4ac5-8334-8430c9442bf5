import S3 from 'aws-sdk/clients/s3'
import { getContentType } from './common.js'

/**
 * AWS S3简单上传
 * 适用于小文件（通常小于5MB）的上传
 * @param {Object} uploadToken - 上传凭证配置
 * @param {File} file - 要上传的文件
 * @param {string} fileUrl - 文件在S3中的路径
 * @returns {Promise} 上传结果
 */
export const simpleUpload = (uploadToken, file, fileUrl) => {
  return new Promise((resolve, reject) => {
    const s3 = new S3({
      ...uploadToken,
      secretAccessKey: uploadToken.accessKeySecret,
      sessionToken: uploadToken.securityToken,
      region: uploadToken.region
    })

    const params = {
      Body: file,
      Bucket: uploadToken.bucketName,
      Key: fileUrl,
      ContentType: getContentType(file)
    }

    s3.putObject(params, (err, data) => {
      if (err) {
        console.error('AWS简单上传失败:', err)
        reject(err)
      } else {
        console.log('AWS简单上传成功:', data)
        resolve(data)
      }
    })
  })
}

/**
 * AWS S3分片上传
 * 适用于大文件（通常大于5MB）的上传
 * @param {Object} uploadToken - 上传凭证配置
 * @param {File} file - 要上传的文件
 * @param {string} fileUrl - 文件在S3中的路径
 * @param {Function} progressCallback - 进度回调函数
 * @returns {Promise} 上传结果
 */
export const multipartUpload = (uploadToken, file, fileUrl, progressCallback) => {
  return new Promise((resolve, reject) => {
    const s3 = new S3({
      ...uploadToken,
      secretAccessKey: uploadToken.accessKeySecret,
      sessionToken: uploadToken.securityToken,
      region: uploadToken.region
    })

    const params = {
      Body: file,
      Bucket: uploadToken.bucketName,
      Key: fileUrl
    }

    const upload = s3.upload(params, {
      queueSize: 3,
      partSize: 5 * 1024 * 1024,
      leavePartsOnError: false, // 失败时清理分片
      connectionTimeout: 60000,
      httpOptions: { timeout: 60000 }
    })

    upload.on('httpUploadProgress', e => {
      const percentage = e.loaded / e.total
      if (typeof progressCallback === 'function') {
        progressCallback(percentage)
      }
    })

    upload.send((err, data) => {
      if (err) {
        console.error('AWS分片上传失败:', err)
        reject(err)
      } else {
        console.log('AWS分片上传成功:', data)
        resolve(data)
      }
    })
  })
}

/**
 * 智能选择AWS上传方式
 * 根据文件大小自动选择简单上传或分片上传
 * @param {Object} uploadToken - 上传凭证配置
 * @param {File} file - 要上传的文件
 * @param {string} fileUrl - 文件在S3中的路径
 * @param {Function} progressCallback - 进度回调函数（仅分片上传时使用）
 * @returns {Promise} 上传结果
 */
export const smartUpload = (uploadToken, file, fileUrl, progressCallback) => {
  const fileSize = file.size
  const MULTIPART_THRESHOLD = 5 * 1024 * 1024 // 5MB阈值

  if (fileSize > MULTIPART_THRESHOLD) {
    return multipartUpload(uploadToken, file, fileUrl, progressCallback)
  } else {
    return simpleUpload(uploadToken, file, fileUrl)
  }
}
