<template>
  <span @click.stop="changeValue()">
    <van-icon v-if="value" name="passed" class="icon-select icon-select--selected" />
    <van-icon v-else name="circle" class="icon-select" />
  </span>
</template>

<script>
export default {
  name: 'SelectCircle',
  props: {
    value: {
      type: <PERSON>olean,
      require: true
    }
  },
  data() {
    return {
      name: ''
    }
  },

  methods: {
    changeValue() {
      this.$emit('changeValue', !this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-select {
  font-size: var(--font-size-h3-size, 24px);
  color: #717171;
  &.icon-select--selected {
    color: var(--brand-bg-color-default, #3277fc);
  }
}
</style>
