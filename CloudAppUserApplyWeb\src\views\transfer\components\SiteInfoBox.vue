<template>
  <div class="site-info-box">
    <div class="site-left">
      <theme-image alt="site" imageName="site.png" />
      <div class="site-title">{{ $t('site') }}</div>
      <theme-image class="help-icon" alt="help-circle" imageName="help_circle.png" @click="onHelp" />
    </div>
    <div class="site-right">
      <div class="site-name text-over-ellipsis">{{ siteName }}</div>
    </div>
  </div>
</template>

<script>
import ThemeImage from '@/components/ThemeImage.vue'
export default {
  name: 'SiteInfoBox',
  components: {
    ThemeImage
  },
  props: {
    siteName: {
      type: String,
      default: ''
    }
  },
  methods: {
    onHelp() {
      this.$emit('help')
    }
  }
}
</script>
<style lang="scss" scoped>
.site-info-box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .site-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
    img {
      width: 24px;
      height: 24px;
    }
    .help-icon {
      width: 16px;
      height: 16px;
    }
    .site-title {
      font-size: var(--font-size-body1-size, 16px);
      margin: 0px 6px;
    }
  }
  .site-right {
    flex: 1;
    height: 100%;
    .site-name {
      width: 100%;
      text-align: right;
    }
  }
}
</style>
