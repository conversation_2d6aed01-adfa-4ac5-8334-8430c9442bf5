import request from '@/api/request'
import baseUrl from '@/api/index.js'

const baseUrl2 = '/mobile_v1.0/'

// 分享请求的接口
// superlive max 获取全量的设备
export const getAllDeviceList = data => request.post(`${baseUrl}/device/list`, data)

// superlive max 查询当前用户绑定的设备列表
export const getResourceDeviceList = data => request.post(`${baseUrl}/channel/share-able/list`, data)

// superlive max 查询我的分享和他人分享给我的
export const getUserShareList = data => request.post(`${baseUrl}/channel/list-user-shared`, data)

// superlive max 修改指定设备的分享权限
export const updateShareAuth = data => request.post(`${baseUrl}/channel/share-auth/update`, data)

// superlive max 以人员维度，接受或拒绝所有通道共享
export const updateShareStatus = data => request.post(`${baseUrl}/channel/share/accept-or-reject`, data)

// superlive max 获取通道信息，包括能力集
export const getChannelDetail = data => request.post(`${baseUrl}/device/detail`, data)

// superlive max 获取设备信息，包括能力集
export const getDeviceDetail = data => request.post(`${baseUrl}/device/detail`, data)

// superlive max 分享设备通道
export const setShareChannel = data => request.post(`${baseUrl}/channel/share`, data)

// superlive max 查询邮箱号是否存在
export const getEmailExist = data => request.post(`${baseUrl2}/user/info/email/is-exist`, data)

// superlive max 查询手机号是否存在
export const getPhoneExist = data => request.post(`${baseUrl2}/user/info/phone/is-exist`, data)

// superlive max 查询邮箱或手机号是否存在
export const getLoginInfo = data => request.post(`${baseUrl2}/user/info/login-type/list`, data)

// superlive max 获取当前用户信息
export const getUserInfo = data => request.post(`${baseUrl2}/user/info/get`, data)

// superlive max 取消通道分享
export const deleteShareChannel = data => request.post(`${baseUrl}/channel/share/delete`, data)

// superlive max 接受/拒绝分享申请
export const agreeRefuseShareChannel = data => request.post(`${baseUrl}/channel/share-request/agree-or-refuse`, data)

// superlive max 查询邮箱或手机号是否存在
export const getUserExist = data => request.post(`${baseUrl2}/user/login-name/exist`, data)
