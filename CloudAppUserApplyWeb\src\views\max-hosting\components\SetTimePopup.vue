<template>
  <van-popup
    :value="visible"
    @change="value => $emit('update:visible', value)"
    position="bottom"
    round
    :close-on-click-overlay="false"
    get-container="#app"
  >
    <div class="site-device-setting-time-pop-container">
      <div class="header">
        <span class="title">
          {{ $t('settingTime') }}
        </span>
        <img
          class="close-btn"
          src="@/assets/img/common/trusteeship/close.png"
          @click="$emit('update:visible', false)"
        />
      </div>
      <div class="content">
        <van-radio-group v-model="selectedValue" ref="checkboxGroup">
          <van-radio
            v-for="item in timeList"
            class="permission-item"
            :key="item.value"
            :name="item.value"
            label-position="left"
          >
            {{ item.label }}
            <template #icon="props">
              <img
                class="img-icon"
                :src="
                  props.checked ? require('@/assets/img/common/check.png') : require('@/assets/img/common/check_no.png')
                "
              />
            </template>
          </van-radio>
        </van-radio-group>
      </div>
      <van-button class="footer-btn" type="primary" @click="handleHosting">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'SetTimePopup',
  props: {
    site: {
      type: Object,
      default: () => ({})
    },
    device: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timeList: [
        {
          value: 0,
          label: this.$t('forever')
        },
        {
          value: 3600,
          label: this.$t('manyHours', [1])
        },
        {
          value: 7200,
          label: this.$t('manyHours', [2])
        },
        {
          value: 14400,
          label: this.$t('manyHours', [4])
        },
        {
          value: 28800,
          label: this.$t('manyHours', [8])
        }
      ],
      selectedValue: 3600,
      initialValue: 0
    }
  },
  watch: {
    visible(value) {
      if (value) {
        this.selectedValue = this.device.trustDuration
        this.initialValue = this.device.trustDuration
      }
    }
  },
  methods: {
    handleHosting() {
      if (this.selectedValue === this.initialValue) {
        this.$emit('update:visible', false)
        return
      }
      this.$emit('submit', this.selectedValue)
    }
  }
}
</script>

<style lang="scss">
.site-device-setting-time-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    height: 52px;
    padding: 12px;
    position: relative;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    .title {
      display: inline-block;
      width: 100%;
      color: var(--text-color-primary, #1a1a1a);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-btn {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 14px;
    }
  }
  .content {
    padding: 0 16px;
    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    }
    .img-icon {
      width: 24px;
      height: 24px;
    }
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 10px auto 0;
  }
}
</style>
