<template>
  <div class="channel-wrapper">
    <nav-bar>
      <template #left>
        <div class="nav-bar-left-text" @click="back">{{ $t('cancel') }}</div>
      </template>
    </nav-bar>
    <div class="channel-list-content">
      <tvt-better-scroll
        class="tvt-better-scroll"
        @pullingUp="pullingUp"
        @pullingDown="pullingDown"
        :pullingStatus="pullingStatus"
      >
        <div class="channel-list">
          <!-- 通道勾选 onlineStatus 0 禁用 1 在线 2 不在线 99 未知-->
          <van-checkbox-group v-model="checkResult">
            <van-checkbox
              v-for="(item, index) of curChannelList"
              :key="`${item.sn}~${item.chlIndex}~${item.chlId}~${index}`"
              class="channel-cell-wrapper"
              :disabled="item.isDisabled"
              :name="`${item.sn}~${item.chlIndex}~${item.chlId}`"
              :ref="`${item.sn}~${item.chlIndex}~${item.chlId}~${index}`"
            >
              <div :class="['channel-checkbox-content', item.isDisabled ? 'channel-checkbox-content-disabled' : '']">
                <div :class="['channel-checkbox-title', item.groupName ? 'channel-checkbox-title2' : '']">
                  <div class="channel-ellipsis-text">
                    {{ item.name }}
                  </div>
                  <div class="channel-ellipsis-text channel-desc-text">
                    {{ item.devDesc }}
                  </div>
                </div>
                <div class="channel-checkbox-desc channel-ellipsis-text" v-if="item.groupName">
                  {{ $t('addedTo', [`"${item.groupName}"`]) }}
                </div>
              </div>
            </van-checkbox>
          </van-checkbox-group>
        </div>
      </tvt-better-scroll>
    </div>
    <div class="footer">
      <van-button type="primary" class="footer-btn" :disabled="btnDisabled" @click="handleConfirm">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapState, mapMutations, mapActions } from 'vuex'
import { appSetWebBackEnable } from '@/utils/appbridge'
export default {
  name: 'AddChannel',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      pullingStatus: 0,
      curChannelList: [], // 当前通道列表
      checkResult: [],
      curGroupName: null
    }
  },
  created() {
    appSetWebBackEnable(true)
  },
  mounted() {
    this.refreshChannel()
  },
  computed: {
    ...mapState('maxDefense', [
      'allChannelList',
      'channelList',
      'defenseRecord',
      'channelObj',
      'groupChannelList',
      'ipcLinkageList'
    ]),
    // 添加时按钮是否禁用
    btnDisabled() {
      // 有分组名称且选中了通道才能点击确定按钮
      return !(this.checkResult && this.checkResult.length)
    }
  },
  methods: {
    ...mapActions('maxDefense', ['getChannelList']),
    ...mapMutations('maxDefense', ['SET_CHANNEL_LIST']),
    back() {
      this.$utils.routerPush({
        path: '/maxDefense/addEditDefense',
        query: { type: 'edit' }
      })
    },
    refreshChannel() {
      // const { id } = this.defenseRecord
      const curChannelList = []
      this.allChannelList.map(item => {
        // 判断某个通道是否被当前分组添加
        const idx = this.channelList.findIndex(
          item2 => item2.sn === item.sn && item2.chlIndex === item.chlIndex && item2.chlId === item.chlId
        )
        if (idx > -1) {
          // 已经被当前分组添加的不展示
          return
        }
        // 判断是否被其他分组添加
        const groupRecord =
          this.groupChannelList.find(
            item3 => item3.sn === item.sn && item3.chlIndex === item.chlIndex && item3.chlId === item.chlId
          ) || {}
        console.log('groupRecord', groupRecord, 'groupChannelList', this.groupChannelList)
        const { groupName } = groupRecord
        const isDisabled = item.onlineStatus !== 1 // 是否禁用
        curChannelList.push({
          ...item,
          groupName,
          isDisabled
        })
        this.curChannelList = curChannelList
      })
    },
    async pullingUp(callback) {
      await this.getChannelList()
      this.refreshChannel()
      if (callback) callback()
    },
    async pullingDown(callback) {
      await this.getChannelList()
      this.refreshChannel()
      if (callback) callback()
    },
    async handleConfirm() {
      console.log('checkResult', this.checkResult)
      // 把添加的设备传入store
      const addChannelList = this.checkResult.map(key => this.channelObj[key])
      // 简化存储的信息
      const simpleChannelList = addChannelList.map(item => {
        const { chlIndex, chlId, name, sn, capability, nodeType } = item
        // BypassSwitch旁路开关，默认0关闭 linkageList 联动项
        let linkageList = []
        // 找到能力集
        if (capability) {
          // 根据能力集找到可以支持联动项
          linkageList = this.ipcLinkageList
            .filter(item => !item.filterAble || capability.includes(item.value))
            .map(item2 => item2.value)
        } else if (nodeType === 'sensor') {
          // 没有能力集则认为nodeType为sensor（传感器），没有声音和闪灯
          linkageList = this.ipcLinkageList.filter(item => !item.filterAble).map(item2 => item2.value)
        }
        return {
          chlIndex,
          chlId,
          sn,
          name,
          extra: { bypassSwitch: 0, linkageList }
        }
      })
      const deviceSet = new Set(this.channelList.map(item => `${item.sn}~${item.chlIndex}~${item.chlId}`))
      // 过滤掉重复的
      const filterChannelList = simpleChannelList.filter(
        item => !deviceSet.has(`${item.sn}~${item.chlIndex}~${item.chlId}`)
      )
      const newChannelList = [...this.channelList, ...filterChannelList]
      // 更新当前选中的通道
      this.SET_CHANNEL_LIST(newChannelList)
      // 返回
      this.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.channel-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
  .van-cell {
    padding: 5px 12px;
  }
  .channel-list-content {
    width: 100%;
    height: calc(100% - 140px);
    overflow: auto;
    margin: 10px 0px;
    box-sizing: border-box;
    .tvt-better-scroll {
      overflow: auto;
    }
    .channel-list {
      width: 100%;
      height: 100%;
      padding: 0px 20px;
      box-sizing: border-box;
    }
  }
}
.footer-btn {
  width: 343px;
  height: 46px;
  border-radius: 23px;
}
</style>
<style lang="scss">
.van-collapse-item__content {
  padding: 2px 0px !important;
}
.channel-cell-item {
  .van-cell__title {
    width: 100%;
  }
}
.channel-cell-wrapper {
  width: 100%;
  height: 52px;
  padding: 15px 0px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  overflow: hidden;
  .channel-cell-item {
    width: 100%;
  }
  .van-checkbox__label {
    width: calc(100% - 28px);
    display: flex;
    align-items: center;
  }
  .channel-checkbox-content {
    flex: 1;
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
  }
  .channel-checkbox-title {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    overflow: hidden;
  }
  .channel-checkbox-title2 {
    max-width: calc(100% - 120px);
  }
  .channel-checkbox-desc {
    max-width: 120px;
  }
  .channel-desc-text {
    font-size: var(--font-size-text-size, 12px);
  }
  .channel-ellipsis-text {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }
  .van-checkbox {
    margin-right: 10px;
  }
}
</style>
