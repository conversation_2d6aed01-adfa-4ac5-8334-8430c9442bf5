// 标签显示label
export const statusLabel = (value, options) => {
  let index = options.findIndex(v => v.value == value)
  if (index != -1) {
    return options[index].label
  } else {
    return ''
  }
}

// 拼接 权限配置的显示
export const authLabel = (list, options) => {
  var str = ''
  list.forEach((v, index) => {
    let arr = options.filter(item => item.value === v)
    if (arr && arr[0]) {
      str += arr[0].label
      if (index != list.length - 1) {
        str += '、'
      }
    }
  })
  return str
}
