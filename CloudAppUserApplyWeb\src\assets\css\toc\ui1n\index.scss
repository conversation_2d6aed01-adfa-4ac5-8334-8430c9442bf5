@import './checkUserPwd.scss';
@import './checkPwd.scss';
@import './upgrade.scss';
@import './navBar.scss';
@import './trusteeship/myInstaller.scss';
@import './trusteeship/deviceDetails.scss';
@import './trusteeship/check.scss';
@import './trusteeship/permission.scss';
@import './trusteeship/validity.scss';
@import './ipc-upgrade/index.scss';
@import './transfer/transferRequest.scss';
@import './share/shareManage.scss';
@import './defense/index.scss';
@import './line-bind/index.scss';
@import './household-management/householdManagement.scss';

html,
body,
#app {
  background-color: $UI1N-background-color;
  color: $UI1N-90-black-color;
}

// 根据主题 全局 修改vant样式
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $UI1N-color-primary;
}

.van-button--primary {
  color: $UI1N-white-color;
  background-color: $UI1N-color-primary;
  border: 1px solid $UI1N-color-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $UI1N-white-color;
}

.van-dialog__message {
  color: $UI1N-white-color;
}

input::-webkit-input-placeholder {
  color: $UI1N-placeholder-color;
}

// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 40px;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .footer-btn {
    width: 345px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-color: $UI1N-button-background-color;
    border-radius: 20px;
    color: $UI1N-white-color;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $UI1N-color-primary;

  .van-tabs__content {
    background-color: $UI1N-background-color;
  }
}

// 弹窗背景色
.van-dialog {
  color: $UI1N-white-color;
  background-color: $UI1N-light-background-color;
}

// 弹窗按钮
.van-dialog__cancel, .van-dialog__confirm {
  color: $UI1N-color-primary;
  background-color: $UI1N-background-color;
}

// 弹窗按钮border
.van-hairline--top::after {
  border-color: $UI1N-border-color;
}
.van-hairline--left::after {
  border-color: $UI1N-border-color;
}

// Tab背景色
.van-tabs__nav {
  background-color: $UI1N-light-background-color;
}
.van-tab {
  color: $UI1N-font-color;
}
.van-tab--active {
  color: $UI1N-color-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $UI1N-color-primary;
  border-color: $UI1N-color-primary;
}

// switch背景色
.van-switch {
  background-color: $UI1N-font-color;
}
.van-switch--on {
  background-color: $UI1N-color-primary;
}

// cell背景色
.van-cell {
  color: $UI1N-white-color;
  background-color: $UI1N-light-background-color;
}
.van-collapse-item__content {
  background-color: $UI1N-light-background-color;
}
.van-cell--clickable:active {
  background-color: $UI1N-light-background-color;
}