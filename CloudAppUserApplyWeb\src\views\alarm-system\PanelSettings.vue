<template>
  <div class="panel-settings">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- DSC 标题 -->
      <div class="dsc-title">DSC</div>

      <!-- Settings 区域 -->
      <div class="alarm-box-wrapper settings-section">
        <!-- 设置列表 -->
        <div class="settings-list">
          <div
            v-for="(item, index) in settingsItems"
            :key="index"
            class="alarm-bottom-box settings-item"
            @click="handleSettingClick(item)"
          >
            <div class="setting-content">
              <div class="setting-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="4" />

    <!-- 安装商访问确认弹窗 -->
    <van-dialog
      v-model="showInstallerAccessDialog"
      title=""
      :show-cancel-button="true"
      :cancel-button-text="$t('cancel')"
      :confirm-button-text="$t('ok')"
      @confirm="handleInstallerAccessConfirm"
      @cancel="handleInstallerAccessCancel"
      class="common-dialog"
    >
      <div class="common-dialog-content">
        <div class="dialog-message">{{ $t('confirmInstallerAccess') }}</div>
      </div>
    </van-dialog>

    <!-- 退出登录确认弹窗 -->
    <van-dialog
      v-model="showLogoutDialog"
      title=""
      :show-cancel-button="true"
      :cancel-button-text="$t('cancel')"
      :confirm-button-text="$t('ok')"
      @confirm="handleLogoutConfirm"
      @cancel="handleLogoutCancel"
      class="common-dialog"
    >
      <div class="common-dialog-content">
        <div class="dialog-message">{{ $t('confirmLogout') }}</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import { mapGetters, mapActions } from 'vuex'
import { handleCommonError } from '@/utils/alarmSystem'
import { riscoLogout, pimaDisconnect } from '@/api/alarmSystem'
import { setCacheData } from '@/utils/appbridge'

// 路由常量定义
const ROUTES = {
  NOTIFICATIONS: '/alarmSystem/notifications',
  USERS: '/alarmSystem/users',
  PANEL_DATE_TIME: '/alarmSystem/panelDateTime',
  PANEL_LIST: '/alarmSystem/panelList'
}

// 设置项配置
const SETTINGS_CONFIG = [
  { key: 'notifications', route: ROUTES.NOTIFICATIONS },
  { key: 'users', route: ROUTES.USERS },
  { key: 'panelDateTime', route: ROUTES.PANEL_DATE_TIME },
  { key: 'installerAccess', type: 'dialog' },
  { key: 'logout', type: 'dialog' }
]

export default {
  name: 'PanelSettings',
  components: {
    AlarmBottomNavigation
  },
  computed: {
    ...mapGetters('alarmSystem', ['systemType']),
    // 国际化的设置项列表
    settingsItems() {
      return SETTINGS_CONFIG.map(item => ({
        name: this.$t(item.key),
        action: item.key,
        route: item.route,
        type: item.type
      }))
    }
  },
  data() {
    return {
      showInstallerAccessDialog: false,
      showLogoutDialog: false
    }
  },
  methods: {
    ...mapActions('alarmSystem', ['clearAllPanelData']),
    handleSettingClick(item) {
      const config = SETTINGS_CONFIG.find(c => c.key === item.action)
      if (config.route) {
        this.$router.push(config.route)
      } else if (config.type === 'dialog') {
        this.showDialog(item.action)
      } else {
        this.$toast(this.$t('clickedItem', { name: item.name }))
      }
    },
    // 显示对话框的统一方法
    showDialog(type) {
      if (type === 'installerAccess') {
        this.showInstallerAccessDialog = true
      } else if (type === 'logout') {
        this.showLogoutDialog = true
      }
    },
    /**
     * 处理退出登录确认
     * 根据系统类型调用不同的退出逻辑，确保数据清理完整性
     */
    async handleLogoutConfirm() {
      this.showLogoutDialog = false
      try {
        this.$loading.show()

        if (this.systemType === 'Risco') {
          await this.performRiscoLogout()
        } else if (this.systemType === 'Pima') {
          await this.performPimaLogout()
        } else {
          // 其他系统：只清除本地状态
          this.clearLocalData()
        }

        this.$toast(this.$t('logoutSuccess'))
      } catch (error) {
        handleCommonError(error)
      } finally {
        this.$loading.hide()
        // 统一在finally中跳转，确保用户能够退出
        this.$router.push(ROUTES.PANEL_LIST)
      }
    },

    /**
     * 执行Risco系统退出登录
     * 调用API接口并处理失败情况
     */
    async performRiscoLogout() {
      try {
        const siteId = this.$store.getters['alarmSystem/siteId']
        const sessionToken = this.$store.getters['alarmSystem/sessionId']

        if (siteId && sessionToken) {
          await riscoLogout(siteId, { sessionToken })
        }
      } catch (error) {
        console.error('Risco logout API failed:', error)
        // 即使API失败也要清除本地数据
      } finally {
        this.clearLocalData()
      }
    },

    /**
     * 执行Pima系统退出登录
     * 调用API接口并处理失败情况
     */
    async performPimaLogout() {
      try {
        await pimaDisconnect({ data: null })
      } catch (error) {
        console.error('Pima logout API failed:', error)
        // 即使API失败也要清除本地数据
      } finally {
        this.clearLocalData()
      }
    },

    /**
     * 清除本地数据
     * 包括store状态和APP缓存数据
     */
    clearLocalData() {
      // 清除store状态和localStorage缓存
      this.clearAllPanelData()

      // 清除APP缓存
      setCacheData({ key: 'risco_access_token', value: '' })
      setCacheData({ key: 'site_login_info', value: '' })
      setCacheData({ key: 'site_session_id', value: '' })
    },
    handleLogoutCancel() {
      this.showLogoutDialog = false
      // 用户取消操作，不需要额外处理
    },
    async handleInstallerAccessConfirm() {
      this.showInstallerAccessDialog = false
      try {
        this.$loading.show()
        // TODO: 实现安装商访问API调用
        // await this.approveInstallerAccess()
        this.$toast(this.$t('installerAccessApproved'))
      } catch (error) {
        handleCommonError(error)
      } finally {
        this.$loading.hide()
      }
    },
    handleInstallerAccessCancel() {
      this.showInstallerAccessDialog = false
      // 用户取消操作，不需要额外处理
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-settings {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px;
  }
  .dsc-title {
    height: 40px;
    box-sizing: border-box;
    font-size: 18px;
    font-weight: bold;
    padding: 10px 16px 6px 16px;
  }
  .settings-list {
    .settings-item {
      width: 100%;
      height: 46px;
      padding: 12px 16px;
      box-sizing: border-box;
      .setting-content {
        flex: 1;
        .setting-name {
          font-size: 16px;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
  }
}
.common-dialog-content {
  padding: 20px;
  text-align: center;
  .dialog-message {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
