<template>
  <div class="choose-capability-wrapper">
    <div class="choose-capability-head">
      <div class="choose-capability-title">{{ $t('devOperationTitle') }}</div>
      <theme-image class="close-icon" alt="close" imageName="close.png" @click="handleCancel" />
    </div>
    <div class="choose-capability-body">
      <van-cell-group>
        <van-cell
          clickable
          :title="$t('deviceOperation')"
          :label="$t('deviceOperationDesc')"
          @click="toggle"
          :border="false"
        >
          <template #right-icon>
            <van-checkbox :value="value" ref="checkboxe" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>
    <div class="choose-capability-foot">
      <van-button class="footer-btn" type="primary" @click="handleClick">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'ChooseOperation',
  components: {
    ThemeImage
  },
  props: {
    value: {
      type: Boolean,
      default: true
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    confirm: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      chooseCapability: [] // 选中的能力集
    }
  },
  created() {
    // 首次进入清除选中的设备
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },

  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleClick() {
      this.$toastSuccess(this.$t('settingSuccess'))

      this.$emit('confirm')
    },
    changeValue(value) {
      this.$emit('update:input', value)
    },
    toggle() {
      console.log('toggle', !this.value)
      this.$emit('input', !this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-capability-wrapper {
  width: 100%;
  height: 300px;
  overflow: auto;
  .choose-capability-head {
    width: 100%;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 14px 12px;
    box-sizing: border-box;
    .choose-capability-title {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-icon {
      position: absolute;
      top: 18px;
      right: 12px;
      width: 16px;
      height: 16px;
    }
  }
  .choose-capability-body {
    width: 100%;
    height: calc(100% - 112px);
    box-sizing: border-box;
    padding-top: 20px;

    ::v-deep .van-cell-group .van-cell {
      height: min-content !important;
    }
  }
  .choose-capability-foot {
    width: 100%;
    height: 60px;
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 327px;
      height: 40px;
      border-radius: 23px;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
<style lang="scss">
.choose-capability-body {
  .van-cell {
    height: 52px !important;
  }
}
</style>
