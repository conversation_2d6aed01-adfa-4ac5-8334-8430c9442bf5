<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="isAdd ? $t('addRoom') : $t('roomDetail')" @clickLeft="back"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <van-cell class="household-item" name="name" is-link @click.stop="editRoomNo">
        <template #title> <span class="required-icon">*</span>{{ $t('roomName') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ roomNo }}</span>
        </template>
      </van-cell>

      <div class="room-container">
        <div class="room-header">
          <span class="room-title">
            {{ $t('household') }}
          </span>
          <span class="room-add" @click="addRoomMember">
            <van-icon name="plus" />
          </span>
        </div>
        <div class="room-list" v-if="loading || memberList.length">
          <van-swipe-cell v-for="(item, index) in memberList" :key="item.buildingMemberId">
            <van-cell
              class="room-item"
              center
              :name="item.buildingMemberId"
              :is-link="isAdd"
              @click.stop="editRoomMember(item, index)"
            >
              <template #title>
                <span class="room-name text-over-ellipsis">{{ item.memberName }}</span>
              </template>
              <template #label>
                <span class="contact-text text-over-ellipsis">{{ formatPhoneNumber(item.memberContact) }}</span>
              </template>
              <template #right-icon>
                <theme-image class="refuse-img" alt="face" :imageName="getFaceImageName(item.facialImage)" />
                <theme-image class="refuse-img" alt="card" :imageName="getCardImageName(item.cardNo)" />
                <theme-image class="refuse-img" alt="arrow" imageName="arrow_right.png" />
              </template>
            </van-cell>
            <template #right>
              <van-button square type="danger" class="swipe-right-btn" @click="deleteRoomMember(item, index)">
                <theme-image class="refuse-img" alt="delete" imageName="refuse.png" />
              </van-button>
            </template>
          </van-swipe-cell>
        </div>
        <div class="no-data" v-else>
          <div class="no-data-img">
            <theme-image :class="uiStyleFlag === 'ui1b' ? 'vms-img' : ''" alt="noData" imageName="no_data.png" />
          </div>
          <div class="no-data-text">
            <van-button class="add-btn" type="primary" @click="addRoomMember">
              {{ $t('add') }}
            </van-button>
          </div>
        </div>
      </div>
    </tvt-better-scroll>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :loading="addLoading" @click="handleClick">
        {{ isAdd ? $t('confirm') : $t('deleteRoom') }}
      </van-button>
    </div>
    <edit-room-no ref="editRoomNo" :value="roomNo" @confirm="confirmNo"></edit-room-no>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import EditRoomNo from './dialog/EditRoomNo.vue'
import { formatPhoneNumber } from '@/utils/common.js'
import {
  queryRoomMembers,
  deleteRoomMember,
  editBuildingRoom,
  delBuildingRoom,
  addRoomMembers,
  addBuildingRoom
} from '@/api/householdManagement.js'
import { mapState, mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'addRoom',
  components: {
    NavBar,
    EditRoomNo,
    ThemeImage
  },
  props: {},
  data() {
    return {
      isAdd: true,
      pullingStatus: 0,
      roomId: '',
      buildingId: '',
      loading: false,
      addLoading: false,
      roomNo: ''
    }
  },
  mounted() {
    this.buildingId = this.$route.query.buildingId

    // 编辑房间
    if (this.$route.query.roomId) {
      this.isAdd = false
      this.roomId = this.$route.query.roomId
      this.queryDetail()
    } else {
      // 添加房间
      this.isAdd = true
    }
    this.roomNo = this.roomInfo.roomNo
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('householdManagement', ['roomInfo', 'memberList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    getFaceImageName() {
      return hasFace => `household/${hasFace ? 'face_cert_active.png' : 'face_cert.png'}`
    },
    getCardImageName() {
      return hasCard => `household/${hasCard ? 'card_cert_active.png' : 'card_cert.png'}`
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_MEMBER_LIST', 'DEL_MEMBER', 'SET_MEMBER_INFO', 'SET_ROOM_INFO']),
    back() {
      this.SET_ROOM_INFO({})
      this.SET_MEMBER_LIST([])
      this.$router.go(-1)
    },
    editRoomNo() {
      this.$refs.editRoomNo.show = true
    },
    handleClick() {
      if (this.isAdd) {
        this.addRoom()
      } else {
        // 删除
        this.deleteRoom()
      }
    },
    async addRoom() {
      // 先创建房间， 然后用房间id添加住户， 设计如此
      try {
        this.$loading.show()
        this.addLoading = true

        if (!this.roomNo) {
          this.$toast(this.$t('enterRoomName'))
          return
        }

        const { data } = await addBuildingRoom({
          id: this.buildingId,
          roomNo: this.roomNo
        })
        // 新增房间添加了住户再新增房间住户
        if (this.memberList.length) {
          const memberList = this.memberList.map(member => ({
            buildingId: this.buildingId,
            roomId: data.roomId,
            memberName: member.memberName,
            memberContact: member.memberContact,
            facialImage: member.facialImage,
            cardNo: member.cardNo
          }))
          await addRoomMembers(memberList)
        }
        this.$toast(this.$t('addSuccess'))
        this.back()
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.addLoading = false
      }
    },
    async deleteRoom() {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        this.$loading.show()

        await this.$dialog.confirm(tips)

        await delBuildingRoom({
          id: this.roomId
        })

        this.$toast(this.$t('deleteSuccess'))
        this.back()
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
      }
    },
    addRoomMember() {
      if (this.memberList.length >= 5) {
        this.$toast(this.$t('memberInMax'))
        return
      }
      this.SET_ROOM_INFO({
        ...this.roomInfo,
        roomNo: this.roomNo
      })
      if (this.isAdd) {
        // 新增房间添加住户
        this.$router.push({
          path: '/household/addMember'
        })
      } else {
        // 编辑房间添加住户
        this.$router.push({
          path: '/household/addMember',
          query: {
            id: this.roomId
          }
        })
      }
    },
    editRoomMember(member, index) {
      if (this.isAdd) {
        // 新增房间时编辑住户
        this.SET_MEMBER_INFO({ editIndex: index, ...member })
        setTimeout(() => {
          this.$router.push({
            path: '/household/addMember'
          })
        }, 50)
      } else {
        // 编辑房间的住户
        this.SET_MEMBER_INFO({ ...member })
        const { buildingMemberId } = member
        setTimeout(() => {
          this.$router.push({
            path: '/household/addMember',
            query: {
              roomId: this.roomId,
              buildingMemberId
            }
          })
        }, 50)
      }
    },
    async deleteRoomMember(member, index) {
      const tips = {
        message: this.$t('deleteConfirm'),
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }

      try {
        await this.$dialog.confirm(tips)
        this.$loading.show()

        if (this.isAdd) {
          this.DEL_MEMBER(index)
        } else {
          await deleteRoomMember({
            id: member.buildingMemberId
          })

          const { data } = await queryRoomMembers({ roomId: this.roomId })
          const { records = [] } = data || []

          this.SET_MEMBER_LIST(records)
        }

        this.$toast(this.$t('deleteSuccess'))
      } catch (error) {
        console.error(error)
        return false
      } finally {
        this.$loading.hide()
      }
    },
    async confirmNo(value) {
      try {
        this.$loading.show()
        if (this.isAdd) {
          this.roomNo = value
        } else {
          await editBuildingRoom({
            roomId: this.roomId,
            roomNo: value
          })
          this.roomNo = value
          this.SET_ROOM_INFO({
            ...this.roomInfo,
            roomNo: value
          })
          this.$toast(this.$t('changeSuccessfully'))
        }

        this.$refs.editRoomNo.show = false
      } catch (error) {
        console.error(error)
        return false
      } finally {
        this.$loading.hide()
      }
    },
    async pullingDown(callback) {
      // 新增不刷新
      if (this.isAdd) {
        callback && callback()
        return
      }
      await this.queryDetail()
      callback && callback()
    },
    async queryDetail() {
      try {
        this.$loading.show()
        this.SET_MEMBER_LIST([])
        this.loading = true
        const { data } = await queryRoomMembers({ roomId: this.roomId })
        const { records = [] } = data || []

        this.SET_MEMBER_LIST(records)
      } catch (error) {
        console.error(error)
      } finally {
        this.$loading.hide()
        this.loading = false
      }
    },
    formatPhoneNumber
  }
}
</script>
<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 80px;
  box-sizing: border-box;
  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;

    .household-item {
      ::v-deep .van-cell__title {
        height: 24px;
      }

      ::v-deep .van-cell__value {
        height: 24px;
      }
      padding-top: 12px;
      padding-bottom: 12px;
      &::after {
        border: none;
      }

      .right-value {
        display: inline-block;
        width: 100%;
      }
    }

    .room-container {
      padding: 10px 0;

      .room-header {
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        padding: 10px 6px 10px 16px;
      }

      .room-add {
        padding: 0 15px;
      }

      .room-list {
        .room-item {
          height: 60px;
          // line-height: 30px;

          &::v-deep .van-cell__title {
            flex: 1;
            min-width: 0; // 确保flex布局下文本可以正常省略
            width: 100%;
          }

          &::v-deep .van-cell__right-icon {
            // line-height: 70px;
          }
          &::v-deep .van-cell__value {
            flex: none;
          }
          &::v-deep .van-cell__label {
            margin: 0px;
          }
        }
        .room-name {
          display: block; // 改为block
          width: 100%; // 设置为100%
          height: 20px;
          line-height: 20px;
        }
        .contact-text {
          display: block; // 同样改为block
          width: 100%;
          height: 20px;
          line-height: 20px;
        }
        .room-member-name {
          display: inline-block;
          width: 90%;
        }
        .swipe-right-btn {
          height: 100%;

          &::v-deep .van-button__text {
            height: 24px;
          }
        }
        .refuse-img {
          width: 24px;
          height: 24px;
        }
        &::v-deep .van-cell-right-icon {
          position: relative;
          top: 3px;
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    // padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;

      .add-btn {
        padding: 0 35px;
        border-radius: 23px;
        line-height: 46px;
        text-align: center;
      }
    }
  }
}
</style>
