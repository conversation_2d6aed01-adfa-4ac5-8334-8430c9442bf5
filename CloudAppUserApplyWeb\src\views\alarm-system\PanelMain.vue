<template>
  <div class="panel-main">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- DSC 标题 -->
      <div class="dsc-title">{{ siteName }}</div>

      <!-- DSC 状态组件（不包含标题） -->
      <alarm-system-status auto-fetch />

      <!-- 操作按钮组件 -->
      <alarm-system-actions />

      <!-- 警报列表组件 -->
      <alarm-system-alarms />

      <!-- 故障列表组件 -->
      <alarm-system-troubles />

      <!-- 最新活动组件 -->
      <alarm-system-activity />
    </div>

    <!-- 底部导航组件 -->
    <alarm-bottom-navigation />
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import AlarmSystemStatus from './components/AlarmSystemStatus.vue'
import AlarmSystemActions from './components/AlarmSystemActions.vue'
import AlarmSystemAlarms from './components/AlarmSystemAlarms.vue'
import AlarmSystemTroubles from './components/AlarmSystemTroubles.vue'
import AlarmSystemActivity from './components/AlarmSystemActivity.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'PanelMain',
  components: {
    AlarmBottomNavigation,
    AlarmSystemStatus,
    AlarmSystemActions,
    AlarmSystemAlarms,
    AlarmSystemTroubles,
    AlarmSystemActivity
  },
  data() {
    return {
      activeNavIndex: 0
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['siteName'])
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleNavClick({ item, index }) {
      console.log('底部导航点击:', item.action, index)

      switch (item.action) {
        case 'home':
          this.$toast('首页')
          break
        case 'profile':
          this.$toast('用户中心')
          break
        case 'security':
          this.$toast('安全设置')
          break
        case 'menu':
          this.$toast('菜单')
          break
        case 'settings':
          this.$toast('设置')
          break
        default:
          this.$toast(`点击了 ${item.action}`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #444;

  .panel-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    gap: 6px;

    .dsc-title {
      font-size: 18px;
    }
  }
}
</style>
