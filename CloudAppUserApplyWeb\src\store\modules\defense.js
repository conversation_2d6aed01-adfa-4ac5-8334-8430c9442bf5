export default {
  namespaced: true,
  state: () => ({
    initStatus: false, // 是否第一次进入
    noDataImg: null,
    allChannelList: [
      // {
      //   siteId: '1067490434814705664',
      //   siteName: 'Test1',
      //   sn: '969690A875AB46BE759DB3EFE6C9BFA0',
      //   devName: '3536C',
      //   snPlain: 'N44D20190726',
      //   chlIndex: 1,
      //   chlSn: null,
      //   chlName: 'IP頻道01',
      //   capability:
      //     '{"chlIndex":1,"verID":"A80B7109C822C3F426A06BF8B8EE6C64","version":"*******(6800)","model":"TD-9523A3-FR","manufacturer":"2","mac":"00:18:ae:a3:ab:51","protocol":259,"name":"IP頻道01","alarmInNum":1,"alarmOutNum":1,"supportFun":["osd","snp","cls"],"videoForm":["PAL","NTSC"],"stream":[],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 2,
      //   version: '*******(6800)',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1712583993000,
      //   siteCreateTime: 1668586086000
      // },
      // {
      //   siteId: '1215611930010583041',
      //   siteName: 'tesT',
      //   sn: '30EE927F9B11408B16540C210F014477',
      //   devName: 'N018AE006D80',
      //   snPlain: 'N018AE006D80',
      //   chlIndex: 17,
      //   chlSn: '306C25F1339597349CFA464A3C2E24D6',
      //   chlName: 'Camera_17',
      //   capability:
      //     '{"chlIndex":17,"verID":"4cd00bf062e3b09f548ed8feb9c78511","version":"5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta","model":"TD-9565S4-C","date":"","manufacturer":"2","chlSn":"306C25F1339597349CFA464A3C2E24D6","mac":"70:ab:49:83:7a:3e","coustomerID":"208","protocol":259,"name":"IP Camera 143","alarmInNum":0,"alarmOutNum":0,"supportFun":["a","d","ir","m","ma","mc","o","osd","p","ptz","snp","t","cls"],"videoForm":["PAL","NTSC"],"stream":[{"name":"mainCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"3200x1800"},{"fps":20,"value":"2688x1520"},{"fps":20,"value":"1920x1080"}]},{"name":"subCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":20,"value":"1280x720"},{"fps":20,"value":"704x480"},{"fps":20,"value":"640x480"},{"fps":20,"value":"352x240"}]},{"name":"aux1Caps","supEnct":["h264","h265","h265p"],"supEnctMode":["CBR"],"res":[{"fps":20,"value":"704x480"},{"fps":20,"value":"352x240"}]}],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 2,
      //   version: '5.2.3.20311(4f577580)B240408.ID11.U1(07A08).beta',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1711503964000,
      //   siteCreateTime: 1703073309000
      // },
      // {
      //   siteId: '1215611930010583041',
      //   siteName: 'tesT',
      //   sn: '30EE927F9B11408B16540C210F014477',
      //   devName: 'N018AE006D80',
      //   snPlain: 'N018AE006D80',
      //   chlIndex: 18,
      //   chlSn: '',
      //   chlName: 'IP Camera 02',
      //   capability:
      //     '{"chlIndex":18,"verID":"163D52962EBF596310A269071B7975DB","version":"*******(45060)","model":"TD-8543IE3N","date":"","manufacturer":"2","chlSn":"","mac":"58:5b:69:07:89:e7","protocol":259,"name":"IP Camera 02","alarmInNum":1,"alarmOutNum":1,"supportFun":["a","at","cm","d","m","ma","mc","osd","p","plc","ptz","san","sal","snp","t","tb","cls"],"videoForm":["PAL","NTSC"],"stream":[{"name":"mainCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":25,"value":"2560x1440"},{"fps":25,"value":"2304x1296"},{"fps":25,"value":"1920x1080"},{"fps":25,"value":"1280x720"}]},{"name":"subCaps","supEnct":["h264","h265","h265p"],"supEnctMode":["VBR","CBR"],"res":[{"fps":25,"value":"1280x720"},{"fps":25,"value":"704x576"},{"fps":25,"value":"640x480"},{"fps":25,"value":"352x288"}]},{"name":"aux1Caps","supEnct":["h264","h265","h265p"],"supEnctMode":["CBR"],"res":[{"fps":25,"value":"704x576"},{"fps":25,"value":"352x288"}]}],"platformCaps":{"alarmNoticeType":["img","imgAndVideo"],"cloudStorage":true}}',
      //   onlineStatus: 1,
      //   version: '*******(45060)',
      //   enableStatus: 1,
      //   delStatus: 1,
      //   createTime: 1711503964000,
      //   siteCreateTime: 1703073309000
      // }
    ], // 站点通道列表
    siteChannelList: [], // 站点设备通道树形结构
    channelObj: {}, // 站点Id-设备sn-通道chlIndex及其对应的通道信息
    capabilityObj: {}, // 设备能力集  设备sn-通道chlIndex及其对应的能力集数组
    defenseGroupList: [], // 布防组列表
    groupChannelList: [], // 布防组列表对应的所有通道
    defenseRecord: {
      groupName: '区域分组',
      channelList: []
    }, // 记录当前新增或编辑的布防组
    channelRecord: {} // 编辑的通道信息
  }),
  getters: {},
  mutations: {
    SET_INIT_STATUS(state, data) {
      state.initStatus = data
    },
    SET_NO_DATA_IMG(state, data) {
      state.noDataImg = data
    },
    SET_ALL_CHANNEL_LIST(state, data) {
      state.allChannelList = data
    },
    SET_SITE_CHANNEL_LIST(state, data) {
      state.siteChannelList = data
    },
    SET_CHANNEL_OBJ(state, data) {
      state.channelObj = data
    },
    SET_CAPABILITY_OBJ(state, data) {
      state.capabilityObj = data
    },
    SET_DEFENSE_GROUP_LIST(state, data) {
      state.defenseGroupList = data
    },
    SET_GROUP_CHANNEL_LIST(state, data) {
      state.groupChannelList = data
    },
    SET_CHANNEL_RECORD(state, data) {
      state.channelRecord = data
    },
    SET_CHANNEL_LIST(state, data) {
      state.channelList = data
    },
    SET_DEFENSE_RECORD(state, data) {
      // 将新增的通道加入到通道列表中
      state.defenseRecord = data
    }
  },
  actions: {}
}
