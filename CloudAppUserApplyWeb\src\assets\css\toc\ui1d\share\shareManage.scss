.share-list-wrapper  .share-item-wrapper {
  background-color: $UI1D-light-background-color;
}

.share-list-wrapper  .share-item-wrapper .share-item-right .recive-share-btn{
  color: $white-color;
  background-color: $UI1D-color-primary;
}

.share-list-wrapper {
  border-top: 1px solid $border-color;
}

.van-swipe-cell .share-item-wrapper {
  border-bottom: 1px solid $border-color;
}

.share-item-wrapper .share-item-left .share-title {
  color: $white-color;
}

.share-detail-wrapper .share-detail-content .share-info-box {
  background-color: $UI1D-light-background-color;
}

.share-detail-wrapper .share-detail-content .share-info-box .share-title {
  color: $white-color;
}

.share-detail-wrapper .share-detail-content .share-authority-box {
  color: $white-color;
  background-color: $UI1D-light-background-color;
}

.share-detail-wrapper .share-detail-content .share-authority-line:not(:last-child) {
  border-bottom: 1px solid $border-color;
}

.share-item-wrapper .share-item-left .share-line-text {
  color: $UI1D-font-color;
}

.share-detail-wrapper .share-detail-content .share-info-box .share-line-text {
  color: $UI1D-font-color;
}

.share-detail-wrapper .share-detail-content .share-authority-title {
  color: $UI1D-font-color;
}
