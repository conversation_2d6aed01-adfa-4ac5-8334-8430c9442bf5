<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="$t('faceEntry')" @clickLeft="back"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="room-list" v-if="needRequest || memberList.length">
        <van-cell
          v-for="item in memberList"
          class="room-item"
          center
          :key="item.id"
          :name="item.id"
          is-link
          @click.stop="modifyResident(item)"
        >
          <template #title>
            <div class="room-item-box">
              <div class="room-item-left">
                <img v-if="item.faceImageUrl" class="avatar" :src="item.faceImageUrl" alt="avatar" />
                <theme-image v-else class="avatar" alt="avatar" imageName="household/face_avatar.png" />
              </div>
              <div class="room-item-right">
                <div class="room-name text-over-ellipsis">{{ item.buildingName }}</div>
                <div class="contact-text text-over-ellipsis">{{ item.roomNo }}</div>
                <div class="username">{{ item.memberName }}</div>
              </div>
            </div>
          </template>
          <template #right-icon>
            <div class="right-icon-container">
              <theme-image class="arrow-img" alt="arrow" imageName="arrow_right.png" />
            </div>
          </template>
        </van-cell>
      </div>
      <div class="no-data" v-else>
        <div class="no-data-img">
          <theme-image alt="noData" imageName="no_data.png" />
        </div>
        <div class="no-data-text">
          <span>{{ $t('noData') }}</span>
        </div>
      </div>
    </tvt-better-scroll>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { mapMutations } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'
import { appClose } from '@/utils/appbridge'
import { queryRoomMembers, getFaceImageBatch } from '@/api/householdManagement.js'

export default {
  name: 'faceEntry',
  components: {
    NavBar,
    ThemeImage
  },
  data() {
    return {
      pullingStatus: 0,
      needRequest: true,
      memberList: [],
      pageNum: 1,
      pageSize: 1000 // 查询全量数据
    }
  },
  mounted() {
    this.fetchMemberList()
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_RESIDENT_INFO']),
    back() {
      appClose()
    },
    async pullingDown(callback) {
      await this.fetchMemberList()
      callback && callback()
    },
    async fetchMemberList() {
      try {
        this.$loading.show()
        const { data } = await queryRoomMembers({ queryType: 2, pageNum: this.pageNum, pageSize: this.pageSize })
        this.needRequest = false
        this.memberList = data.records || []

        // 批量获取用户头像图片
        await this.fetchMemberAvatars()
      } catch (error) {
        console.error(error)
        this.needRequest = true
      } finally {
        this.$loading.hide()
      }
    },
    async fetchMemberAvatars() {
      try {
        // 提取有效的buildingMemberId
        const memberIds = this.memberList.map(member => member.buildingMemberId).filter(Boolean)

        if (memberIds.length === 0) return

        // 批量获取头像图片
        const { data: avatarData } = await getFaceImageBatch({ ids: memberIds })

        if (!Array.isArray(avatarData)) return

        // 创建映射表并更新用户头像
        const avatarMap = new Map()
        avatarData.forEach(item => {
          if (item.buildingMemberId && item.faceImageUrl) {
            avatarMap.set(item.buildingMemberId, item.faceImageUrl)
          }
        })

        // 更新用户列表头像 - 创建新数组确保响应式更新
        this.memberList = this.memberList.map(member => {
          const avatarUrl = avatarMap.get(member.buildingMemberId)
          if (avatarUrl) {
            return { ...member, faceImageUrl: avatarUrl }
          }
          return member
        })
      } catch (error) {
        console.error('批量获取用户头像失败:', error)
      }
    },
    modifyResident(item) {
      // 存储当前点击的住户信息，确保包含头像信息
      const residentInfo = {
        ...item,
        // 确保传递头像URL，优先使用已获取的faceImageUrl
        facialImageUrl: item.faceImageUrl || item.facialImageUrl || null
      }

      this.SET_RESIDENT_INFO(residentInfo)

      // 跳转到编辑住户页面
      this.$router.push({
        path: '/household/modifyResident'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  box-sizing: border-box;
  .tvt-better-scroll {
    padding-top: 10px;
    height: calc(100% - 44px);
    box-sizing: border-box;
    .room-list {
      .room-item {
        height: 100px;
        font-size: 12px;
        &::v-deep .van-cell__title {
          flex: 1;
          min-width: 0;
          width: 100%;
        }
        &::v-deep .van-cell__value {
          flex: none;
        }
        &::v-deep .van-cell__label {
          margin: 0px;
        }
      }
      .room-name {
        display: block;
        width: 100%;

        height: 22px;
        line-height: 22px;
      }
      .contact-text {
        display: block;
        width: 100%;
        height: 22px;
        line-height: 22px;
      }

      &::v-deep .van-cell-right-icon {
        position: relative;
        top: 3px;
      }
    }
  }

  .no-data {
    padding: 8px 15px;
    .no-data-img {
      display: flex;
      justify-content: center;
      align-items: center;
      .theme-image-container {
        width: 235px;
        height: 211px;
      }
      .vms-img {
        width: 120px;
        height: 123px;
      }
    }
    .no-data-text {
      width: 300px;
      margin: auto;
      text-align: center;
    }
  }
}

.room-item-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  height: 100px;
  padding: 0 15px;
  overflow: hidden;
  .room-item-left {
    display: flex;
    align-items: center;
    .avatar {
      width: 60px;
      height: 80px;
      object-fit: cover;
    }
  }
  .room-item-right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 10px;
    padding: 10px 0;
    box-sizing: border-box;
    overflow: hidden;
  }
}

.right-icon-container {
  display: flex;
  align-items: center;

  .arrow-img {
    width: 16px;
    height: 16px;
    margin-left: 4px;
  }
}
</style>
