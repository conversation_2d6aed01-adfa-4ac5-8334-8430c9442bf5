<template>
  <div class="devices-list-wrapper">
    <div
      v-for="item of dataList"
      :key="item.sn"
      :class="['device-list-item', showAuthTime ? 'device-list-mutli-item' : '']"
    >
      <van-cell
        clickable
        :key="item.sn"
        :title="`${item.devName || item.deviceName}`"
        @click.stop.native="clickRecord(item)"
      >
        <template #title>
          <div :class="['device-item-box', showAuthTime ? 'device-mutli-item-box' : '']">
            <div class="device-icon">
              <theme-image alt="device" imageName="device.png" />
            </div>
            <div class="device-item-title">
              <div class="device-title text-over-ellipsis2">{{ item.devName || item.deviceName }}</div>
              <template v-if="showAuthTime">
                <div class="device-capability">
                  <span class="device-line-label"> {{ $t('permission') }}: </span>
                  <template v-if="item.checkCapability && item.checkCapability.length">
                    <span
                      v-for="(item2, index2) of item.checkCapability"
                      :key="item2.value"
                      class="device-capability-item"
                    >
                      <span>{{ item2.label }}</span>
                      <span class="separator-box" v-if="index2 < item.checkCapability.length - 1"></span>
                    </span>
                  </template>
                  <span v-else>--</span>
                </div>
                <div class="device-capability">
                  <span class="device-line-label"> {{ $t('duration') }}: </span>
                  <span class="device-capability-item">
                    <span>{{ resValidity(item) }}</span>
                  </span>
                </div>
              </template>
            </div>
          </div>
        </template>
      </van-cell>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'
import { HOSTING_VALIDITY_LIST } from '@/utils/options'

export default {
  name: 'DeviceList',
  components: {
    ThemeImage
  },
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    showAuthTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      validityList: HOSTING_VALIDITY_LIST(),
      statusObj: {
        0: this.$t('toAccept'),
        1: this.$t('accepted')
      }
    }
  },
  created() {},
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    clickRecord(record) {
      this.$emit('click', record)
    },
    // 获取时限文本
    resValidity(item) {
      let str = '--'
      let i = this.validityList.findIndex(o => o.value == item.trustDuration)
      if (i !== -1) {
        str = this.validityList[i].label
      }
      return str
    }
  }
}
</script>
<style lang="scss" scoped>
.devices-list-wrapper {
  width: 100%;
  height: 100%;
  .device-list-item {
    width: 100%;
    height: 72px;
    padding: 0px 14px;
    box-sizing: border-box;
  }
  .device-list-mutli-item {
    height: 97px;
  }
  .device-item-box {
    width: 100%;
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;
    .device-icon {
      width: 70px;
      height: 49px;
      margin: 0px 6px;
      .theme-image-container {
        width: 70px;
        height: 49px;
      }
      img {
        width: 70px;
        height: 49px;
      }
    }
    .device-item-title {
      width: calc(100% - 100px);
      font-size: var(--font-size-body1-size, 16px);
      .device-title {
        width: 100%;
        word-break: break-all;
        font-size: var(--font-size-body1-size, 16px);
        line-height: 22px;
      }
      .device-capability {
        width: 100%;
        overflow: hidden;
        font-size: var(--font-size-text-size, 12px);
        line-height: 18px;
      }
      .device-capability-item {
        display: inline-flex;
        align-items: center;
      }
      .separator-box {
        display: inline-block;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        margin: 0px 4px;
      }
    }
  }
  .device-mutli-item-box {
    align-items: flex-start;
  }
  .device-right-box {
    display: inline-flex;
    align-items: center;
    .device-status-text {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
    }
  }
}
</style>
<style lang="scss">
.devices-list-wrapper {
  .van-collapse-item__content {
    padding: 2px 8px !important;
  }
  .device-list-item {
    .van-cell {
      height: 72px;
      padding: 10px 0px;
      box-sizing: border-box;
      display: inline-flex;
      align-items: center;
      background-color: transparent;
    }
  }
  .device-list-mutli-item {
    .van-cell {
      height: 97px;
      padding: 10px 0px;
      box-sizing: border-box;
      display: inline-flex;
      align-items: center;
      background-color: transparent;
    }
  }
  .van-cell__value {
    right: -15px;
    width: min-content;
    display: flex;
    justify-content: flex-end;
    .van-checkbox {
      justify-content: flex-end;
    }
  }
  .van-checkbox__icon {
    font-size: var(--font-size-body1-size, 16px);
  }
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
}
</style>
