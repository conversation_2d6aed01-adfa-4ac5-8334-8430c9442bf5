import { setCacheData } from '@/utils/appbridge'
import { dateFormat } from '@/utils/common'
import {
  getSiteDetails,
  getPanelState,
  getAllSites,
  getPairEntities,
  getPimaGeneralStatus,
  getEventLog,
  getNotifications,
  getFaults
} from '@/api/alarmSystem'
import {
  saveRiscoLoginToStorage,
  saveSiteLoginToStorage,
  clearAuthFromStorage,
  STORAGE_KEYS
} from '@/utils/authStorage'
import { transformPimaStatusData } from '@/utils/alarmSystem'

// 创建alarmSystem模块的初始状态
const createInitialState = () => ({
  systemType: 'Tyco', // 报警系统类型 Tyco/Risco/Pima
  userInfo: null, // 用户信息
  // 站点列表管理
  siteList: [], // 站点列表数据
  // Risco登录相关状态
  riscoLoginInfo: {
    accessToken: '',
    refreshToken: '',
    tokenType: '',
    expiresAt: '',
    currentLoginAttempt: 0,
    maxLoginAttempts: 0,
    errorText: '',
    errorTextCodeJp: '',
    status: 100,
    validationErrors: []
  },
  // 站点登录信息
  siteLoginInfo: {
    siteId: '',
    siteName: '',
    sessionId: '',
    expiresAt: '',
    cpId: '',
    systemType: '',
    pairEntityId: '' // Pima系统面板ID
  },
  // 面板状态信息
  panelState: {
    data: null, // 完整的GetState响应数据
    partitionCount: 1, // 分区数量
    partitions: [], // 分区详细信息
    status: null, // 面板状态
    isOnline: false, // 连接状态
    lastUpdated: null // 最后更新时间
  },
  // 报警数据信息
  alarmData: {
    alarmList: [], // 完整的alarm列表
    lastUpdated: null // 最后更新时间
  },
  // 故障数据信息
  troubleData: {
    troubleList: [], // 完整的trouble列表
    lastUpdated: null // 最后更新时间
  }
})

/**
 * 需要在数据清理时保留的字段
 * 这些字段通常是系统配置或不涉及用户隐私的数据
 */
const PERSISTENT_FIELDS = ['systemType', 'userInfo', 'siteList']

export default {
  namespaced: true,
  state: createInitialState,
  getters: {
    // 获取报警系统类型（优先从路由参数，其次从store，最后使用默认值）
    systemType: (state, _, rootState) => {
      return rootState.route?.query?.systemType || state.systemType || 'Risco'
    },
    // 获取有效的siteId（优先从路由参数，其次从store）
    siteId: (state, _, rootState) => {
      return rootState.route?.query?.siteId || state.siteLoginInfo?.siteId
    },
    siteName: (state, _, rootState) => {
      return rootState.route?.query?.siteName || state.siteLoginInfo?.siteName
    },
    // 获取有效的sessionId（优先从路由参数，其次从store）
    sessionId: (state, _, rootState) => {
      return rootState.route?.query?.sessionId || state.siteLoginInfo?.sessionId
    },
    // 便利的系统类型检查getters
    isPimaSystem: (_, getters) => {
      return getters.systemType === 'Pima'
    },
    isRiscoSystem: (_, getters) => {
      return getters.systemType === 'Risco'
    },
    isTycoSystem: (_, getters) => {
      return getters.systemType === 'Tyco'
    },
    // 判断是否有足够的数据进行API调用
    canFetchPanelState: (_, getters) => {
      return getters.siteId && getters.sessionId
    },
    // 获取最新的alarm数据（用于AlarmSystemAlarms组件）
    latestAlarm: state => {
      return state.alarmData.alarmList.length > 0 ? state.alarmData.alarmList[0] : null
    },
    // 获取全量alarm数据（用于Alarms页面）
    alarmList: state => {
      return state.alarmData.alarmList
    },

    // 获取最新的trouble数据（用于AlarmSystemTroubles组件）
    latestTrouble: state => {
      return state.troubleData.troubleList.length > 0 ? state.troubleData.troubleList[0] : null
    },

    // 获取全量trouble数据（用于Troubles页面）
    troubleList: state => {
      return state.troubleData.troubleList
    }
  },
  mutations: {
    SET_SYSTEM_TYPE(state, data) {
      state.systemType = data
    },
    SET_USER_INFO(state, data) {
      state.userInfo = data
    },
    CLEAR_USER_INFO(state) {
      state.userInfo = null
    },
    // Risco登录相关mutations
    SET_RISCO_LOGIN_INFO(state, data) {
      state.riscoLoginInfo = {
        ...state.riscoLoginInfo,
        ...data
      }
    },
    SET_RISCO_TOKENS(state, { accessToken, refreshToken, tokenType, expiresAt }) {
      state.riscoLoginInfo.accessToken = accessToken
      state.riscoLoginInfo.refreshToken = refreshToken
      state.riscoLoginInfo.tokenType = tokenType
      state.riscoLoginInfo.expiresAt = expiresAt
    },
    CLEAR_RISCO_LOGIN_INFO(state) {
      state.riscoLoginInfo = {
        accessToken: '',
        refreshToken: '',
        tokenType: '',
        expiresAt: '',
        currentLoginAttempt: 0,
        maxLoginAttempts: 0,
        errorText: '',
        errorTextCodeJp: '',
        status: 100,
        validationErrors: []
      }
    },
    // 站点登录相关mutations
    SET_SITE_LOGIN_INFO(state, data) {
      state.siteLoginInfo = {
        ...state.siteLoginInfo,
        ...data
      }
    },
    CLEAR_SITE_LOGIN_INFO(state) {
      state.siteLoginInfo = {
        siteId: '',
        siteName: '',
        sessionId: '',
        expiresAt: '',
        cpId: '',
        systemType: '',
        pairEntityId: ''
      }
    },

    SET_PANEL_STATE_INFO(state, data) {
      state.panelState = {
        ...state.panelState,
        data: {
          ...data
        },
        // 下面的字段是提取出来的关键字段
        partitionCount: data.state?.status?.partitions?.length || 1,
        partitions: data.state?.status?.partitions || [],
        status: data.state?.status?.systemStatus,
        isOnline: data.state?.isOnline && data.state?.media > 0,
        lastUpdated: Date.now()
      }
    },

    // 专门用于更新面板状态（armPartition接口返回的数据）
    UPDATE_PANEL_STATUS(state, statusData) {
      if (state.panelState.data && state.panelState.data.state) {
        // statusData 对应 data.state.status 层级的数据
        // 更新 panelState.data.state.status 字段
        state.panelState.data.state.status = {
          ...state.panelState.data.state.status,
          ...statusData
        }

        // 同步更新 panelState 中的相关字段
        if (statusData.partitions) {
          // 更新分区相关字段
          state.panelState.partitionCount = statusData.partitions.length
          state.panelState.partitions = statusData.partitions
        }

        // 更新系统状态
        if (statusData.systemStatus !== undefined) {
          state.panelState.status = statusData.systemStatus
        }

        // 更新时间戳
        state.panelState.lastUpdated = Date.now()
      }
    },

    CLEAR_PANEL_STATE(state) {
      state.panelState = {
        data: null,
        partitionCount: 1,
        partitions: [],
        status: null,
        isOnline: false,
        lastUpdated: null
      }
    },

    // 站点列表管理相关 mutations
    SET_SITE_LIST(state, siteList) {
      state.siteList = siteList
    },

    UPDATE_SITE_IN_LIST(state, { siteId, updates }) {
      const site = state.siteList.find(s => s.id === siteId)
      if (site) {
        Object.assign(site, updates)
      }
    },
    DELETE_SITE_FROM_LIST(state, siteId) {
      const index = state.siteList.findIndex(s => s.id === siteId)
      if (index > -1) {
        state.siteList.splice(index, 1)
      }
    },
    ADD_SITE_TO_LIST(state, site) {
      state.siteList.push(site)
    },

    // 报警数据相关 mutations
    SET_ALARM_LIST(state, alarmList) {
      console.log('alarmList', alarmList)
      state.alarmData = {
        alarmList: alarmList || [],
        lastUpdated: Date.now()
      }
    },

    CLEAR_ALARM_DATA(state) {
      state.alarmData = {
        alarmList: [],
        lastUpdated: null
      }
    },

    // 故障数据相关 mutations
    SET_TROUBLE_LIST(state, troubleList) {
      state.troubleData = {
        troubleList: troubleList || [],
        lastUpdated: Date.now()
      }
    },

    CLEAR_TROUBLE_DATA(state) {
      state.troubleData = {
        troubleList: [],
        lastUpdated: null
      }
    },

    // 清除store中存储的所有面板数据 - 退出登录时调用
    CLEAR_ALL_PANEL_DATA(state) {
      const initialState = createInitialState()
      const preservedData = {}
      // 保存需要保留的字段（如系统配置）
      PERSISTENT_FIELDS.forEach(field => {
        if (state[field] !== undefined) {
          preservedData[field] = state[field]
        }
      })
      // 重置所有状态到初始值，然后恢复保留的字段
      Object.assign(state, initialState, preservedData)
    }
  },
  actions: {
    // 保存Risco登录信息到store和APP缓存
    saveRiscoLoginInfo({ commit }, userInfo) {
      // 提取登录响应中的关键信息
      const riscoData = {
        ...userInfo
      }
      // 保存到Vuex store
      commit('SET_RISCO_LOGIN_INFO', riscoData)
      // 保存到APP本地缓存
      const cacheData = {
        key: 'risco_login_info',
        value: JSON.stringify(riscoData)
      }
      setCacheData(cacheData)
      // 如果有accessToken，单独保存token信息
      if (riscoData.accessToken) {
        const tokenData = {
          key: 'risco_access_token',
          value: riscoData.accessToken
        }
        setCacheData(tokenData)
      }
      // 新增：保存到localStorage
      saveRiscoLoginToStorage(riscoData)
      return riscoData
    },
    // 清除Risco登录信息
    clearRiscoLoginInfo({ commit }) {
      commit('CLEAR_RISCO_LOGIN_INFO')
      // 清除APP缓存中的数据
      setCacheData({ key: 'risco_login_info', value: '' })
      setCacheData({ key: 'risco_access_token', value: '' })
      // 新增：清除localStorage
      clearAuthFromStorage(STORAGE_KEYS.RISCO_LOGIN)
    },
    // 保存站点登录信息到store和APP缓存
    saveSiteLoginInfo({ commit }, siteInfo) {
      // 保存到Vuex store
      commit('SET_SITE_LOGIN_INFO', siteInfo)

      // 保存到APP本地缓存
      const cacheData = {
        key: 'site_login_info',
        value: JSON.stringify(siteInfo)
      }
      setCacheData(cacheData)

      // 单独保存sessionId
      if (siteInfo.sessionId) {
        const sessionData = {
          key: 'site_session_id',
          value: siteInfo.sessionId
        }
        setCacheData(sessionData)
      }
      // 新增：保存到localStorage
      saveSiteLoginToStorage(siteInfo)
      return siteInfo
    },
    // 清除站点登录信息
    clearSiteLoginInfo({ commit }) {
      commit('CLEAR_SITE_LOGIN_INFO')
      // 清除APP缓存中的数据
      setCacheData({ key: 'site_login_info', value: '' })
      setCacheData({ key: 'site_session_id', value: '' })
      // 新增：清除localStorage
      clearAuthFromStorage(STORAGE_KEYS.SITE_LOGIN)
    },
    // 站点列表管理相关 actions
    // 加载站点列表
    async loadSiteList({ commit, state }) {
      try {
        let response
        let transformedSites = []
        // 根据报警系统类型调用不同的API
        if (state.systemType === 'Pima') {
          // Pima系统：调用GetPairEntities接口
          response = await getPairEntities({ data: null })
          if (response?.data && Array.isArray(response.data)) {
            transformedSites = response.data
              .map(panel => transformPimaPanelToPanel(panel))
              .filter(panel => panel !== null)
          }
        } else {
          // Risco系统：调用getAllSites接口
          response = await getAllSites()
          if (response?.response && Array.isArray(response.response)) {
            transformedSites = response.response.map(site => transformSiteToPanel(site)).filter(panel => panel !== null)
          }
        }
        commit('SET_SITE_LIST', transformedSites)
      } catch (error) {
        console.error('Load site list failed:', error)
        throw error
      }
    },
    // 获取面板状态（完整流程，包含站点详情验证）
    async fetchPanelState({ commit }, { siteId, systemType }) {
      try {
        let panelData

        // 根据系统类型调用不同的API
        if (systemType === 'Pima') {
          // Pima系统：直接调用GetGeneralStatus接口（sessionToken自动注入）
          const response = await getPimaGeneralStatus({ data: null })
          panelData = transformPimaStatusData(response.data)
        } else {
          // Risco系统：原有逻辑（sessionToken自动注入）
          // 1. 获取站点详情
          const siteDetailsResponse = await getSiteDetails(siteId)
          const siteDetails = siteDetailsResponse.response

          // 2. 检查安全系统
          if (!hasSecuritySystem(siteDetails)) {
            throw new Error('No security system configured for this site')
          }
          // 3. 获取面板状态
          const panelResponse = await getPanelState(siteId, {
            fromControlPanel: true
          })

          panelData = panelResponse.response
        }
        console.log('panelData', panelData)
        commit('SET_PANEL_STATE_INFO', panelData || {})
        return panelData
      } catch (error) {
        console.error('Failed to fetch panel state:', error)
        throw error
      }
    },

    // 清除面板状态
    clearPanelState({ commit }) {
      commit('CLEAR_PANEL_STATE')
    },
    // 获取报警数据列表
    async fetchAlarms({ commit }, { siteId, systemType, count = 100 }) {
      try {
        let alarmList = []
        // 根据系统类型调用不同的API
        if (systemType === 'Pima') {
          // Pima系统：调用GetNotifications接口（sessionToken自动注入）
          const response = await getNotifications({ data: false }) // false = 应用过滤器
          if (response.data && Array.isArray(response.data)) {
            // 定义警报相关的通知类型
            const ALARM_NOTIFICATION_TYPES = [1, 2, 3, 4, 5, 6, 7, 8] // Burglary, Panic, Fire, Duress, Medical, Tamper、Faults、ArmDisarm
            alarmList = response.data
              .filter(notification => ALARM_NOTIFICATION_TYPES.includes(notification.NotificationType))
              .map((notification, index) => ({
                id: index,
                type: notification.Message || 'Unknown Event',
                time: dateFormat(notification.SentDate),
                pictureUrls: notification.PictureUrls || [],
                rawData: notification
              }))
              .slice(0, count) // 限制返回数量
          }
        } else {
          // Risco系统：使用原有的getEventLog接口（sessionToken自动注入）
          const response = await getEventLog(siteId, {
            offset: 0,
            count
          })
          if (response.response && response.response.controlPanelEventsList) {
            // 只保留alarm类型的数据
            alarmList = response.response.controlPanelEventsList
              .filter(item => item.groupName === 'Alarms')
              .map((event, index) => ({
                id: event.lineNumber || index,
                type: event.eventName || event.eventText || 'Unknown Event',
                time: dateFormat(event.logTime),
                rawData: event
              }))
          }
        }

        commit('SET_ALARM_LIST', alarmList)
        return alarmList
      } catch (error) {
        console.error('Failed to fetch alarms:', error)
      }
    },
    // 清除报警数据
    clearAlarmData({ commit }) {
      commit('CLEAR_ALARM_DATA')
    },
    // 获取故障数据列表
    async fetchTroubles({ commit }, { siteId, systemType, count = 100 }) {
      try {
        let troubleList = []
        // 根据系统类型调用不同的API
        if (systemType === 'Pima') {
          // Pima系统：调用GetFaults接口（sessionToken自动注入）
          const response = await getFaults({ data: null })
          if (response.data && Array.isArray(response.data)) {
            troubleList = response.data
              .map((faultText, index) => ({
                id: index,
                type: faultText || 'Unknown Fault',
                rawData: { faultText }
              }))
              .slice(0, count) // 限制返回数量
          }
        } else {
          // Risco系统：使用getEventLog接口获取trouble类型的数据（sessionToken自动注入）
          const response = await getEventLog(siteId, {
            offset: 0,
            count
          })
          if (response.response && response.response.controlPanelEventsList) {
            // 只保留trouble类型的数据
            troubleList = response.response.controlPanelEventsList
              .filter(item => item.groupName === 'Troubles')
              .map((event, index) => ({
                id: event.lineNumber || index,
                type: event.eventName || event.eventText || 'Unknown Event',
                time: dateFormat(event.logTime),
                rawData: event
              }))
          }
        }
        commit('SET_TROUBLE_LIST', troubleList)
        return troubleList
      } catch (error) {
        console.error('Failed to fetch troubles:', error)
        commit('SET_TROUBLE_LIST', [])
        throw error
      }
    },
    // 清除故障数据
    clearTroubleData({ commit }) {
      commit('CLEAR_TROUBLE_DATA')
    },
    // 清除所有面板数据和localStorage缓存 - 退出登录时调用
    clearAllPanelData({ commit }) {
      commit('CLEAR_ALL_PANEL_DATA')
      // 清除localStorage中的认证缓存
      clearAuthFromStorage(STORAGE_KEYS.SITE_LOGIN)
    }
  }
}

// 辅助函数--如果controlPanel.id字段有值（不为null），则表示该站点已经关联了控制面板，即已预设了安全系统
function hasSecuritySystem(siteDetails) {
  return siteDetails?.controlPanel && siteDetails.controlPanel.id
}

// 站点数据转换为面板数据 (Risco系统)
function transformSiteToPanel(site) {
  if (!site || typeof site.id === 'undefined') {
    console.warn('Invalid site data:', site)
    return null
  }
  return {
    id: site.id,
    name: site.name || 'Unknown Panel',
    serialNumber: site.siteUUID || 'N/A',
    ...site
  }
}

// Pima面板数据转换为统一面板数据格式
function transformPimaPanelToPanel(panel) {
  if (!panel || !panel.pairId) {
    console.warn('Invalid Pima panel data:', panel)
    return null
  }
  return {
    id: panel.pairId, // 使用pairId作为唯一标识
    name: panel.name || 'Unknown Panel', // 面板名称
    serialNumber: panel.pairId, // 使用pairId作为序列号显示
    unreadNotificationCount: panel.unreadNotificationCount || 0, // 未读通知数量
    timeZone: panel.timeZone || '', // 时区信息
    utcTimeOffsetSeconds: panel.utcTimeOffsetSeconds || 0, // UTC时间偏移
    keyStatus: panel.KeyStatus || 0, // 按键状态
    systemType: 'Pima', // 标记为Pima系统
    ...panel // 保留原始数据
  }
}
