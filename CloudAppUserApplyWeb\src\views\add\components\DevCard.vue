<template>
  <div class="card" :class="{ 'grey-card': devInfo?.noCode && !devInfo?.code }">
    <div class="content" @click="showCodeInput">
      <div class="left-info">
        <img class="dev-img" :src="require('@/assets/img/common/site/slice.png')" alt="" />
        <div class="dev-name">
          {{ devInfo?.devName }}
          <span v-if="devInfo.channelNum">{{ $t('nChannels', { n: devInfo.channelNum }) }}</span>
          <div class="sn-line">
            <img class="circle-img" v-if="devInfo.status" :src="infoIconMap[devInfo.status]" alt="" />
            <div class="sn-num">SN:{{ devInfo.snPlain }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="dev-status" v-if="devInfo?.bindStatus">
      <img
        :class="{ 'loading-status': devInfo?.bindStatus === 1 }"
        :src="bindStatusMap?.[devInfo.bindStatus - 1]?.img"
        alt=""
      />
      <div class="dev-tips text-over-ellipsis">
        <div>
          {{ getName() }}
        </div>
      </div>
    </div>
    <div class="dev-status dev-upgrade" v-else-if="devInfo?.noCode && !devInfo?.code">
      <div class="left-box">{{ devInfo?.code ? '' : $t('noCode') }}</div>
      <div class="right-btn" @click.stop="openCodeInput">{{ $t('input') }}</div>
    </div>
    <div class="bottom-info">
      MAC:{{ devInfo.macAddress }} | IP:{{ devInfo.ipAddress }} | DHCP:{{
        devInfo.isActivated ? $t('open') : $t('close')
      }}
    </div>
  </div>
</template>

<script>
import loadingImg from '@/assets/img/common/site/loading.png'
import checkCircleImg from '@/assets/img/common/site/check_circle.png'
import warnImg from '@/assets/img/common/site/warn.png'
export default {
  props: {
    devInfo: {
      type: Object,
      default: () => {}
    },
    hiddenHaddle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      infoIconMap: {
        0: require('@/assets/img/common/site/offline_info.png'),
        1: require('@/assets/img/common/site/online_info.png')
      },
      bindStatusMap: [
        {
          img: loadingImg,
          text: this.$t('adding')
        },
        {
          img: checkCircleImg,
          text: this.$t('addSuccess')
        },
        {
          img: warnImg,
          text: this.$t('addFail')
        }
      ]
    }
  },
  computed: {},
  mounted() {},
  methods: {
    getName() {
      if (this.devInfo?.errorCode) {
        if (this.devInfo.errorCode === 7063) {
          return this.$t('bindByOther', { name: this.devInfo?.userName })
        }
        return this.$t(`errorCode.${this.devInfo?.errorCode}`)
      }
      return this.bindStatusMap?.[this.devInfo.bindStatus - 1]?.text
    },
    openCodeInput() {
      this.$emit('openCodeInput')
    },
    showCodeInput() {
      if (this.devInfo?.noCode && !this.devInfo?.code) {
        this.$emit('openCodeInput')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  box-sizing: border-box;
  border-radius: 6px;
  background: var(--bg-color-white, #ffffff);
  width: 100%;
  border: 1px solid var(--outline-color-primary, #e7e7e7);
  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 73px;
    background: var(--bg-color-white, #ffffff);
    .left-info {
      display: flex;
      .dev-img {
        width: 70px;
        height: 49px;
        margin: 12px;
      }
      .dev-name {
        height: 22px;
        color: var(--text-color-primary, #101d34);
        font-size: var(--font-size-body2-size, 14px);
        font-weight: 400;
        line-height: 22px;
        margin-top: 16px;
        .sn-line {
          display: flex;
          height: 20px;
          margin: 2px 0 3px 0;
          img {
            width: 16px;
            height: 16px;
            margin: 2px 4px 0 0;
          }
          .sn-num {
            color: var(--text-color-primary, #82879b);
            font-size: var(--font-size-text-size, 12px);
            font-weight: 400;
            line-height: 20px;
          }
        }
      }
    }
  }
  .dev-upgrade {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    margin-top: 10px;
    padding: 0 12px;
    .left-box {
      > span {
        color: var(--text-color-primary, #101d34);
        font-size: var(--font-size-text-size, 12px);
        font-weight: 400;
      }
    }
    .right-btn {
      color: var(--brand-bg-color-active, #1d71f3);
      font-size: var(--font-size-text-size, 12px);
      font-weight: 400;
    }
  }
  .dev-status {
    width: calc(100% - 21px);
    height: 28px;
    display: flex;
    align-items: center;
    line-height: 28px;
    border-radius: 8px;
    margin-left: 9px;
    background: #f0f0ef;
    img {
      height: 16px;
      width: 16px;
      margin: 0 4px 0 12px;
    }
    .dev-tips {
      width: calc(100% - 28px);
      padding: 0 12px 0 4px;
      color: var(--text-color-primary, #101d34);
      font-size: var(--font-size-text-size, 12px);
    }
  }
  .haddle-btn {
    height: 76px;
    display: flex;
    background: var(--bg-color-white, #ffffff);
    padding: 20px 62px;
    justify-content: space-between;
    .haddle-li {
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
  .bottom-info {
    color: #8f9cb3;
    background: #fafbfd;
    padding-left: 12px;
    font-size: var(--font-size-text-size, 12px);
    font-weight: 400;
    line-height: 25px;
  }
}
.grey-card {
  .content {
    .left-info {
      .dev-img {
        filter: grayscale(90%);
      }
      .dev-name {
        color: var(--text-color-placeholder, #9a9ca2);
      }
      .sn-num {
        color: var(--text-color-placeholder, #9a9ca2) !important;
      }
    }
  }
  .dev-upgrade {
    background: #f0f0ef80;
    .left-box {
      font-size: var(--font-size-text-size, 12px);
    }
  }
  .bottom-info {
    color: var(--text-color-placeholder, #9a9ca2);
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-status {
  animation: rotate 2s linear infinite;
}
</style>
