import CryptoJS from 'crypto-js'

/**
 * 生成随机AES256密钥（32字节随机数经过Base64编码）
 * 示例使用：const key = generateAESKey()
 */
export function generateAESKey() {
  // 生成32字节随机数据（256位）
  const randomKey = CryptoJS.lib.WordArray.random(32)
  // 转换为Base64字符串（44字符长度）
  return CryptoJS.enc.Base64.stringify(randomKey)
}

/**
 * 加密
 * @param word 明文
 * @param key 秘钥
 * mode和padding 需要与服务端相对应
 */
export function encrypt(word, key) {
  let srcs = CryptoJS.enc.Utf8.parse(word)
  let aesKey = CryptoJS.enc.Utf8.parse(key)
  let encrypted = CryptoJS.AES.encrypt(srcs, aesKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding
  })

  return encrypted.toString()
}

/**
 * 解密
 * @param word 密文
 * @param key 秘钥
 * mode和padding 需要与服务端相对应
 */
export function decrypt(word, key) {
  let aesKey = CryptoJS.enc.Utf8.parse(key)
  let decrypt = CryptoJS.AES.decrypt(word, aesKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.ZeroPadding
  })
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
  return decryptedStr.toString()
}
