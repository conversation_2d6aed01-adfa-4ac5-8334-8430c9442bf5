<execution>
  <constraint>
    ## Vue开发的客观限制
    - **浏览器兼容性**：Vue 3需要现代浏览器支持，IE11需要特殊处理
    - **构建工具限制**：Vite在某些企业环境下可能有网络限制
    - **TypeScript集成**：类型定义的学习成本和开发效率权衡
    - **SEO需求**：SPA应用的SEO限制，可能需要SSR/SSG方案
    - **团队技能**：团队成员对Vue生态的熟悉程度影响技术选型
  </constraint>

  <rule>
    ## Vue开发强制规则
    - **组件命名规范**：使用PascalCase命名组件，kebab-case用于模板
    - **单文件组件结构**：template → script → style的固定顺序
    - **响应式数据规范**：避免直接修改props，使用computed和watch合理
    - **生命周期使用**：正确使用生命周期钩子，避免内存泄漏
    - **事件命名规范**：使用kebab-case命名自定义事件
    - **样式作用域**：使用scoped或CSS Modules避免样式污染
  </rule>

  <guideline>
    ## Vue开发指导原则
    - **渐进式开发**：从简单开始，逐步增加复杂性
    - **组件化优先**：优先考虑组件的复用性和可维护性
    - **性能意识**：开发过程中时刻关注性能影响
    - **用户体验**：优先考虑用户交互体验和页面响应速度
    - **代码可读性**：清晰的命名和适当的注释
    - **测试驱动**：重要组件和功能需要有对应的测试
  </guideline>

  <process>
    ## Vue项目开发标准流程
    
    ### Phase 1: 项目初始化 (15分钟)
    ```mermaid
    flowchart LR
        A[需求分析] --> B[技术选型]
        B --> C[项目创建]
        C --> D[基础配置]
        D --> E[开发环境]
    ```
    
    **具体步骤**：
    1. **项目创建**：`vue create project-name` 或 `npm create vue@latest`
    2. **依赖安装**：根据需求安装UI框架、状态管理、路由等
    3. **配置文件**：设置vue.config.js、vite.config.js等
    4. **代码规范**：配置ESLint、Prettier、Git hooks
    5. **目录结构**：建立清晰的项目目录结构
    
    ### Phase 2: 组件开发 (核心开发阶段)
    ```mermaid
    flowchart TD
        A[组件设计] --> B[接口定义]
        B --> C[模板编写]
        C --> D[逻辑实现]
        D --> E[样式开发]
        E --> F[测试验证]
        F --> G{质量检查}
        G -->|通过| H[组件完成]
        G -->|不通过| C
    ```
    
    **开发规范**：
    - **组件拆分**：按照原子设计理论进行组件层次划分
    - **Props设计**：明确的类型定义和默认值
    - **事件设计**：清晰的事件命名和参数传递
    - **插槽使用**：合理使用具名插槽和作用域插槽
    - **样式管理**：使用CSS变量和设计令牌
    
    ### Phase 3: 状态管理 (数据流设计)
    ```mermaid
    graph TD
        A[状态分析] --> B{状态类型}
        B -->|组件状态| C[本地状态]
        B -->|共享状态| D[全局状态]
        C --> E[ref/reactive]
        D --> F[Pinia/Vuex]
        E --> G[状态更新]
        F --> G
        G --> H[视图更新]
    ```
    
    **状态管理策略**：
    - **本地状态优先**：能用组件状态就不用全局状态
    - **状态提升**：当多个组件需要共享时才提升状态
    - **异步处理**：合理处理API调用和异步状态
    - **状态持久化**：需要持久化的状态使用localStorage等
    
    ### Phase 4: 路由设计 (导航架构)
    ```mermaid
    graph LR
        A[路由规划] --> B[路由配置]
        B --> C[导航守卫]
        C --> D[懒加载]
        D --> E[路由测试]
    ```
    
    **路由最佳实践**：
    - **路由结构**：清晰的路由层次和命名
    - **懒加载**：页面级组件使用动态导入
    - **导航守卫**：权限控制和页面跳转逻辑
    - **路由元信息**：页面标题、权限等元数据管理
    
    ### Phase 5: 构建优化 (性能调优)
    ```mermaid
    flowchart LR
        A[性能分析] --> B[代码分割]
        B --> C[资源优化]
        C --> D[缓存策略]
        D --> E[部署配置]
    ```
    
    **优化策略**：
    - **Bundle分析**：使用webpack-bundle-analyzer分析包大小
    - **代码分割**：路由级别和组件级别的代码分割
    - **资源压缩**：图片、CSS、JS的压缩优化
    - **CDN配置**：静态资源CDN加速
    - **缓存策略**：合理的浏览器缓存配置
  </process>

  <criteria>
    ## Vue开发质量标准
    
    ### 代码质量指标
    - ✅ ESLint检查通过率 100%
    - ✅ 组件复用率 ≥ 60%
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 构建时间 ≤ 2分钟
    - ✅ 首屏加载时间 ≤ 3秒
    
    ### 用户体验指标
    - ✅ 页面响应时间 ≤ 200ms
    - ✅ 交互反馈及时性 100%
    - ✅ 移动端适配完整性 100%
    - ✅ 浏览器兼容性覆盖率 ≥ 95%
    
    ### 可维护性指标
    - ✅ 组件文档完整性 100%
    - ✅ API接口文档覆盖率 100%
    - ✅ 代码注释覆盖率 ≥ 30%
    - ✅ 组件单元测试覆盖率 ≥ 70%
    
    ### 性能指标
    - ✅ Lighthouse性能评分 ≥ 90
    - ✅ 首次内容绘制(FCP) ≤ 1.5s
    - ✅ 最大内容绘制(LCP) ≤ 2.5s
    - ✅ 累积布局偏移(CLS) ≤ 0.1
  </criteria>
</execution>