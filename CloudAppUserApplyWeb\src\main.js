// 兼容 IE
// https://github.com/zloirock/core-js/blob/master/docs/2019-03-19-core-js-3-babel-and-a-look-into-the-future.md#babelpolyfill
import 'core-js/stable'
import 'regenerator-runtime/runtime'

import Vue from 'vue'
import App from './App.vue'
import '@/utils/appLogger'
import router from './router'
import store from './store'
import { sync } from 'vuex-router-sync'
import { utils } from './utils/index'
import TvtXml2Json from './utils/tvtXml2Json.js'
import Loading from '@/components/loading/loading.js'
import { Toast } from 'vant'
import draggable from 'vuedraggable'
import i18n, { initI18n } from './lang'
import { appLog } from '@/utils/appbridge'
import {
  getParamsFromUserAgent,
  getVersionAndStyle,
  isExistTypeUi,
  ifDevelopment,
  loadRemoteCSS,
  loadRemoteVariableCSS,
  normalizeLanguageCode
} from '@/utils/common.js'
import ThemeImage from '@/components/ThemeImage'
import NavBar from '@/components/NavBar'
//开发模式使用这个打印
ifDevelopment(() => {
  import('vconsole').then(VConsole => {
    new VConsole.default()
  })
})

Vue.component('ThemeImage', ThemeImage)
Vue.component('NavBar', NavBar)
Vue.prototype.$moment = window.moment
Vue.prototype.$utils = utils
Vue.prototype.$TvtXml2Json = TvtXml2Json

let firstParamObj = getParamsFromUserAgent()
let sencondParamObj = getVersionAndStyle() //为了兼容之前云升级里面的取参方式
let { version, style, language, appType, appName, appPlatform, bridgeType, appId } = firstParamObj.style
  ? firstParamObj
  : sencondParamObj
ifDevelopment(() => (appType = 'TOC')) // 调试代码 指定TOBW
ifDevelopment(() => (style = 'UI1C')) // 调试代码 指定UI2B
// 保存原始的style用于语言判断
let originalStyle = style

appLog('log/info', `${new Date()} navigator.userAgent ${navigator.userAgent}`)
// 判断现在H5里面能使用的UI
let typeUiobj = isExistTypeUi(appType, style, window.typeUiList)
let type = appType
let UI = typeUiobj.UI
if (UI === 'UI1J') {
  // SmartEyesPlus（慧眼家）和 SuperCamPlus 共用一套样式
  UI = 'UI1I' // 将SmartEyesPlus（慧眼家）的直接赋值SuperCamPlus的样式
  originalStyle = 'UI1I'
}
// 标准化语言代码--APP传过来的语言有zh和zh-CN两种，需要统一成zh-CN这种形式
language = normalizeLanguageCode(language)
appLog('log/info', `${new Date()} 标准化语言 ${language}`)
let data = { appPlatform, version, style: UI, originalStyle, language, appType, appName, bridgeType, appId }
ifDevelopment(() => (language = 'zh-CN')) // 调试代码 指定中文
store.commit('app/SET_APP_INFO', data)

// 根据传过来的安全区范围设置css变量
const { top, bottom } = firstParamObj
document.documentElement.style.setProperty('--safeAreaTop', `${top}px`)
document.documentElement.style.setProperty('--safeAreaBottom', `${bottom}px`)

// 全局引入按需引入UI库 vant
import vant from './plugins/vant'
// 引入全局样式
import '@/assets/css/index.scss'
// import('@/assets/css/' + type + '/' + UI + '/index.scss') //此地址通过变量拼接
// 加载远程定制主题css文件
loadRemoteCSS(type, UI)
// 加载远程定制主题变量css文件
loadRemoteVariableCSS(type, UI)
// 移动端适配
import 'amfe-flexible'

vant.forEach(plugin => {
  Vue.use(plugin)
})
Vue.use(Loading)

// 引入自定义的全局组件
import TvtBetterScroll from '@/components/TvtBetterScroll'
Vue.component('TvtBetterScroll', TvtBetterScroll)

Vue.component('draggable', draggable)

// 注册自定义的toast
Vue.prototype.$toastFail = message => {
  Toast.fail({
    className: 'max-custom-toast',
    iconPrefix: 'max-toast-icon',
    icon: require('@/assets/img/common/fail_icon.png'),
    message,
    position: 'middle'
  })
}
Vue.prototype.$toastSuccess = message => {
  Toast.success({
    className: 'max-custom-toast',
    iconPrefix: 'max-toast-icon',
    icon: require('@/assets/img/common/success_icon.png'),
    message,
    position: 'middle'
  })
}

// 设置语言
let vueI18n = i18n
if (language) {
  // 设置本地缓存额语言
  localStorage.setItem('LANUAGE_TYPE', language)
}

Vue.config.productionTip = false

/**********************方案二  ***************************** */

// 将Vue实例创建封装在异步函数中
async function initApp() {
  // 等待语言包加载完成--可能由于请求慢导致Vue初始化慢影响页面展示
  // 如果出现这种情况，则使用方案一：把initI18n放到外面，在使用到国际化文案的页面中使用computed属性获取最新的词条
  // 定制语言需要使用tyle而不是UI，因为有些定制客户没有主题但是有定制语言，例如UI1L，这种UI会变成中性，所以需要使用原始的style来区分
  await initI18n(language, appType, originalStyle)

  // 同步router状态到store
  sync(store, router)

  new Vue({
    el: '#app',
    router,
    store,
    i18n: vueI18n,
    render: h => h(App)
  })
}

initApp()
