export const serviceStatus = {
  pending: 0,
  serving: 1,
  rejected: 2,
  deleted: 3
}

const userType = {
  visitor: 0, // 未激活，游客身份
  installer: 1 // 安装商
}

// - 如果是个人角色，展示用户头像，用户昵称、个人资料中的邮箱、手机号，无地址就不展示或者展示为空占位。
// - 如果是企业角色，展示企业logo，企业名称，联系人，个人资料中的邮箱，手机号（若无手机号，就展示企业信息中的联系方式），地址
// 产品给出的定义
export function formatInstallerInfo(data) {
  const info = {
    logo: '',
    loginName: '',
    installerCoName: '',
    email: '',
    mobile: '',
    addr: '',
    installerUserId: data.installerUserId
  }
  if (data.status === userType.visitor) {
    info.logo = data.image
    info.loginName = data.nickname
    info.installerCoName = ''
    info.email = data.email
    info.mobile = data.mobile
    info.addr = data.address
  } else {
    info.logo = data?.merchant?.logo || ''
    info.installerCoName = data?.merchant?.merchantName || ''
    info.addr = data?.merchant?.addr || ''
    info.loginName = data?.merchant?.contact || ''
    info.email = data.email || ''
    info.mobile = data.mobile || data?.merchant?.tel || ''
  }

  return info
}
