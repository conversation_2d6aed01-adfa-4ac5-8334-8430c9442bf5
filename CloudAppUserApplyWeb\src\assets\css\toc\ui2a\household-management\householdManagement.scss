.household-manangement-wrapper,
.household-manangement-dialog {
  .input-item {
    border-bottom: 1px solid $USE44-light-gray-color;

    &::after {
      border: none;
    }
  }

  .error-msg {
    color: $USE44-red-color;
  }

  .footer-btn {
    color: $USE44-white-color;
  }

  .room-member-input {
    background-color: transparent;

    .van-field__control {
      color: $USE44-white-color;
    }
  }

  .van-dialog__header {
    color: $USE44-font-color;
  }

  .cruise-line-div {
    border: 1px solid $USE44-light-gray-color;
    background-color: $USE44-background-color;

    .common-input {
      background-color: transparent;
    }
  }

  .tvt-better-scroll {
    .household-item {
      background: $USE44-light-background-color;
      color: $USE44-font-color;

      .household-title {
        color: $USE44-white-color;

      }

      .household-line-text {
        color: $vms-gray;
      }

      .required-icon {
        color: $vms-red;
      }

      .right-value {
        color: $vms-gray;
      }

      &:not(:last-child) {
        border-bottom: 1px solid $USE44-light-gray-color;
      }

      &::after {
        border-color: $USE44-light-gray-color;
      }
    }

    .room-header {
      color: $vms-gray;
    }

    .device-select-icon {
      &.success {
        color: $USE44-font-color;
      }
    }
  }

  .no-data {
    .add-btn {
      background-color: $USE44-button-background-color;
      color: $USE44-font-color;
    }
  }

  .no-data-text {
    .footer-btn {
      background-color: $USE44-button-background-color;
      color: $USE44-font-color;
    }
  }
}

.household-item-box {
  background: $USE44-light-background-color;
  color: $USE44-font-color;
  .household-title {
    color: $USE44-white-color;
  }
  .required-icon {
    color: $vms-red;
  }
  .right-value {
    color: $vms-gray;
  }
}