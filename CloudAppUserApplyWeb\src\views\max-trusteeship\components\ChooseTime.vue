<template>
  <div class="choose-capability-wrapper">
    <div class="choose-capability-head">
      <div class="choose-capability-title">{{ $t('settingTime') }}</div>
      <img class="close-icon" alt="close" :src="require('@/assets/img/common/close.png')" @click="handleCancel" />
    </div>
    <div class="choose-capability-body">
      <van-radio-group v-model="time">
        <van-cell-group>
          <van-cell
            v-for="(item, index) of options"
            clickable
            :key="item.value"
            :title="item.label"
            @click="!item.disabled ? toggle(index) : null"
          >
            <template #right-icon>
              <van-radio :disabled="item.disabled || false" :name="item.value" ref="radio" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </div>
    <div class="choose-capability-foot">
      <van-button class="footer-btn" type="primary" @click="handleClick">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ChooseTime',
  components: {},
  props: {
    value: {
      type: Number,
      default: () => null
    },
    options: {
      type: Array,
      default: () => []
    },
    cancel: {
      type: Function,
      default: () => {}
    },
    confirm: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      time: null // 选中的时间
    }
  },
  watch: {
    value: {
      handler(val, oldVal) {
        if (val !== oldVal) {
          this.time = val
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toggle(index) {
      this.$refs.radio[index].toggle()
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleClick() {
      this.$emit('input', this.time)
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-capability-wrapper {
  width: 100%;
  height: 400px;
  overflow: auto;
  .choose-capability-head {
    width: 100%;
    height: 52px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 14px 12px;
    box-sizing: border-box;
    .choose-capability-title {
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-icon {
      position: absolute;
      top: 18px;
      right: 12px;
      width: 16px;
      height: 16px;
    }
  }
  .choose-capability-body {
    width: 100%;
    height: calc(100% - 112px);
  }
  .choose-capability-foot {
    width: 100%;
    height: 60px;
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    .footer-btn {
      width: 327px;
      height: 40px;
      border-radius: 23px;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
<style lang="scss">
.choose-capability-body {
  .van-cell {
    height: 52px !important;
  }
}
</style>
