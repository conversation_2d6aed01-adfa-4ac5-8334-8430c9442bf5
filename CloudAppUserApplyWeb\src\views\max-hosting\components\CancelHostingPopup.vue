<template>
  <div>
    <van-popup v-model="visible" position="bottom" round :close-on-click-overlay="false" get-container="#app">
      <div class="site-hosting-cancel-pop-container">
        <div class="header">
          <span class="title">
            {{ $t('cancelHosting') }}
          </span>
        </div>
        <div class="content">
          {{ $t('cancelHostingTip') }}
        </div>
        <div class="footer-btn">
          <div class="stop-btn" @click="visible = false">
            {{ $t('notNow') }}
          </div>
          <div class="cancel-btn" @click="cancelHosting">
            {{ $t('stillCancel') }}
          </div>
        </div>
      </div>
    </van-popup>
    <van-popup v-model="errorVisible" position="bottom" round get-container="#app">
      <div class="site-hosting-cancel-pop-container">
        <div class="header">
          <span class="title">
            {{ $t('cancelHosting') }}
          </span>
        </div>
        <div class="content high-padding">
          {{ $t('cancelHostingError') }}
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { cancelSite } from '@/api/maxHosting'
import { appClose, showToast } from '@/utils/appbridge'

export default {
  name: 'CancelHostingPopup',
  props: {
    id: {
      type: [String, Number],
      default: ''
    },
    siteNum: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      visible: false,
      errorVisible: false
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    async cancelHosting() {
      await cancelSite({
        siteId: this.id
      })
      this.visible = false
      // 开始只有一个站点，取消后直接跳到服务页
      if (this.siteNum <= 1) {
        showToast({
          msg: this.$t('cancelled'),
          style: 0
        })
        appClose()
      } else {
        this.$toastSuccess(this.$t('cancelled'))

        // 大于一个站点则返回服务详情页
        this.$emit('refresh')
      }
    }
  }
}
</script>

<style lang="scss">
.site-hosting-cancel-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 52px;
    padding: 16px;
    .title {
      color: var(--text-color-secondary, #3d3c3c);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
  }
  .content {
    width: 100%;
    box-sizing: border-box;
    padding: 0 25px;
    word-break: break-all;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-secondary, #3d3c3c);
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin: 36px 0 20px;
    &.high-padding {
      padding-bottom: 80px;
    }
  }
  .footer-btn {
    display: flex;
    height: 52px;
    .stop-btn {
      flex: 1;
      height: 52px;
      line-height: 52px;
      text-align: center;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
    }
    .cancel-btn {
      flex: 1;
      height: 52px;
      line-height: 52px;
      text-align: center;
      color: var(--error-bg-color-default, #ff3b30);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
    }
  }
}
</style>
