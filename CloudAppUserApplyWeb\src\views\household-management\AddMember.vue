<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="$t('addRoomMember')" @clickLeft="back"></nav-bar>
    <div class="household-content">
      <!-- 基本信息部分 -->
      <div class="section-title">{{ $t('basicInfo') }}</div>
      <van-cell class="household-item" name="building">
        <template #title>
          <span class="household-title">{{ $t('buildingName') }}</span>
        </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ buildingInfo && buildingInfo.buildingName }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item" name="room">
        <template #title>
          <span class="household-title">{{ $t('roomName') }}</span>
        </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ roomInfo && roomInfo.roomNo }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item" name="name" is-link @click.stop="showNameDialog">
        <template #title>
          <span class="required-icon">*</span>
          <span class="household-title">{{ $t('targetName') }}</span>
        </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ curMemberInfo && curMemberInfo.memberName }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item" name="idCard" is-link @click.stop="editMemberAccount">
        <template #title>
          <span class="required-icon">*</span>
          <span class="household-title">{{ $t('memberAccount') }}</span>
        </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ curMemberInfo && curMemberInfo.memberContact }}</span>
        </template>
      </van-cell>

      <!-- 凭证部分 -->
      <div class="section-title">{{ $t('certificate') }}</div>
      <van-cell class="household-item" name="face" is-link @click="!facialImage && editTargetFace()">
        <template #title>
          <span class="household-title">{{ $t('personFace') }}</span>
        </template>
      </van-cell>
      <!-- 人脸图片展示区域 -->
      <div class="household-item-box">
        <div class="face-content" v-if="facialImage">
          <div class="face-image">
            <img :src="facialImage" alt="face" />
          </div>
          <div class="face-actions">
            <theme-image class="action-icon" alt="delete" imageName="photo_delete.png" @click.stop="deleteTargetFace" />
            <div class="divider-line"></div>
            <theme-image class="action-icon" alt="edit" imageName="photo_edit.png" @click.stop="editTargetFace" />
          </div>
        </div>
      </div>
      <!-- 门禁卡 -->
      <van-cell class="household-item" name="card" is-link @click.stop="editDoorCard">
        <template #title>
          <span class="household-title">{{ $t('doorCard') }}</span>
        </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ memberInfo && memberInfo.cardNo }}</span>
        </template>
      </van-cell>
    </div>

    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('confirm') }}
      </van-button>
    </div>
    <edit-dialog
      ref="nameDialog"
      :title="$t('targetName')"
      :value="tempMemberName"
      :maxLength="30"
      @confirm="confirmName"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import EditDialog from './dialog/EditDialog.vue'
import { appReqeustNative } from '@/utils/appbridge.js'
import { mapState, mapMutations } from 'vuex'
import { base64ToBlob } from '@/utils/common.js'
import MD5 from 'js-md5'
import { smartUpload as ossSmartUpload } from '@/utils/aliOss'
import { smartUpload as awsSmartUpload } from '@/utils/awsS3'
import { updateRoomMember, addRoomMembers, getOssAccess, getFaceImage } from '@/api/householdManagement.js'

// 常量定义
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB文件大小限制
const BUSINESS_TYPE = 'buildingUserMgr' // 业务类型
const FACE_CAMERA_TIMEOUT = 3000 // 人脸拍照超时时间
const NATIVE_BRIDGE_CONFIG = {
  url: 'REQUEST_PLUGIN_CAMERA_FACE',
  params: '',
  timeout: FACE_CAMERA_TIMEOUT
}

// 工具方法：生成人脸文件名
const generateFaceFileName = () => `face${Date.now()}.jpg`

export default {
  name: 'addMember',
  components: {
    NavBar,
    ThemeImage,
    EditDialog // 注册组件
  },
  data() {
    return {
      roomId: '', // 房间id 编辑房间住户时才有
      buildingMemberId: '', // 住户id 编辑房间住户时才有
      curMemberInfo: {},
      tempMemberName: '',
      facialImage: null,
      loading: null,
      fileInfo: {
        facialImageHash: '',
        faceFile: null,
        faceFileUrl: ''
      }
    }
  },
  mounted() {
    // 编辑住户
    if (this.$route.query.roomId) {
      this.roomId = this.$route.query.roomId
    }
    if (this.$route.query.buildingMemberId) {
      this.buildingMemberId = this.$route.query.buildingMemberId
    }
    this.initMemberInfo()
  },
  computed: {
    ...mapState('householdManagement', ['buildingInfo', 'roomInfo', 'memberInfo', 'memberList']),
    // 新增计算属性
    isEditMode() {
      return !!this.buildingMemberId
    }
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_MEMBER_INFO', 'SET_MEMBER_LIST']),
    back() {
      this.SET_MEMBER_INFO({})
      this.$router.back()
    },
    // 初始化当前住户字段
    async initMemberInfo() {
      this.curMemberInfo = {
        ...this.roomInfo,
        ...this.memberInfo
      }
      this.fileInfo = { ...(this.memberInfo?.fileInfo || {}) }
      if (this.isEditMode) {
        const { data } = await getFaceImage({
          id: this.buildingMemberId
        })
        this.facialImage = data
      } else {
        this.facialImage = this.curMemberInfo.facialImage
      }
    },

    // 打开姓名编辑弹窗
    showNameDialog() {
      this.tempMemberName = this.curMemberInfo.memberName
      this.$refs.nameDialog.show = true
    },
    confirmName(value) {
      this.$refs.nameDialog.show = false
      this.curMemberInfo.memberName = value
      this.tempMemberName = ''
    },
    // 保存当前编辑后的住户信息
    saveMemberInfo() {
      this.SET_MEMBER_INFO({
        ...this.memberInfo,
        memberName: this.curMemberInfo.memberName,
        facialImage: this.facialImage,
        fileInfo: { ...this.fileInfo }
      })
    },
    // 打开住户账号编辑弹窗
    editMemberAccount() {
      this.saveMemberInfo()
      this.$router.push({
        path: '/household/addRoomMember',
        query: {
          id: this.roomId
        }
      })
    },
    // 打开人脸编辑弹窗
    takeFacePhoto() {
      return new Promise((resolve, reject) => {
        appReqeustNative(NATIVE_BRIDGE_CONFIG, res => {
          try {
            if (!res) {
              reject(new Error('未收到响应数据'))
              return
            }
            const data = JSON.parse(res)
            if (!data || typeof data.code === 'undefined') {
              reject(new Error('响应数据格式错误'))
              return
            }
            const code = Number(data.code)
            if (code === 200) {
              if (!data.body) {
                reject(new Error('未收到图片数据'))
                return
              }
              const faceImg = `data:image/jpg;base64,${data.body}`
              resolve(faceImg)
            } else if (code === 101) {
              this.$toast({
                message: this.$t('personFace.sizeTip'),
                position: 'middle'
              })
              reject(new Error(this.$t('personFace.sizeTip')))
            } else {
              reject(new Error(data.msg || '拍照失败'))
            }
          } catch (error) {
            reject(new Error('数据解析失败: ' + error.message))
          }
        })
      })
    },
    // 编辑人脸
    async editTargetFace() {
      this.saveMemberInfo()
      try {
        // 打开人脸录入页面拍摄
        const faceImg = await this.takeFacePhoto()
        if (faceImg) {
          // 将base64转换为文件并上传
          this.uploadFacialImage(faceImg)
        }
      } catch (error) {
        console.error('拍照失败：', error)
        this.$toast.fail(this.$t('uploadFail'))
      }
    },
    // 优化后的上传策略
    async uploadFacialImage(base64Str) {
      try {
        this.$loading.show({
          message: this.$t('uploading'),
          forbidClick: true
        })

        const blob = base64ToBlob(base64Str)
        const faceFile = blob
        const faceFileName = generateFaceFileName()

        // 计算文件Hash
        this.fileInfo.facialImageHash = await this.calculateFileHash(faceFile)
        this.fileInfo.faceFile = faceFile

        // 获取上传凭证
        const params = { businessType: BUSINESS_TYPE, businessId: this.curMemberInfo?.id }
        const { data: ossData } = await getOssAccess(params)
        const { storageInfo, storageType } = ossData
        if (!storageInfo) {
          throw new Error('未获取到存储配置信息')
        }
        let uploadToken
        try {
          uploadToken = JSON.parse(storageInfo)
        } catch (error) {
          throw new Error('存储配置信息格式错误')
        }
        if (!uploadToken.objectNamePrefix) {
          throw new Error('上传配置缺少对象名称前缀')
        }
        if (!this.buildingInfo?.userId) {
          throw new Error('缺少用户ID信息')
        }
        const objectNamePrefix = uploadToken.objectNamePrefix
        const faceFileUrl = `${objectNamePrefix}${this.buildingInfo.userId}/${faceFileName}`
        uploadToken.faceFileUrl = faceFileUrl
        this.fileInfo.faceFileUrl = faceFileUrl

        // 根据存储类型和文件大小选择上传策略
        if (storageType === 'ALI_OSS') {
          await this.smartOssUpload(uploadToken, faceFile)
        } else {
          await this.smartAwsUpload(uploadToken, faceFile)
        }

        this.facialImage = base64Str
        this.$toast.success(this.$t('uploadSuccess'))
      } catch (error) {
        this.$toast.fail(this.$t('uploadFail'))
        console.error('上传人脸图片失败：', error)
      } finally {
        this.$loading.hide()
      }
    },
    // 创建上传进度回调函数
    createProgressCallback() {
      return percentage => {
        this.loading?.setText(
          this.$t('uploadProgress', {
            percent: (percentage * 100).toFixed(2)
          })
        )
      }
    },
    // 智能选择阿里云上传方式 - 使用工具函数
    async smartOssUpload(uploadToken, file) {
      return ossSmartUpload(uploadToken, file, uploadToken.faceFileUrl, this.createProgressCallback())
    },
    // 智能选择AWS上传方式 - 使用工具函数
    async smartAwsUpload(uploadToken, file) {
      return awsSmartUpload(uploadToken, file, uploadToken.faceFileUrl, this.createProgressCallback())
    },
    // 工具方法：清理空参数
    cleanEmptyParams(params) {
      const cleanedParams = { ...params }
      for (let key in cleanedParams) {
        if (!cleanedParams[key]) delete cleanedParams[key]
      }
      return cleanedParams
    },
    // 工具方法：文件大小验证
    validateFileSize(file) {
      if (file.size > MAX_FILE_SIZE) {
        this.$toast.fail(this.$t('fileTooLarge'))
        return false
      }
      return true
    },
    // 工具方法：计算文件Hash
    calculateFileHash(file) {
      return new Promise(resolve => {
        const reader = new FileReader()
        reader.onload = e => {
          resolve(MD5(e.target.result))
        }
        reader.readAsArrayBuffer(file)
      })
    },

    async deleteTargetFace() {
      try {
        await this.$dialog.confirm({
          title: this.$t('tips'),
          message: this.$t('deleteConfirm'),
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel')
        })
        // 用户点击确认后执行删除操作
        this.facialImage = null
        this.$toast.success(this.$t('deleteSuccess'))
      } catch {
        // 用户点击取消，不做任何操作
      }
    },
    // 编辑门禁卡
    editDoorCard() {
      this.saveMemberInfo()
      // 处理编辑门禁卡的逻辑
      this.$router.push({
        path: '/household/addDoorCard'
      })
    },
    // 表单验证
    validateForm() {
      if (!this.curMemberInfo.memberName) {
        this.$toast(this.$t('enterMemberName'))
        return false
      }
      if (!this.curMemberInfo.memberContact) {
        this.$toast(this.$t('enterMemberAccount'))
        return false
      }
      return true
    },

    // 构建编辑住户参数
    buildUpdateMemberParams() {
      const { buildingMemberId, memberName, memberContact, cardNo = '' } = this.curMemberInfo
      let params = {
        buildingMemberId,
        memberName,
        memberContact,
        facialImage: this.fileInfo?.faceFileUrl,
        facialImageHash: this.fileInfo?.facialImageHash,
        cardNo
      }

      params = this.cleanEmptyParams(params)

      if (!this.facialImage) {
        // 表示删除人脸
        params.facialImage = ''
        params.facialImageHash = ''
      }

      return params
    },

    // 构建新增住户参数
    buildAddMemberParams() {
      const { id: buildingId } = this.buildingInfo
      const { roomId } = this.roomInfo
      const { memberName, memberContact, cardNo = '' } = this.curMemberInfo

      let params = {
        buildingId,
        roomId,
        memberName,
        memberContact,
        facialImage: this.fileInfo?.faceFileUrl,
        facialImageHash: this.fileInfo?.facialImageHash,
        cardNo
      }

      return this.cleanEmptyParams(params)
    },

    // 更新住户
    async updateMember() {
      const params = this.buildUpdateMemberParams()
      await updateRoomMember(params)
      this.$toast(this.$t('editSuccess'))
    },

    // 新增住户
    async addMember() {
      const params = this.buildAddMemberParams()
      await addRoomMembers(params)
      this.$toast(this.$t('addSuccess'))
    },

    // 处理提交错误
    handleSubmitError(error) {
      console.error(error)
      if (error?.basic?.code) {
        this.$toast.fail(this.$t(`errorCode.${error.basic.code}`))
      } else {
        this.$toast.fail(this.isEditMode ? this.$t('editFail') : this.$t('addFail'))
      }
    },

    // 主提交方法
    async handleConfirm() {
      if (!this.validateForm()) {
        return
      }

      this.$loading.show()
      try {
        const { buildingMemberId } = this.curMemberInfo
        const { roomId } = this.roomInfo

        if (buildingMemberId) {
          await this.updateMember()
        } else if (roomId) {
          await this.addMember()
        }

        this.back()
      } catch (error) {
        this.handleSubmitError(error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  overflow: auto;
  padding-bottom: 80px;
  box-sizing: border-box;

  .household-content {
    padding-top: 10px;

    .section-title {
      padding: 12px 16px;
      font-size: 14px;
    }
  }

  .household-item {
    padding: 12px 16px;

    .household-title {
      font-size: 14px;
    }

    .required-icon {
      color: $vms-red;
      margin-right: 4px;
    }

    .right-value {
      display: inline-block;
      width: 100%;
    }

    .arrow-img {
      width: 16px;
      height: 16px;
    }
  }
  .face-content {
    padding: 0 16px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .face-image {
      width: 80px;
      height: 108px;
      border-radius: 4px;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .face-actions {
      width: 80px;
      height: 24px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .action-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
      }
      .divider-line {
        width: 1px;
        height: 24px;
        background: #ebebeb;
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .footer-btn {
      width: 296px;
      border-radius: 23px;
      line-height: 46px;
      text-align: center;
    }
  }
}
.dialog-input {
  padding: 20px 16px;
}
</style>
