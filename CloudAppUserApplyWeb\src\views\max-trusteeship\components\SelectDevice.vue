<template>
  <div class="device-camera-wrapper">
    <div class="choose-device-content">
      <van-collapse v-model="activeNames">
        <!-- 一级：设备 -->
        <van-collapse-item
          v-for="(item, index) of deviceList"
          :title="item.devName"
          :name="item.sn"
          :key="`${item.sn}~${index}`"
        >
          <!-- 一级：设备左侧勾选 -->
          <template #title>
            <div class="device-collapse-box">
              <van-checkbox
                v-if="['add', 'check'].includes(type)"
                :name="`${item.sn}~device`"
                :ref="`${item.sn}~device`"
                v-model="deviceChecked[item.sn]"
                label-disabled
                @click.native="e => toggleDevice(e, item.sn, item)"
              ></van-checkbox>
              <div class="device-icon">
                <theme-image alt="device" imageName="nvr.png" />
              </div>
              <div class="device-collapse-title">
                {{ item.devName }}
              </div>
            </div>
          </template>
          <!-- 二级：权限和时长 -->
          <van-cell-group>
            <van-cell>
              <template #title>
                <div class="channel-collapse-box">
                  <div class="channel-collapse-title">
                    <div class="channel-title">{{ $t('trustPermission') }}</div>
                    <div class="channel-capability" v-if="deviceObj[item.sn] && deviceObj[item.sn].checkCapability">
                      <span
                        v-for="(item2, index2) of deviceObj[item.sn].checkCapability"
                        :key="item2.value"
                        class="channel-capability-item"
                      >
                        <span>{{ item2.label }}</span>
                        <span
                          class="separator-box"
                          v-if="index2 < deviceObj[item.sn].checkCapability.length - 1"
                        ></span>
                      </span>
                    </div>
                  </div>
                </div>
              </template>
              <template #right-icon v-if="['add', 'edit'].includes(type)">
                <div class="channel-more-box" @click="e => handleCapability(e, item)">
                  <theme-image class="more-icon" alt="more" imageName="more.png" />
                </div>
              </template>
            </van-cell>
            <van-cell>
              <template #title>
                <div class="channel-collapse-box">
                  <div class="channel-collapse-title">
                    <div class="channel-title">{{ $t('trustTime') }}</div>
                    <div class="channel-capability">
                      <span class="channel-capability-item">{{ getTimeLable(deviceObj[item.sn].effectiveTime) }}</span>
                    </div>
                  </div>
                </div>
              </template>
              <template #right-icon v-if="['add', 'edit'].includes(type)">
                <div class="channel-more-box" @click="e => handleTime(e, item)">
                  <theme-image class="more-icon" alt="more" imageName="more.png" />
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </van-collapse-item>
      </van-collapse>
    </div>
    <van-popup v-model="showCapabilityOptions" round position="bottom">
      <choose-capability
        :title="$t('permissionSetting')"
        v-model="capabilitys"
        :options="capabilityOptions"
        @cancel="handleCancel"
        @confirm="handleConfirm"
      />
    </van-popup>
    <van-popup v-model="showTimeOptions" round position="bottom">
      <choose-time
        v-model="effectiveTime"
        :options="timeOptions"
        @cancel="handleCancelTime"
        @confirm="handleConfirmTime"
      />
    </van-popup>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { DEVICE_CAPABILITY_LIST, TRUSTEESHIP_VALIDITY_LIST_FULLNAME } from '@/utils/options'
import ChooseCapability from '@/views/share/components/ChooseCapability.vue'
import ChooseTime from './ChooseTime.vue'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'SelectDevice',
  components: {
    ChooseCapability,
    ChooseTime,
    ThemeImage
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      // add表示新增（勾选和权限编辑）detail表示查看
      default: () => 'add'
    },
    deviceList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeNames: [], // 打开的折叠面板
      deviceChecked: {}, // 设备是否勾选
      deviceObj: {}, // 设备的对象--包含勾选的能力集等信息
      showCapabilityOptions: false, // 是否展示权限勾选弹窗
      record: {}, // 当前操作权限的设备
      capabilityOptions: DEVICE_CAPABILITY_LIST(), // 设备权限选项
      timeOptions: TRUSTEESHIP_VALIDITY_LIST_FULLNAME(), // 设备时间选项
      capabilitys: [], // 某个设备勾选的权限列表
      showTimeOptions: false, // 是否展示时间勾选弹窗
      effectiveTime: null // 某个设备勾选的时间
    }
  },
  created() {
    // 首次进入清除选中的设备
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  watch: {
    deviceList: {
      handler(val) {
        if (val && val.length) {
          // 遍历设备树
          const deviceObj = {}
          val.forEach(item => {
            const { sn, checkCapability = [], effectiveTime } = item
            deviceObj[sn] = {
              ...item,
              checkCapability: checkCapability.slice(),
              effectiveTime
            }
          })
          this.deviceObj = deviceObj
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取时长对应的名称
    getTimeLable(val) {
      const item = this.timeOptions.find(item => item.value === val)
      if (item) {
        return item.label
      } else {
        return null
      }
    },
    // 全部勾选
    selectAll(flag) {
      // 勾选所有的设备
      const deviceChecked = {}
      this.deviceList.forEach(item => {
        const { sn } = item
        // console.log('item', item, this.$refs[key])
        // 触发设备勾选
        deviceChecked[sn] = flag
      })
      this.deviceChecked = deviceChecked
      this.$nextTick(() => {
        // 将选中的设备传递出去
        this.emitCheckDevice()
      })
    },
    // 设备勾选
    toggleDevice(e, key) {
      e.stopPropagation()
      // console.log('flag', flag, 'key', key)
      // 判断是否全选
      if (this.deviceChecked[key]) {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: false
        }
      } else {
        this.deviceChecked = {
          ...this.deviceChecked,
          [key]: true
        }
      }
      this.$nextTick(() => {
        // 将选中的设备传递出去
        this.emitCheckDevice()
      })
    },
    // 根据deviceChecked获取选择的设备
    emitCheckDevice() {
      const deviceKeyList = Object.keys(this.deviceChecked).filter(key => this.deviceChecked[key])
      // 把添加的设备传入store
      const addDeviceList = deviceKeyList.map(key => ({ ...this.deviceObj[key] }))
      // 发送input更新v-model
      this.$emit('input', addDeviceList)
      // 发送select通知外部勾选变化
      this.$emit('select', addDeviceList)
    },
    // 编辑权限
    handleCapability(e, record) {
      e.stopPropagation() // 阻止冒泡
      this.showCapabilityOptions = true
      const { sn } = record
      const { checkCapability = [] } = this.deviceObj[sn]
      this.record = { ...record, checkCapability }
      this.capabilitys = checkCapability.map(item => item.value)
    },
    handleCancel() {
      this.showCapabilityOptions = false
      this.capabilitys = []
      this.record = {}
    },
    handleConfirm() {
      this.showCapabilityOptions = false
      const { sn } = this.record
      this.deviceObj = {
        ...this.deviceObj,
        [sn]: {
          ...this.deviceObj[sn],
          checkCapability: this.capabilityOptions.filter(item => this.capabilitys.includes(item.value))
        }
      }
      if (this.type === 'edit') {
        // 编辑权限时将当前记录传递出去，方便外层更新数据
        this.$emit('change', {
          ...this.record,
          checkCapability: this.capabilityOptions.filter(item => this.capabilitys.includes(item.value))
        })
      }
      this.capabilitys = []
      this.record = {}
      this.$nextTick(() => {
        // 将选中的通道传递出去
        this.emitCheckDevice()
      })
    },
    // 编辑时长
    handleTime(e, record) {
      e.stopPropagation() // 阻止冒泡
      this.showTimeOptions = true
      const { sn } = record
      const { effectiveTime } = this.deviceObj[sn]
      this.record = { ...record, effectiveTime }
      this.effectiveTime = Number(effectiveTime)
    },
    handleCancelTime() {
      this.showTimeOptions = false
      this.effectiveTime = null
      this.record = {}
    },
    handleConfirmTime() {
      this.showTimeOptions = false
      const { sn } = this.record
      this.deviceObj = {
        ...this.deviceObj,
        [sn]: {
          ...this.deviceObj[sn],
          effectiveTime: this.effectiveTime
        }
      }
      this.effectiveTime = null
      this.record = {}
      this.$nextTick(() => {
        // 将选中的设备传递出去
        this.emitCheckDevice()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.device-camera-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
  .device-collapse-box {
    width: calc(100vw - 50px);
    display: inline-flex;
    align-items: center;
    overflow: hidden;
    .device-icon {
      width: 24px;
      height: 24px;
      margin: 0px 6px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .device-collapse-title {
      width: 100%;
      font-size: var(--font-size-body1-size, 16px);
      padding: 0px 5px;
      overflow: hidden;
      box-sizing: border-box;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .channel-collapse-box {
    width: 100%;
    display: inline-flex;
    align-items: center;
    padding-left: 20px;
    box-sizing: border-box;
    .channel-icon {
      width: 24px;
      height: 24px;
      margin: 0px 6px;
    }
    .channel-collapse-title {
      flex: 1;
      font-size: var(--font-size-body2-size, 14px);
      .channel-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: var(--font-size-body2-size, 14px);
        line-height: 22px;
      }
      .channel-capability {
        width: 100%;
        overflow: hidden;
        font-size: var(--font-size-text-size, 12px);
        line-height: 18px;
      }
      .channel-capability-item {
        display: inline-flex;
        align-items: center;
      }
      .separator-box {
        display: inline-block;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        margin: 0px 4px;
      }
    }
  }
  .channel-more-box {
    width: 24px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    .more-icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
<style lang="scss">
.device-camera-wrapper {
  .van-collapse-item__content {
    padding: 2px 8px !important;
  }
  .van-cell {
    height: 60px;
    padding: 10px;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
  }
  .van-cell__value {
    right: -15px;
    width: min-content;
    display: flex;
    justify-content: flex-end;
    .van-checkbox {
      justify-content: flex-end;
    }
  }
  .van-checkbox__icon {
    font-size: var(--font-size-body1-size, 16px);
    width: 20px;
  }
  .van-hairline--top-bottom::after {
    border-width: 0px;
  }
}
</style>
