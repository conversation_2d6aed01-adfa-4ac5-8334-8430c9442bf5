import i18n from '@/lang'

export default {
  namespaced: true,
  state: () => ({
    chlCapability: {}, // 通道能力集
    initStatus: false, // 是否第一次进入
    devType: '2', // 设备类型,1：IPC 2：NVR 3：DVR 4：依图套装 5：TVT套装 7：依图NVR 8：NVMS 9：卡片机 10：门铃 11：太阳能IPC
    devId: null,
    chlId: null, // 巡航线的chlId
    cruiseIndex: null, // 当前巡航线的id，编辑模式下才有
    pointList: [], // 当前通道下所有预置点列表
    cruisesList: [], // 当前通道下所有巡航线列表
    curiseRecord: {
      name: '',
      index: null,
      presetPointList: [
        // { pointName: '3', point: 3, speed: 5, holdTime: '5s' },
        // { pointName: '4', point: 4, speed: 2, holdTime: '10s' },
        // { pointName: '5', point: 5, speed: 3, holdTime: '5m' },
        // { pointName: '6', point: 6, speed: 1, holdTime: '10m' }
      ] // 当前巡航线下面的所有预置点
    }, // 记录新增/编辑的预置点信息
    // 新增/编辑的预置点
    pointRecord: {
      // name: '3',
      // index: 3,
      // speed: 5,
      // holdTime: 5
    },
    // 速度 1-8
    speedList: [
      { label: 1, value: 1 },
      { label: 2, value: 2 },
      { label: 3, value: 3 },
      { label: 4, value: 4 },
      { label: 5, value: 5 },
      { label: 6, value: 6 },
      { label: 7, value: 7 },
      { label: 8, value: 8 }
    ],
    // 所有预置点持续时间，包含NVR和IPC的持续时间并集
    allHoldTimeList: [
      { label: i18n.t('manySecond', [5]), value: 5 },
      { label: i18n.t('manySecond', [10]), value: 10 },
      { label: i18n.t('manySecond', [15]), value: 15 },
      { label: i18n.t('manySecond', [20]), value: 20 },
      { label: i18n.t('manySecond', [25]), value: 25 },
      { label: i18n.t('manySecond', [30]), value: 30 },
      { label: i18n.t('manySecond', [60]), value: 60 },
      { label: i18n.t('manyMinutes', [2]), value: 120 },
      { label: i18n.t('manyMinutes', [3]), value: 180 },
      { label: i18n.t('manyMinutes', [4]), value: 240 }
    ],
    // NVR预置点持续时间 1-8个档 下拉框选项 5秒、10秒、15秒、20秒、25秒、30秒、1分、2分、3分
    holdTimeList: [
      { label: i18n.t('manySecond', [5]), value: 5 },
      { label: i18n.t('manySecond', [10]), value: 10 },
      { label: i18n.t('manySecond', [15]), value: 15 },
      { label: i18n.t('manySecond', [20]), value: 20 },
      { label: i18n.t('manySecond', [25]), value: 25 },
      { label: i18n.t('manySecond', [30]), value: 30 },
      { label: i18n.t('oneMinute'), value: 60 },
      { label: i18n.t('manyMinutes', [2]), value: 120 },
      { label: i18n.t('manyMinutes', [3]), value: 180 }
    ],
    // IPC预置点持续时间 1-8个档 下拉框选项 5秒、10秒、15秒、20秒、25秒、30秒、60秒、2分、3分、4分
    ipcHoldTimeList: [
      { label: i18n.t('manySecond', [5]), value: 5 },
      { label: i18n.t('manySecond', [10]), value: 10 },
      { label: i18n.t('manySecond', [15]), value: 15 },
      { label: i18n.t('manySecond', [20]), value: 20 },
      { label: i18n.t('manySecond', [25]), value: 25 },
      { label: i18n.t('manySecond', [30]), value: 30 },
      { label: i18n.t('manySecond', [60]), value: 60 },
      { label: i18n.t('manyMinutes', [2]), value: 120 },
      { label: i18n.t('manyMinutes', [3]), value: 180 },
      { label: i18n.t('manyMinutes', [4]), value: 240 }
    ]
  }),
  getters: {},
  mutations: {
    SET_INIT_STATUS(state, data) {
      state.initStatus = data
    },
    SET_DEV_TYPE(state, data) {
      state.devType = data
    },
    SET_DEVID(state, data) {
      state.devId = data
    },
    SET_CHL_CAPABILITY(state, data) {
      state.chlCapability = data
    },
    SET_CRUISES_INDEX(state, data) {
      state.cruiseIndex = data
    },
    SET_CHLID(state, data) {
      state.chlId = data
    },
    SET_POINT_LIST(state, data) {
      state.pointList = data
    },
    SET_CRUISES_LIST(state, data) {
      state.cruisesList = data
    },
    SET_CRUISE_RECORD(state, data) {
      state.curiseRecord = data
    },
    SET_POINT_RECORD(state, data) {
      state.pointRecord = data
    },
    SET_HOLD_TIME_LIST(state, data) {
      state.holdTimeList = data
    },
    SET_IPC_HOLD_TIME_LIST(state, data) {
      state.ipcHoldTimeList = data
    }
  },
  actions: {}
}
