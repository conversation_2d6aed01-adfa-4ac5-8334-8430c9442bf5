export default {
  upgrade: 'Cloud Upgrade',
  cancel: 'Cancel',
  confirm: 'Confirm',
  deviceUpdate: 'Device',
  cameraUpdate: 'Camera',
  allUpdate: 'Upgrade All',
  updateNow: 'Upgrade',
  currentVersion: 'Current Version',
  latestVersion: 'Latest Version',
  updateContent: 'Update Content',
  hasLatestVersion: 'Running the latest version',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Download Waiting',
  inprogress: 'Downloading',
  downloadFail: 'Download failed',
  downloadFinished: 'Download completed',
  inupgrade: 'Upgrading',
  upgradeFail: 'Upgrade failed',
  upgradeSuccess: 'Upgrade successful',
  deviceUpgradeInfo: 'During the upgrade the device will be disconnected and automatically restarted. Please confirm',
  upgradeTip:
    'After the download is complete, the device will automatically upgrade. During the upgrade process, the device will automatically restart. Please do not manually restart the device or disconnect the power supply until the automatic restart is complete.',
  cameraUpgradeInfo: 'During the upgrade the camera will be disconnected and automatically restarted. Please confirm.',
  pwdUserNameError: 'Username or password error',
  permissionAuth: 'admin authority authentication',
  pleaseEnterUser: 'Please enter username',
  pleaseEnterPwd: 'Please enter password',
  noCameraUpgrade: 'No upgradeable cameras detected',
  handleCheck: 'Check for updates',
  paySuccess: 'Payment successful',
  payFail: 'Payment failed',
  done: 'Complete',
  rePurchase: 'Repurchase',
  cloudStorage: 'Cloud Storage',
  INSTRUMENT_DECLINED: 'Transaction exceeds card limit',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Payer account cannot be used for this transaction',
  PAYER_ACCOUNT_RESTRICTED: 'Payer account is restricted',
  TRANSACTION_LIMIT_EXCEEDED: 'The total payment amount exceeds the transaction limit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'The transaction exceeds the recipient receiving limit',
  myInstaller: 'My installer',
  trusteeshipDevice: 'Managed device',
  addTrusteeship: 'Share with installer',
  waitReceived: 'To be received',
  received: 'Received',
  refuse: 'Refuse',
  delete: 'Delete',
  operationSuccess: 'Operation successful',
  operationFail: 'Operation failed',
  cancelTrusteeship: 'Cancel sharing with installer',
  chooseDevice: 'Select device',
  noAvaiableDevice: 'Showing devices supporting installer sharing and bound to your account.',
  leastChoose: 'Select at least one device',
  details: 'Details',
  live: 'Live',
  rec: 'Playback',
  config: 'Configuration',
  confirmTrusteeshipTip:
    'The sharing request has been sent to the installer. Please wait for the installer to process it',
  cancelTrusteeshipTip:
    'By canceling sharing with the installer, all permissions required for remote maintenance services will be revoked. Please confirm.',
  unBindTrusteeship: 'After unbinding all device sharing will be cancelled. Please confirm',
  trusteeshipPermissions: 'Sharing permissions',
  trusteeshipTime: 'Sharing duration',
  unBind: 'Unbinding',
  serviceException: 'Service exception',
  pullingText: 'Pull down to refresh...',
  loosingText: 'Release to refresh...',
  loosing: 'Refreshing...',
  loadingText: 'Loading...',
  refreshComplete: 'Refresh successfully',
  noMore: 'No more',
  checkSuccess: 'Checked successfully',
  checkFail: 'Check failed',
  viewUpdateContent: 'View update content',
  deviceDisconnected: 'Failed to connect to device',
  updateNote: 'Update note',
  noData: 'No Data',
  tips: 'Tips',
  password: 'Password',
  pwdError: 'Login error. You can try {0} more times',
  pwdErrorLock: 'The devcie is locked due to repeating errors. Please try again later',
  noPermissions: 'No permissions',
  permission: 'Permission',
  validity: 'Validity',
  permissionValidity: 'Permission validity',
  isSaveModify: 'Confirm saving the changes',
  manyMinutes: '{0}min',
  manyHours: '{0}h',
  manyDays: '{0}d',
  manyMinutesEn: '{0} minutes', // 缺少翻译
  manyHoursEn: '{0} hours', // 缺少翻译
  manyDaysEn: '{0} days', // 缺少翻译
  oneWeek: '1 week',
  forever: 'Forever',
  expired: 'Expired',
  residue: 'Residue',
  transferRequest: 'Transfer request',
  acceptTransfer: 'Accept',
  refuseTransferConfirm: 'Confirm declining the transfter',
  bindInstallerText: 'You can host devices after binding the Installer({account}). Bind now?',
  bindSuccess: 'Binding successful',
  acceptSuccess: 'Accepted successfully',
  from: 'From',
  lineAuthBind: 'line Authority Binding',
  bindFail: 'Binding failed',
  binding: 'Binding',
  householdManagement: 'Buildings & Residents',
  addBuilding: 'Add building',
  buildingName: 'Building Name',
  enterBuildingName: 'Please enter the building name',
  buildingNum: 'Building Number',
  enterBuildingNum: 'Please enter the building number',
  relateDevice: 'Associated devices',
  roomNum: 'Room quantity',
  room: 'Room',
  addRoom: 'Add Room',
  roomName: 'Room Number',
  enterRoomName: 'Please enter the room number',
  household: 'Residents',
  addRoomMember: 'Add Residents',
  changeSuccessfully: 'Successfully modified',
  email: 'Email',
  enterMemberEmail: 'Please enter email address',
  mobile: 'Phone',
  enterMemberMobile: 'Please enter phone number',
  emailNameError: 'Incorrect email format',
  mobileError: 'Incorrect phone number format',
  emailNameNotEmpty: 'The email cannot be empty',
  mobileNotEmpty: 'The phone number cannot be empty',
  memberInMax: 'The quantity of the residents has reached the limit.',
  memberMobileRepeate: 'This phone number already exists in this room.',
  emailRepeate: 'This email already exists in this room.',
  supportDash: 'Only support -, _, and blank spaces as special characters.',
  errorCode: {
    400: 'Parameter error',
    404: 'The requested resource does not exist',
    500: 'System exception',
    502: 'Server request failed',
    503: 'Server exception',
    504: 'Server request timeout',
    550: 'Request timeout',
    1000: 'Parameter error',
    1005: 'Image verification code error',
    1007: 'Picture verification code is required',
    1008: 'The verification code has expired',
    1009: 'Verification code error',
    1011: 'The parameter is not filled in correctly',
    1012: 'API not recognized',
    1013: 'The verification code could not be sent.',
    1015: 'The user already exists',
    1027: 'Please enter the correct device serial number/security code',
    1028: 'The camera is already enabled or disabled',
    4500: 'Parameter error',
    5000: 'You do not have permission to perform this operation',
    5001: 'The current user has no permission',
    6000: 'The current business status does not support this operation',
    6001: 'Too frequent operation',
    7000: 'Parameter error',
    7001: 'The user does not exist',
    7002: 'Old password error',
    7003: 'Token Error',
    7004: 'Your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7005: 'Invalid signature',
    7006: 'Mobile number already exists',
    7007: 'The user is locked. Please contact the administrator to unlock',
    7009: 'Your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7010: 'The administrator account is not activated',
    7011: 'Account not activated',
    7019: 'The username already exists',
    7021: 'Deletion failed. Please clear all hosts under this host group first',
    7023: 'The mailbox has been bound',
    7028: 'The template has been used in the project and cannot be deleted',
    7029: 'The template name already exists',
    7030: 'The data already exists',
    7032: 'The firmware package already exists',
    7034: 'The firmware package has been released and cannot be deleted',
    7040: 'Device does not exist or not online',
    7042: 'There are other tasks in the startup state',
    7043: 'The task has not been approved!',
    7044: 'Operation failed. There are no devices eligible for upgrade',
    7045: 'The task is not approved',
    7056: 'This version is already in the compatibility management and cannot be deleted',
    7057: 'Issuing document cannot be blank',
    7061: 'Crrection already exists',
    7065: 'The channel has already been shared',
    7066: 'The customer code already exists',
    7068: 'The customer code does not exist',
    7069: 'Too much data. Please narrow the scope and search again',
    7072: 'The device already exists',
    7081: 'Import failed',
    7082: 'Export failed',
    7084: 'The customer country code already exists',
    7086: 'Operation failed. System Exception',
    7087: 'The product already exists',
    7088: 'Your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7090: 'Your account has been logged out due to a long period of inactivity or logging in to other devices. Please log in again',
    7093: 'Image and text information is not configured',
    7094: 'The service terms information does not exist',
    9000: 'System exception',
    9001: 'The protocol version is too low. The old version is no longer compatible and needs to be upgraded.',
    9002: 'Protocol version error.',
    9003: 'Failed to sent verification code',
    9004: 'Database operation failed',
    9005: 'The data does not exist',
    9006: 'The data already exists',
    9007: 'The data to be viewed does not exist',
    9008: 'The data does not exist',
    9009: 'Data exception',
    9500: 'System exception',
    10000: 'Failed to connect device',
    10001: 'System exception',
    12344: 'Network connection failed',
    12345: 'Network connection timeout',
    20021: 'This email has already been used',
    20024: 'The account has already been activated',
    20030: 'The link has expired',
    23024: 'The payment card provided has expired',
    23025: 'The transaction has been rejected due to violation',
    32018: 'The data doesnt exist.',
    32019: 'Operation failed',
    32021: 'Data does not exist',
    32022: 'Hosting service is not supported because {0} device and installer are not in the same country/region.',
    33001: 'No permission to operate this device',
    33002: 'No permission to operate this site',
    33003: 'The site does not exist',
    33004: 'The length of the device name must be between 0 and 32',
    33010: 'The device already exists',
    34001: 'This account has been bound.',
    34003: 'The state information is incorrect',
    34004: 'Authorization failed.',
    34005: 'Operation Failed: The authorization has expired. Please obtain it again.',
    34006: 'Device transfer does not exist',
    34007: 'Can only accept transfers from same user',
    34021: 'The quantity of the building has reached the upper limit.',
    34022: 'The building name already exists',
    34023: 'The Building number already exists',
    34024: 'Deleting failed. Please first delete the rooms and devices in this building.',
    34025: 'Association failed. This device has been associated to another building.',
    34026: 'The quantity of the devices in this building has reached the limit.',
    34027: 'Operation failed. This building has been deleted.',
    34028: 'This room number already exists.',
    34029: 'The quantity of the rooms in this building has reached the limit.',
    34030: 'The quantity of the residents in this room has reached the limit.',
    34031: 'Operation failed. This room has been deleted.',
    34033: 'Deletion failed. Please first delete the residents in this room.',
    34035: 'The phone number already exists in this room.',
    34036: 'This email already exists in this room.',
    34037: 'Operation failed. This resident has been deleted.',
    536870934: 'Operation failure. Please check the device status',
    536870940: 'Operation failure. Please check the device status',
    536870943: 'Invalid parameter',
    536870945: 'Operation failure. Please check the device status',
    536870947: 'Username does not exist',
    536870948: 'Username or password error',
    536871017: 'Operation failure. Please check the device status',
    536871039: 'Invalid parameter',
    536871060: 'Operation failed. Please check the device status',
    536871082: 'Operation failure. Please check the device status',
    536871083: 'Operation failure. Please check the device status',

    // ipc 云升级错误码
    ipc: {
      499: 'Unknown error', //未知错误
      612: 'Operation failure. Please check the device status',
      730: 'Operation failure. Please check the device status',
      731: 'Operation failure. Please check the device status',
      732: 'Operation failure. Please check the device status',
      735: 'Operation failure. Please check the device status'
    }
  }
}
