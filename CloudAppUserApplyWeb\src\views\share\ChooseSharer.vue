<template>
  <div class="choose-share-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="choose-share-content">
      <div class="share-form-box">
        <van-tabs v-model="active">
          <van-tab :title="$t('email')" name="email">
            <email-input ref="emailRef" v-model="email" @handleScan="handleScan" />
          </van-tab>
          <van-tab :title="$t('mobileNum')" name="mobile">
            <mobile-input ref="mobileRef" v-model="mobile" @handleScan="handleScan" />
          </van-tab>
        </van-tabs>
      </div>
      <div class="share-history-box" v-if="historyShareList.length">
        <div class="share-history-title">
          {{ $t('historyShare') }}
        </div>
        <div class="share-history-list">
          <tvt-better-scroll
            class="tvt-better-scroll"
            @pullingUp="pullingUp"
            @pullingDown="pullingDown"
            :pullingStatus="pullingStatus"
          >
            <div
              class="share-history-item"
              v-for="item in historyShareList"
              :key="item.id"
              @click="handleHistotyShare(item)"
            >
              {{ item.shareName }}
            </div>
          </tvt-better-scroll>
        </div>
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" :disabled="btnDisabled" @click="handleClick">
        {{ $t('nextStep') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import EmailInput from './components/EmailInput.vue'
import MobileInput from './components/MobileInput.vue'
import { appLog, appClose, appOpenScan, getCacheData } from '@/utils/appbridge'
import { dateFormat } from '@/utils/common.js'
import { validateEmail, validateCommonPhone } from '@/utils/validate'
import { MOBILE_COUNTRY_CODE } from 'tvtcloudcountrycode'
import { mapState, mapMutations } from 'vuex'
import { decrypt } from '@/utils/encrypt.js'
import {
  getUserInfo,
  // getEmailExist,
  // getPhoneExist,
  getUserExist
} from '@/api/share'

export default {
  name: 'ChooseSharer',
  components: {
    NavBar,
    EmailInput,
    MobileInput
  },
  props: {},
  data() {
    return {
      dataList: [],
      pullingStatus: 0,
      historyShareList: [],
      active: 'email',
      email: '',
      mobile: '',
      generateAESKey: '+GYLe29QjUFVsYZc/QUEC6g2P/61PjC312jwGvZw26o='
    }
  },
  created() {
    // 获取历史分享列表
    appLog('log/info', `${new Date()} H5页面初始化，请求历史分享记录`)
    this.getHistoryShareList()
    // 从路由中获取设备sn和通道chlIndex,用作默认勾选
    // console.log('this.$route', this.$route)
    const query = this.$route.query
    // console.log('query', query)
    if (query.sn) {
      const { sn, chlIndex } = query
      // 设置默认选中的通道
      this.SET_INIT_CHOOSE_CHANNEL([{ sn, chlIndex }])
    } else {
      this.SET_INIT_CHOOSE_CHANNEL([])
    }
    if (this.shareUser) {
      const { active, email, mobile } = this.shareUser
      this.active = active
      this.email = email
      this.mobile = mobile
    }
    // 获取当前登录用户信息
    this.getUserInfo()
  },
  mounted() {},
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('share', ['userInfo', 'shareUser']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    // 按钮是否可点击
    btnDisabled() {
      if (this.active === 'email') {
        // 校验邮箱是否填写正确
        if (this.email) {
          return !this.validateEmail(true)
        } else {
          return true
        }
      } else if (this.active === 'mobile') {
        // 校验手机号是否填写正确
        if (this.mobile) {
          return !this.validateMobile(true)
        } else {
          return true
        }
      } else {
        return true
      }
    }
  },
  methods: {
    ...mapMutations('share', ['SET_USER_INFO', 'SET_SHARE_USER', 'SET_SCAN_USER_INFO', 'SET_INIT_CHOOSE_CHANNEL']),
    timeFormat: dateFormat,
    back() {
      appClose()
    },
    pullingUp(callback) {
      this.getList({ type: 'up', callback })
    },
    pullingDown(callback) {
      this.listParams.pageNum = 1
      this.getList({ type: 'down', callback })
    },
    getList(params) {
      console.log('params', params)
    },
    // 获取当前用户登录信息--保证分享不能是本人
    async getUserInfo() {
      const res = await getUserInfo()
      if (res.basic.code === 200) {
        this.SET_USER_INFO(res.data)
      }
    },
    // 扫码
    handleScan() {
      const callback = data => {
        if (data) {
          const { body } = JSON.parse(data)
          const { url } = body
          const obj = JSON.parse(url)
          if (obj.type === 'userInfo') {
            // 扫码的用户信息
            // console.log('userInfo', obj.data)
            this.SET_SCAN_USER_INFO(obj.data)
            // 进入扫码结果页
            this.$utils.routerPush({
              path: '/share/scanSharer'
            })
          }
        }
      }
      appOpenScan(callback)
    },
    // 点击历史分享记录
    handleHistotyShare(item) {
      // 判断当前的历史分享是邮箱还是手机号
      if (item.shareName.indexOf('@') > -1) {
        // 说明是邮箱
        // 切换到email
        this.active = 'email'
        this.email = item.shareName
        this.mobile = null
      } else {
        // 说明是手机号
        this.active = 'mobile'
        this.mobile = item.shareName
        this.email = null
      }
    },
    // 校验邮箱
    validateEmail(validateEmpty = false) {
      if (this.email === '') {
        return !validateEmpty
      }
      return !!validateEmail(this.email)
    },
    // 校验手机
    validateMobile(validateEmpty = false) {
      if (this.mobile === '') {
        if (validateEmpty) {
          return false
        }
        this.errorMsg = ''
        return true
      }
      // 找到countryCode
      if (this.mobile.indexOf('+') === -1) {
        return false
      } else {
        const [code, number] = this.mobile.split('+')
        const country = MOBILE_COUNTRY_CODE.find(country => country.code === Number(code))
        if (country && number) {
          const countryLocale = country.locale
          return !!validateCommonPhone(number, countryLocale)
        }
        return false
      }
    },
    // 进入下一步
    async handleClick() {
      // 先确定用户邮箱/手机号存在
      let flag = false
      if (this.active === 'email') {
        // 判断邮箱是否为当前用户--不能分享给自己
        if (this.userInfo && this.userInfo.email === this.email) {
          this.$toastFail(this.$t('notShareSelf'))
          return
        }
        // 查验当前邮箱是否存在
        // const { data } = await getEmailExist({ email: this.email, platform: 50, appType: 2 })
        // flag = data
        const { data } = await getUserExist({ loginName: this.email })
        flag = data
      } else {
        // 判断邮箱是否为当前用户--不能分享给自己
        if (this.userInfo && this.userInfo.mobile === this.mobile) {
          this.$toastFail(this.$t('notShareSelf'))
          return
        }
        // 查验当前手机号是否存在
        // const { data } = await getPhoneExist({ mobile: this.mobile, platform: 50, appType: 2 })
        // flag = data
        const { data } = await getUserExist({ loginName: this.mobile })
        flag = data
      }
      if (!flag) {
        // 不存在
        this.$toastFail(this.$t('noFoundUser'))
        return
      }
      this.SET_SHARE_USER({
        active: this.active,
        email: this.email,
        mobile: this.mobile
      })
      // 进入设备选择页面
      this.$utils.routerPush({
        path: '/share/chooseDevice'
      })
    },
    // 获取历史分享
    getHistoryShareList() {
      const callback = data => {
        appLog('log/info', `${new Date()} 请求历史分享记录返回，结果是: ${data}`)

        if (data) {
          const obj = JSON.parse(data)
          console.log('getCacheData', obj)
          if (obj && obj.body) {
            this.historyShareList = (JSON.parse(obj.body) || [])
              .filter(item => item.shareName)
              .map(item => ({
                shareName: decrypt(item.shareName, this.generateAESKey)
              }))
          }
        }
      }
      getCacheData('historyShareList', callback)
    }
  }
}
</script>
<style lang="scss" scoped>
.choose-share-wrapper {
  height: 100%;
  overflow: hidden;
  .choose-share-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    padding: 24px 36px;
    box-sizing: border-box;
    .share-form-box {
      width: 100%;
      height: 80px;
      padding-bottom: 20px;
    }
    .share-history-box {
      width: 100%;
      height: calc(100% - 182px);
      overflow: hidden;
      margin-top: 24px;
      .share-history-title {
        height: 20px;
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      .share-history-list {
        width: 100%;
        height: calc(100% - 20px);
        overflow: auto;
      }
      .share-history-item {
        font-family: 'Source Han Sans CN', sans-serif;
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
  ::v-deep .van-tabs__wrap {
    height: 30px;
  }
  ::v-deep .van-tabs__wrap .van-tab {
    flex: none;
    padding: 0px;
    margin-right: 24px;
  }
  ::v-deep .van-tabs__line {
    display: none;
  }
  ::v-deep .van-tab {
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
  }
  ::v-deep .van-tab--active {
    position: relative;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
    &::before {
      content: '';
      position: absolute;
      left: 0px;
      bottom: 0px;
      width: 100%;
      height: 2px;
      border-radius: 1px;
    }
  }
}
</style>
