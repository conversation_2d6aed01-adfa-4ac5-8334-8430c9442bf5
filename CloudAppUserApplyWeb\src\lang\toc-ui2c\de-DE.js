// 德语
export default {
  upgrade: 'Cloud-Upgrade',
  cancel: 'Abbrechen',
  confirm: 'Bestätigen',
  deviceUpdate: 'Ger<PERSON>',
  cameraUpdate: 'Kamera',
  allUpdate: 'Alle upgraden',
  updateNow: 'Upgrade',
  currentVersion: 'Aktuelle Version',
  latestVersion: 'Neueste Version',
  updateContent: 'Update-Inhalt',
  hasLatestVersion: 'Bereits die neueste Version',
  online: 'Online',
  offline: 'Offline',
  waitDownload: 'Wartet auf Download',
  inprogress: 'Wird heruntergeladen',
  downloadFail: 'Download fehlgeschlagen',
  downloadFinished: 'Download abgeschlossen',
  inupgrade: 'Wird aktualisiert',
  upgradeFail: 'Update fehlgeschlagen',
  upgradeSuccess: 'Upgrade erfolgreich',
  deviceUpgradeInfo:
    'Während des Upgrades wird das Gerät getrennt und automatisch neu gestartet. Sind Sie sicher, dass Sie das Upgrade durchführen möchten?',
  upgradeTip:
    'Nach Abschluss des Downloads wird das Gerät automatisch upgegradet. Während des Upgrades wird das Gerät automatisch neu gestartet. Bitte starten Sie das Gerät nicht manuell neu oder trennen Sie die Stromversorgung, bis der automatische Neustart abgeschlossen ist.',
  cameraUpgradeInfo:
    'Während des Upgrades wird die Kamera getrennt und automatisch neu gestartet. Sind Sie sicher, dass Sie das Upgrade durchführen möchten?',
  pwdUserNameError: 'Benutzername oder Passwort fehlerhaft',
  permissionAuth: 'Super-Administrator-Berechtigungs-Authentifizierung',
  pleaseEnterUser: 'Bitte geben Sie den Benutzernamen ein',
  pleaseEnterPwd: 'Bitte geben Sie das Passwort ein',
  noCameraUpgrade: 'Keine upgradefähige Kamera gefunden',
  handleCheck: 'Updateerkennung',
  checkSuccess: 'Erfolgreich geprüft',
  checkFail: 'Prüfung fehlgeschlagen',
  viewUpdateContent: 'Update-Inhalt anzeigen',
  deviceDisconnected: 'Fehler beim Verbinden des Geräts',
  updateNote: 'Update-Hinweis',
  noData: 'Keine Daten',
  tips: 'Tipps',
  paySuccess: 'Zahlung erfolgreich',
  payFail: 'Zahlung fehlgeschlagen',
  done: 'Abgeschlossen',
  rePurchase: 'Erneut kaufen',
  cloudStorage: 'Cloud-Speicherung',
  INSTRUMENT_DECLINED: 'Transaktion überschreitet das Kartengrenzen',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Zahlungskonto kann für diese Transaktion nicht verwendet werden',
  PAYER_ACCOUNT_RESTRICTED: 'Das Zahlungskonto ist eingeschränkt',
  TRANSACTION_LIMIT_EXCEEDED: 'Der Gesamtzahlungsbetrag überschreitet das Transaktionslimit',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'Die Transaktion überschreitet das Empfänger-Empfangslimit',
  fail: 'Transaktion fehlgeschlagen',
  myInstaller: 'Mein Installateur',
  trusteeshipDevice: 'Verwaltetes Gerät',
  addTrusteeship: 'Hosting hinzufügen',
  waitReceived: 'Zu empfangen',
  received: 'Empfangen',
  refuse: 'Ablehnen',
  delete: 'Löschen',
  operationSuccess: 'Betriebs-Erfolg',
  operationFail: 'Betriebs-Fehler',
  cancelTrusteeship: 'Hosting abbrechen',
  chooseDevice: 'Gerät auswählen',
  noAvaiableDevice:
    'Die Geräteversion unterstützt die Hosting-Funktion und kann nur durch Hinzufügen von Geräten über die Hosting-Funktion gehostet werden.',
  leastChoose: 'Wählen Sie mindestens ein Gerät',
  finish: 'Abschließen',
  details: 'Details',
  deviceDetails: 'Details',
  live: 'Live-Ansicht',
  rec: 'Wiedergabe',
  config: 'Konfiguration',
  confirmTrusteeshipTip:
    'Die Hosting-Anfrage wurde an den Installateur gesendet, bitte warten Sie, bis der Installateur sie bearbeitet hat',
  cancelTrusteeshipTip:
    'Nach Abbruch des Hostings kann der Installateur Ihnen keine Fernwartungsdienste mehr zur Verfügung stellen. Sind Sie sicher, dass Sie abbrechen möchten?',
  unBindTrusteeship:
    'Nach dem Entbinden werden alle Geräte-Hostings abgebrochen. Sind Sie sicher, dass Sie entbinden möchten?',
  trusteeshipPermissions: 'Verwaltete Berechtigungen',
  trusteeshipTime: 'Aufbewahrungszeit',
  unBind: 'Entbinden',
  serviceException: 'Dienst-Ausnahme',
  netTimeOut: 'Netzwerkverbindung Timeout',
  pullingText: 'Sofort nach dem Veröffentlichen aktualisieren',
  loosingText: 'Aktualisieren nach der Veröffentlichung',
  loosing: 'Aktualisieren...',
  loadingText: 'Wird geladen...',
  refreshComplete: 'Erfolgreich aktualisiert',
  noMore: 'Keine weiteren...',
  noMoreDevice: 'Keine verfügbaren Geräte',
  invalid: 'Abgelaufen',
  password: 'Passwort',
  ipcUpgrade: 'Cloud-Upgrade',
  pwdError: 'Passwortfehler, Sie können es {0} Mal versuchen',
  pwdErrorLock: 'Zu viele Fehler wurden gesperrt, bitte versuchen Sie es später erneut!',
  noPermissions: 'Keine Berechtigung',
  permission: 'Berechtigung',
  validity: 'Gültigkeit',
  permissionValidity: 'Autorisieren',
  isSaveModify: 'Möchten Sie die Änderungen speichern?',
  manyMinutes: '{0} Min',
  manyHours: '{0} Std',
  manyDays: '{0} Tg',
  manyMinutesEn: '{0} Minuten',
  manyHoursEn: '{0} Stunden',
  manyDaysEn: '{0} Tage',
  oneWeek: '1 Woche',
  forever: 'Permanent',
  expired: 'Abgelaufen',
  residue: 'Verbleibende Zeit',
  transferRequest: 'Transferanfrage',
  acceptTransfer: 'Akzeptieren',
  refuseTransferConfirm: 'Sind Sie sicher, dass Sie den Transfer ablehnen möchten?',
  bindInstallerText: 'Sie können Geräte nach dem Binden des Installateurs ({Konto}) hosten. Jetzt binden?',
  bindSuccess: 'Bindung erfolgreich',
  acceptSuccess: 'Erfolgreich akzeptiert',
  from: 'Von',
  shareManage: 'Sharing-Verwaltung',
  shareDetail: 'Sharing-Details',
  acceptShare: 'Sharing akzeptieren',
  permissionText: 'Die Berechtigungen, die Sie erhalten haben',
  livePreview: 'Live-Vorschau',
  playback: 'Wiedergabe',
  alarm: 'Alarm',
  intercom: 'Intercom',
  gimbal: 'PTZ',
  refuseShareConfirm: 'Bestätigen Sie die Ablehnung des Shares?',
  acceptAll: 'Alle akzeptieren',
  exitShare: 'Exit sharing',
  cancelDefense: 'Disarmed',
  homeDefense: 'Nachtmodus',
  outDefense: 'Armed',
  defenseDeployment: 'Arming/Disarming',
  oneClickDeployment: 'One-click Arm',
  oneClickDisarm: 'One-click Disarm',
  oneClickRemoval: 'One-click Alarm Clearance',
  deploySuccess: 'Arm Erfolgreich',
  disarmSuccess: 'Disarm erfolgreich',
  add: 'Hinzufügen',
  edit: 'Bearbeiten',
  setting: 'Einstellung',
  all: 'Alle',
  name: 'Name',
  cameraSensor: 'Link zur Kamera',
  deleteDeviceConfirm: 'Bestätigen Sie die Löschung des Geräts?',
  onlySameDevice: 'Nur Kameras desselben Geräts können innerhalb derselben Gruppe hinzugefügt werden',
  pleaseChooseChannel: 'Bitte wählen Sie einen Kanal',
  pleaseAddCameraSensor: 'Bitte fügen Sie Kamera/Sensor hinzu',
  defensiveLinkageItem: 'Disarm-Verknüpfungselement',
  defensiveDesc: 'Das ausgewählte Disarm-Verknüpfungselement im Disarm-Status ist nicht wirksam',
  bypassHome: 'Nachtmodus',
  bypassHomeDesc: 'Wenn aktiviert, wird der Bereich während des Stay-Armings automatisch umgangen',
  ipcSound: 'IPC-Sound',
  ipcLight: 'IPC-Licht',
  pleaseChooseLinkage: 'Bitte wählen Sie das Disarm-Verknüpfungselement',
  deleteConfirm: 'Bestätigen Sie die Löschung?',
  pleaseAddGroup: 'Bitte fügen Sie zuerst eine Gruppe hinzu',
  groupChannelEmpty: 'Kein Kanal wurde der Verteidigungsgruppe hinzugefügt',
  removalSuccess: 'Der Alarmlöschbefehl wurde gesendet',
  removalFail: 'Fehler beim Senden der Alarmentfernung',
  reqSuccess: 'Erfolg',
  reqFail: 'Fehler',
  groupLimit: 'Unterstützt das Hinzufügen von bis zu {limit} Gruppen',
  areaGroup: 'Bereichsgruppe',
  pleaseEnter: 'Bitte eingeben',
  groupNoDevice: 'Keine Geräte unter Gruppierung hinzugefügt',
  lineAuthBind: 'Linienberechtigungsbindung',
  bindFail: 'Binding fehlgeschlagen',
  binding: 'Binding',
  chooseSharer: 'Empfänger auswählen',
  shareManagment: 'Freigabeverwaltung',
  mobileNum: 'Telefonnummer',
  historyShare: 'Freigabeverlauf',
  nextStep: 'Nächster Schritt',
  choose: 'Auswählen',
  preview: 'Vorschau',
  shareAuthSetting: 'Freigabeberechtigungen einstellen',
  confirmShare: 'Bestätigen',
  shareDevice: 'Gerät teilen',
  shareDesc: 'Ihr Gerät wird mit diesem Benutzer geteilt',
  shareDesc2: 'Basierend auf den von Ihnen festgelegten Berechtigungen kann er Inhalte anzeigen oder Geräte steuern',
  remark: 'Bemerkungen',
  shareSuccess: 'Freigabe erfolgreich',
  shareToUser: 'Ihr Gerät wurde erfolgreich mit dem Benutzer geteilt',
  myShare: 'Mein Gerät',
  otherShare: 'Fremdes Gerät',
  accept: 'Akzeptieren',
  reject: 'Ablehnen',
  toAccept: 'Zur Annahme ausstehend',
  accepted: 'Akzeptiert',
  rejected: 'Abgelehnt',
  allAccept: 'Alle akzeptieren',
  acceptSuccMsg: 'Erfolgreich akzeptiert, Sie können dies auf der Geräte-Seite der App überprüfen',
  acceptFail: 'Akzeptieren fehlgeschlagen',
  rejectFail: 'Ablehnen fehlgeschlagen',
  rejectMsg: 'Abgelehnt',
  cancelShare: 'Freigabe abbrechen',
  cancelShareDesc: 'Nach dem Abbrechen der Freigabe kann der Empfänger Ihr Gerät nicht mehr anzeigen oder steuern.',
  notNow: 'Nicht jetzt',
  stillCancel: 'Fortfahren',
  initiateShare: 'Freigabe initiieren',
  settingSuccess: 'Einstellung erfolgreich',
  deviceNum: '{0} Gerät(e)',
  noShareDevice: 'Keine geteilten Geräte',
  noFoundUser: 'Der Benutzer wurde nicht gefunden',
  notShareSelf: 'Kann nicht mit sich selbst teilen',
  tourist: 'Gast',
  andOther: 'und andere',
  shareViewFail: 'Kann nicht angezeigt werden, das Gerät wurde nicht mehr geteilt',
  messageDetail: 'Nachrichtendetails',
  agreeShare: 'Zustimmen zu teilen',
  agreed: 'Zugestimmt',
  agree: 'Zustimmen',
  rejectAll: 'Alles ablehnen',
  shareApplyDesc:
    '{0} hat eine Gerätefreigabeanforderung an Sie gestellt. Wenn zugelassen, bitte bestätigen Sie den Kanal und die Berechtigungen.',
  chooseChanAuth: 'Kanalberechtigung auswählen',
  shareOverTenDesc:
    'Die Konfigurationsparameter dieses Geräts wurden mit 10 Benutzern geteilt und können nicht erneut geteilt werden',
  channelEmpty: 'Die Kanalliste ist leer',
  msgExpired: 'Der Nachrichtinhalt ist abgelaufen',
  alarmNotification: 'Alarmbenachrichtigung',
  service: 'Dienst',
  serviceDetail: 'Dienstdetails',
  site: 'Standort',
  changeSiteName: 'Standortname ändern',
  cancelHosting: 'Standortverwaltung abbrechen',
  cancelHostingTip:
    'Nach dem Abbrechen der Autorisierung kann der Installateur den Standort nicht mehr basierend auf den von Ihnen erteilten Berechtigungen anzeigen oder verwalten.',
  cancelHostingError:
    'Der aktuelle Standort wird vom Installateur gehostet und kann nicht außer Betrieb genommen werden. Wenn Sie ihn stornieren möchten, wenden Sie sich bitte an Ihren Installateur.',
  cancelDeviceHostingTip:
    'Nach dem Abbrechen der Autorisierung können Sie dem Installateur keine Geräteberechtigungen mehr erteilen, und der Installateur kann Ihnen keine Remote-Dienste mehr bereitstellen.',
  devicePermissionApprove: 'Geräteverwaltungsanfrage',
  enterSiteName: 'Bitte geben Sie den Standortnamen ein',
  deviceHostDetail: 'Geräteverwaltungsdetails',
  addInstaller: 'Installateur hinzufügen',
  enterInstallerEmail: 'Geben Sie die E-Mail-Adresse des Installateurs ein',
  applicationSuccess: 'Erfolgreich gesendet',
  applicationInfo:
    'Die Autorisierungsanfrage wurde an den Installateur gesendet. Bitte warten Sie, bis er sie bearbeitet.',
  remarkPlaceholder: 'Bemerkung',
  hostingDevice: 'Verwaltete Geräte',
  noDeviceHosting: 'Keine Geräteverwaltungsanfrage',
  sitePermissionApprove: 'Standortverwaltungsanfrage',
  sitePermissionCancel: 'Standortverwaltung abbrechen',
  siteHostingTip:
    'Der Installateur hat eine Anfrage zur Verwaltung des Standorts in Ihrem Namen gestellt. Dieser Standort erleichtert dem Installateur die Verwaltung Ihrer Geräte.',
  siteCancelHostingTip:
    'Der Installateur hat die Verwaltung Ihres Standorts storniert. Falls Ihre Geräte Verwaltungs- oder Wartungsdienste benötigen, können Sie sich unter „Service“ bewerben.',
  deviceCancelHostingTip:
    'Der Installateur hat die Verwaltung Ihres Geräts storniert. Falls Ihr Gerät Verwaltungs- oder Wartungsdienste benötigt, können Sie sich unter „Service“ bewerben.',
  stillUnbind: 'Fortfahren',
  unBindDesc:
    'Nach dem Entbinden können Sie dem Installateur keine Geräteberechtigungen mehr erteilen, und der Installateur kann Ihnen keine Remote-Dienste mehr bereitstellen.',
  trusteeshipService: 'Autorisierungsdienst',
  trusteeshipText: 'Immer für Sie da, jederzeit und überall',
  usedNumber: '{0} Personen haben es genutzt',
  operateText: 'Ein-Klick-Autorisierungsdienst',
  fastText: 'Schnelle Wartungsreaktionen',
  protectText: 'Sicher und zuverlässig',
  immediateTrusteeship: 'Jetzt autorisieren',
  introduceText: 'Hallo, ich bin Ihr exklusiver Serviceberater',
  noTrusteeship: 'Sie haben derzeit keine Geräte unter Verwaltung',
  trustDeviceCount: '{0} Gerät(e) verwaltet',
  trustDeviceNone: 'Keine verwalteten Geräte',
  duration: 'Dauer',
  siteDescTitle: 'Was ist ein Standort?',
  siteDesc1: 'Ein „Standort“ erleichtert dem Installateur die Verwaltung Ihrer Geräte.',
  siteDesc2:
    'Wenn Sie dem Installateur die Verwaltung Ihres Standorts autorisieren, kann er einfacher Geräte für Sie hinzufügen und mit Ihrer Autorisierung schnell Geräteprobleme lösen.',
  trustPermission: 'Verstanden!',
  iKnow: 'Abgelehnt, kann in Zukunft weiterhin eine Geräteverwaltung im Service beantragen.',
  deviceRejectMsg: 'Verwaltungsberechtigungen',
  trustTime: 'Verwaltungsdauer',
  permissionSetting: 'Berechtigungseinstellungen',
  settingTime: 'Dauer festlegen',
  trusteeshipSuccess: 'Erfolgreich gesendet',
  trusteeshipToUser:
    'Die Autorisierungsanfrage wurde an den Installateur gesendet. Bitte warten Sie, bis er sie bearbeitet.',
  trusteeshipDetail: 'Autorisierungsdetails',
  unTrusteeshipDesc:
    'Nach dem Abbrechen der Autorisierung können Sie dem Installateur keine Geräteberechtigungen mehr erteilen, und der Installateur kann Ihnen keine Remote-Dienste mehr bereitstellen.',
  transferApply: 'Geräteübertragungsanfrage',
  deviceTransferDesc:
    'Der Installateur hat eine Geräteübertragungsanfrage an Sie gesendet. Wenn Sie sie akzeptieren, werden Sie an diesen Installateur gebunden, der Ihnen künftig einen Gerätemanagementservice bieten kann.',
  bindAcceptTransfer: 'Binden und akzeptieren',
  goUnBind: 'Zum Entbinden gehen',
  bindedInstaller: 'Bereits gebundener Installateur',
  transferDesc: 'Der Installateur hat eine Geräteübertragungsanfrage an Sie gesendet.',
  bindedInstallerDesc:
    'Sie haben bereits einen Installateur gebunden. Sie können entweder nur diese Übertragung akzeptieren oder den vorherigen Installateur entbinden, um diesen zu binden.',
  viewDetail: 'Details anzeigen',
  bindInstaller: 'Installateur binden',
  bindInstallerDesc:
    'Sie sollten einen Installateur binden, bevor Sie ein Gerät autorisieren. Bitte zeigen oder teilen Sie den QR-Code mit dem Installateur zur Bindung.',
  noTrusteeshipDevice: 'Keine Geräte unter Verwaltung',
  deviceTrusteeship: 'Geräteverwaltung',
  chooseTrusteeshipDevice: 'Geräte zur Verwaltung auswählen',
  deviceTransfer: 'Geräteübertragung',
  transferDeviceCount: 'Sie haben derzeit {0} Gerät(e) in Übertragung',
  transferDevice: 'Gerät übertragen',
  transferSuccess: 'Übertragung erfolgreich',
  rejectTransfer: 'Ablehnen',
  rejectDeviceTransfer: 'Geräteübertragung ablehnen',
  rejectTransfetDesc: 'Nach dem Ablehnen der Übertragung können Sie das Gerät nicht mehr anzeigen oder steuern.',
  waitTrust: 'Zu verwalten',
  waitTrustReceived: 'Zu akzeptieren',
  devicePermissionCancel: 'Geräteverwaltung abbrechen',
  duringTrust: 'Unter Verwaltung',
  residueTime: 'Verbleibende Zeit',
  manyMinutesEn2: '{0} Minute(n)',
  manySeconds: '{0}s',
  refresh: 'Aktualisieren',
  netErrText: 'Netzwerkausnahme. Klicken Sie zum Wiederholen',
  cancelled: 'Abgebrochen',
  transferAcceptedDesc: 'Übertragung erfolgreich',
  transferRejectedDesc: 'Abgelehnt',
  applyExpiredDesc: 'Die Anfrage ist abgelaufen. Bitte fordern Sie eine erneute Zusendung an',
  applyCancelledDesc: 'Abgebrochen',
  noDeviceTransfer: 'Keine Geräteübertragungsanfrage',
  skip: 'Überspringen',
  selectDevice: 'Gerät auswählen',
  ensure: 'Bestätigen',
  waitingTips: 'Gerät wird hinzugefügt, bitte verlassen Sie die aktuelle Seite nicht',
  finished: 'Fertig',
  successAdd: 'Erfolgreich {num} Geräte hinzugefügt',
  failAdd: '{num} Geräte konnten nicht hinzugefügt werden',
  nChannels: '{n} Kanäle',
  deviceName: 'Gerätename',
  open: 'öffnen',
  close: 'schließen',
  adding: 'Wird hinzugefügt',
  addToSite: 'Zum Konto hinzufügen',
  bindByOther: 'Das Gerät wurde bereits von Benutzer {name} hinzugefügt',
  enterVerificationCode: 'Verifizierungscode eingeben',
  enterDevVerificationCode: 'Bitte geben Sie den Verifizierungs-/Sicherheitscode ein',
  noCode: 'Fehlgeschlagen, Sicherheitscode zu erhalten',
  input: 'Eingeben',
  codeTips1: 'Sie können die folgenden Schritte ausführen, um den Geräteverifizierungs-/Sicherheitscode zu erhalten',
  codeTips2: '1. Öffnen Sie die Schnittstelle, auf der der Verifizierungscode/Sicherheitscode liegt:',
  codeTips3: 'Geräte-Seite: Hauptmenü-Einstellungen-Netzwerk-NAT',
  codeTips4: 'Webseite: Hauptmenü-Funktionspanel-Netzwerk-NAT',
  codeTips5:
    '2. Der Geräteverifizierungs-/Sicherheitscode befindet sich rechts neben dem Geräte-QR-Code. Siehe Abbildung unten für Details:',
  emptyDevice: 'Kein Gerät',
  immediateBuy: 'Jetzt kaufen',
  secureAndQuick: 'Sicher und beruhigend, bequem und schnell.',
  purchaseRecords: 'Kaufhistorie',
  cloudStorageInUse: 'Cloud-Speicher wird genutzt',
  serviceBought: '{0} Kanal(e) sind im Dienst',
  linkCameraSensor: 'Mit Kamera/Sensor verbinden',
  defenseAreaSetting: 'Sicherheitsbereichseinstellung',
  enterGroupName: 'Bitte geben Sie den Gruppennamen ein',
  createGroup: 'Gruppe erstellen',
  msgPushSwitch: 'Push',
  alarmOut: 'Alarm aus',
  saveModify: 'Änderung speichern',
  addedTo: 'Hinzugefügt zu {0}',
  pleaseEnterName: 'Bitte Namen eingeben',
  noGroupDesc: 'Es wurden keine Gruppen hinzugefügt. Sie können jetzt welche hinzufügen',
  createSuccess: 'Erfolgreich erstellt',
  createFail: 'Erstellen fehlgeschlagen',
  saveSuccess: 'Erfolgreich gespeichert',
  saveFail: 'Speichern fehlgeschlagen',
  repeatedlyRestart: 'Wiederholter Neustart',
  versionUnchanged: 'Version unverändert',
  versionException: 'Versionsausnahme',
  transferManagApply: 'Antrag auf Geräteübertragung und Hosting-Berechtigung',
  stillReject: 'Trotzdem ablehnen',
  trusteeshipAuthApply: 'Antrag auf Hosting-Berechtigung',
  agreeTrusteeshipDesc: 'Zugestimmt, Hosting-Berechtigungen können später im Service geändert werden',
  rejectTrusteeshipDesc: 'Abgelehnt, kann weiterhin eine Geräteverwaltung im Service beantragen',
  transferApplyTip: 'Der Installateur hat eine Standort- ({0}) und Gerätehosting-Anfrage für Sie initiiert.',
  userFeedback: 'Benutzer-Feedback',
  feedBackPlaceholder1:
    'Bitte geben Sie Ihr Feedback zur Nutzung der App ein, damit wir Ihnen in Zukunft eine bessere Produkterfahrung bieten können.',
  feedBackPlaceholder2: 'Bitte geben Sie Ihr Feedback zur Nutzung der App ein.',
  starOne: 'Sehr unzufrieden, hat nicht die erwartete Wirkung erzielt.',
  starTwo: 'Nicht zufrieden, alle Anforderungen wurden nicht vollständig erfüllt.',
  starThree:
    'Im Allgemeinen zufrieden, Service und Erfahrung sind grundsätzlich in Ordnung, aber es gibt Verbesserungspotenzial.',
  starFour: 'Zufrieden, gute Erfahrung und nahe an Perfektion.',
  starFive: 'Sehr zufrieden, Erwartungen übertroffen, ausgezeichnete Erfahrung.',
  feedBackSuccess: 'Feedback erfolgreich.',
  feedBackFail: 'Feedback fehlgeschlagen.',
  feedBackInfo:
    'Um Ihnen in Zukunft eine bessere Erfahrung zu bieten, wären wir Ihnen sehr dankbar für Ihr Feedback und Ihre wertvollen Meinungen.',
  submit: 'Absenden.',
  feedBackFinish: 'Sie können Ihre Bewertung im Bereich "Mein Nutzer-Feedback" auch in Zukunft fortsetzen.',
  resetDevPwd: 'Gerätepasswort zurücksetzen.',
  resetPwdTips: 'Der Installateur erinnert Sie daran, das Gerätepasswort zurückzusetzen.',
  modifyDevPwd: 'Gerätepasswort ändern.',
  pwdTips:
    'Das Passwort muss 8–16 Zeichen lang sein und mindestens zwei der folgenden Zeichenarten enthalten: Zahlen, Buchstaben (Groß- und Kleinschreibung beachten) und Sonderzeichen.',
  confirmNewPassword: 'Neues Passwort bestätigen.',
  emptyPsw: 'Das Passwort darf nicht leer sein.',
  emptyOldPwd: 'Das alte Passwort darf nicht leer sein.',
  setPwdTips:
    'Zum Ändern des Passworts ist eine Verifizierung des alten Passworts erforderlich. Bitte geben Sie zuerst das alte Passwort ein.',
  oldPwd: 'Altes Passwort',
  enterOldPwd: 'Bitte geben Sie das alte Passwort ein.',
  newPwd: 'Passwort',
  enterNewPwd: 'Bitte geben Sie ein neues Passwort ein.',
  confirmPwd: 'Passwort bestätigen',
  enterConfirmPwd: 'Bitte geben Sie das neue Passwort erneut ein.',
  notMatchPsw: 'Die eingegebenen Passwörter stimmen nicht überein. Bitte erneut eingeben.',
  updateTips: 'Sie können Ihren Gerätenamen zur späteren Identifikation ändern.',
  pleaseEnterDevName: 'Bitte geben Sie den Gerätenamen ein.',
  updateDevName: 'Gerätenamen ändern',
  errRenameTips: 'Falsche Eingabe des Gerätenutzernamens oder Passworts.',
  mediumPwd:
    '1. Die Länge beträgt 8–16 Zeichen.\n2. Es enthält zwei oder mehr der folgenden Zeichenarten: Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole.',
  strongPwd:
    '1. Die Länge beträgt 8–16 Zeichen.\n2. Es enthält drei oder mehr der folgenden Zeichenarten: Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole.',
  strongerPwd: '1. Die Länge beträgt 9–16 Zeichen.\n2. Es enthält Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole.',
  mediumPwdTips:
    'Die Passwortlänge beträgt 8–16 Zeichen und muss mindestens zwei der folgenden Zeichen enthalten: Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole.',
  strongPwdTips:
    'Die Passwortlänge beträgt 8–16 Zeichen und muss mindestens drei der folgenden Zeichen enthalten: Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole.',
  strongerPwdTips:
    'Die Passwortlänge beträgt 9–16 Zeichen und muss Zahlen, Kleinbuchstaben, Großbuchstaben und Symbole enthalten.',
  waitingForUpgrade: 'Wartet auf Upgrade',
  duringTrsutResidueTime: 'Unter Verwahrung Verbleibende Zeit',
  selectAll: 'Alles auswählen',
  cancelSelectAll: 'Alles abwählen',
  errorCode: {
    211: 'Benutzer ist gesperrt',
    215: 'System beschäftigt',
    217: 'Verbindung Timeout',
    400: 'Parameterfehler',
    404: 'Die angeforderte Ressource (Webseite, etc.) existiert nicht',
    500: 'Systemausnahme',
    502: 'Serveranforderung fehlgeschlagen',
    503: 'Serverausnahme',
    504: 'Serveranforderung Timeout',
    550: 'Anforderungs-Timeout',
    604: 'Verbindung zum Gerät konnte nicht hergestellt werden, bitte überprüfen Sie den Gerätestatus',
    733: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    734: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    736: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    737: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    738: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    739: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    740: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    1000: 'Parameterfehler',
    1005: 'Bildverifizierungscode-Fehler',
    1007: 'Bildverifizierungscode erforderlich',
    1008: 'Der Verifizierungscode ist abgelaufen',
    1009: 'Verifizierungscode-Fehler',
    1011: 'Der Parameter wurde nicht korrekt ausgefüllt!',
    1012: 'API nicht erkannt',
    1013: 'Der Verifizierungscode konnte nicht gesendet werden',
    1015: 'Der Benutzer existiert bereits',
    1027: 'Bitte geben Sie die richtige Gerätenummer/ Sicherheitscode ein',
    1028: 'Die Kamera ist aktiviert oder deaktiviert',
    4500: 'Parameterfehler',
    5000: 'Entschuldigung, Sie haben keine Berechtigung, diese Operation durchzuführen',
    5001: 'Der aktuelle Benutzer hat keine Berechtigung',
    6000: 'Der aktuelle Geschäftszustand unterstützt diese Operation nicht',
    6001: 'Zu häufige Operation',
    7000: 'Parameterfehler',
    7001: 'Der Benutzer existiert nicht',
    7002: 'Altes Passwort fehlerhaft!',
    7003: 'Token ist fehlerhaft!',
    7004: 'Hallo, Ihr Konto wurde aufgrund einer langen Inaktivität oder der Anmeldung auf anderen Geräten abgemeldet. Bitte melden Sie sich erneut an',
    7005: 'Ungültige Signatur',
    7006: 'Die Handynummer existiert bereits',
    7007: 'Der Benutzer ist gesperrt, bitte kontaktieren Sie den Administrator, um ihn zu entsperren',
    7009: 'Hallo, Ihr Konto wurde aufgrund einer langen Inaktivität oder der Anmeldung auf anderen Geräten abgemeldet. Bitte melden Sie sich erneut an',
    7010: 'Das Administrator-Konto ist nicht aktiviert',
    7011: 'Konto nicht aktiviert',
    7019: 'Der Benutzername existiert bereits',
    7021: 'Löschung fehlgeschlagen! Bitte löschen Sie zuerst alle Hosts unter dieser Host-Gruppe',
    7023: 'Die Mailbox wurde gebunden',
    7028: 'Die Vorlage wurde im Projekt verwendet und kann nicht gelöscht werden!',
    7029: 'Der Vorlagenname existiert bereits!',
    7030: 'Die Daten existieren bereits!',
    7032: 'Das Firmware-Paket existiert bereits!',
    7034: 'Das Firmware-Paket wurde veröffentlicht und kann nicht gelöscht werden!',
    7040: 'Gerät existiert nicht oder ist nicht online',
    7042: 'Es gibt andere Aufgaben im Startzustand',
    7043: 'Die Aufgabe wurde nicht genehmigt!',
    7044: 'Operation fehlgeschlagen. Es gibt keine Geräte, die für ein Upgrade berechtigt sind!',
    7045: 'Die Aufgabe wurde nicht genehmigt!',
    7046: 'Erreichtes Versionsgrenzen',
    7048: 'Gerät existiert nicht.',
    7056: 'Diese Version wurde in die unterstützende Kompatibilitätsverwaltung aufgenommen und kann nicht gelöscht werden!',
    7057: 'Das ausstellende Dokument darf nicht leer sein!',
    7061: 'Korrektur fehlgeschlagen, kann keine Korrektur erneut erstellen!',
    7062: 'Das Gerät ist offline',
    7063: 'Das Gerät wurde von jemand anderem gebunden',
    7065: 'Der Kanal wurde bereits geteilt',
    7066: 'Der Kundencode existiert bereits!',
    7068: 'Der Kundencode existiert nicht!',
    7069: 'Zu viele Daten, bitte verengen Sie den Suchbereich und versuchen Sie es erneut!',
    7071: 'Das Gerät ist für diesen Cloud-Service nicht aktiviert',
    7072: 'Das Gerät existiert bereits',
    7081: 'Import fehlgeschlagen!',
    7082: 'Export fehlgeschlagen!',
    7084: 'Der Kunden-Ländercode existiert bereits',
    7085: 'Der Benutzer kann das Gerät nicht binden',
    7086: 'Die Operation wird aufgrund einer Systemausnahme abgelehnt',
    7087: 'Das Produkt existiert bereits!',
    7088: 'Hallo, Ihr Konto wurde aufgrund einer langen Inaktivität oder der Anmeldung auf anderen Geräten abgemeldet. Bitte melden Sie sich erneut an',
    7090: 'Hallo, Ihr Konto wurde aufgrund einer langen Inaktivität oder der Anmeldung auf anderen Geräten abgemeldet. Bitte melden Sie sich erneut an',
    7093: 'Bild- und Textinformationen sind nicht konfiguriert!',
    7094: 'Die Servicebedingungen-Informationen existieren nicht!',
    9000: 'Systemausnahme!',
    9001: 'Die Protokollversion ist zu niedrig. Die alte Version ist nicht mehr kompatibel und muss aktualisiert werden',
    9002: 'Protokollversionsfehler, nicht erkannte Versionsfelder oder Fehlermeldungen',
    9003: 'Fehler beim Senden des Verifizierungscodes',
    9004: 'Datenbankoperation fehlgeschlagen',
    9005: 'Die Daten existieren nicht',
    9006: 'Die Daten existieren bereits',
    9007: 'Die anzuzeigenden Daten existieren nicht',
    9008: 'Die Daten existieren nicht',
    9009: 'Daten-Ausnahme',
    9500: 'Systemausnahme!',
    10000: 'Fehler beim Verbinden des Geräts',
    10001: 'Systemausnahme!',
    10004: 'Parameterfehler',
    12344: 'Netzwerkverbindung fehlgeschlagen',
    12345: 'Netzwerkverbindung Timeout',
    20021: 'Diese E-Mail wurde bereits verwendet',
    20024: 'Das Konto wurde aktiviert',
    20030: 'Der Link ist abgelaufen',
    20070: 'Einladen dieses Benutzers fehlgeschlagen, da Sie sich in verschiedenen Rechenzentren befinden.',
    20071: 'Einladen dieses Benutzers fehlgeschlagen, da Sie zu verschiedenen Händlern gehören.',
    21109: 'Das Gerät hat das Teilen beendet',
    23024: 'Die bereitgestellte Zahlungskarte ist abgelaufen',
    23025: 'Die Transaktion wurde aufgrund eines Verstoßes abgelehnt',
    32018: 'Daten existieren nicht',
    32019: 'Operation fehlgeschlagen',
    32021: 'Daten existieren nicht',
    32022: '{0} Gerät und Installateur sind nicht im selben Land/Region und unterstützen kein Hosting',
    33001: 'Keine Berechtigung für die Bedienung dieses Geräts',
    33002: 'Keine Berechtigung für die Bedienung dieser Seite',
    33003: 'Die Seite existiert nicht',
    33004: 'Die Länge des Gerätenamens muss zwischen 0 und 32 liegen',
    33010: 'Das Gerät existiert bereits',
    33016: 'Das Gerät wird gebunden',
    33601: 'Wiederholtes Gerät!',
    33602: 'Limit maximale Armierungsgruppenzahl!',
    33603: 'Operation fehlgeschlagen!',
    33681: 'Das Gerät unterstützt diese Upgrade-Methode nicht',
    33682: 'Der Kanal existiert nicht',
    33683: 'Kanal offline',
    34001: 'Dieses Konto wurde gebunden.',
    34003: 'Die Statusinformationen sind fehlerhaft',
    34004: 'Autorisierung fehlgeschlagen.',
    34005: 'Operation fehlgeschlagen: Die Autorisierung ist abgelaufen. Bitte holen Sie sie erneut.',
    34006: 'Gerätetransfer existiert nicht',
    34007: 'Kann nur Transfers vom gleichen Benutzer annehmen',
    34203: 'Dieser Installateur existiert nicht.',
    34204:
      'Der Verwaltungsstatus des Geräts wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    34205:
      'Der Status der Anfrage wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    34206:
      'Der Status der Anfrage wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    34207:
      'Der Status der Seite wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    34208:
      'Der Status der Seite wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    34209:
      'Der Verwaltungsstatus des Geräts wurde aktualisiert. Bitte aktualisieren Sie die Seiten, um mit dem neuesten Status zu synchronisieren.',
    99006: 'Netzwerkverbindung nicht verfügbar',
    100003: 'Das angegebene Gerät wurde nicht gefunden',
    100008: 'Das Gerät existiert bereits',
    101001: 'Verbindung zum Gerät konnte nicht hergestellt werden, bitte überprüfen Sie den Gerätestatus',
    101003: 'Verbindung zum Gerät konnte nicht hergestellt werden, bitte überprüfen Sie den Gerätestatus',
    101007: 'Das angegebene Gerät wurde nicht gefunden',
    130000: 'Verbindung zum Gerät konnte nicht hergestellt werden, bitte überprüfen Sie den Gerätestatus',
    130001: 'Verbindung zum Gerät konnte nicht hergestellt werden, bitte überprüfen Sie den Gerätestatus',
    536870931: 'Verbindung getrennt',
    536870934: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536870940: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536870943: 'Ungültiger Parameter',
    536870945: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536870947: 'Benutzername existiert nicht',
    536870948: 'Benutzername oder Passwort fehlerhaft',
    536871017: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536871030: 'Kein HDD',
    536871039: 'Ungültiger Parameter',
    536871049: 'Cloud-Upgrade-Ausnahme',
    536871060: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536871082: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    536871083: 'Betriebsfehler, bitte überprüfen Sie den Gerätestatus',
    ipc: {
      499: 'Unbekannter Fehler',
      604: 'Gerät kann nicht verbunden werden, bitte überprüfen Sie den Gerätestatus.',
      612: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      730: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      731: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      732: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      733: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      734: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      735: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      736: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      737: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      738: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      739: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      740: 'Vorgang fehlgeschlagen, bitte überprüfen Sie den Gerätestatus.',
      99006: 'Netzwerkverbindung nicht verfügbar',
      101001: 'Gerät kann nicht verbunden werden, bitte überprüfen Sie den Gerätestatus.',
      101003: 'Gerät kann nicht verbunden werden, bitte überprüfen Sie den Gerätestatus.',
      130001: 'Gerät kann nicht verbunden werden, bitte überprüfen Sie den Gerätestatus.',
      536870931: 'Verbindung getrennt',
      536871049: 'Cloud-Upgrade-Fehler'
    }
  }
}
