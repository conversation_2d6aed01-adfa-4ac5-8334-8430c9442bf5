@import './checkUserPwd.scss';
@import './upgrade.scss';
@import './navBar.scss';
@import './trusteeship/myInstaller.scss';
@import './trusteeship/deviceDetails.scss';
@import './transfer/transferRequest.scss';
@import './share/shareManage.scss';
@import './defense/index.scss';
@import './preset-point/index.scss';
@import './cruise-line/index.scss';
@import './line-bind/index.scss';
@import './ipc-upgrade/index.scss';

html,
body,
#app {
  background-color: $vms-background;
  color: $vms-black;
}

// 根据主题 全局 修改vant样式  VMS 
.van-dialog__confirm, .van-dialog__confirm:active {
  color: $vms-primary;
}

.van-button--primary {
  color: $vms-white;
  background-color: $vms-primary;
  border: 1px solid $vms-primary;
}
.van-dialog{
  width: 300px;
}

.van-dialog__header {
  font-weight: 700;
  font-size: 15px;
  color: $black-color;
}

.van-dialog__cancel{
  color:$vms-main-black;
}

.van-dialog__message{
  color:$vms-light-black;
  font-size: 14px;
} 

// 底部按钮 通用样式
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: $vms-white;
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 296px;
    height: 46px;
    background: $vms-primary;
    border-radius: 23px;
    text-align: center;
    color: $vms-white;
  }
}

// van-tab底部滑条颜色
.van-tabs__line {
  background-color: $vms-primary;
}

// Tab背景色
.van-tabs__nav {
  background-color: $vms-white;
}
.van-tab {
  color: $gray-color;
}
.van-tab--active {
  color: $vms-primary;
}

// checkbox背景色
.van-checkbox__icon--checked .van-icon {
  background-color: $vms-primary;
  border-color: $vms-primary;
}

// switch背景色
.van-switch--on {
  background-color: $vms-primary;
}