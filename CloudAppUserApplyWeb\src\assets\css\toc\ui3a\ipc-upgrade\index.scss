.ipc-upgrade-list {
  ::v-deep.van-popup--center {
    width: 320px;
  }
  ::v-deep .van-popup__close-icon--top-right {
    top: 6px;
    right: 6px;
  }
  ::v-deep.van-popup {
    width: 300px;
  }
  .pop-dialog {
    width: 320px;
    border-radius: 8px 8px 8px 8px;
    background-color: $UI3A-light-background-color!important;
    .pop-div {
      position: relative;
    }
    .dialog-title {
      width: 300px;
      font-weight: 700;
      height: 54px;
      line-height: 54px;
      font-size: 15px;
      color: $UI3A-white-color;
      text-align: center;
      border-bottom: 1px solid $UI3A-light-gray-color;
    }
    .update-box {
      padding: 8px;
      color: $UI3A-white-color;
      .update-content {
        max-height: 394px;
        overflow-y: scroll;
        line-height: 22px;
        padding: 0 10px 18px 16px;
        word-break: break-all;
      }
    }
    .dialog-close-img {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 13px;
      height: 13px;
      img {
        width: 100%;
        height: 100%；;
      }
    }
  }
  .title-text {
    height: 28px;
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .title-text-img {
        width: 35px;
        height: 27px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .title-text-button {
    width: 70px;
    height: 25px;
    line-height: 25px;
    border-radius: 12.5px 12.5px 12.5px 12.5px;
    text-align: center;
    border: 1px solid $UI3A-color-primary;
    color: $UI3A-color-primary;
  }
  .view-btn {
    color: $UI3A-color-primary;
    font-weight: 500;
    font-size: 12px;
  }
  .title {
    color: $UI3A-50-white-color;
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    font-weight: 700;
  }
  .title-text-title {
    font-weight: 500;
    font-size: 15px;
    line-height: 27px;
    margin-left: 15px;
    max-width: 190px;
  }
  .download-status {
    color: $UI3A-color-primary;
    font-size: 12px;
    display: flex;
    .download-status-text {
      margin-right: 8px;
    }
  }
  .ipc-upgrade {
    .container {
      color: $UI3A-white-color;
      background-color: $UI3A-light-background-color;
      padding: 15px 15px;
    }
  }
  // 列表通用
  .list-content {
    padding-top: 20px;
    .list-content-row {
      display: flex;
      line-height: 24px;
      .label {
        width: 112px;
        flex-shrink: 0;
        color: $UI3A-40-white-color;
      }
      .value {
        max-width: 240px;
        flex: 1;
        word-wrap: break-word;
        white-space: pre-wrap;
        color: $UI3A-white-color;
      }
      .zh-label {
        width: 70px;
      }
      .zh-value {
        max-width: 280px;
      }
    }
    .download-tip {
      line-height: 24px;
      font-size: 12px;
    }
    .has-latest-version {
      color: $green-color;
    }
  }
}