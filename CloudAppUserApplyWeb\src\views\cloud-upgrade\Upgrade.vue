<template>
  <div class="upgrade-list">
    <nav-bar @clickLeft="back"></nav-bar>
    <!-- 设备升级 -->
    <div class="device-upgrade">
      <div class="title">{{ $t('deviceUpdate') }}</div>
      <div class="container">
        <div class="title-text">
          <div class="left">
            <div class="title-text-img">
              <theme-image alt="nvr" imageName="nvr.png" />
            </div>
            <div class="title-text-title text-over-ellipsis">
              {{ devInfo.devName }}
            </div>
          </div>
          <div class="right">
            <div
              :class="[
                'title-text-button',
                !isDeviceNeedUpgrade ||
                !['newVersion', 'latest'].includes(devInfo.newState) ||
                devUpgradeObj[this.devId]
                  ? 'title-button-disabled'
                  : ''
              ]"
              @click="deviceUpgrade"
            >
              {{ $t('updateNow') }}
            </div>
          </div>
        </div>
        <div class="list-content" v-if="isDeviceNeedUpgrade">
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('currentVersion') }}
            </div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              <span>{{ devInfo.version || '--' }}</span>
            </div>
          </div>
          <div class="list-content-row" v-if="devInfo.newVersion">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('latestVersion') }}
            </div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              <span>{{ devInfo.newVersion || '--' }}</span>
            </div>
          </div>
          <div class="list-content-row" v-if="devInfo.versionNote">
            <div class="view-btn" @click="viewUpdateContent(devInfo.versionNote)">
              {{ $t('viewUpdateContent') }}
            </div>
          </div>
          <!-- 设备升级 状态 下载中、下载失败、升级中、升级失败==安装失败 ？、升级成功、离线-->
          <!-- <div class="list-content-row" v-if="devInfo.state">
            <div class="download-status">
              <div
                v-if="devInfo.showDevStatusText"
                class="download-status-text"
                :style="`color: ${statusColor[devInfo.devStatusColor]}`"
              >
                {{ devInfo.devStatusText }}
              </div>
              <span
                class="progress"
                :style="`color: ${statusColor[devInfo.devStatusColor]}`"
                v-if="devInfo.progress && devInfo.state != 'installing'"
                ><span>(</span>{{ devInfo.progress }}<span>)</span></span
              >
            </div>
          </div> -->
          <!-- 展示新的状态及进度条 -->
          <div class="download-status-box">
            <van-progress
              v-if="devInfo.showDevStatusText && !['offLine', 'newVersion', 'latest'].includes(devInfo.newState)"
              class="upgrade-progress"
              :percentage="Number(devInfo.newProgress || 0)"
              stroke-width="5"
              :show-pivot="false"
            />
            <div
              v-if="devInfo.showDevStatusText"
              class="download-status-text"
              :style="`color: ${statusColor[devInfo.devStatusColor]}`"
            >
              {{ devInfo.newdevStatusText }}
              <!-- 此标签为了触发上面van-progress同步渲染更新 -->
              <span style="visibility: hidden">{{ devInfo.progress }}</span>
            </div>
          </div>
          <!-- 提示 -->
          <div class="list-content-row" v-if="isShowUpgradeTip">
            <div class="download-tip">{{ $t('upgradeTip') }}</div>
          </div>
        </div>
        <!-- 设备 没有更新的 -->
        <div class="list-content" v-else>
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">
              {{ $t('currentVersion') }}
            </div>
            <span :class="['value', languageFlag ? 'zh-value' : '']"
              ><span>{{ devInfo.version }}</span>
            </span>
          </div>
          <div class="list-content-row">
            <div class="has-latest-version">{{ $t('hasLatestVersion') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 摄像机升级 -->
    <!-- ViewStation跟NVR协议一样，但是没有摄像机，需要区分开来 -->
    <template v-if="isCameraSupportUpgrade">
      <div class="camera-upgrade" v-if="isHasCameraUpgrade">
        <div class="title-div">
          <div class="title">{{ $t('cameraUpdate') }}</div>
          <div class="title-img" @click="upgradeAllCamera">
            <theme-image alt="upgrade" imageName="upgrade.png" />
          </div>
        </div>
        <div class="camera-has-upgrade" v-if="cameraList.length > 0">
          <div class="container-ul">
            <div class="container-li" v-for="(item, index) in cameraList" :key="'camera' + index">
              <div>
                <div class="title-text">
                  <div class="left">
                    <div class="title-text-img">
                      <theme-image alt="camera" imageName="camera.png" />
                    </div>
                    <div class="title-text-title text-over-ellipsis">
                      {{ item._name }}
                    </div>
                  </div>
                  <div class="right">
                    <div
                      :class="['title-text-button', item.newState !== 'newVersion' ? 'title-button-disabled' : '']"
                      @click="upgradeCamera(item)"
                    >
                      {{ $t('updateNow') }}
                    </div>
                  </div>
                </div>
                <div class="list-content">
                  <div class="list-content-row">
                    <div :class="['label', languageFlag ? 'zh-label' : '']">
                      {{ $t('currentVersion') }}
                    </div>
                    <div :class="['value', languageFlag ? 'zh-value' : '']">
                      <span>{{ item.version || '--' }}</span>
                    </div>
                  </div>
                  <div class="list-content-row">
                    <div :class="['label', languageFlag ? 'zh-label' : '']">
                      {{ $t('latestVersion') }}
                    </div>
                    <div :class="['value', languageFlag ? 'zh-value' : '']">
                      <span>{{ item.newVersion || '--' }}</span>
                    </div>
                  </div>
                  <div class="list-content-row" v-if="item.versionNote">
                    <div class="view-btn" @click="viewUpdateContent(item.versionNote)">
                      {{ $t('viewUpdateContent') }}
                    </div>
                  </div>
                  <!-- 通道升级的过程分为待下载、下载中 下载进度、升级中、升级成功、升级失败的状态 (IPC没有离线状态) -->
                  <!-- <div class="list-content-row" v-if="item.state">
                    <div class="download-status">
                      <div
                        v-if="item.showIpcStatusText"
                        class="download-status-text"
                        :style="`color: ${statusColor[item.IpcStatusColor]}`"
                      >
                        {{ item.IpcStatusText }}
                      </div>
                      <span
                        class="progress"
                        :style="`color: ${statusColor[item.IpcStatusColor]}`"
                        v-if="item.progress && item.state != 'installing'"
                        ><span>(</span>{{ item.progress }}<span>)</span></span
                      >
                    </div>
                  </div> -->
                  <!-- 展示新的状态及进度条 -->
                  <div class="download-status-box">
                    <van-progress
                      v-if="item.showIpcStatusText && !['offLine', 'latest'].includes(item.newState)"
                      class="upgrade-progress"
                      :percentage="Number(item.newProgress || 0)"
                      stroke-width="5"
                      :show-pivot="false"
                    />
                    <div
                      v-if="item.showIpcStatusText"
                      class="download-status-text"
                      :style="`color: ${statusColor[item.IpcStatusColor]}`"
                    >
                      {{ item.newIpcStatusText }}
                      <!-- 此标签为了触发上面van-progress同步渲染更新 -->
                      <span style="visibility: hidden">{{ item.progress }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="camera-upgrade" v-else>
        <div class="title-div">
          <div class="title">{{ $t('cameraUpdate') }}</div>
          <div class="title-img" @click="upgradeAllCamera">
            <theme-image alt="upgrade" imageName="upgrade.png" />
          </div>
        </div>
        <div class="camera-no-upgrade">{{ $t('noCameraUpgrade') }}</div>
      </div>
    </template>
    <div class="footer">
      <div class="footer-btn" @click="checkVersion">
        {{ $t('handleCheck') }}
      </div>
    </div>
    <!-- 查看 更新内容弹框 -->
    <van-popup v-model="showUpdateContent" class="pop-dialog" :close-on-click-overlay="false">
      <div class="pop-div">
        <div class="dialog-title">{{ $t('updateNote') }}</div>
        <div class="dialog-close-img" @click="closePopup">
          <theme-image alt="close" imageName="close.png" />
        </div>
      </div>
      <div class="update-box">
        <div class="update-content">{{ updateContent }}</div>
      </div>
    </van-popup>
    <!-- 权限校验弹框 -->
    <check-user-pwd ref="checkUserPwd" @devUpgrade="devUpgrade"></check-user-pwd>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import CheckUserPwd from './CheckUserPwd.vue'
import { appSetTitle, appBack, appRequestDevice, getCacheData, setCacheData, appLog } from '@/utils/appbridge'
import { transformXml, getParamsFromUserAgent, getUrlQuery, getMaxUrlQuery, debounce } from '@/utils/common'
import { urlGetCloudUpgradeInfo, urlCloudUpgrade, urlCheckVersion, urlCloudUpgradeNode } from '@/api/cloudUpgrade'
import { STATUS_COLOR_THEME } from '@/utils/options.js'
import md5 from 'js-md5'
import sha1 from 'sha1'
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'upgradeList',
  components: {
    NavBar,
    CheckUserPwd,
    ThemeImage
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      devId: '',
      bindState: 0, // SN和安全码 添加的   bindState是1     ip添加的 bindState是0  IP添加的设备升级是要输入账号密码
      isDeviceNeedUpgrade: true, //设备 有没有更新的 devInfo state字段   lastest/false  newVersion/true
      isCameraSupportUpgrade: true, //摄像机是否支持云升级
      isHasCameraUpgrade: true, //是否有摄像机云升级
      showUpdateContent: false,
      updateContent: '',
      devInfo: {
        state: '',
        devName: '',
        version: '',
        newVersion: '',
        newVersionGUID: '',
        progress: '',
        versionNote: '',
        showDevStatusText: false, //设备 升级的状态展示
        devStatusColor: '',
        devStatusText: ''
      },
      cameraList: [], //摄像机 字段 _name,state,progress,version,newVersion,newVersionGUID,_id,_ip,showIpcStatusText,IpcStatusText,IpcStatusColor,versionNote
      recordCamData: [], // 对比记录的是否展示的ipc
      devNewVersionInfo: [], // 版本更新内容
      timer: null,
      cloudUpgradeStateMap: {
        // latest: this.$t('hasLatestVersion'), // 当前为最新版本
        // newVersion: '', // 有新版本
        // checkingVersion: '', // 版本检测中
        waitingForUpgrade: this.$t('waitDownload'), // 待下载
        downloading: this.$t('inprogress'), // 下载中
        downloadFail: this.$t('downloadFail'), // 下载失败，网络异常
        downloadNetException: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailNodeInvalid: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailOSSException: this.$t('downloadFail'), // 下载失败，OSS异常所致
        downloadFailNodeDisconnect: this.$t('downloadFail'), // 下载失败，通道离线导致的失败
        downloadFailFileWritExecption: this.$t('downloadFail'), // 下载失败，文件写失败
        downloadFailFileReadExecption: this.$t('downloadFail'), // 下载失败，文件读取失败
        downloadFailFileOpenExecption: this.$t('downloadFail'), // 下载失败，文件打开失败
        downloadSuccess: this.$t('downloadFinished'), // 下载完成
        installing: this.$t('inupgrade'), // 升级中
        installSuccess: this.$t('upgradeSuccess'), // 升级成功
        installFail: this.$t('upgradeFail'), // 升级失败，转发失败的错误原因还未明确
        installFailNodeDisconnect: this.$t('upgradeFail'), // 升级失败，通道离线所致
        installFailNodeInvalid: this.$t('upgradeFail'), // 升级失败，通道被删除或POE通道被拔出所致
        offLine: this.$t('offline') //app给的code判断的设备离线
      },
      errorCode: null, //10000 设备离线
      bridgeType: null, // APP类型，需要区分出superlive max
      devType: null, // 设备类型 1：IPC 2：NVR 3：DVR 4：依图套装 5：TVT套装 7：依图NVR 8：NVMS 9：卡片机 10：门铃 11：太阳能IPC 13 ViewStation
      appPlatform: null, // APP平台  Android /  IOS
      // 新的云升级状态映射 -- 待下载、升级中、重启中、升级成功
      newCloudUpgradeStateMap: {
        // waitingForUpgrade: this.$t('waitDownload'), // 待下载
        waitingForUpgrade: this.$t('waitingForUpgrade'), // 待升级对应待升级
        downloading: this.$t('inupgrade'), // 下载中对应升级中
        downloadSuccess: this.$t('inupgrade'), // 下载完成对应升级中
        installing: this.$t('inupgrade'), // 升级中对应升级中
        restart: this.$t('inupgrade'), // 重启中  -- 需要记录之前的升级状态，如果有下载则离线对应重启中，重启中也展示升级中
        offLine: this.$t('offline'), //app给的code判断的设备离线
        installSuccess: this.$t('upgradeSuccess'), // 升级成功  -- 需要记录之前的升级状态，如果有下载则最新版本对应升级成功
        installFail: this.$t('upgradeFail'), // 升级失败，转发失败的错误原因还未明确
        installFailNodeDisconnect: this.$t('upgradeFail'), // 升级失败，通道离线所致
        installFailNodeInvalid: this.$t('upgradeFail'), // 升级失败，通道被删除或POE通道被拔出所致
        downloadFail: this.$t('downloadFail'), // 下载失败，网络异常
        downloadNetException: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailNodeInvalid: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailOSSException: this.$t('downloadFail'), // 下载失败，OSS异常所致
        downloadFailNodeDisconnect: this.$t('downloadFail'), // 下载失败，通道离线导致的失败
        downloadFailFileWritExecption: this.$t('downloadFail'), // 下载失败，文件写失败
        downloadFailFileReadExecption: this.$t('downloadFail'), // 下载失败，文件读取失败
        downloadFailFileOpenExecption: this.$t('downloadFail') // 下载失败，文件打开失败
      },
      // 记录设备及通道的云升级状态数组--为了正确的判断重启中和升级成功  key: sn, value: 云升级状态数组
      upgradeStatusObj: {},
      devUpgradeObj: {}, //是否点击过升级按钮  key:sn  value: true/false
      upgradeVersionObj: {}, //记录升级的版本--点击升级时设备的版本，方便后续判断是否已经升级到最新版本了
      // 记录设备及通道的云升级进度数组--为了正确的判断下载的进度  key: sn, value: 进度
      upgradeProgressObj: {},
      // 当设备判断为重启中时，记录设备重启时间
      upgradeRestartTimeObj: {}
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
    // 存储云升级数据
    this.setUpgradeStatusStore({
      upgradeStatusObj: this.upgradeStatusObj,
      upgradeVersionObj: this.upgradeVersionObj,
      upgradeProgressObj: this.upgradeProgressObj,
      upgradeRestartTimeObj: this.upgradeRestartTimeObj
    })
  },
  async mounted() {
    appSetTitle(this.$t('upgrade'))
    const { bridgeType, appPlatform } = getParamsFromUserAgent()
    this.bridgeType = bridgeType
    this.appPlatform = appPlatform
    let json = {}
    if (bridgeType === 'superMax') {
      // superlive max走正常的网址解析
      json = getMaxUrlQuery(window.location.href)
    } else {
      json = getUrlQuery(window.location.href)
    }
    this.devId = json.devId
    this.bindState = json.bindState
    this.devInfo.devName = decodeURIComponent(decodeURIComponent(json.devName))
    this.devType = json.devType
    //1.DVR不支持通道云升级 2. ViewStation跟NVR协议一样，但是没有摄像机，需要区分开来
    if (this.devType == 3 || this.devType == 13) {
      this.isCameraSupportUpgrade = false
    }
    this.recordCamData = []
    // 获取暂存的数据
    await this.getUpgradeStatusStore()
    if (bridgeType === 'superMax') {
      await this.initCheckVersion() // superlive max进入云升级页面先调用检测更新--避免下载错误持续存在
    }
    // 之前superlive cloud等APP的逻辑
    this.getCloudUpgradeInfo()
    this.startTimer() //开启定时器
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    isShowUpgradeTip() {
      let stateList = [
        'waitingForUpgrade',
        'downloading',
        'downloadFail',
        'downloadNetException',
        'downloadFailNodeInvalid',
        'downloadFailOSSException',
        'downloadFailNodeDisconnect',
        'downloadFailFileWritExecption',
        'downloadFailFileReadExecption',
        'downloadFailFileOpenExecption',
        'downloadSuccess',
        'installing',
        'offLine'
      ]
      return !!stateList.includes(this.devInfo.state)
    },
    languageFlag() {
      return this.language == 'zh'
    },
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    statusColor() {
      return STATUS_COLOR_THEME(this.appStyleFlag, this.uiStyleFlag)
    }
  },
  methods: {
    back() {
      clearInterval(this.timer)
      this.timer = null
      // 存储云升级数据
      this.setUpgradeStatusStore({
        upgradeStatusObj: this.upgradeStatusObj,
        upgradeVersionObj: this.upgradeVersionObj,
        upgradeProgressObj: this.upgradeProgressObj,
        upgradeRestartTimeObj: this.upgradeRestartTimeObj
      })
      appBack()
    },
    // 查询NVR云升级信息
    getCloudUpgradeInfo() {
      let that = this
      const req = {
        devId: this.devId,
        url: 'getCloudUpgradeInfo',
        params: urlGetCloudUpgradeInfo()
      }
      // console.log('请求参数req', req)
      appRequestDevice(req, function (res) {
        // console.log('getCloudUpgradeInfo回调结果res', res)
        that.pageContent = res
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        that.errorCode = resData.code
        // console.log('获取解析的resData', resData)
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          // console.log(xmlObject.response.content, 'content')
          if (xmlObject.response.status == 'success') {
            // 处理设备
            let devInfo = xmlObject.response.content.devInfo
            that.devInfo.state = devInfo.state
            that.devInfo.version = devInfo.version
            that.devInfo.newVersionGUID = devInfo.newVersionGUID
            that.devInfo.progress = devInfo.progress ? parseInt(devInfo.progress / 100) + '%' : ''
            // 设备状态提示
            if (devInfo.state) {
              that.devInfo.showDevStatusText = true
              that.devInfo.devStatusColor = devInfo.state || ''
              that.devInfo.devStatusText = that.cloudUpgradeStateMap[devInfo.state] || ''
            } else {
              that.devInfo.showDevStatusText = false
              that.devInfo.devStatusColor = ''
              that.devInfo.devStatusText = ''
            }
            // 状态为下载中，下载成功，升级中的时候需要额外判断下是否记录了之前的版本，没有则加上--辅助判断升级成功
            if (['downloading', 'downloadSuccess', 'installing'].includes(devInfo.state)) {
              // console.log('devInfo.version', devInfo.version)
              if (!that.upgradeVersionObj[that.devId]) {
                that.upgradeVersionObj[that.devId] = that.devInfo.version
              }
            }
            // 没有当前版本或者当前版本和之前的版本不一致则认为是升级成功
            const version = that.upgradeVersionObj[that.devId] || null
            const upgradeFlag = version !== that.devInfo.version
            // 新的设备状态提示
            const newState = that.dealDevState(that.devId, devInfo.state, upgradeFlag)
            that.devInfo.newState = newState
            that.devInfo.newdevStatusText = that.newCloudUpgradeStateMap[newState] || ''
            // 新的设备进度
            const newProgress = that.dealDevProgress(
              that.devId,
              newState,
              devInfo.progress ? parseInt(devInfo.progress / 100) : ''
            )
            that.devInfo.newProgress = newProgress
            // console.log('newProgress', newProgress)
            // 使用新状态判断是否展示升级状态及进度
            if (newState == 'latest' || (devInfo.state == 'checkingVersion' && !devInfo.newVersionGUID)) {
              that.isDeviceNeedUpgrade = false
            } else {
              that.isDeviceNeedUpgrade = true
            }
            if (newState == 'latest' || newState === 'offLine') {
              // 最新版本/离线后点击升级按钮置为false
              that.devUpgradeObj[that.devId] = false
            }
            //版本信息 查看更新内容
            let newVersionInfo = xmlObject.response.content.newVersionInfo
            let versionArr = newVersionInfo.item
              ? Array.isArray(newVersionInfo.item)
                ? newVersionInfo.item
                : [newVersionInfo.item]
              : []
            let updateInfoArr = []
            versionArr.forEach(v => {
              updateInfoArr.push({
                newVersionGUID: v._id,
                newVersion: v.version || '',
                versionNote: v.versionNote || ''
              })
            })
            that.devNewVersionInfo = updateInfoArr
            // 设备的新版本
            let index = that.devNewVersionInfo.findIndex(v => v.newVersionGUID == that.devInfo.newVersionGUID)
            if (index != -1) {
              that.devInfo.newVersion = that.devNewVersionInfo[index].newVersion
              that.devInfo.versionNote = that.devNewVersionInfo[index].versionNote
            } else {
              that.devInfo.newVersion = ''
              that.devInfo.versionNote = ''
            }
            // 处理ipc
            let chlsInfo = xmlObject.response.content.chlsInfo
            // console.log('chlsInfo', chlsInfo)
            let resultList = []
            if (chlsInfo && chlsInfo.item) {
              if (chlsInfo.item.length === 0) {
                resultList = []
              } else if (!Array.isArray(chlsInfo.item)) {
                resultList = [chlsInfo.item]
              } else {
                resultList = chlsInfo.item
              }
              let resArr = resultList
              // 状态最后为checkingVersion的也不要显示
              let newArr = []
              resArr.forEach(v => {
                let flag = v.state === 'checkingVersion' && !v.newVersionGUID
                if (!flag) {
                  newArr.push(v)
                }
              })
              let cameraList = []
              cameraList = newArr.map(v => {
                const progress = v.progress
                const cameraInfo = {
                  ...v,
                  newVersion: v.newVersion
                    ? v.newVersion
                    : (v.state === 'installSuccess' && !v.newVersion) || v.state === 'latest'
                    ? that.$t('hasLatestVersion')
                    : '',
                  progress: v.progress ? parseInt(v.progress / 100) + '%' : '',
                  showIpcStatusText: !!that.cloudUpgradeStateMap[v.state],
                  IpcStatusText: that.cloudUpgradeStateMap[v.state] || '',
                  IpcStatusColor: v.state || '' //拿到颜色的key去匹配
                }
                // console.log('progress', progress)
                // 状态为下载中，下载成功，升级中的时候需要额外判断下是否记录了之前的版本，没有则加上--辅助判断升级成功
                if (['downloading', 'downloadSuccess', 'installing'].includes(v.state)) {
                  if (!that.upgradeVersionObj[v._id]) {
                    that.upgradeVersionObj[v._id] = v.version
                  }
                }
                // 没有当前版本或者当前版本和之前的版本不一致则认为是升级成功
                const version = that.upgradeVersionObj[v._id] || null
                const upgradeFlag = version !== v.version
                // 新的设备状态提示
                const newState = that.dealDevState(v._id, v.state, upgradeFlag)
                cameraInfo.newState = newState
                cameraInfo.newIpcStatusText = that.newCloudUpgradeStateMap[newState] || ''
                // 新的设备进度
                const newProgress = that.dealDevProgress(v._id, newState, progress ? parseInt(progress / 100) : '')
                cameraInfo.newProgress = newProgress
                // 是否展示状态
                cameraInfo.showIpcStatusText = that.cloudUpgradeStateMap[v.state] || false
                // 状态颜色
                cameraInfo.IpcStatusColor = newState || '' //拿到颜色的key去匹配
                return cameraInfo
              })
              // console.log('cameraList', cameraList)
              // 摄像机的数据匹配
              let realCameraArr = []
              cameraList.forEach(v => {
                let i = that.devNewVersionInfo.findIndex(item => item.newVersionGUID == v.newVersionGUID)
                if (i != -1) {
                  realCameraArr.push({
                    ...v,
                    newVersion: that.devNewVersionInfo[i].newVersion,
                    versionNote: that.devNewVersionInfo[i].versionNote
                  })
                } else {
                  realCameraArr.push({
                    ...v
                  })
                }
              })
              // console.log('realCameraArr', realCameraArr)
              // 过滤掉最新版本的
              that.cameraList = realCameraArr.filter(item => item.newState !== 'latest')
            } else {
              that.cameraList = resultList
            }
            that.cameraList.forEach(v => {
              let isShow = v.newState === 'latest' || (v.state === 'checkingVersion' && !v.newVersionGUID)
              if (!isShow) {
                let index = that.recordCamData.findIndex(item => item._id == v._id)
                if (index != -1) {
                  that.recordCamData.splice(index, 1, v)
                } else {
                  that.recordCamData.push(v)
                }
              }
            })
            // console.log(that.recordCamData, 'recordCamData')
            //可升级的 ipc通道
            that.isHasCameraUpgrade = that.cameraList.length !== 0 //如果都是最新 则不展示摄像机升级
            // 正常状态去除重启时间
            delete that.upgradeRestartTimeObj[that.devId]
          }
        } else if (resData.code == 10000 || resData.code == 604) {
          // 只是在升级过程中重启离线的情况会显示到升级状态里
          that.devInfo.showDevStatusText = true
          that.devInfo.state = 'offLine'
          that.devInfo.devStatusColor = 'offLine'
          that.devInfo.devStatusText = that.cloudUpgradeStateMap['offLine']
          that.devInfo.progress = '' //展示离线的时候 不展示进度
          // 升级过程中的离线定义为重启中
          // 新的设备状态提示
          const newState = that.dealDevState(that.devId, 'offLine')
          if (newState === 'restart') {
            // 重启中记录下重启时间
            if (!that.upgradeRestartTimeObj[that.devId]) {
              that.upgradeRestartTimeObj[that.devId] = new Date().getTime()
            }
          }
          that.devInfo.newState = newState
          const newdevStatusText = that.newCloudUpgradeStateMap[newState] || ''
          // 新的设备进度
          const newProgress = that.dealDevProgress(that.devId, newState, '')
          that.devInfo = {
            ...that.devInfo,
            newState,
            newdevStatusText,
            newProgress
          }
        }
      })
    },
    // 设备升级
    deviceUpgrade() {
      // 已经是最新版本则不响应事件
      if (!this.isDeviceNeedUpgrade) return
      if (!['newVersion', 'latest'].includes(this.devInfo.newState)) {
        return
      }
      // 点击升级按钮后，再次点击无效
      if (this.devUpgradeObj[this.devId]) {
        return
      }
      let that = this
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
      } else if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
      } else if (this.bindState == 0) {
        this.$refs.checkUserPwd.show = true // 用户名密码添加的设备
      } else {
        that.cloudUpgrade()
      }
    },
    // 输入用户名密码后的 设备升级
    devUpgrade(data) {
      this.$refs.checkUserPwd.show = false
      this.cloudUpgrade(true, data)
    },
    // 设备升级核心调用
    cloudUpgrade(type, data) {
      let that = this
      let params = urlCloudUpgrade(that.devInfo.newVersionGUID)
      if (type) {
        let dealPwd = sha1(md5(data.password).toUpperCase()).toUpperCase() //加密后的密码
        params = urlCloudUpgrade(that.devInfo.newVersionGUID, true, data.username, dealPwd)
      }
      let tips = this.diffTip(this.$t('upgradeTip'))
      this.$dialog
        .confirm(tips)
        .then(() => {
          const req = {
            devId: this.devId,
            url: 'cloudUpgrade',
            params: params
          }
          that.$toast.loading({
            // superlive max显示加载中文字
            message: that.bridgeType === 'superMax' ? that.$t('loadingText') : '',
            className: 'upgrade-check-loading',
            forbidClick: true,
            position: 'middle',
            duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
          })
          that.devUpgradeObj[that.devId] = true
          appRequestDevice(req, function (res) {
            that.$toast.clear()
            // console.log('设备云升级返回结果', res)
            const upgradeFlag = that.dealResult(res, 'device')
            // 判断云升级接口是否正常
            that.devUpgradeObj[that.devId] = upgradeFlag
            if (upgradeFlag) {
              // 云升级接口成功后，记录升级前的版本
              that.upgradeVersionObj[that.devId] = that.devInfo.version
            }
          })
        })
        .catch(() => {
          that.devUpgradeObj[that.devId] = false
        })
    },
    // 初始化检测更新--用来在初始化进入页面时检查设备状态，有错误则提示
    initCheckVersion() {
      let that = this
      const req = {
        devId: this.devId,
        url: 'checkVersion',
        params: urlCheckVersion()
      }
      appRequestDevice(req, function (res) {
        // console.log(res, '初始化检测更新')
        let resData = res.replace(/\\t|\\n/g, '')
        resData = JSON.parse(resData)
        if (resData.code == 200) {
          const xmlObject = transformXml(resData.body)
          that.pageContent = xmlObject
          if (xmlObject.response.status !== 'success') {
            const errorCode = xmlObject.response.errorCode
            if (errorCode) {
              that.$toast(that.$t(`errorCode.${errorCode}`))
              return
            }
          }
        } else if (resData.code) {
          // 10000、101001的 无法连接设备
          that.$toast(that.$t(`errorCode.${resData.code}`))
        }
      })
    },
    // 检测更新 先关闭定时器 检测完后再开启轮询
    checkVersion: debounce(function () {
      let that = this
      const req = {
        devId: this.devId,
        url: 'checkVersion',
        params: urlCheckVersion()
      }
      if (that.timer) {
        clearInterval(that.timer)
        that.timer = null
      }
      that.$toast.loading({
        // superlive max显示加载中文字
        message: that.bridgeType === 'superMax' ? that.$t('loadingText') : '',
        className: 'upgrade-check-loading',
        forbidClick: true,
        position: 'middle',
        duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
      })
      appRequestDevice(req, function (res) {
        // console.log(res, '检测更新')
        that.$toast.clear()
        that.startTimer() //开启定时器
        that.dealResult(res, '', that.$t('checkSuccess'), that.$t('checkFail'), true)
      })
    }, 100),
    // 查看更新内容
    viewUpdateContent(content) {
      this.showUpdateContent = true
      this.updateContent = content || this.$t('noData')
    },
    closePopup() {
      this.showUpdateContent = false
      setTimeout(() => {
        this.updateContent = ''
      }, 300) // 弹框动画api 默认是300ms 这里处理是滚动条位置初始化
    },
    // 兼容 样式的提示
    diffTip(word) {
      let tip = {
        className: 'upgrade-message',
        message: word,
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      if (this.style == 'UI1B') {
        tip['title'] = this.$t('tips')
      }
      return tip
    },
    // 摄像机升级
    upgradeCamera(item) {
      if (item.newState !== 'newVersion') return
      let that = this
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      }
      if (this.errorCode == 101001) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      }
      let tips = this.diffTip(this.$t('cameraUpgradeInfo'))
      this.$dialog
        .confirm(tips)
        .then(() => {
          const req = {
            devId: this.devId,
            url: 'cloudUpgradeNode',
            params: urlCloudUpgradeNode([item])
          }
          // console.log('请求通道升级', req)
          const { _id, version } = item
          appRequestDevice(req, function (res) {
            // console.log('通道升级返回res', res)
            const upgradeFlag = that.dealResult(res, 'cameara')
            if (upgradeFlag) {
              // 云升级接口成功后，记录升级前的版本
              that.upgradeVersionObj[_id] = version
            }
          })
        })
        .catch(() => {})
    },
    // 摄像机一键升级
    upgradeAllCamera() {
      let that = this
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      }
      if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      }
      let tips = this.diffTip(this.$t('cameraUpgradeInfo'))
      this.$dialog
        .confirm(tips)
        .then(() => {
          let list = this.cameraList
          const req = {
            devId: this.devId,
            url: 'cloudUpgradeNode',
            params: urlCloudUpgradeNode(list)
          }
          appRequestDevice(req, function (res) {
            const upgradeFlag = that.dealResult(res, 'allCamera')
            if (upgradeFlag) {
              // 云升级接口成功后，记录升级前的版本
              list.forEach(item => {
                const { _id, version } = item
                that.upgradeVersionObj[_id] = version
              })
            }
          })
        })
        .catch(() => {})
    },
    // 查询云升级 设备和通道信息的定时器
    startTimer() {
      let that = this
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        that.getCloudUpgradeInfo()
      }, 3000)
    },
    dealResult(res, type, successInfo, failInfo, isNoGet) {
      // console.log(res)
      let that = this
      let resData = res.replace(/\\t|\\n/g, '')
      resData = JSON.parse(resData)
      if (resData.code == 200) {
        const xmlObject = transformXml(resData.body)
        that.pageContent = xmlObject
        if (xmlObject.response.status == 'success') {
          if (successInfo) {
            that.$toast(successInfo)
          }
          if (!isNoGet) {
            that.getCloudUpgradeInfo()
          }
          return true
        } else {
          let errorCode = xmlObject.response.errorCode
          if (errorCode != '536870940' && type === 'device' && !that.isDeviceNeedUpgrade) {
            //提示 已是最新版本
            that.$toast(that.$t('hasLatestVersion'))
          } else if (errorCode != '536870940' && type === 'allCamera' && this.cameraList.length === 0) {
            //提示 未检测到可升级的摄像机
            that.$toast(that.$t('noCameraUpgrade'))
          } else if (errorCode) {
            that.$toast(that.$t(`errorCode.${errorCode}`))
          } else if (failInfo) {
            that.$toast(failInfo)
          }
          return false
        }
      } else if (resData.code) {
        if (resData.code === 206 && type === 'allCamera' && this.cameraList.length === 0) {
          //提示 未检测到可升级的摄像机
          that.$toast(that.$t('noCameraUpgrade'))
          return false
        }
        // 10000、604的 无法连接设备
        that.$toast(that.$t(`errorCode.${resData.code}`))
        return false
      }
    },
    // 获取云升级暂存数据--点击云升级退出后保存的云升级状态数据
    async getUpgradeStatusStore() {
      const that = this
      await new Promise(resolve => {
        const callback = data => {
          if (that.bridgeType === 'superMax') {
            // max支持输出日志
            appLog('log/info', `${new Date()} NVR云升级页面退出时保存的数据返回，结果是: ${data}`)
          }
          if (data) {
            const obj = JSON.parse(data)
            // console.log(`${new Date()} 暂存数据返回, 结果是: ${data}`)
            if (obj && obj.body) {
              // this.upgradeStatusObj = JSON.parse(obj.body) || {}
              const { upgradeStatusObj, upgradeVersionObj, upgradeProgressObj, upgradeRestartTimeObj } =
                JSON.parse(obj.body) || {}
              this.upgradeStatusObj = upgradeStatusObj || {}
              this.upgradeVersionObj = upgradeVersionObj || {}
              this.upgradeProgressObj = upgradeProgressObj || {}
              this.upgradeRestartTimeObj = upgradeRestartTimeObj || {}
            }
            resolve()
          }
        }
        getCacheData('upgradeStatusStore', callback)
      })
    },
    // 云升级页面退出时暂存数据
    setUpgradeStatusStore(data) {
      // console.log('data', data)
      setCacheData({ key: 'upgradeStatusStore', value: JSON.stringify(data) })
    },
    // 处理设备的新状态 -- sn 设备sn， state 设备状态  devInfo 设备详细信息
    dealDevState(sn, state, upgradeFlag = true) {
      // 针对离线和最新版本需要额外判断之前的状态
      let newState
      let statusSet = new Set(this.upgradeStatusObj[sn] || [])
      const upgradeVersionObj = { ...this.upgradeVersionObj }
      // console.log('this.upgradeStatusObj[sn]', this.upgradeStatusObj[sn])
      // 针对离线需要额外判断是否记录过重启时间，如果记录的重启时间跟当前时间超过5min则认为是离线
      let isOffLine = false
      const restartTime = this.upgradeRestartTimeObj[sn]
      if (restartTime) {
        const nowTime = new Date().getTime()
        const diffTime = nowTime - restartTime
        if (diffTime > 5 * 60 * 1000) {
          isOffLine = true
        }
      }
      switch (state) {
        case 'offLine':
          if (!isOffLine && statusSet.has('downloading')) {
            // 之前有下载中，则离线可以认为是重启中
            newState = 'restart'
            statusSet.add('restart')
          } else {
            // 否则就是正常的离线
            newState = 'offLine'
            statusSet.add('offLine')
          }
          break
        case 'latest':
          if (statusSet.has('downloading') && !statusSet.has('installSuccess') && upgradeFlag) {
            // 之前有下载中，且没有升级成功，且升级成功标志为true，则可以认为最新版本是升级成功
            newState = 'installSuccess'
            statusSet.add('installSuccess')
          } else {
            // 否则就是正常的已是最新版本
            newState = 'latest'
            // 已是最新版本则清空记录的状态
            statusSet = new Set()
            // 已是最新版本泽清空记录的版本
            delete upgradeVersionObj[sn]
          }
          break
        case 'installSuccess':
          if (statusSet.has('installSuccess')) {
            // 已经升级完成则显示最新版本
            newState = 'latest'
            statusSet.add('latest')
          } else {
            newState = 'installSuccess'
            statusSet.add('installSuccess')
          }
          break
        case 'newVersion':
          newState = state
          // 有新版本
          statusSet = new Set()
          // 有新版本则清空记录的版本
          delete upgradeVersionObj[sn]
          break
        case 'upgradeFail':
        case 'installFailNodeDisconnect':
        case 'installFailNodeInvalid':
          if (statusSet.has('installFail')) {
            // 已经提示过升级失败则显示最新版本
            newState = 'latest'
            statusSet.add('latest')
            // 最新版本则清空记录的版本
            delete upgradeVersionObj[sn]
          } else {
            newState = 'installFail'
            statusSet.add('installFail')
            // 升级失败则清空记录的版本
            delete upgradeVersionObj[sn]
          }
          break
        case 'downloadFail':
        case 'downloadNetException':
        case 'downloadFailNodeInvalid':
        case 'downloadFailOSSException':
        case 'downloadFailNodeDisconnect':
        case 'downloadFailFileWritExecption':
        case 'downloadFailFileReadExecption':
        case 'downloadFailFileOpenExecption':
          if (statusSet.has('downloadFail')) {
            // 已经提示过下载失败则显示最新版本
            newState = 'latest'
            statusSet.add('latest')
            // 最新版本则清空记录的版本
            delete upgradeVersionObj[sn]
          } else {
            newState = 'downloadFail'
            statusSet.add('downloadFail')
            // 升级失败则清空记录的版本
            delete upgradeVersionObj[sn]
          }
          break
        default:
          newState = state
          statusSet.add(state)
          break
      }
      // 更新设备/通道的云升级状态
      this.upgradeStatusObj[sn] = Array.from(statusSet)
      // 更新设备/通道的云升级版本信息
      this.upgradeVersionObj = { ...upgradeVersionObj }
      return newState
    },
    // 处理设备的进度
    dealDevProgress(sn, state, progress) {
      // // 进度条分为40-60-80-100分为四段
      // 进度条分为90-95-100分为三段
      // 下载中0-90 升级中95 重启中 进度不动 升级完成100
      let newProgress
      const upgradeProgressObj = { ...this.upgradeProgressObj }
      switch (state) {
        case 'waitingForUpgrade':
          // 待升级
          newProgress = 0
          break
        case 'downloading':
          if (progress) {
            // 下载中根据下载进度乘以0.9
            newProgress = Math.min(parseInt(progress * 0.9), 90)
          } else {
            newProgress = 0
          }
          upgradeProgressObj[sn] = newProgress
          break
        case 'downloadSuccess':
          newProgress = 90
          upgradeProgressObj[sn] = newProgress
          break
        case 'installing':
          newProgress = 95
          upgradeProgressObj[sn] = newProgress
          break
        case 'restart':
          // newProgress = 80
          // 重启中进度不变
          newProgress = upgradeProgressObj[sn] || 95 // 有记录的进度则用记录的进度，没有则默认使用升级完成的进度
          break
        case 'installSuccess':
          // 升级完成进度100
          newProgress = 100
          // 升级完成则清除之前记录的进度
          delete upgradeProgressObj[sn]
          break
        default:
          newProgress = 0
          // 其余情况则清除之前记录的进度
          delete upgradeProgressObj[sn]
          break
      }
      this.upgradeProgressObj = upgradeProgressObj
      return newProgress
    }
  }
}
</script>
<style lang="scss" scoped>
.upgrade-list {
  height: calc(100% - 70px);
  overflow: auto;
}
.title-button-disabled {
  color: var(--brand-bg-color-disabled, #d6e4fe);
  border: 1px solid var(--brand-bg-color-disabled, #d6e4fe);
}
.download-tip {
  color: #ff3a39;
}
</style>
