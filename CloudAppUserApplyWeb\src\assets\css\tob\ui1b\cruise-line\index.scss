.cruise-line-list {
    background-color: $vms-light-white;
}

.cruise-line-list .cruise-line-line:not(:last-child) {
    border-bottom: 1px solid $border-color;
}

.cruise-line-line .cruise-line-left .cruise-line-title {
    color: $vms-black;
}

.cruise-line-line .cruise-line-right .cruise-line-text {
    color: $vms-light-black2;
}

.cruise-line-footer {
    background-color: transparent;
}

.cruise-line-footer .footer-btn {
    background-color: $vms-primary;
}

.add-preset-point .preset-label {
    color: $vms-light-black2;
}

.add-preset-point .preset-add-icon {
    color: $vms-primary;
}

.cruise-line-wrapper .no-data .add-point-btn {
    color: $vms-primary;
    border: 1px solid $vms-primary;
}

.point-line-content .point-line-list {
    background-color: $vms-light-white;
}

.point-line-content .point-line-list .point-line-wrapper:not(:last-child) {
    border-bottom: 1px solid $border-color;
}

.point-line-list .point-line-wrapper .point-line-left .point-line-title {
    color: $vms-light-black3;
}

.point-line-list .point-line-wrapper .point-line-left .point-line-text {
    color: $vms-light-black2;
}


.cruise-point-list {
    background-color: $vms-light-white;
}

.cruise-point-list .cruise-point-line:not(:last-child) {
    border-bottom: 1px solid $border-color;
}

.cruise-point-line .cruise-point-left .cruise-point-title {
    color: $vms-black;
}

.cruise-point-line .cruise-point-right .cruise-point-text {
    color: $vms-light-black2;
}

.cruise-point-footer {
    background-color: transparent;
}

.cruise-point-footer .footer-btn {
    background-color: $vms-primary;
}

.choose-point-list {
    background-color: $vms-light-white;
}

.choose-point-list .choose-point-item {
    border-bottom: 1px solid $border-color;
}

.choose-point-list .choose-point-item .active-item {
    color: $vms-primary;
}