<template>
  <div class="line-bind-wrapper">
    <!-- <nav-bar @clickLeft="back"></nav-bar> -->
    <div class="line-bind-content">
      <div class="line-bind-img" v-if="bindStatus !== 0">
        <img
          class="bind-img"
          :src="
            require('@/assets/img/' +
              appStyleFlag +
              '/' +
              uiStyleFlag +
              (bindStatus === 1 ? '/bind_success.png' : '/bind_fail.png'))
          "
        />
      </div>
      <div class="bind-res-text">
        {{ bindStatus === 0 ? $t('binding') : bindStatus === 1 ? $t('bindSuccess') : $t('bindFail') }}
      </div>
    </div>
  </div>
</template>

<script>
// import NavBar from '@/components/NavBar'
import { appBack } from '@/utils/appbridge'
import { lineAppLink } from '@/api/lineBind'
import { mapState } from 'vuex'

export default {
  name: 'LineAuthBind',
  components: {
    // NavBar
  },
  props: {},
  data() {
    return {
      bindStatus: 0 // 绑定状态 0 绑定中 1 绑定成功 2 绑定失败
    }
  },
  created() {
    // appSetTitle(this.$t('lineAuthBind')) // 设置app的title
    // url的形式是：https://glbsit-sit-wh-ddc2-bus.starvisioncloud.com/userapp/?code=a1UXHWyBG3BTY5dIMM9Arg&state=00000190-2e2e-be51-0eac-93f628420000#/lineAuthBind
    // 需要特殊解析其中的code和state
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')
    const friendshipStatusChanged = urlParams.get('friendship_status_changed')
    if (code && state) {
      // line App返回了code和state，则发送绑定请求
      const params = {
        type: 1, // 目前只支持Line: 1
        code,
        state
      }
      // 只有当 friendshipStatusChanged 参数存在时才添加到 params
      if (friendshipStatusChanged !== null && friendshipStatusChanged !== undefined) {
        // 将字符串转换为布尔值
        // 'true' 或 '1' 会被转换为 true，其他值会被转换为 false
        params.friendshipStatusChanged = ['true', '1'].includes(friendshipStatusChanged.toLowerCase())
      }
      this.lineAppBind(params)
    }
  },
  mounted() {
    // this.lineAppBind()
  },
  computed: {
    ...mapState('app', ['style', 'language', 'appType']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    back() {
      appBack()
    },
    // 绑定Line App
    async lineAppBind(params) {
      this.$loading.show()
      try {
        await lineAppLink(params)
        this.bindStatus = 1
      } catch (err) {
        console.error(err)
        this.bindStatus = 2
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.line-bind-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .line-bind-content {
    width: 100%;
    height: calc(100% - 35px);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    margin-top: 35px;
    padding: 48px 0px;
    box-sizing: border-box;
    .line-bind-img .bind-img {
      width: 42px;
      height: 42px;
      margin-top: 14px;
    }
  }
}
</style>
