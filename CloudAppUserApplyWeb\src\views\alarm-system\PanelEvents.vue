<template>
  <div class="panel-events">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- DSC 标题 -->
      <div class="dsc-title">DSC</div>

      <!-- Events 区域 -->
      <div class="events-section">
        <div class="events-filter alarm-box-wrapper">
          <div class="events-title">{{ $t('events') }}</div>
          <!-- 选项卡 -->
          <div class="event-tabs">
            <div
              v-for="(tab, index) in eventTabs"
              :key="index"
              :class="['tab-item', { active: activeTab === tab.key }]"
              @click="switchTab(tab.key)"
            >
              {{ tab.label }}
            </div>
          </div>
        </div>
        <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
          <!-- 可滚动的日期分组容器 -->
          <div v-if="loading || eventGroups.length" class="events-scroll-container">
            <div v-for="(dateGroup, groupIndex) in eventGroups" :key="`date-group-${groupIndex}`" class="date-group">
              <!-- 日期显示 -->
              <div class="event-date alarm-sub-text">{{ dateGroup.dateLabel }}</div>

              <!-- 事件列表 -->
              <div class="event-list">
                <div
                  v-for="(event, index) in dateGroup.events"
                  :key="`event-${groupIndex}-${index}`"
                  class="event-item alarm-bottom-box"
                  @click="handleEventClick(event)"
                >
                  <div class="event-icon">
                    <theme-image class="event-indicator" :imageName="event.iconName" alt="event" />
                  </div>
                  <div class="event-content">
                    <div class="event-header">
                      <theme-image class="zone-icon" imageName="alarm-system/user.png" alt="zone" />
                      <div class="zone-info">{{ event.location }}</div>
                    </div>
                    <div class="event-description alarm-sub-text">{{ event.description }}</div>
                  </div>
                  <div class="event-time alarm-sub-second-text">{{ event.time }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-alarms">
            <span class="no-alarms-text">{{ $t('noAlarmsFound') }}</span>
          </div>
        </tvt-better-scroll>
      </div>
    </div>

    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="3" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import { getEventLog, getNotifications, getFaults, getPimaLog } from '@/api/alarmSystem'
export default {
  name: 'PanelEvents',
  components: {
    AlarmBottomNavigation
  },
  data() {
    return {
      loading: false,
      pullingStatus: 0,
      activeTab: 'all',
      eventTabs: [
        { key: 'all', label: this.$t('all') },
        { key: 'alarm', label: this.$t('alarms') },
        { key: 'failure', label: this.$t('failures') },
        { key: 'activitie', label: this.$t('activities') }
      ],
      // 格式化后的数据
      filterEventsList: []
    }
  },
  computed: {
    ...mapGetters('alarmSystem', [
      'siteId',
      'sessionId',
      'systemType',
      'isTycoSystem',
      'isRiscoSystem',
      'isPimaSystem',
      'canFetchPanelState',
      'alarmList'
    ]),
    eventGroups() {
      if (!this.filterEventsList.length) return []

      // 按日期分组的事件
      const groups = {}
      const today = this.$moment().format('YYYY-MM-DD')

      this.filterEventsList.forEach(alarm => {
        const alarmDate = this.$moment(alarm.logTime).format('YYYY-MM-DD')

        if (!groups[alarmDate]) {
          groups[alarmDate] = {
            date: alarmDate,
            dateLabel: alarmDate === today ? 'Today' : alarmDate,
            events: []
          }
        }

        groups[alarmDate].events.push(alarm)
      })

      // 按日期排序，最新的在前
      return Object.values(groups).sort((a, b) => this.$moment(b.date).valueOf() - this.$moment(a.date).valueOf())
    }
  },
  mounted() {
    // 获取事件列表
    this.getEventList()
  },
  methods: {
    // 下拉刷新处理
    pullingDown(callback) {
      this.getEventList().finally(() => {
        callback && callback()
      })
    },
    async getEventList() {
      if (!this.canFetchPanelState) {
        console.warn('Missing required data for fetching alarms')
        return
      }
      try {
        this.loading = true
        this.$loading.show()
        let functionName = `get${this.systemType}EventList`
        this[functionName]()
        await this[functionName]()
      } catch (error) {
        console.error('Mock data error:', error)
        await new Promise(resolve => setTimeout(resolve, 800))
        let mockData = {
          lineNumber: 0,
          logTime: '2020-07-08T14:41:27.714Z',
          eventText: 'string',
          eventName: 'string',
          eventDescriptorHint: 'string',
          sourceType: 0,
          sourceName: 'string',
          group: 0,
          groupName: 'alarm',
          sourceID: 0,
          reportStatus: 0,
          priority: 0,
          viUID: 'string',
          partAssociationCSV: 'string',
          eventId: 0,
          msd: { msdTag: 0, msdttl: '2020-07-08T14:41:27.714Z' }
        }
        this.filterEventsList = []
        for (let i = 0; i < 20; i++) {
          this.filterEventsList.push({
            rawData: mockData,
            id: i,
            location: this.extractLocation(mockData.eventName || mockData.eventText),
            description: this.extractDescription(mockData.eventName || mockData.eventText),
            time: this.formatEventTime(mockData.logTime),
            iconName: this.getEventIcon(mockData.groupName)
          })
        }
        this.handleApiError(error)
      } finally {
        this.loading = false
        this.$loading.hide()
      }
    },
    // 统一的错误处理
    handleApiError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
    },
    async getRiscoEventList() {
      const response = await getEventLog(this.siteId, {
        offset: 0,
        count: 100,
        sessionToken: this.sessionId
      })
      if (response.data && response.data.controlPanelEventsList && response.data.controlPanelEventsList.length > 0) {
        this.filterEventsList = response.data.controlPanelEventsList
          .filter(event => (this.activeTab !== 'all' ? event.groupName === this.activeTab : true))
          .map((event, index) => ({
            rawData: event,
            id: index,
            location: this.extractLocation(event.eventName || event.eventText),
            description: this.extractDescription(event.eventName || event.eventText),
            time: this.formatEventTime(event.logTime),
            iconName: this.getEventIcon(event.groupName)
          }))
      }
    },
    async getPimaEventList() {
      let response = []
      let isAll = this.activeTab === 'all' ? true : false
      switch (this.activeTab) {
        case 'all':
        case 'alarm':
          response = await getNotifications({ data: isAll })
          break
        case 'failure':
          response = await getFaults({ data: null })
          break
        case 'activitie':
          response = await getPimaLog({ data: null })
          break
      }
      if (response.data && Array.isArray(response.data)) {
        this.filterEventsList = response.data.map((notification, index) => ({
          id: index,
          rawData: notification
        }))
      }
    },
    // 提取位置信息
    extractLocation(eventText) {
      if (!eventText) return ''
      // 尝试提取位置信息，如 "Front Door"
      const match = eventText.match(/([A-Za-z\s]+Door|[A-Za-z\s]+Window|[A-Za-z\s]+Zone)/i)
      return match ? match[1].trim() : 'Unknown Location'
    },
    // 提取描述信息
    extractDescription(eventText) {
      if (!eventText) return 'Unknown Event'
      // 如果包含 "Burglary" 等关键词，返回对应描述
      if (eventText.toLowerCase().includes('burglary')) return 'Burglary Alarm'
      if (eventText.toLowerCase().includes('fire')) return 'Fire Alarm'
      if (eventText.toLowerCase().includes('panic')) return 'Panic Alarm'
      return eventText
    },
    // 格式化事件时间
    formatEventTime(logTime) {
      if (!logTime) return ''

      try {
        const date = new Date(logTime)
        return this.$moment(date).format('HH:mm')
      } catch (error) {
        console.error('Format time error:', error)
        return logTime
      }
    },
    switchTab(tabKey) {
      this.activeTab = tabKey
      this.getEventList()
    },
    getEventIcon(eventType) {
      const iconMap = {
        alarm: 'alarm-system/clock.png',
        failure: 'alarm-system/trouble.png',
        activity: 'alarm-system/activity.png'
      }
      return iconMap[eventType] || 'alarm-system/activity.png'
    },
    handleEventClick(event) {
      console.log('事件点击:', event)
      this.$toast(`查看事件: ${event.description}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-events {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    // 设置所有直接子元素的垂直间距
    > * {
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0; // 最后一个元素不需要底部间距
      }
    }

    .dsc-title {
      font-size: 16px;
      line-height: 44px;
      font-weight: 600;
      padding: 0 16px;
    }
  }

  .events-section {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0px;

    .events-filter {
      width: 100%;
      height: 80px;
      padding: 10px 16px 12px 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .events-title {
        height: 22px;
        font-size: 14px;
        line-height: 22px;
        font-weight: 500;
      }
    }
    .tvt-better-scroll {
      flex: 1;
    }

    .events-scroll-container {
      padding: 0px 16px;
    }
  }

  .event-tabs {
    width: 100%;
    height: 28px;
    border-radius: 6px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;

    .tab-item {
      flex: 1;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Regular, sans-serif;
      font-weight: 400;
      font-size: 14px;
      text-align: center;
    }
  }

  .date-group {
    .event-date {
      width: 100%;
      height: 40px;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .event-list {
    .event-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      .event-icon {
        margin-right: 12px;

        .event-indicator {
          width: 32px;
          height: 32px;
        }
      }

      .event-content {
        flex: 1;
        width: 0px;
        .event-header {
          display: flex;
          align-items: center;
          margin-bottom: 3px;

          .zone-icon {
            width: 20px;
            height: 20px;
            margin-right: 6px;
          }

          .zone-info {
            flex: 1;
            width: 0px;
            height: 18px;
            font-family: PingFangSC-Regular, sans-serif;
            font-weight: 400;
            font-size: 12px;
            line-height: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .event-description {
          height: 18px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .event-time {
        height: 18px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        flex-shrink: 0;
      }

      &:hover {
        background-color: #333;
      }
    }
  }
}
</style>
