import { getToken } from '@/utils/basic.js'

export default {
  namespaced: true,
  state: () => ({
    version: '',
    style: '',
    language: '',
    appType: '',
    appName: '',
    token: '',
    sid: '',
    appPlatform: '',
    bridgeType: '',
    imageCache: {} // 使用对象而不是Map，因为Vuex的state需要是普通对象
  }),
  getters: {},
  mutations: {
    SET_APP_INFO(state, data) {
      state.appPlatform = data.appPlatform
      state.version = data.version
      state.style = data.style
      state.language = data.language
      state.appName = data.appName
      state.appType = data.appType
      state.bridgeType = data.bridgeType
      state.appId = data.appId
    },
    SET_TOKEN(state, data) {
      state.token = data
    },
    SET_SID(state, data) {
      state.sid = data
    },
    SET_IMAGE_CACHE(state, { key, value }) {
      state.imageCache[key] = value
    }
  },
  actions: {
    getToken({ commit }) {
      return getToken().then(res => {
        let resData = JSON.parse(res)
        let token = ''
        let sid = ''
        if (resData && resData.body) {
          token = resData.body.token
          sid = resData.body.aesKey
        }
        // console.log(token, '获取到的token')
        // console.log(sid, '获取到的sid')
        commit('SET_TOKEN', token)
        if (sid) {
          commit('SET_SID', atob(sid))
        }
      })
    }
  }
}
