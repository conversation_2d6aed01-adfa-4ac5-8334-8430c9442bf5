<template>
  <div class="alarms-page">
    <!-- 顶部导航 -->
    <nav-bar :title="$t('alarms')" @clickLeft="goBack" />

    <!-- 页面内容 -->
    <div class="alarms-content">
      <!-- Action 模块 - 仅在Pima和Tyco系统中显示 -->
      <div v-if="isTycoSystem || isPimaSystem" class="action-section alarm-box-wrapper">
        <div class="action-title">{{ $t('action') }}</div>
        <div class="action-buttons">
          <!-- Tyco系统显示两个按钮 -->
          <template v-if="isTycoSystem">
            <button class="siren-btn" @click="handleActivateSiren" :disabled="loading">
              {{ $t('activateSiren') }}
            </button>
            <button class="siren-btn" @click="handleMuteSiren" :disabled="loading">
              {{ $t('muteSiren') }}
            </button>
          </template>

          <!-- Pima系统只显示一个按钮 -->
          <template v-else-if="isPimaSystem">
            <button class="siren-btn" @click="handleMuteSiren" :disabled="loading">
              {{ $t('muteSiren') }}
            </button>
          </template>
        </div>
      </div>
      <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
        <div v-if="loading || groupedAlarms.length" class="alarms-groups">
          <!-- 按日期分组显示 -->
          <div v-for="group in groupedAlarms" :key="group.date" class="date-group">
            <div class="date-header">{{ group.dateLabel }}</div>
            <div class="alarms-list">
              <div
                v-for="alarm in group.alarms"
                :key="alarm.id"
                class="alarm-item alarm-box-wrapper"
                @click="handleAlarmClick(alarm)"
              >
                <div class="alarm-icon">
                  <theme-image class="alarm-indicator" imageName="alarm-system/alarm.png" alt="alarm" />
                </div>
                <div class="alarm-content">
                  <div class="alarm-location">{{ alarm.location }}</div>
                  <div class="alarm-description">{{ alarm.description }}</div>
                </div>
                <div class="alarm-right">
                  <div class="alarm-icon-btn">
                    <theme-image class="alarm-live" imageName="alarm-system/alarm_live.png" alt="alarm" />
                  </div>
                  <div class="alarm-icon-btn">
                    <theme-image class="alarm-playback" imageName="alarm-system/alarm_playback.png" alt="alarm" />
                  </div>
                  <div class="alarm-time">{{ alarm.time }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-alarms">
          <span class="no-alarms-text">{{ $t('noAlarmsFound') }}</span>
        </div>
      </tvt-better-scroll>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'
import { setMuteSiren } from '@/api/alarmSystem'
export default {
  name: 'Alarms',

  data() {
    return {
      loading: false,
      pullingStatus: 0
    }
  },

  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo']),
    ...mapGetters('alarmSystem', [
      'siteId',
      'sessionId',
      'systemType',
      'isTycoSystem',
      'isRiscoSystem',
      'isPimaSystem',
      'canFetchPanelState',
      'alarmList'
    ]),
    // 按日期分组的警报
    groupedAlarms() {
      if (!this.alarmList.length) return []

      const groups = {}
      const today = this.$moment().format('YYYY-MM-DD')

      this.alarmList.forEach(alarm => {
        const alarmDate = this.$moment(alarm.rawData.logTime).format('YYYY-MM-DD')

        if (!groups[alarmDate]) {
          groups[alarmDate] = {
            date: alarmDate,
            dateLabel: alarmDate === today ? 'Today' : alarmDate,
            alarms: []
          }
        }

        groups[alarmDate].alarms.push({
          ...alarm,
          time: this.formatEventTime(alarm.rawData.logTime),
          location: this.extractLocation(alarm.rawData.eventText),
          description: this.extractDescription(alarm.rawData.eventText)
        })
      })

      // 按日期排序，最新的在前
      return Object.values(groups).sort((a, b) => this.$moment(b.date).valueOf() - this.$moment(a.date).valueOf())
    }
  },

  mounted() {
    // 获取警报列表
    this.fetchAlarmData()
  },

  methods: {
    ...mapActions('alarmSystem', ['fetchAlarms']),
    // 返回上一页
    goBack() {
      this.$router.back()
    },
    // 下拉刷新处理
    pullingDown(callback) {
      this.fetchAlarmData(true).finally(() => {
        callback && callback()
      })
    },
    // 获取报警数据
    async fetchAlarmData(forceRefresh = false) {
      // 如果有缓存数据且不是强制刷新，直接使用缓存
      if (!forceRefresh && this.alarmList.length) {
        return
      }
      if (!this.canFetchPanelState) {
        console.warn('Missing required data for fetching alarms')
        return
      }
      try {
        this.loading = true
        this.$loading.show()
        await this.fetchAlarms({
          siteId: this.siteId,
          sessionToken: this.sessionId,
          systemType: this.systemType,
          count: 100
        })
      } catch (error) {
        console.error('Fetch alarms failed:', error)
        this.handleAlarmError(error)
      } finally {
        this.loading = false
        this.$loading.hide()
      }
    },
    // 统一的错误处理
    handleAlarmError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
    },
    // 提取位置信息
    extractLocation(eventText) {
      if (!eventText) return ''
      // 尝试提取位置信息，如 "Front Door"
      const match = eventText.match(/([A-Za-z\s]+Door|[A-Za-z\s]+Window|[A-Za-z\s]+Zone)/i)
      return match ? match[1].trim() : 'Unknown Location'
    },

    // 提取描述信息
    extractDescription(eventText) {
      if (!eventText) return 'Unknown Event'
      // 如果包含 "Burglary" 等关键词，返回对应描述
      if (eventText.toLowerCase().includes('burglary')) return 'Burglary Alarm'
      if (eventText.toLowerCase().includes('fire')) return 'Fire Alarm'
      if (eventText.toLowerCase().includes('panic')) return 'Panic Alarm'
      return eventText
    },

    // 格式化事件日期
    formatEventDate(logTime) {
      if (!logTime) return ''

      try {
        const date = new Date(logTime)
        return this.$moment(date).format('DD/MM/YYYY')
      } catch (error) {
        console.error('Format date error:', error)
        return ''
      }
    },

    // 格式化事件时间
    formatEventTime(logTime) {
      if (!logTime) return ''

      try {
        const date = new Date(logTime)
        return this.$moment(date).format('HH:mm:ss')
      } catch (error) {
        console.error('Format time error:', error)
        return logTime
      }
    },

    // 处理警报点击
    handleAlarmClick(alarm) {
      console.log('Alarm clicked:', alarm)
      this.$toast(`查看警报: ${alarm.description}`)
    },

    // 处理激活警报器 (仅Tyco系统)
    handleActivateSiren() {
      console.log('Activate siren clicked')
      this.$toast(this.$t('sirenActivateSuccess'))
      // 这里可以添加实际的激活警报器API调用
      // 例如：await activateSirenAPI(this.siteLoginInfo.siteId, this.siteLoginInfo.sessionId)
    },
    // 处理静音警报器
    async handleMuteSiren() {
      this.$loading.show()
      try {
        if (this.isPimaSystem) {
          await setMuteSiren({ data: null })
        }
        this.$toast(this.$t('sirenMuteSuccess'))
      } catch (error) {
        this.handleApiError(error)
      } finally {
        this.$loading.hide()
      }
    },
    // 统一的API错误处理
    handleApiError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
      // 记录详细错误信息用于调试
      console.error('API Error Details:', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.alarms-page {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .page-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;

      .back-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .page-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .alarms-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
    padding: 0;

    .action-section {
      padding: 20px 15px 14px 15px;

      .action-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        margin-left: 11px;
        margin-bottom: 5px;
      }

      .action-buttons {
        display: flex;
        justify-content: space-between;
        gap: 15px;
        .siren-btn {
          flex: 1;
          padding: 5px 24px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 400;
          cursor: pointer;
          min-width: 120px;
        }
      }
    }

    .no-alarms {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;

      .no-alarms-text {
        font-size: 14px;
        color: #ccc;
      }
    }

    .tvt-better-scroll {
      flex: 1;
    }
    .alarms-groups {
      .date-group {
        .date-header {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          line-height: 22px;
          padding: 8px 0;
        }

        .alarms-list {
          .alarm-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            margin-bottom: 1px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;

            .alarm-icon {
              margin-right: 5px;

              .alarm-indicator {
                width: 38px;
                height: 38px;
              }
            }

            .alarm-content {
              flex: 1;
              width: 0px;
              .alarm-location {
                margin-bottom: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .alarm-description {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .alarm-right {
              display: flex;
              align-items: center;
              .alarm-icon-btn {
                margin-right: 12px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
