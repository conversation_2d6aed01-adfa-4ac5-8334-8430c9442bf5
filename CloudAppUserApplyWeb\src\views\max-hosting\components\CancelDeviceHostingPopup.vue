<template>
  <div>
    <van-popup v-model="visible" position="bottom" round :close-on-click-overlay="false" get-container="#app">
      <div class="site-hosting-cancel-pop-container">
        <div class="header">
          <span class="title">
            {{ $t('cancelTrusteeship') }}
          </span>
        </div>
        <div class="content">
          {{ $t('cancelDeviceHostingTip') }}
        </div>
        <div class="footer-btn">
          <div class="stop-btn" @click="visible = false">
            {{ $t('notNow') }}
          </div>
          <div class="cancel-btn" @click="cancelHosting">
            {{ $t('stillCancel') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { cancelDevice } from '@/api/maxHosting'

export default {
  name: 'CancelDeviceHostingPopup',
  props: {
    siteId: {
      type: [String, Number],
      default: ''
    },
    deviceId: {
      type: [String, Number],
      default: ''
    },
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      errorVisible: false
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    async cancelHosting() {
      try {
        await cancelDevice(
          {
            sn: this.deviceId
          },
          {
            autoErrorMsg: false
          }
        )
        this.visible = false
        this.$toastSuccess(this.$t('cancelled'))
        this.$router.push(`/max/hosting/service-detail/${this.userId}`)
      } catch (error) {
        if ([34204, 34205, 34206, 34207, 34208, 34209].includes(error.basic.code)) {
          this.$toast(this.$t('noPermissions'))
        } else {
          this.$toast(this.$t(`errorCode.${error.basic.code}`))
        }
      }
    }
  }
}
</script>

<style lang="scss">
.site-hosting-cancel-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 52px;
    padding: 16px;
    .title {
      color: var(--text-color-secondary, #3d3c3c);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
  }
  .content {
    width: 100%;
    box-sizing: border-box;
    padding: 0 25px;
    word-break: break-all;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-secondary, #3d3c3c);
    font-family: 'PingFang SC';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin: 36px 0 20px;
    &.high-padding {
      padding-bottom: 80px;
    }
  }
  .footer-btn {
    display: flex;
    height: 52px;
    .stop-btn {
      flex: 1;
      height: 52px;
      line-height: 52px;
      text-align: center;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
    }
    .cancel-btn {
      flex: 1;
      height: 52px;
      line-height: 52px;
      text-align: center;
      color: var(--error-bg-color-default, #ff3b30);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
    }
  }
}
</style>
