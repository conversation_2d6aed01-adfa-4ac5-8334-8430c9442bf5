<template>
  <div class="channel-list-content">
    <div class="channel-list">
      <van-cell
        v-for="(item, index) of curChannelList"
        clickable
        :key="`${item.sn}~${item.chlIndex}~${item.chlId}`"
        :title="`${item.name}`"
        @click.stop.native="e => handleClick(e, item, index)"
      >
        <template #title>
          <div class="channel-cell-wrapper">
            <div class="channel-checkbox-content">{{ item.name }}</div>
          </div>
        </template>
      </van-cell>
    </div>
  </div>
</template>

<script>
export default {
  name: 'detailChannel',
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {},
  props: {
    channelList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      curChannelList: [] // 展示的通道
    }
  },
  watch: {
    channelList: {
      handler(val) {
        if (val && JSON.stringify(val) !== JSON.stringify(this.curChannelList)) {
          this.curChannelList = val.slice()
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleClick(e, item, index) {
      // 阻止冒泡事件
      e.stopPropagation()
      this.$emit('click', item, index)
    }
  }
}
</script>

<style lang="scss" scoped>
.van-hairline--top-bottom::after {
  border-width: 0px;
}
// .van-hairline-unset--top-bottom::after {
//   border-width: 0px;
// }
.channel-list {
  .van-cell {
    padding: 0px;
  }
}
.channel-list-content {
  width: 100%;
  height: calc(100%);
  box-sizing: border-box;
  font-family: 'PingFang SC', PingFangSC-Regular, sans-serif;
}
</style>
<style lang="scss">
.van-collapse-item__content {
  padding: 2px 0px !important;
}
.channel-cell-wrapper {
  width: 100%;
  height: 52px;
  padding: 15px 0px;
  box-sizing: border-box;
  display: flex;
  .van-checkbox {
    margin-right: 10px;
  }
  img {
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }
}
.channel-checkbox-content {
  flex: 1;
}
</style>
