﻿<template>
  <div class="alarm-bottom-box output-device-item">
    <div class="device-icon">
      <theme-image imageName="alarm-system/outputs_light.png" :alt="output.name" class="output-icon" />
    </div>
    <div class="device-info">
      <div class="device-name text-over-ellipsis">{{ output.name }}</div>
      <div class="alarm-sub-text device-status">{{ statusText }}</div>
    </div>
    <div class="device-actions">
      <van-popover v-model="showPopover" placement="bottom-end" :offset="[-10, 0]" trigger="click">
        <ul class="action-btn output-device-popover">
          <li v-for="action in actions" :key="action.key" @click="handleAction(action.key)">
            {{ action.label }}
          </li>
        </ul>
        <template #reference>
          <div class="power-switch">
            <theme-image imageName="alarm-system/outputs_off.png" class="output-icon" />
          </div>
        </template>
      </van-popover>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OutputDevice',
  props: {
    output: {
      type: Object,
      required: true,
      validator(value) {
        return value && value.id != null && typeof value.name === 'string' && typeof value.isOn === 'boolean'
      }
    }
  },
  emits: ['action'],
  data() {
    return {
      showPopover: false
    }
  },
  computed: {
    // 计算状态文本
    statusText() {
      return this.output.status || (this.output.isOn ? this.$t('on') : this.$t('off'))
    },
    // 动作配置
    actions() {
      return [
        { key: 'off', label: this.$t('off') },
        { key: 'on', label: this.$t('on') },
        { key: 'rename', label: this.$t('rename') }
      ]
    }
  },
  methods: {
    // 处理操作事件
    handleAction(action) {
      // 关闭popover
      this.showPopover = false
      // 触发action事件
      this.$emit('action', {
        output: this.output,
        action: action
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.output-device-item {
  height: 64px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0px 20px 0px 16px;
  &:last-child {
    border-bottom: none;
  }

  .device-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .output-icon {
      width: 32px;
      height: 32px;
    }
  }

  .device-info {
    flex: 1;
    min-width: 0;

    .device-name {
      width: 100%;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
    }

    .device-status {
      height: 22px;
      line-height: 22px;
      font-size: 12px;
    }
  }

  .device-actions {
    display: flex;
    align-items: center;

    .power-switch {
      width: 24px;
      height: 24px;
      cursor: pointer;
      padding: 4px;
    }
  }
}

.action-btn {
  width: 190px;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;

  li {
    padding: 12px 16px;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
