<template>
  <!-- 解除绑定确定弹窗 -->
  <van-popup
    v-model="showReject"
    round
    position="bottom"
    :style="{ height: '224px' }"
    class="reject-transfer-dialog"
    @click-overlay="cancelRject"
  >
    <popup-confirm
      :title="$t('rejectDeviceTransfer')"
      :text="$t('rejectTransfetDesc')"
      :cancelText="$t('notNow')"
      :confirmText="$t('reject')"
      @cancel="cancelRject"
      @confirm="confirmReject"
    />
  </van-popup>
</template>
<script>
import { postDataToH5, closeDialog } from '@/utils/appbridge'
import PopupConfirm from '@/components/PopupConfirm.vue'

export default {
  name: 'rejectTransferPopup',
  components: {
    PopupConfirm
  },
  props: {},
  data() {
    return {
      showReject: true // 解除绑定
    }
  },
  created() {},
  mounted() {},
  computed: {},
  methods: {
    // 取消解除绑定
    cancelRject() {
      // 发送操作结果给托管管理页面
      postDataToH5({
        observerName: 'rejectTransferPopup',
        data: { operate: 'cancel' }
      })
      closeDialog()
    },
    // 确定解除绑定
    confirmReject() {
      // 发送操作结果给托管管理页面
      postDataToH5({
        observerName: 'rejectTransferPopup',
        data: { operate: 'confirm' }
      })
      closeDialog()
    }
  }
}
</script>
<style lang="scss">
html body #app:has(.app-container .reject-transfer-dialog) {
  background-color: var(--bg-color-dialogs, #0000001a);
}
.app-container:has(.reject-transfer-dialog) {
  background-color: transparent;
}
</style>
