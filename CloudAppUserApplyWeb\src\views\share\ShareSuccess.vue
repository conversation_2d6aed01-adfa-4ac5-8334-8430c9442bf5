<template>
  <div class="share-success-wrapper">
    <div class="share-success-content">
      <div class="share-success-title">{{ $t('shareSuccess') }}</div>
      <div class="share-success-text">
        <div class="share-success-desc">{{ $t('shareToUser') }}</div>
        <div class="share-success-desc">{{ sharer }}</div>
      </div>
      <div class="share-device-img">
        <theme-image alt="shareDevice" imageName="device_success.png" />
      </div>
    </div>
    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('finish') }}
      </van-button>
    </div>
  </div>
</template>
<script>
import { appClose } from '@/utils/appbridge'
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'ShareSuccess',
  components: {
    ThemeImage
  },
  props: {},
  data() {
    return {
      sharer: '', // 分享人的联系方式 邮箱或手机
      shareDeviceIcon: null // 设备分享ICON
    }
  },
  created() {
    // 根据shareUser确定当前分享的联系方式
    const { active, email, mobile } = this.shareUser
    if (active === 'email') {
      this.sharer = email
    } else {
      this.sharer = mobile
    }
  },
  mounted() {},
  computed: {
    ...mapState('share', ['shareUser'])
  },
  methods: {
    // 确定
    handleConfirm() {
      // 直接退出H5
      appClose()
    }
  }
}
</script>
<style lang="scss" scoped>
.share-success-wrapper {
  height: 100%;
  overflow: hidden;
  .share-success-content {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 124px 36px 0px 36px;
    .share-success-title {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: bold;
      line-height: 24px;
    }
    .share-success-text {
      width: 100%;
      margin: 16px 0px;
    }
    .share-success-desc {
      width: 100%;
      text-align: center;
      font-family: 'Source Han Sans CN', sans-serif;
      font-size: var(--font-size-body2-size, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .share-device-img {
      width: 100%;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      .theme-image-container {
        width: 303px;
        height: 120px;
      }
    }
  }
}
</style>
