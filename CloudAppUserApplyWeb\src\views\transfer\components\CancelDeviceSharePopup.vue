<template>
  <div>
    <van-popup
      :value="visible"
      @change="value => $emit('update:visible', value)"
      position="bottom"
      round
      :close-on-click-overlay="false"
      get-container="#app"
    >
      <div class="site-hosting-cancel-pop-container">
        <div class="header">
          <span class="title">
            {{ $t('rejectShareTitle') }}
          </span>
        </div>
        <div class="content">
          {{ $t('rejectShareTips') }}
        </div>
        <div class="footer-btn">
          <div class="stop-btn" @click="$emit('update:visible', false)">
            {{ $t('notNow') }}
          </div>
          <div class="cancel-btn" @click="cancelHosting">
            {{ $t('stillReject') }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'cancelDeviceSharePopup',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      errorVisible: false
    }
  },
  methods: {
    async cancelHosting() {
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss">
.site-hosting-cancel-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 52px;
    padding: 16px;
    .title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: var(--font-size-body1-size, 16px);
      line-height: 24px;
      letter-spacing: 0%;
      text-align: center;
      vertical-align: middle;
    }
  }
  .content {
    padding-top: 8px;
    padding-right: 32px;
    padding-bottom: 24px;
    padding-left: 32px;

    font-family: PingFang SC;
    font-weight: 400;
    font-size: var(--font-size-body1-size, 16px);
    line-height: 24px;
    color: var(--text-color-primary, #1a1a1a);
  }
  .footer-btn {
    display: flex;
    padding: 0 20px;

    .stop-btn {
      flex: 1;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: var(--icon-color-secondary, #646568);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      background: #f4f5fb;
      border-radius: 20px;
      margin-right: 8px;
    }
    .cancel-btn {
      flex: 1;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: var(--error-bg-color-default, #f74336);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      background: #fff5f5;
      border-radius: 20px;
    }
  }
}
</style>
