//获取当前通道下所有的预置点列表
export const urlChlPresetList = chlId => {
  const xml = '<condition><chlId>' + chlId + '</chlId></condition>'
  return xml
}

// 添加预置点
export const urlCreateChlPreset = (index, name, chlId) => {
  const xml = `<content><index>${index}</index><name><![CDATA[${name}]]></name><chlId>${chlId}</chlId></content>`
  return xml
}

// IPC相关
const OCX_XML_Header = '<?xml version="1.0" encoding="UTF-8"?>'
const Font = '<config xmlns="http://www.ipc.com/ver10" version="1.7">'
const Back = '</config>'
const OCX_XML_Config = Font + Back

// 获取IPC的所有预置点列表
export const urlPtzGetPresets = () => {
  const result = OCX_XML_Header + OCX_XML_Config
  return result
}

//  添加预置点
export const urlPtzModifyPresetPosition = index => {
  let result = OCX_XML_Header
  result += Font
  result += '<presetInfo>' + '<id>' + index + '</id>' + '</presetInfo>'
  result += Back
  return result
}
