# localStorage认证缓存功能说明

## 功能概述

为Risco系统的认证信息添加localStorage缓存机制，在开发环境下提供更好的开发体验，避免频繁重新登录。

## 核心特性

### 🔄 自动缓存
- **用户登录时**：自动将Risco登录信息保存到localStorage
- **面板登录时**：自动将站点登录信息保存到localStorage
- **退出登录时**：自动清除所有localStorage缓存

### ⏰ 智能过期
- **开发环境**：缓存7天，提供最佳开发体验
- **生产环境**：缓存1小时，平衡便利性和安全性
- **自动清理**：过期数据自动删除，不占用存储空间

### 🔧 智能恢复
- **API拦截器**：当store中没有认证信息时，自动从localStorage恢复
- **无缝体验**：页面刷新后无需重新登录
- **调试友好**：恢复时在控制台输出日志信息

## 技术实现

### 文件结构
```
src/
├── utils/
│   ├── authStorage.js          # 认证缓存工具函数
│   └── authStorage.test.js     # 功能测试文件
├── api/
│   └── alarmSystem.js          # API拦截器（已修改）
├── store/modules/
│   └── alarmSystem.js          # Store模块（已修改）
└── views/alarm-system/
    └── PanelSettings.vue       # 设置页面（已修改）
```

### 核心API

#### 保存认证信息
```javascript
import { saveRiscoLoginToStorage, saveSiteLoginToStorage } from '@/utils/authStorage'

// 保存Risco登录信息
saveRiscoLoginToStorage(riscoLoginInfo)

// 保存站点登录信息
saveSiteLoginToStorage(siteLoginInfo)
```

#### 获取认证信息
```javascript
import { getRiscoLoginFromStorage, getSiteLoginFromStorage } from '@/utils/authStorage'

// 获取Risco登录信息
const riscoInfo = getRiscoLoginFromStorage()

// 获取站点登录信息
const siteInfo = getSiteLoginFromStorage()
```

#### 清除缓存
```javascript
import { clearAllAuthStorage } from '@/utils/authStorage'

// 清除所有认证缓存
clearAllAuthStorage()
```

## 使用场景

### 开发环境优化
- **页面刷新**：无需重新登录，直接使用缓存的认证信息
- **浏览器重启**：7天内无需重新输入登录凭据
- **多标签页**：不同标签页共享认证状态

### 生产环境安全
- **短期缓存**：1小时过期时间，减少安全风险
- **自动清理**：过期数据自动删除
- **错误安全**：localStorage操作失败不影响正常功能

## 安全考虑

### 数据保护
- **过期机制**：设置合理的过期时间
- **自动清理**：过期数据自动删除
- **错误处理**：localStorage操作异常不影响主流程

### 隐私保护
- **本地存储**：数据仅存储在用户本地浏览器
- **用户控制**：用户可以手动清除浏览器数据
- **透明性**：开发者工具中可以查看存储的数据

## 调试指南

### 查看缓存数据
1. 打开浏览器开发者工具
2. 进入Application/Storage标签页
3. 展开Local Storage
4. 查看`risco_login_cache`和`site_login_cache`项

### 控制台日志
当从localStorage恢复认证信息时，会在控制台输出：
```
🔄 从localStorage恢复Risco登录信息
🔄 从localStorage恢复站点登录信息
```

### 手动测试
运行测试文件中的代码：
```javascript
// 在浏览器控制台中执行
// 参考 src/utils/authStorage.test.js
```

## 故障排除

### 常见问题

**Q: 为什么刷新页面后还是需要重新登录？**
A: 检查浏览器是否禁用了localStorage，或者缓存是否已过期。

**Q: 如何清除所有缓存数据？**
A: 可以在控制台执行`localStorage.clear()`或使用`clearAllAuthStorage()`函数。

**Q: 生产环境缓存时间太短怎么办？**
A: 出于安全考虑，生产环境缓存时间设置为1小时。如需调整，请修改`authStorage.js`中的`CACHE_DURATION`常量。

### 错误处理
所有localStorage操作都包含try-catch错误处理，操作失败时会在控制台输出警告信息，但不会影响应用的正常运行。

## 更新日志

### v1.0.0 (2024-01-29)
- ✅ 实现localStorage认证缓存功能
- ✅ 添加智能过期机制
- ✅ 集成API拦截器自动恢复
- ✅ 更新Store模块保存逻辑
- ✅ 优化退出登录清理流程
- ✅ 添加开发调试支持