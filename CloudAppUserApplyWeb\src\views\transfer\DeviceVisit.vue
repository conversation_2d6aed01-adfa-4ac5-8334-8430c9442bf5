<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft" :title="$t('deviceVisitTitle')"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="detail-content">
        <div class="visit-tip">
          {{ $t('deviceVisitTip') }} <span>"{{ deviceName }}"</span>
        </div>
        <div class="card">
          <installer-info :data="detail" />
        </div>
      </div>
    </tvt-better-scroll>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import InstallerInfo from '@/views/max-hosting/components/InstallerInfo'
import { appBack } from '@/utils/appbridge'
import { getInstallerInfo } from '@/api/maxHosting'
import { formatInstallerInfo } from '@/views/max-hosting/common'
export default {
  name: 'deviceVisit',
  components: {
    NavBar,
    InstallerInfo
  },
  data() {
    return {
      pullingStatus: 0,
      installerUserId: '',
      deviceName: '',
      detail: {}
    }
  },
  created() {
    const { installerUserId, deviceName } = this.$route.query
    this.deviceName = deviceName || ''
    if (installerUserId) {
      this.installerUserId = installerUserId
      this.getDetail()
    }
  },
  methods: {
    clickLeft() {
      appBack()
    },
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.installerUserId
      })

      if (detail) {
        this.detail = formatInstallerInfo(detail)
      }
    },
    async pullingDown(callback) {
      // 刷新
      await this.getDetail()
      if (callback) callback()
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  position: relative;
  overflow: auto;

  .tvt-better-scroll {
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 60px);
    box-sizing: border-box;

    ::v-deep .better-scroll-box {
      background-color: var(--bg-color-white, #ffffff);
    }

    .visit-tip {
      box-sizing: border-box;
      padding-top: 8px;
      padding-right: 24px;
      padding-bottom: 8px;
      padding-left: 24px;
      color: var(--icon-color-primary, #393939);
      font-family: PingFang SC;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      line-height: 22px;
      span {
        font-weight: 600;
      }
    }
    .card {
      width: calc(100% - 40px);
      margin: 0px auto 0;
    }
  }
}
</style>
