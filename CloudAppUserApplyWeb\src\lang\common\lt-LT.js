// 立陶宛语
export default {
  upgrade: 'Debesų atnaujinimas',
  cancel: '<PERSON><PERSON><PERSON><PERSON>',
  confirm: '<PERSON><PERSON><PERSON><PERSON>',
  deviceUpdate: 'Įrenginys',
  cameraUpdate: 'Kamera',
  allUpdate: 'Visi atnaujinimai',
  updateNow: 'Atnaujinti',
  currentVersion: '<PERSON>bar<PERSON><PERSON> versija',
  latestVersion: 'Naujausia versija',
  updateContent: 'Atnaujinti turinį',
  hasLatestVersion: 'Esama versija naujausia',
  online: 'Prisijungęs',
  offline: 'Neprisijungęs',
  waitDownload: 'Laukiama atsiuntimo',
  inprogress: 'Atsisiunčiama',
  downloadFail: 'Atsisiuntimas nepavyko',
  downloadFinished: 'Atsisiuntimas baigtas',
  inupgrade: 'Atnaujinimas',
  upgradeFail: 'Atnaujinti nepavyko',
  upgradeSuccess: 'Sėkmingai atnaujinta',
  deviceUpgradeInfo:
    'Atnaujinimo metu įrenginys bus atjungtas ir automatiškai paleistas iš naujo. Ar  tęsti naujinimą?',
  upgradeTip:
    'Baigus atsisiuntimą, prietaisas bus automatiškai atnaujintas. Atnaujinimo proceso metu prietaisas bus automatiškai paleistas iš naujo. Neperkraukite prietaiso rankiniu būdu ir neišjunkite maitinimo šaltinio, kol nebus baigtas automatinis perkrovimas.',
  cameraUpgradeInfo: 'Atnaujinimo metu kamera bus atjungta ir automatiškai paleista iš naujo. Ar tęsti naujinimą?',
  pwdUserNameError: 'Vartotojo vardo arba slaptažodžio klaida',
  permissionAuth: 'Aukščiausio lygio administratoriaus įgaliojimų autentifikavimas',
  pleaseEnterUser: 'Įveskite vartotojo vardą',
  pleaseEnterPwd: 'Įveskite slaptažodį',
  noCameraUpgrade: 'Neaptikta kamerų naujinimui',
  handleCheck: 'Naujinio aptikimas',
  paySuccess: 'Mokėjimas pavyko',
  payFail: 'Mokėjimas nepavyko',
  done: 'Užbaigta',
  rePurchase: 'Pirkti iš naujo',
  cloudStorage: 'Debesų saugykla',
  INSTRUMENT_DECLINED: 'Operacija viršija kortelės limitą',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: 'Mokėtojo sąskaita negali būti naudojama šiai operacijai',
  PAYER_ACCOUNT_RESTRICTED: 'Mokėtojo sąskaita apribota',
  TRANSACTION_LIMIT_EXCEEDED: 'Bendra mokėjimo suma viršija operacijos limitą',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: 'Operacija viršija gavėjo gavimo limitą',
  myInstaller: 'Mano montuotojas',
  trusteeshipDevice: 'Valdomas įrenginys',
  addTrusteeship: 'Pridėti hostingą',
  waitReceived: 'Laikiama patvirtinimo',
  received: 'Gauta',
  refuse: 'Atsisakyta',
  delete: 'Ištrinti',
  operationSuccess: 'Operacija pavyko',
  operationFail: 'Operacija nepavyko',
  cancelTrusteeship: 'Atšaukti hostingą',
  chooseDevice: 'Pasirinkite įrenginį',
  noAvaiableDevice:
    'Galima tvarkyti tik tuos įrenginius, kurių įrenginių versijos palaiko hostingo funkciją ir yra pridėtos per susiejimą.',
  leastChoose: 'Pasirinkite bent vieną įrenginį',
  details: 'Detalės',
  live: 'Tiesiogis',
  rec: 'Atkūrimas',
  config: 'Konfigūracija',
  confirmTrusteeshipTip: 'Hostingo užklausa išsiųsta montuotojui. Palaukite, kol montuotojas ją apdoros',
  cancelTrusteeshipTip:
    'Atšaukęs hostingą, montuotojas negalės suteikti jums nuotolinės priežiūros paslaugų. Ar tikrai norite atšaukti?',
  unBindTrusteeship: 'Po atsiejimo visas įrenginio hostingas bus atšauktas. Ar tęsti?',
  trusteeshipPermissions: 'Hostingo leidimai',
  trusteeshipTime: 'Hostingo laikas',
  unBind: 'Atsiejimas',
  serviceException: 'Paslaugos trikdis',
  pullingText: 'Truktelkyte žemyn, kad įkelti...',
  loosingText: 'Aatnaujinti...',
  loosing: 'Atnaujinama...',
  loadingText: 'Įkeliama...',
  refreshComplete: 'Sėkmingai atnaujinta',
  noMore: 'Daugiau nėra',
  checkSuccess: 'Patikrinta sėkmingai',
  checkFail: 'Patikrinimas nepavyko',
  viewUpdateContent: 'Peržiūrėkite naujinio turinį',
  deviceDisconnected: 'Nepavyko prijungti įrenginio',
  updateNote: 'Naujinio pastabą',
  noData: 'Nėra duomenų',
  tips: 'Patarimai',
  password: 'Slaptažodis',
  pwdError: 'Slaptažodžio klaida, galite pabandyti dar {0} kartus',
  pwdErrorLock: 'Užrakinta! Įvyko per daug klaidų. Bandykite dar kartą vėliau!',
  noPermissions: 'Jokių leidimų',
  permission: 'Leidimas',
  validity: 'Galiojimas',
  permissionValidity: 'Leidimo galiojimas',
  isSaveModify: 'Ar norite išsaugoti pakeitimus?',
  manyMinutes: '{0}min',
  manyHours: '{0}val',
  manyDays: '{0}d',
  manyMinutesEn: '{0}min',
  manyHoursEn: '{0}val',
  manyDaysEn: '{0}d',
  oneWeek: '1 savaitė',
  forever: 'Visam laikui',
  expired: 'Baigėsi galiojimo laikas',
  residue: 'Likutis',
  transferRequest: 'Prašymas perkelti',
  acceptTransfer: 'Priimti',
  refuseTransferConfirm: 'Ar tikrai norite atmesti perdavimą?',
  bindInstallerText: 'Įrenginius galite hostinti susieję montuotojo paskyrą ({account}). Susieti dabar?',
  bindSuccess: 'Susiejimas sėkmingas',
  acceptSuccess: 'Priimta sėkmingai',
  from: 'Iš',
  shareManage: 'Bendrinimo valdymas',
  shareDetail: 'Detalių bendrinimas',
  acceptShare: 'Priimti bendrinimą',
  permissionText: 'Leidimai, kuriuos gavote',
  livePreview: 'Tiesioginė peržiūra',
  playback: 'Atkūrimas',
  alarm: 'Aliarmas',
  intercom: 'Domofonas',
  gimbal: 'PTZ',
  refuseShareConfirm: 'Patvirtinti atsisakymą bendrinti?',
  acceptAll: 'Priimti viską',
  exitShare: 'Išeiti iš bendrinimo',
  cancelDefense: 'Atšaukti apsaugą',
  homeDefense: 'Sukurkite apsagą namuose',
  outDefense: 'Nustatykite apsaugą lauke',
  defenseDeployment: 'Apsauga/Išjungimas',
  oneClickDeployment: 'Diegimas vienu spustelėjimu',
  oneClickDisarm: 'Išjungti apsaugą vienu spustelėjimu',
  oneClickRemoval: 'Išjungimas vienu paspaudimu',
  deploySuccess: 'Sėkmingas diegimas',
  disarmSuccess: 'Sėkmingas apsaugos išjungimas',
  add: 'Pridėti',
  edit: 'Redaguoti',
  setting: 'Nustatymai',
  all: 'Visi',
  name: 'Vardas',
  cameraSensor: 'Sąsaja su kamera',
  deleteDeviceConfirm: 'Patvirtinti įrenginio pašalinimą?',
  onlySameDevice: 'Į tą pačią grupę galima įtraukti tik to paties įrenginio kameras',
  pleaseChooseChannel: 'Prašome pasirinkti kanalą',
  pleaseAddCameraSensor: 'Pridėkite kamerą / jutiklį',
  defensiveLinkageItem: 'Apsaugos sąryšio elementas',
  defensiveDesc: 'Pasirinktas susiejimo elementas, išjungus apsaugą bus neaktyvus',
  bypassHome: 'Nustatykite namuose apsaugos apėjimą',
  bypassHomeDesc: 'Jei įjungta, apsaugos režimo įjungimo metu zona bus automatiškai apeinama',
  ipcSound: 'IPC Garsas',
  ipcLight: 'IPC Blykstė',
  pleaseChooseLinkage: 'Pasirinkite apsaugos sąryšio elementą',
  deleteConfirm: 'Patvirtinti ištrynimą?',
  pleaseAddGroup: 'Pirmiausia pridėkite grupę',
  groupChannelEmpty: 'Nei vienas kanalas nebuvo įtrauktas į apsaugos grupę',
  removalSuccess: 'Išsiųsta aliarmo atšaukimo komanda',
  removalFail: 'Nepavyko išsiųsti įspėjimo pašalinimo',
  reqSuccess: 'Įvykdyta',
  reqFail: 'Nepavyko',
  groupLimit: 'Palaikomas pridėjimas iki {limit} grupių',
  areaGroup: 'Srities grupė',
  pleaseEnter: 'Įveskite',
  groupNoDevice: 'Prie grupavimo nepridėta jokių įrenginių',
  addPoint: 'Pridėti',
  addSuccess: 'Pridėta sėkmingai',
  addFail: 'Pridėti nepavyko',
  editSuccess: 'Redagavimas sėkmingas',
  editFail: 'Nepavyko redaguoti',
  deleteSuccess: 'Ištrinta sėkmingai',
  deleteFail: 'Nepavyko ištrinti',
  pointNameExist: 'Šis Preset taško pavadinimas jau yra',
  noPointSelect: 'Nėra Preset taškų parinkimui. Pirmiausia sukurkite Preset taškus',
  choosePoint: 'Pasirinkite',
  presetPoint: 'Preset taškas',
  presetPointName: 'Preset taško pavadinimas',
  cruiseLineName: 'Kruizo pavadinimas',
  pleaseEnterPoint: 'Įveskite Preset taško pavadinimą',
  pleaseEnterLine: 'Įveskite Kruizo pavadinimą',
  presetPointEmpty: 'Preset taškų sąrašas negali būti tuščias!',
  lineNameExist: 'Šis Kruizo pavadinimas jau yra',
  presetPointLimit: 'Preset taškų skaičius negali viršyti {0}!',
  manySecond: '{0} sekundžių',
  oneMinute: '1 minutė',
  speed: 'Greitis',
  holdTime: 'Trukmė',
  deletePointConfirm: 'Patvirtinti Preset taško ištrynimą?',
  pleaseChoosePoint: 'Pasirinkite Preset tašką',
  pleaseChooseSpeed: 'Pasirinkite greitį',
  pleaseChooseHoldTime: 'Pasirinkite trukmę',
  lineAuthBind: 'linijos autorizacijos susiejimas',
  bindFail: 'Susiejimas nepavyko',
  binding: 'Susiejimas',

  householdManagement: 'Pastatai ir gyventojai',
  addBuilding: 'Pridėti pastatą',
  buildingName: 'Pastato pavadinimas',
  enterBuildingName: 'Įveskite pastato pavadinimą',
  buildingNum: 'Pastato numeris',
  enterBuildingNum: 'Įveskite pastato numerį',
  relateDevice: 'Susieti įrenginiai',
  roomNum: 'Kambarių kiekis',
  room: 'Kambarys',
  addRoom: 'Pridėti kambarį',
  roomName: 'Kambario numeris',
  enterRoomName: 'Įveskite kambario numerį',
  household: 'Gyventojai',
  addRoomMember: 'Pridėti gyventojus',
  changeSuccessfully: 'Sėkmingai pakeista',
  email: 'El. paštas',
  enterMemberEmail: 'Įveskite el. pašto adresą',
  mobile: 'Telefonas',
  enterMemberMobile: 'Įveskite telefono numerį',
  emailNameError: 'Neteisingas el. pašto formatas',
  mobileError: 'Neteisingas telefono numerio formatas',
  emailNameNotEmpty: 'El. paštas negali būti tuščias',
  mobileNotEmpty: 'Telefono numeris negali būti tuščias',
  memberInMax: 'Gyventojų skaičius šiame kambaryje pasiekė maksimalią ribą.',
  memberMobileRepeate: 'Šis telefono numeris jau yra šiame kambaryje.',
  emailRepeate: 'Šis el. laiškas jau yra šiame kambaryje.',
  supportDash: 'Tik -, _ ir tarpas palaikomi kaip specialieji simboliai',
  pageUpdateTitle: 'Pranešimas apie atnaujinimą',
  pageUpdateContent: 'Puslapis atnaujintas. Atnaujinkite puslapį.',
  ok: 'OK',
  targetFaceManagement: 'Veido duomenų bazė',
  targetName: 'Vardas',
  targetType: 'Tipas',
  cardId: 'Kortelės numeris',
  strangerList: 'Lankytojas',
  whiteList: 'Leidimų sąrašas',
  blackList: 'Juodas sąrašas',
  admin: 'Administratorius',
  filter: 'Filtras',
  searchTargetFace: 'Įveskite vardą',
  personType: 'Naudotojo tipas',
  addPerson: 'Pridėti naudotoją',
  personFace: 'Veidas',
  floor: 'Aukštas',
  verificationMethod: 'Atrakinimo režimas',
  lockPermission: 'Durų spyna',
  startTime: 'Paleidimo laikas',
  endTime: 'Pabaigos laikas',
  gender: 'Lytis',
  age: 'Age',
  telephone: 'Mobilusis',
  termOfValidity: 'Galiojimo terminas',
  foreverValid: 'Always Valid',
  custom: 'Pasirinktinis',
  jobNumber: 'Identifikavimo numeris',
  male: 'Vyras',
  female: 'Moteris',
  Password: 'PIN kodas',
  FaceMatch: 'Veido palyginimas',
  SwipingCard: 'Kortelė',
  MatchAndPass: 'Veido palyginimas + PIN kodas',
  MatchAndCard: 'Veido palyginimas + kortelė',
  MatchorCard: 'Veido palyginimas arba kortelė',
  AllType: 'Veido palyginimas arba PIN kodas, arba kortelė',
  doorLock: 'Durų spyna',
  'personFace.uploadTip': 'Įkelkite veido atvaizdą',
  'personFace.sizeTip': 'Paveikslėlio dydis negali viršyti 200k',
  ageRangeTip: 'Amžiaus intervalas yra {min}~{max}',
  floorRangeTip: 'Grindų diapazonas yra {min}~{max}',
  roomRangeTip: 'Kambario numerio intervalas yra {min}~{max}',
  cardIdTip: 'Kortelės numerių skaičius negali viršyti {max}!',
  passwordRangeTip: 'PIN kodo ilgio diapazonas yra {min}~{max}',
  passwordLengthTip: 'PIN kodo ilgis yra {ilgis}',
  timeRangeTip: 'Pabaigos laikas turi būti vėlesnis už pradžios laiką!',
  doorLockTip: 'Pasirinkite bent vieną durų spyną',
  jobNumberTip: 'Įveskite identifikacinį numerį',
  cardIdsTip: 'Įveskite kortelės numerį',
  confirmDeletePerson: 'Patvirtinti šio naudotojo ištrynimą?',
  cardIdExist: 'Kortelės numeris jau egzistuoja',
  faceMatchErrorCode: {
    '-2': 'Parametro klaida!',
    '-3': 'Vaizdas neatitinka reikalavimų',
    '-5': 'Naudotojų skaičius pasiekė maksimalią ribą.',
    '-6': 'Paveikslėlio dydis viršija ribą!',
    '-11': 'Nepalaikomas vaizdo formatas',
    '-12': 'Vaizdo duomenys yra sugadinti',
    '-16': 'Įkelti nepavyko',
    '-18': 'Paveikslėlio dydis viršija ribą',
    '-19': 'Nepavyko atnaujinti funkcijų bibliotekos įrašo',
    '-20': 'Nepavyko pridėti funkcijų bibliotekos įrašo',
    '-21': 'Nepavyko išgauti vaizdo funkcijų',
    '-22': 'Nepavyko išsaugoti bazinio vaizdo',
    '-23': 'Nepavyko išsaugoti naudotojo informacijos',
    '-24': 'Nepavyko išsaugoti asmeninės informacijos',
    '-25': 'Toks veidas jau egzistuoja',
    '-26': 'Nežinomos funkcijos bibliotekos veikimo klaida',
    '-30': 'Atrakinimo režimo parametro klaida',
    '-31': 'PIN kodo pakartoti negalima',
    '-32': 'PIN kodas negali būti tuščias',
    '-33': 'ID numeris negali būti tuščias',
    '-34': 'Kortelės numeris negali būti tuščias',
    '-35': 'Nėra veido nuotraukos',
    499: 'Parametrų tikrinimas nepavyko'
  },
  errorCode: {
    400: 'Parametrų klaida',
    404: 'Prašomas šaltinis (tinklalapis ir pan.) neegzistuoja',

    500: 'Sistemos trikdis!',
    502: 'Serverio užklausa nepavyko',
    503: 'Serverio trikdis',
    504: 'Baigėsi serverio užklausos skirtasis laikas',
    550: 'Baigėsi užklausos skirtasis laikas',

    1000: 'Parametrų klaida',
    1005: 'Vaizdo patvirtinimo kodo klaida',
    1007: 'Būtinas nuotraukos patvirtinimo kodas',
    1008: 'Patvirtinimo kodas baigė galioti',
    1009: 'Patvirtinimo kodo klaida',
    1011: 'Parametrai užpildyti neteisingai!',
    1012: 'API neatpažinta',
    1013: 'Nepavyko išsiųsti patvirtinimo kodo',
    1015: 'Vartotojas jau egzistuoja',
    1027: 'Įveskite teisingą įrenginio serijos numerį / saugos kodą',
    1028: 'Kamera įjungta arba išjungta',
    4500: 'Parametrų klaida',
    5000: 'Atsiprašome, jūs neturite leidimo atlikti šios operacijos',
    5001: 'Dabartinis vartotojas neturi leidimo',
    6000: 'Dabartinė verslo būsena nepalaiko šios operacijos',
    6001: 'Per dažnas veikimas',
    7000: 'Parametrų klaida',
    7001: 'Vartotojas neegzistuoja',
    7002: 'Seno slaptažodžio klaida!',
    7003: 'Token klaida!',
    7004: 'Sveiki, jūsų paskyra buvo atsijungta dėl ilgo neveiklumo arba prisijungimo prie kitų įrenginių. Prašome prisijungti dar kartą',
    7005: 'Neteisingas parašas',
    7006: 'Mobiliojo telefono numeris jau yra',
    7007: 'Vartotojas užrakintas. Norėdami atrakinti, susisiekite su administratoriumi',
    7009: 'Sveiki, jūsų paskyra buvo atsijungta dėl ilgo neveiklumo arba prisijungimo prie kitų įrenginių. Prašome prisijungti dar kartą',
    7010: 'Administratoriaus paskyra nėra aktyvuota',
    7011: 'Paskyra neaktyvuota',
    7019: 'Vartotojo vardas jau yra',
    7021: 'Nepavyko ištrinti! Pirmiausia, išvalykite visus šios hosto grupės hostus',
    7023: 'Pašto dėžutė buvo susieta',
    7028: 'Šablonas buvo panaudotas projekte ir jo ištrinti negalima!',
    7029: 'Šablono pavadinimas jau yra!',
    7030: 'Duomenys jau yra!',
    7032: 'Programinės įrangos paketas jau yra!',
    7034: 'Programinės įrangos paketas buvo išleistas ir jo negalima ištrinti!',
    7040: 'Įrenginio nėra arba jis neprisijungęs',
    7042: 'Paleisties būsenoje yra ir kitų užduočių',
    7043: 'Užduotis nepatvirtinta!',
    7044: 'Operacija nepavyko. Nėra įrenginių, kuriuos būtų galima atnaujinti!',
    7045: 'Užduotis nepatvirtinta!',
    7056: 'Ši versija buvo įtraukta į palaikomąjį suderinamumo valdymą ir jos negalima ištrinti!',
    7057: 'Išduodamo nurodymo laukas negali būti tuščias!',
    7061: 'Taisymas nepavyko, negalima pakartoti taisymo!',
    7065: 'Kanalas jau bendrinamas',
    7066: 'Kliento kodas jau yra!',
    7068: 'Kliento kodas neegzistuoja!',
    7069: 'Per daug duomenų, susiaurinkite paieškos sritį ir ieškokite dar kartą!',
    7072: 'Įrenginys jau yra',
    7081: 'Importuoti nepavyko!',
    7082: 'Eksportuoti nepavyko!',
    7084: 'Kliento šalies kodas jau yra',
    7086: 'Operacija atmesta dėl sistemos sutrikimo',
    7087: 'Produktas jau yra!',
    7088: 'Sveiki, jūsų paskyra buvo atsijungta dėl ilgo neveiklumo arba prisijungimo prie kitų įrenginių. Prašome prisijungti dar kartą',
    7090: 'Sveiki, jūsų paskyra buvo atsijungta dėl ilgo neveiklumo arba prisijungimo prie kitų įrenginių. Prašome prisijungti dar kartą',
    7093: 'Vaizdo ir teksto informacija nesukonfigūruota!',
    7094: 'Paslaugos sąlygų informacijos nėra!',
    9000: 'Sistemos trikdis!',
    9001: 'Protokolo versija per žema. Senoji versija nebesuderinama ir ją reikia atnaujinti',
    9002: 'Protokolo versijos klaida, neatpažintas versijos laukas arba klaidos pranešimas',
    9003: 'Nepavyko išsiųsti patvirtinimo kodo',
    9004: 'Duomenų bazės operacija nepavyko',
    9005: 'Duomenų nėra',
    9006: 'Duomenys jau yra',
    9007: 'Duomenų, kuriuos norite peržiūrėti, nėra',
    9008: 'Duomenų nėra',
    9009: 'Duomenų trikdis',
    9500: 'Sistemos trikdis!',
    10000: 'Nepavyko prisijungti prie įrenginio',
    10001: 'Sistemos trikdis!',
    12344: 'Nepavyko prisijungti prie tinklo',
    12345: 'Baigėsi tinklo ryšio skirtasis laikas',
    20021: 'Šis e-paštas buvo panaudotas',
    20024: 'Paskyra aktyvuota',
    20030: 'Nuorodos galiojimo laikas baigėsi',
    20070: 'Nepavyko pakviesti šio naudotojo, nes esate skirtinguose duomenų centruose.',
    20071: 'Nepavyko pakviesti šio naudotojo, nes priklausote skirtingiems tiekėjams.',
    23024: 'Pateiktos mokėjimo kortelės galiojimo laikas baigėsi',
    23025: 'Sandoris atmestas dėl pažeidimo',
    32018: 'Duomenų nėra.',
    32019: 'Operacija nepavyko',
    32021: 'Duomenų nėra',
    32022: 'Hostingo paslauga nepalaikoma, nes {0} įrenginys ir montuotojas nėra toje pačioje šalyje / regione.',
    33001: 'Nėra leidimo naudoti šį įrenginį',
    33002: 'Nėra leidimo naudoti šią svetainę',
    33003: 'Svetainė neegzistuoja',
    33004: 'Įrenginio pavadinimo ilgis turi būti nuo 0 iki 32',
    33010: 'Įrenginys jau yra',
    33601: 'pasikartojantis įrenginys!',
    33602: 'apriboti maksimalų apsaugos įjungimo grupės skaičių!',
    33603: 'Operacija nepavyko!',
    34001: 'Ši paskyra buvo susieta.',
    34003: 'Būsenos informacija yra neteisinga',
    34004: 'Autorizacija nepavyko.',
    34005: 'Operacija nepavyko: įgaliojimas pasibaigė. Gaukite dar kartą.',
    34006: 'Įrenginio perdavimo nėra',
    34007: 'Gali priimti perdavimus tik iš to paties vartotojo',
    34021: 'Pastatų kiekis pasiekė maksimalią ribą.',
    34022: 'Pastato pavadinimas toks jau yra',
    34023: 'Pastato numeris toks jau yra',
    34024: 'Ištrinti nepavyko. Pirmiausia ištrinkite šiame pastate esančius kambarius ir įrenginius.',
    34025: 'Asocijuotasis darbuotojas nepavyko. Šis prietaisas buvo susietas su kitu pastatu.',
    34026: 'Įrenginių kiekis šiame pastate pasiekė ribą.',
    34027: 'Operacija nepavyko. Šis pastatas ištrintas.',
    34028: 'Šis kambario numeris jau yra.',
    34029: 'Kambarių kiekis šiame pastate pasiekė maksimalią ribą.',
    34030: 'Gyventojų skaičius šiame kambaryje pasiekė maksimalią ribą.',
    34031: 'Operacija nepavyko. Šis kambarys ištrintas.',
    34033: 'Ištrinti nepavyko. Pirmiausia ištrinkite šio kambario gyventojus.',
    34035: 'Telefono numeris jau yra šiame kambaryje.',
    34036: 'Šis el. paštas jau yra šiame kambaryje.',
    34037: 'Operacija nepavyko. Šis gyventojas buvo ištrintas.',
    536870934: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536870940: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536870943: 'Netinkamas parametras',
    536870945: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536870947: 'Vartotojo vardas neegzistuoja',
    536870948: 'Vartotojo vardo arba slaptažodžio klaida',
    536871017: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536871039: 'Netinkamas parametras',
    536871060: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536871082: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536871083: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
    536871030: 'Nėra Disko',
    ipc: {
      499: 'Nežinoma klaida',
      612: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
      730: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
      731: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
      732: 'Veikimo sutrikimas, patikrinkite įrenginio būseną',
      735: 'Veikimo sutrikimas, patikrinkite įrenginio būseną'
    }
  }
}
