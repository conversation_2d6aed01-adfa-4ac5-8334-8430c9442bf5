<template>
  <div class="detail-wrapper">
    <nav-bar @clickLeft="clickLeft" :title="title"></nav-bar>
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="detail-content">
        <!-- 设备托管申请 -->
        <div v-if="orderType === 'site'" class="hosting-tip">
          {{ tips }}
        </div>
        <div class="card">
          <installer-info :data="detail" />
        </div>
        <div class="site-list">
          <div class="site">
            <div class="site-header">
              <div class="site-header-left" @click="siteDescVisible = true">
                <img src="@/assets/img/common/trusteeship/site.png" class="site-icon" />
                <div class="site-label">{{ $t('site') }}</div>
                <img src="@/assets/img/common/trusteeship/question.svg" class="site-desc-icon" />
              </div>
              <div class="site-tool">
                <div class="site-name text-over-ellipsis">
                  {{ currentSite.siteName }}
                </div>
              </div>
            </div>
            <div v-if="currentSite.deviceTrusts?.length === 0" class="empty-list">
              <img src="@/assets/img/common/no_data_max.png" class="empty-img" />
              <span class="empty-info">
                {{ $t('noDeviceHosting') }}
              </span>
            </div>
            <template v-else-if="currentSite.status === APPLICATION_STATUS.pending">
              <div class="site-content" v-for="device in currentSite.deviceTrusts" :key="device.sn">
                <div class="device">
                  <dir class="device-header" @click="toggleDevice(currentSite, device)">
                    <img v-if="device.disabled" src="@/assets/img/common/check_box_disabled.png" class="checked-icon" />
                    <img v-else-if="device.checked" src="@/assets/img/common/check.png" class="checked-icon" />
                    <img v-else src="@/assets/img/common/check_no.png" class="checked-icon" />
                    <div class="device-name text-over-ellipsis">
                      {{ device.deviceName }}
                    </div>
                  </dir>
                  <div v-if="device.disabled" class="device-content">
                    <div class="device-permission">
                      <div>
                        <div class="device-permission-title">
                          {{ $t('trustPermission') }}
                        </div>
                        <div class="device-permission-info">--</div>
                      </div>
                    </div>
                    <div class="device-permission">
                      <div>
                        <div class="device-permission-title">
                          {{ $t('trustTime') }}
                        </div>
                        <div class="device-permission-info">--</div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="device-content">
                    <div class="device-permission" @click="openPermissionPopup(currentSite, device)">
                      <div>
                        <div class="device-permission-title">
                          {{ $t('trustPermission') }}
                        </div>
                        <div class="device-permission-info">
                          {{ device.permissionTr }}
                        </div>
                      </div>
                      <img src="@/assets/img/common/more.png" class="device-content-icon" />
                    </div>
                    <div class="device-permission" @click="openTimePopup(currentSite, device)">
                      <div>
                        <div class="device-permission-title">
                          {{ $t('trustTime') }}
                        </div>
                        <div class="device-permission-info">
                          {{ device.hostingTimeTr }}
                        </div>
                      </div>
                      <img src="@/assets/img/common/more.png" class="device-content-icon" />
                    </div>
                  </div>
                </div>
              </div>
              <van-field
                v-model="currentSite.remark"
                rows="6"
                disabled
                autosize
                type="textarea"
                class="host-remark"
                :placeholder="$t('remarkPlaceholder')"
              />
            </template>
            <div
              class="device-list"
              v-else-if="
                currentSite.status === APPLICATION_STATUS.approved ||
                currentSite.status === APPLICATION_STATUS.rejected ||
                currentSite.status === APPLICATION_STATUS.outdated ||
                currentSite.status === APPLICATION_STATUS.invalid
              "
            >
              <div class="device-title">
                {{ $t('hostingDevice') }}
              </div>
              <div class="device-item" v-for="device in currentSite.deviceTrusts" :key="device.sn">
                <div class="device-icon-wrapper"></div>
                <div class="device-info">
                  <div class="device-name text-over-ellipsis">
                    {{ device.deviceName }}
                  </div>
                  <div class="device-permission">
                    {{ $t('permission') }}：{{ device.disabled ? '--' : device.permissionTr }}
                  </div>
                  <div class="device-time">
                    {{ $t('duration') }}：{{ device.disabled ? '--' : device.hostingTimeTr }}
                  </div>
                </div>
              </div>
            </div>
            <div class="device-list" v-else-if="currentSite.status === APPLICATION_STATUS.canceled">
              <div class="device-title">
                {{ $t('hostingDevice') }}
              </div>
              <div class="device-item" v-for="device in currentSite.deviceTrusts" :key="device.sn">
                <div class="device-icon-wrapper"></div>
                <div class="device-info">
                  <div class="device-name text-over-ellipsis">
                    {{ device.deviceName }}
                  </div>
                  <div class="device-permission">{{ $t('permission') }}：--</div>
                  <div class="device-time">{{ $t('duration') }}：--</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="currentSite.status !== APPLICATION_STATUS.canceled" class="padding-bottom"></div>
      </div>
    </tvt-better-scroll>

    <div v-if="currentSite.status !== APPLICATION_STATUS.canceled" class="footer-container">
      <template v-if="currentSite.status === APPLICATION_STATUS.pending">
        <van-button class="footer-btn" @click="rejectApplication">
          {{ rejectText }}
        </van-button>
        <van-button class="footer-btn primary" type="primary" @click="approveApplication" :disabled="!approveEnabled">
          {{ $t('agree') }}
        </van-button>
      </template>
      <van-button v-else class="footer-btn primary disabled" type="primary" disabled>
        {{ disabledBtnText }}
      </van-button>
    </div>
    <set-permission-popup-vue
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setPermissionVisible"
      @submit="changePermission"
      ref="setPermissionRef"
    />
    <set-time-popup
      :site="currentSite"
      :device="currentDevice"
      :visible.sync="setTimeVisible"
      @submit="changeTime"
      ref="setTimeRef"
    />
    <site-desc-popup :visible.sync="siteDescVisible" />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import InstallerInfo from './components/InstallerInfo'
import { showTimeStr2 } from '@/utils/common'
import { appBack } from '@/utils/appbridge'

import SetPermissionPopupVue from './components/SetPermissionPopup'
import SetTimePopup from './components/SetTimePopup'
import SiteDescPopup from './components/SiteDescPopup'
import { formatInstallerInfo } from './common'
import { getInstallerInfo, getTicketDetail, handleTicket } from '@/api/maxHosting'
import { updateMessageStatus } from '@/api/message'

const applicationStatus = {
  pending: 0,
  approved: 1,
  rejected: 2,
  canceled: 3,
  outdated: 4,
  invalid: 5
}

export default {
  name: 'DeviceHostingApprove',
  components: {
    NavBar,
    InstallerInfo,
    SetPermissionPopupVue,
    SetTimePopup,
    SiteDescPopup
  },
  data() {
    return {
      pullingStatus: 0,
      detail: {},
      orderId: '', // 设备工单id
      siteOrderId: '', // 站点工单id
      siteId: '',
      APPLICATION_STATUS: applicationStatus,
      setPermissionVisible: false,
      setTimeVisible: false,
      currentSite: {},
      currentDevice: {},
      siteDescVisible: false,
      installerUserId: '',
      orderType: 'site',
      action: 'apply',
      msgId: ''
    }
  },
  computed: {
    title() {
      if (this.orderType === 'site') {
        if (this.action === 'apply') {
          return this.$t('sitePermissionApprove')
        }
        return this.$t('sitePermissionCancel')
      }

      if (this.action === 'apply') {
        return this.$t('devicePermissionApprove')
      }
      return this.$t('devicePermissionCancel')
    },
    tips() {
      if (this.orderType === 'site') {
        if (this.action === 'apply') {
          return this.$t('siteHostingTip')
        }

        if (this.action === 'cancel') {
          return this.$t('siteCancelHostingTip')
        }
      }

      if (this.action === 'cancel') {
        return this.$t('deviceCancelHostingTip')
      }

      return ''
    },
    rejectText() {
      if (this.orderType === 'site') {
        return this.$t('reject')
      }
      return this.$t('rejectAll')
    },
    approveEnabled() {
      if (this.orderType === 'site') {
        return true
      }
      return this.currentSite.deviceTrusts.some(device => device.checked)
    },
    disabledBtnText() {
      if (this.currentSite.status === applicationStatus.approved) {
        return this.$t('agreed')
      }

      if (this.currentSite.status === applicationStatus.rejected) {
        return this.$t('rejected')
      }

      if (this.currentSite.status === applicationStatus.outdated) {
        return this.$t('expired')
      }

      if (this.currentSite.status === applicationStatus.invalid) {
        return this.$t('invalid')
      }

      return ''
    }
  },
  created() {
    const { id, orderType } = this.$route.params
    const { installerUserId, action, orderId, siteOrderId, msgId } = this.$route.query

    if (id && orderType) {
      this.orderType = orderType
      this.action = action

      if (orderType === 'site') {
        this.siteId = id
        this.orderId = ''
        this.siteOrderId = siteOrderId

        if (action === 'cancel') {
          this.orderId = orderId
        }
      } else {
        this.siteId = ''
        this.orderId = id
      }
      this.msgId = msgId
      this.id = id
      this.installerUserId = installerUserId

      this.refreshRequest()
    } else {
      this.id = ''
      this.installerUserId = ''
      this.orderType = 'site'
      this.action = 'apply'
    }
  },
  methods: {
    formatInstallerInfo,
    async getDetail() {
      const { data: detail } = await getInstallerInfo({
        userId: this.installerUserId
      })

      if (detail) {
        this.detail = formatInstallerInfo(detail)
      }
    },
    // 刷新当前页面请求
    async refreshRequest() {
      this.getDetail()

      const params = {
        ticketIds: []
      }

      if (this.orderId) {
        params.ticketIds.push(this.orderId)
      }

      if (this.siteOrderId) {
        params.ticketIds.push(this.siteOrderId)
      }
      try {
        const { data: site } = await getTicketDetail(params)

        if (site) {
          if (site.deviceTrusts === null) {
            site.deviceTrusts = []
          }
          site.deviceTrusts = site.deviceTrusts
            .map(device => {
              let checked = true
              let disabled = false

              if (device.authList === null && device.trustDuration === null) {
                checked = false
                disabled = true
              }

              if (site.status === applicationStatus.outdated || site.status === applicationStatus.invalid) {
                checked = false
                disabled = true
              }

              if (device.authList === null) {
                device.authList = ['config']
              }

              if (device.trustDuration === null) {
                device.trustDuration = 0
              }

              device.trustDuration = parseInt(device.trustDuration)

              return {
                ...device,
                permissionTr: this.translatePermission(device),
                hostingTimeTr: this.translateTime(device),
                checked,
                disabled
              }
            })
            .slice()
          this.currentSite = site

          if (this.action === 'cancel') {
            this.currentSite.status = applicationStatus.canceled
          }
        }
      } catch (error) {
        console.error(error)
      }
    },
    translateTime(device) {
      if (device.trustDuration === 0) {
        return this.$t('forever')
      }

      return showTimeStr2(0, device.trustDuration * 1000)
    },
    translatePermission(device) {
      if (!Array.isArray(device.authList)) {
        return ''
      }
      const sorted = {
        config: 0,
        live: 1,
        rec: 2
      }
      const arr = []
      device.authList.forEach(item => {
        // 进行排序，后端数据顺序会变
        const index = sorted[item]
        if (index !== undefined) {
          arr[index] = item
        }
      })

      return arr
        .filter(item => item)
        .map(item => this.$t(item))
        .join(' · ')
    },
    async pullingUp(callback) {
      // 刷新
      this.refreshRequest()
      if (callback) callback()
    },
    async pullingDown(callback) {
      // 刷新
      await this.refreshRequest()

      if (callback) callback()
    },
    clickLeft() {
      appBack()
    },
    openPermissionPopup(site, device) {
      this.currentDevice = device

      this.setPermissionVisible = true
    },
    changePermission(value) {
      this.currentDevice.authList = value
      this.currentDevice.permissionTr = this.translatePermission(this.currentDevice)
      const index = this.currentSite.deviceTrusts.findIndex(dev => dev.sn === this.currentDevice.sn)
      if (index > -1) {
        this.$set(this.currentSite.deviceTrusts, index, this.currentDevice)
      }
      this.setPermissionVisible = false
    },
    openTimePopup(site, device) {
      this.currentSite = site
      this.currentDevice = device

      this.setTimeVisible = true
    },
    changeTime(value) {
      this.currentDevice.trustDuration = value
      this.currentDevice.hostingTimeTr = this.translateTime(this.currentDevice)
      const index = this.currentSite.deviceTrusts.findIndex(dev => dev.sn === this.currentDevice.sn)
      if (index > -1) {
        this.$set(this.currentSite.deviceTrusts, index, this.currentDevice)
      }
      this.setTimeVisible = false
    },
    toggleDevice(site, device) {
      device.checked = !device.checked
      const index = this.currentSite.deviceTrusts.findIndex(dev => dev.sn === device.sn)
      if (index > -1) {
        this.$set(this.currentSite.deviceTrusts, index, device)
      }
    },
    async rejectApplication() {
      const ticketIds = []
      if (this.orderId) {
        ticketIds.push(this.orderId)
      }

      if (this.siteOrderId) {
        ticketIds.push(this.siteOrderId)
      }

      const params = {
        ticketIds,
        operateType: 2,
        trusts: {
          installerUserId: this.installerUserId,
          siteId: this.currentSite.siteId,
          deviceTrusts: this.currentSite.deviceTrusts.map(device => ({
            sn: device.sn,
            authList: device.authList,
            trustDuration: device.trustDuration
          }))
        }
      }

      await handleTicket(params)

      updateMessageStatus({
        msgType: 3, // 消息类型， 1告警2呼叫3通知
        updateMsgList: [
          {
            id: this.msgId,
            status: 3 // 1、已读 2、同意 3、拒绝
          }
        ]
      })
      // 联调时需要改成刷新
      this.currentSite.status = applicationStatus.rejected
      if (this.orderType === 'site') {
        this.$toastSuccess(this.$t('rejectMsg'))
      } else {
        this.$toastSuccess(this.$t('deviceRejectMsg', [this.$t('service')]))
      }
      this.refreshRequest()
    },
    async approveApplication() {
      const ticketIds = []
      if (this.orderId) {
        ticketIds.push(this.orderId)
      }

      if (this.siteOrderId) {
        ticketIds.push(this.siteOrderId)
      }

      const params = {
        ticketIds,
        operateType: 1,
        trusts: {
          installerUserId: this.installerUserId,
          siteId: this.currentSite.siteId,
          deviceTrusts: this.currentSite.deviceTrusts
            .filter(device => device.checked)
            .map(device => ({
              sn: device.sn,
              authList: device.authList,
              trustDuration: device.trustDuration
            }))
        }
      }

      try {
        await handleTicket(params)

        updateMessageStatus({
          msgType: 3, // 消息类型， 1告警2呼叫3通知
          updateMsgList: [
            {
              id: this.msgId,
              status: 2 // 1、已读 2、同意 3、拒绝
            }
          ]
        })

        this.currentSite.status = applicationStatus.approved
        this.$toastSuccess(this.$t('agreed'))
        this.refreshRequest()
      } catch (error) {
        console.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-wrapper {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  position: relative;
  overflow: auto;

  .tvt-better-scroll {
    height: calc(100% - 44px);
    box-sizing: border-box;
  }

  .detail-content {
    width: 100%;
    height: calc(100% - 60px);
    box-sizing: border-box;

    ::v-deep .better-scroll-box {
      background-color: var(--bg-color-white, #ffffff);
    }
    .padding-bottom {
      height: 80px;
    }
    .card {
      width: calc(100% - 24px);
      margin: 0 auto;
    }
    .site-hosting {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
  .site-list {
    width: calc(100% - 24px);
    margin: 20px auto 0;
    padding-bottom: 100px;
    flex: 1;
    .site {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .site-header {
      display: flex;
      align-items: center;
      box-sizing: border-box;
      height: 60px;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
    }
    .site-header-left {
      display: flex;
      align-items: center;
      color: var(--text-color-primary, #1a1a1a);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      .site-icon {
        width: 24px;
        height: 24px;
        margin-right: 6px;
      }
      .site-desc-icon {
        width: 15px;
        margin-left: 5px;
      }
      .site-label {
        white-space: nowrap;
      }
    }
    .site-tool {
      flex: 1;
      margin-left: 6px;
      overflow: hidden;
      .site-name {
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: right;
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-right: 6px;
      }
    }
    .device {
      padding-left: 6px;
    }
    .device-header {
      box-sizing: border-box;
      width: 100%;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: left;
      box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);

      .checked-icon {
        width: 24px;
        margin-right: 6px;
      }

      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        width: 100%;
      }
      .device-status {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
    .device-content {
      padding-left: 30px;
      .device-permission {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        height: 40px;
        box-shadow: 0px 1px 0px var(--bg-color-secondary, #eeeeee);
      }
      .device-permission-title {
        align-self: stretch;
        color: var(--icon-color-primary, #2b2b2b);
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .device-permission-info {
        color: var(--text-color-placeholder, #a3a3a3);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
      }
      .device-content-icon {
        width: 24px;
        height: 24px;
      }
    }
    .device-list {
      margin-left: 12px;
    }
    .device-title {
      align-self: stretch;
      color: var(--text-color-primary, #82879b);
      font-family: 'PingFang SC';
      font-size: var(--font-size-text-size, 12px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin: 10px 0;
    }
    .device-item {
      display: flex;
      height: 94px;
      padding: 12px 0;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
      .device-info {
        max-width: calc(100% - 85px);
      }

      .device-icon-wrapper {
        background-color: var(--bg-color-primary, #f9fafc);
        width: 78px;
        height: 48px;

        border-radius: 2px;
        margin-right: 7px;
        background-image: url('@/assets/img/common/device.png');
        background-position: center;
        background-clip: border-box;
        background-size: cover;
      }
      .device-name {
        color: var(--text-color-primary, #1a1a1a);
        font-feature-settings: 'liga' off, 'clig' off;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body1-size, 16px);
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-bottom: 3px;
      }
      .device-permission,
      .device-time {
        color: var(--text-color-placeholder, #9a9ca2);
        font-family: 'PingFang SC';
        font-size: var(--font-size-text-size, 12px);
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 3px;
      }
    }
    .empty-list {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      box-sizing: border-box;
      margin-top: 10%;

      .empty-img {
        width: 120px;
        margin-bottom: 25px;
      }
      .empty-info {
        color: var(--text-color-placeholder, #a3a3a3);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: var(--font-size-body2-size, 14px);
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
  .host-remark {
    width: calc(100% - 32px);
    box-sizing: border-box;
    margin: 20px auto 0;
    background: var(--brand-bg-color-light-disabled, #f2f4f8);
    border: 1px solid var(--bg-color-secondary, #eeeeee);
    border-radius: 8px;
  }
  .footer-container {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    background-color: var(--bg-color-white, #ffffff);
  }
  .footer-btn {
    width: 167px;
    height: 40px;
    border: 1px solid var(--bg-color-secondary, #eeeeee);
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
    margin-right: 10px;

    &:last-child {
      margin-right: 0;
    }
    &.primary {
      background: var(--brand-bg-color-active, #1d71f3);
    }
    &.disabled {
      width: 327px;
    }
  }
  .hosting-tip {
    color: var(--text-color-secondary, #50546d);
    font-family: 'Source Han Sans CN';
    font-size: var(--font-size-body2-size, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 20px 24px 10px 24px;
  }
}
</style>
<style lang="scss">
.service-detail-site-popup-container {
  background: var(--brand-bg-color-light-disabled, #f2f4f8);

  .site-change-item {
    height: 52px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    background: #ffffff00;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-color-primary, #1a1a1a);
    text-align: center;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    background-color: var(--bg-color-white, #ffffff);
  }
  .cancel-btn {
    margin-top: 8px;
  }
}
.service-detail-change-name-popup-container {
  height: 196px;
  .header {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    padding: 16px;
    background-color: var(--bg-color-white, #ffffff);
    .cancel {
      color: var(--text-color-secondary, #50546d);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
    }
    .title {
      color: var(--text-color-primary, #101d34);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .confirm {
      color: var(--brand-bg-color-active, #1d71f3);
      text-align: right;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      &.disabled {
        color: var(--text-color-placeholder, #a3a3a3);
      }
    }
  }
  .content {
    width: 100%;
    height: 124px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .content-input {
    width: 300px;
    padding: 14px 0;
    color: var(--text-color-primary, #101d34);
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: 'PingFang SC';
    font-size: var(--font-size-body1-size, 16px);
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
  }
}
</style>
