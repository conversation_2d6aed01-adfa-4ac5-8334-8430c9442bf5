<template>
  <van-popup
    :value="visible"
    @change="value => $emit('update:visible', value)"
    position="bottom"
    round
    :close-on-click-overlay="false"
    get-container="#app"
  >
    <div class="site-device-setting-permission-pop-container">
      <div class="header">
        <span class="title">
          {{ $t('permissionSetting') }}
        </span>
        <img class="close-btn" src="@/assets/img/common/trusteeship/close.png" @click="handleCancel" />
      </div>
      <div class="content">
        <van-checkbox-group v-model="selectedValue" ref="checkboxGroup">
          <van-checkbox
            v-for="item in devCapabilityList"
            class="permission-item"
            :key="item.value"
            :name="item.value"
            ref="checkboxes"
            label-position="left"
            @click="handleChange"
            :disabled="item.value === 'config' && selectedValue.includes('config')"
          >
            {{ item.label }}
          </van-checkbox>
        </van-checkbox-group>
      </div>
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('confirm') }}
      </van-button>
    </div>
  </van-popup>
</template>

<script>
import { DEVICE_CAPABILITY_LIST } from '@/utils/options'

export default {
  name: 'SetPermissionPopup',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: false
    },
    devCapabilityList: {
      type: Array,
      default: () => DEVICE_CAPABILITY_LIST()
    }
  },
  data() {
    return {
      selectedValue: []
    }
  },
  watch: {
    value: {
      handler(val, oldVal) {
        if (val && JSON.stringify(val) !== JSON.stringify(oldVal)) this.selectedValue = [...val]
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
    },
    handleChange() {
      this.$emit('change', this.selectedValue)
    },
    handleConfirm() {
      this.$emit('submit', this.selectedValue)
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss">
.site-device-setting-permission-pop-container {
  background-color: var(--bg-color-white, #ffffff);
  padding-bottom: 16px;
  .header {
    box-sizing: border-box;
    height: 52px;
    padding: 12px;
    position: relative;
    border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    .title {
      display: inline-block;
      width: 100%;
      color: var(--text-color-primary, #1a1a1a);
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .close-btn {
      width: 24px;
      height: 24px;
      position: absolute;
      right: 14px;
    }
  }
  .content {
    padding: 0 16px;
    .permission-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 52px;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    }
  }
  .footer-btn {
    display: block;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    margin: 10px auto 0;
  }
}
</style>
