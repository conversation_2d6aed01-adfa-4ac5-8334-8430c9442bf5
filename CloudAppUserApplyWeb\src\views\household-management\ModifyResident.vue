<template>
  <div class="household-manangement-wrapper">
    <nav-bar :title="$t('modifyResident')" @clickLeft="back"></nav-bar>
    <div class="household-content">
      <!-- 基本信息部分 -->
      <van-cell class="household-item" name="building">
        <template #title> {{ $t('buildingName') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ curResidentInfo && curResidentInfo.buildingName }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item" name="room">
        <template #title>{{ $t('roomName') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ curResidentInfo && curResidentInfo.roomNo }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item" name="cardCount">
        <template #title>{{ $t('doorCardCount') }}</template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ cardNoList && cardNoList.length }}</span>
        </template>
      </van-cell>

      <van-cell class="household-item household-item-box name-box" name="name" is-link @click.stop="showNameDialog">
        <template #title> <span class="required-icon">*</span>{{ $t('targetName') }} </template>
        <template #default>
          <span class="right-value text-over-ellipsis">{{ curResidentInfo && curResidentInfo.memberName }}</span>
        </template>
      </van-cell>

      <!-- 人脸部分 -->
      <div class="household-item-box">
        <div class="section-title household-title">{{ $t('personFace') }}</div>
        <div class="face-content">
          <div v-if="!facialImage" class="face-placeholder">
            <div class="face-upload-box">
              <div class="camera-icon" @click.stop="editTargetFace">
                <theme-image class="photo-icon" alt="photo" imageName="photo.png" />
              </div>
            </div>
            <div class="right-value upload-text">{{ $t('facePhotoTip') }}</div>
          </div>
          <template v-else>
            <div class="face-image">
              <img :src="facialImage" alt="face" />
            </div>
            <div class="face-actions">
              <theme-image
                class="action-icon"
                alt="delete"
                imageName="photo_delete.png"
                @click.stop="deleteTargetFace"
              />
              <div class="divider-line"></div>
              <theme-image class="action-icon" alt="edit" imageName="photo_edit.png" @click.stop="editTargetFace" />
            </div>
          </template>
        </div>
      </div>
    </div>

    <div class="footer">
      <van-button class="footer-btn" type="primary" @click="handleConfirm">
        {{ $t('confirm') }}
      </van-button>
    </div>
    <!-- 姓名编辑弹窗 -->
    <edit-dialog
      ref="nameDialog"
      :title="$t('targetName')"
      :value="tempMemberName"
      :maxLength="30"
      @confirm="confirmName"
    />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import ThemeImage from '@/components/ThemeImage.vue'
import EditDialog from './dialog/EditDialog.vue'
import { appReqeustNative } from '@/utils/appbridge.js'
import { mapState, mapMutations } from 'vuex'
import { base64ToBlob } from '@/utils/common.js'
import MD5 from 'js-md5'
import { smartUpload as ossSmartUpload } from '@/utils/aliOss'
import { smartUpload as awsSmartUpload } from '@/utils/awsS3'
import { updateRoomMember, getOssAccess, getFaceImage } from '@/api/householdManagement.js'

export default {
  name: 'ModifyResident',
  components: {
    NavBar,
    ThemeImage,
    EditDialog
  },
  data() {
    return {
      tempMemberName: '',
      cardNoList: [],
      curResidentInfo: {},
      facialImage: null,
      fileInfo: {
        facialImageHash: '',
        faceFile: null,
        faceFileUrl: ''
      }
    }
  },
  computed: {
    ...mapState('householdManagement', ['residentInfo'])
  },
  mounted() {
    this.curResidentInfo = { ...this.residentInfo }
    this.cardNoList = this.residentInfo.cardNo ? this.residentInfo.cardNo.split(',') : []
    // 调用初始化方法，单独获取人脸图片
    this.initResidentInfo()
  },
  methods: {
    ...mapMutations('householdManagement', ['SET_RESIDENT_INFO']),

    // 优化：初始化住户信息方法 - 避免重复的API请求
    async initResidentInfo() {
      try {
        // 优先检查是否已经有头像URL（从faceEntry传递过来）
        const existingFaceImageUrl = this.residentInfo.facialImageUrl || this.residentInfo.faceImageUrl

        if (existingFaceImageUrl) {
          // 如果已经有头像URL，直接使用，避免重复请求
          this.facialImage = existingFaceImageUrl
          return
        }

        // 只有当没有头像URL且有住户ID时，才发起API请求
        if (this.curResidentInfo.buildingMemberId) {
          const { data } = await getFaceImage({
            id: this.curResidentInfo.buildingMemberId
          })
          this.facialImage = data
        } else {
          // 如果没有ID，使用本地的人脸图片
          this.facialImage = this.residentInfo.facialImage
        }
      } catch (error) {
        console.error('获取人脸图片失败：', error)
        // 如果获取失败，使用本地的人脸图片作为备选
        this.facialImage = this.residentInfo.facialImage || this.residentInfo.facialImageUrl
      }
    },

    back() {
      this.SET_RESIDENT_INFO({})
      this.$router.back()
    },
    showNameDialog() {
      this.tempMemberName = this.curResidentInfo.memberName
      this.$refs.nameDialog.show = true
    },
    confirmName(value) {
      this.$refs.nameDialog.show = false
      this.curResidentInfo.memberName = value
      this.tempMemberName = ''
    },
    // 保存当前编辑后的住户信息
    saveMemberInfo() {
      this.SET_RESIDENT_INFO({
        ...this.residentInfo,
        memberName: this.curResidentInfo.memberName,
        facialImage: this.facialImage
      })
    },
    // 打开人脸编辑弹窗
    takeFacePhoto() {
      return new Promise((resolve, reject) => {
        appReqeustNative(
          {
            url: 'REQUEST_PLUGIN_CAMERA_FACE',
            params: '',
            timeout: 3000
          },
          res => {
            try {
              const data = JSON.parse(res)
              if (parseInt(data.code) === 200) {
                const faceImg = `data:image/jpg;base64,${data.body}`
                resolve(faceImg)
              } else if (parseInt(data.code) === 101) {
                this.$toast({
                  message: this.$t('personFace.sizeTip'),
                  position: 'middle'
                })
                reject(new Error(this.$t('personFace.sizeTip')))
              } else {
                reject(new Error(data.msg || '拍照失败'))
              }
            } catch (error) {
              reject(error)
            }
          }
        )
      })
    },
    // 编辑人脸
    async editTargetFace() {
      this.saveMemberInfo()
      try {
        // 打开人脸录入页面拍摄
        const faceImg = await this.takeFacePhoto()
        if (faceImg) {
          // 将base64转换为文件并上传
          await this.uploadfacialImage(faceImg)
        }
      } catch (error) {
        console.error('拍照失败：', error)
        // this.$toast.fail(error.message)
      }
    },
    // 上传人脸图片
    async uploadfacialImage(base64Str) {
      try {
        this.$loading.show({
          message: this.$t('uploading'),
          forbidClick: true
        })

        const blob = base64ToBlob(base64Str)
        const faceFile = blob
        const faceFileName = `face${Date.now()}.jpg`

        // 计算文件Hash
        this.fileInfo.facialImageHash = await this.calculateFileHash(faceFile)
        this.fileInfo.faceFile = faceFile

        // 获取上传凭证 - 使用 buildingMemberId 作为 businessId
        const params = { businessType: 'buildingUserMgr', businessId: this.curResidentInfo?.buildingMemberId }
        const { data: ossData } = await getOssAccess(params)
        const { storageInfo, storageType } = ossData
        const uploadToken = JSON.parse(storageInfo)
        const objectNamePrefix = uploadToken.objectNamePrefix
        const faceFileUrl = `${objectNamePrefix}${this.curResidentInfo?.userId || 'default'}/${faceFileName}`
        uploadToken.faceFileUrl = faceFileUrl
        this.fileInfo.faceFileUrl = faceFileUrl

        // 根据存储类型和文件大小选择上传策略
        if (storageType === 'ALI_OSS') {
          await this.smartOssUpload(uploadToken, faceFile)
        } else {
          await this.smartAwsUpload(uploadToken, faceFile)
        }

        this.facialImage = base64Str
        this.$toast.success(this.$t('uploadSuccess'))
      } catch (error) {
        this.$toast.fail(this.$t('uploadFail'))
        console.error('上传人脸图片失败：', error)
      } finally {
        this.$loading.hide()
      }
    },

    // 智能选择阿里云上传方式 - 使用工具函数
    async smartOssUpload(uploadToken, file) {
      const progressCallback = percentage => {
        this.loading?.setText(
          this.$t('uploadProgress', {
            percent: (percentage * 100).toFixed(2)
          })
        )
      }
      return ossSmartUpload(uploadToken, file, uploadToken.faceFileUrl, progressCallback)
    },

    // 智能选择AWS上传方式 - 使用工具函数
    async smartAwsUpload(uploadToken, file) {
      const progressCallback = percentage => {
        this.loading?.setText(
          this.$t('uploadProgress', {
            percent: (percentage * 100).toFixed(2)
          })
        )
      }
      return awsSmartUpload(uploadToken, file, uploadToken.faceFileUrl, progressCallback)
    },

    // 新增：工具方法：文件大小验证
    validateFileSize(file) {
      const maxSize = 10 * 1024 * 1024 // 10MB限制
      if (file.size > maxSize) {
        this.$toast.fail(this.$t('fileTooLarge'))
        return false
      }
      return true
    },

    // 新增：工具方法：计算文件Hash
    calculateFileHash(file) {
      return new Promise(resolve => {
        const reader = new FileReader()
        reader.onload = e => {
          resolve(MD5(e.target.result))
        }
        reader.readAsArrayBuffer(file)
      })
    },

    async deleteTargetFace() {
      try {
        await this.$dialog.confirm({
          title: this.$t('tips'),
          message: this.$t('deleteConfirm'),
          confirmButtonText: this.$t('confirm'),
          cancelButtonText: this.$t('cancel')
        })
        // 用户点击确认后执行删除操作
        this.facialImage = null
        // 清空文件信息
        this.fileInfo = {
          facialImageHash: '',
          faceFile: null,
          faceFileUrl: ''
        }
        this.$toast.success(this.$t('deleteSuccess'))
      } catch {
        // 用户点击取消，不做任何操作
      }
    },
    async handleConfirm() {
      // 检验姓名
      if (!this.curResidentInfo.memberName) {
        this.$toast(this.$t('enterMemberName'))
        return
      }
      this.$loading.show()
      try {
        const { buildingMemberId, memberContact, memberName } = this.curResidentInfo
        // 编辑住户 - 更新 facialImage 传递逻辑
        const params = {
          buildingMemberId,
          memberContact,
          memberName,
          facialImage: this.fileInfo?.faceFileUrl,
          facialImageHash: this.fileInfo?.facialImageHash
        }
        for (let key in params) {
          if (!params[key]) delete params[key]
        }
        if (!this.facialImage) {
          // 表示删除人脸
          params.facialImage = ''
          params.facialImageHash = ''
        }
        await updateRoomMember(params)
        this.$toast(this.$t('editSuccess'))
        this.back()
      } catch (error) {
        console.error('编辑住户失败：', error)
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.household-manangement-wrapper {
  height: 100%;
  .household-content {
    padding-top: 10px;
    .household-item {
      padding: 12px 16px;
      .required-icon {
        margin-right: 4px;
      }
      .right-value {
        font-size: 14px;
      }
    }
    .section-title {
      padding: 12px 16px;
      font-size: 14px;
    }
    .name-box {
      margin-top: 10px;
    }
    .face-content {
      padding: 0 16px 16px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      .face-placeholder {
        width: 100%;
        height: 108px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .face-upload-box {
          width: 80px;
          height: 108px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 12px;
          .camera-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #00000033;
            display: flex;
            justify-content: center;
            align-items: center;
            .photo-icon {
              width: 24px;
              height: 24px;
            }
          }
        }
        .upload-text {
          font-size: 14px;
        }
      }
      .face-image {
        width: 80px;
        height: 108px;
        border-radius: 4px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .face-actions {
        width: 80px;
        height: 24px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .action-icon {
          width: 24px;
          height: 24px;
          cursor: pointer;
        }
        .divider-line {
          width: 1px;
          height: 24px;
          background: #ebebeb;
        }
      }
    }
  }
  .footer {
    position: fixed;
    bottom: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .footer-btn {
      width: 343px;
      height: 40px;
      border-radius: 4px;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
