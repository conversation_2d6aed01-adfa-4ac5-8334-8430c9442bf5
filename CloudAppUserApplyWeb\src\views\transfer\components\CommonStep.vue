<template>
  <div class="common-step-list">
    <div
      :class="[
        'common-step-item',
        value < item.step ? 'step-item-inactive' : '',
        value === item.step ? 'step-item-active' : '',
        value > item.step ? 'step-item-finish' : ''
      ]"
      v-for="item in stepList"
      :key="item.step"
    >
      <div class="step-dot">
        <div :class="['step-dot-before', value >= item.step ? 'step-dot-before-active' : '']"></div>
        <div class="step-dot-icon">
          <theme-image
            v-if="value > item.step"
            class="step-dot-img"
            alt="finish-check"
            imageName="step_finish_check.png"
          />
          <span v-else>{{ item.step }}</span>
        </div>
        <div :class="['step-dot-after', value > item.step ? 'step-dot-after-active' : '']"></div>
      </div>
      <div class="step-title">
        <div class="step-title-ellipsis2">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import ThemeImage from '@/components/ThemeImage.vue'
import i18n from '@/lang'

export default {
  name: 'CommonStep',
  components: {
    ThemeImage
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number,
      default: 1
    },
    stepList: {
      type: Array,
      default: () => [
        {
          step: 1,
          title: i18n.t('deviceTransfer')
        },
        {
          step: 2,
          title: i18n.t('trusteeshipAuthApply')
        }
      ]
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.common-step-list {
  width: 100%;
  height: 70px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-body2-size, 14px);
  .common-step-item {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .step-dot {
      width: 100%;
      margin-top: 6px;
      height: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .step-dot-icon {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .step-title {
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .step-title-ellipsis2 {
      overflow: hidden; // 超出的文本隐藏
      text-overflow: ellipsis; // 溢出用省略号显示
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      // word-break: break-all;
    }
  }
  .step-item-inactive {
    .step-dot-icon {
      background-color: var(--bg-color-secondary, #eeeeee);
      color: var(--bg-color-white, #ffffff);
    }
    .step-title {
      color: #8f9cb3;
    }
  }
  .step-item-active {
    .step-dot-icon {
      background-color: var(--brand-bg-color-active, #1d71f3);
      color: var(--bg-color-white, #ffffff);
    }
    .step-title {
      color: var(--brand-bg-color-active, #1d71f3);
    }
  }
  .step-item-finish {
    .step-dot-icon {
      background-color: var(--brand-bg-color-active, #1d71f3);
      color: var(--bg-color-white, #ffffff);
    }
    .step-title {
      color: var(--bg-color-black, #000000);
    }
  }

  //   .common-step-item:first-child
  .step-dot-before {
    content: '';
    width: calc(50% - 21px);
    height: 1px;
    background-color: var(--outline-color-primary, #c6c6c6);
    position: absolute;
    left: 0px;
  }
  .step-dot-after {
    content: '';
    width: calc(50% - 21px);
    height: 1px;
    background-color: var(--outline-color-primary, #c6c6c6);
    position: absolute;
    right: 0px;
  }
  .common-step-item:first-child .step-dot-before {
    display: none;
  }
  .common-step-item:last-child .step-dot-after {
    display: none;
  }
  .step-dot-before-active,
  .step-dot-after-active {
    background-color: var(--brand-bg-color-active, #1d71f3);
  }
  .step-dot-img {
    width: 22px;
    height: 22px;
  }
}
</style>
