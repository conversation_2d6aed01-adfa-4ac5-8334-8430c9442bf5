<template>
  <div class="upgrade-list">
    <nav-bar @clickLeft="back"></nav-bar>
    <!-- 设备升级 -->
    <div class="device-upgrade">
      <div class="title">{{ $t('deviceUpdate') }}</div>
      <div class="container">
        <div class="title-text">
          <div class="left">
            <div class="title-text-img">
              <theme-image alt="nvr" imageName="nvr.png" />
            </div>
            <div class="title-text-title text-over-ellipsis">{{ devInfo.devName }}</div>
          </div>
          <div class="right">
            <div
              :class="[
                'title-text-button',
                !isDeviceNeedUpgrade || [1, 2].includes(devInfo._state) ? 'title-button-disabled' : ''
              ]"
              @click="deviceUpgrade"
            >
              {{ $t('updateNow') }}
            </div>
          </div>
        </div>
        <div class="list-content" v-if="isDeviceNeedUpgrade">
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('currentVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">{{ devInfo.oldVer || '--' }}</div>
          </div>
          <div class="list-content-row" v-if="devInfo.ver">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('latestVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">{{ devInfo.ver || '--' }}</div>
          </div>
          <div class="list-content-row" v-if="devInfo.verNote">
            <div class="view-btn" @click="viewUpdateContent(devInfo.verNote)">
              {{ $t('viewUpdateContent') }}
            </div>
          </div>
          <!-- 设备升级 状态 下载中、下载失败、升级中、升级失败==安装失败 ？、升级成功、离线-->
          <div class="list-content-row" v-if="devInfo._state">
            <div class="download-status">
              <div
                v-if="devInfo.showDevStatusText"
                class="download-status-text"
                :style="`color: ${statusColor[devInfo.devStatusColor]}`"
              >
                {{ devInfo.devStatusText }}
              </div>
            </div>
          </div>
          <!-- 提示 -->
          <div class="list-content-row" v-if="isShowUpgradeTip">
            <div class="download-tip">{{ $t('upgradeTip') }}</div>
          </div>
        </div>
        <!-- 设备 没有更新的 -->
        <div class="list-content" v-else>
          <div class="list-content-row">
            <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('currentVersion') }}</div>
            <div :class="['value', languageFlag ? 'zh-value' : '']">
              {{ devInfo.oldVer ? devInfo.oldVer : devInfo.ver ? devInfo.ver : '' }}
            </div>
          </div>
          <div class="list-content-row">
            <div class="has-latest-version">{{ $t('hasLatestVersion') }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 摄像机升级 -->
    <!-- ViewStation跟NVR协议一样，但是没有摄像机，需要区分开来 -->
    <template v-if="devType !== '13' && isCameraSupportUpgrade">
      <div class="camera-upgrade" v-if="isHasCameraUpgrade">
        <div class="title-div">
          <div class="title">{{ $t('cameraUpdate') }}</div>
          <div class="title-img" @click="upgradeAllCamera">
            <theme-image alt="upgrade" imageName="upgrade.png" />
          </div>
        </div>
        <div class="camera-has-upgrade" v-if="cameraList.length > 0">
          <div class="container-ul">
            <div class="container-li" v-for="(item, index) in cameraList" :key="'camera' + index">
              <div>
                <div class="title-text">
                  <div class="left">
                    <div class="title-text-img">
                      <theme-image alt="camera" imageName="camera.png" />
                    </div>
                    <div class="title-text-title text-over-ellipsis">
                      {{ channleObj[item.chlIndex] ? channleObj[item.chlIndex].chlName : '' }}
                    </div>
                  </div>
                  <div class="right">
                    <div class="title-text-button" @click="upgradeCamera(item)">{{ $t('updateNow') }}</div>
                  </div>
                </div>
                <div class="list-content">
                  <div class="list-content-row">
                    <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('currentVersion') }}</div>
                    <div :class="['value', languageFlag ? 'zh-value' : '']">{{ item.oldVer || '--' }}</div>
                  </div>
                  <div class="list-content-row">
                    <div :class="['label', languageFlag ? 'zh-label' : '']">{{ $t('latestVersion') }}</div>
                    <div :class="['value', languageFlag ? 'zh-value' : '']">{{ item.ver || '--' }}</div>
                  </div>
                  <div class="list-content-row" v-if="item.verNote">
                    <div class="view-btn" @click="viewUpdateContent(item.verNote)">
                      {{ $t('viewUpdateContent') }}
                    </div>
                  </div>
                  <!-- 通道升级的过程分为待下载、下载中 下载进度、升级中、升级成功、升级失败的状态 (IPC没有离线状态) -->
                  <div class="list-content-row" v-if="item._state">
                    <div class="download-status">
                      <div
                        v-if="item.showIpcStatusText"
                        class="download-status-text"
                        :style="`color: ${statusColor[item.IpcStatusColor]}`"
                      >
                        {{ item.IpcStatusText }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="camera-upgrade" v-else>
        <div class="title-div">
          <div class="title">{{ $t('cameraUpdate') }}</div>
          <div class="title-img" @click="upgradeAllCamera">
            <theme-image alt="upgrade" imageName="upgrade.png" />
          </div>
        </div>
        <div class="camera-no-upgrade">{{ $t('noCameraUpgrade') }}</div>
      </div>
    </template>
    <div class="footer">
      <div class="footer-btn" @click="checkVersion">{{ $t('handleCheck') }}</div>
    </div>
    <!-- 查看 更新内容弹框 -->
    <van-popup v-model="showUpdateContent" class="pop-dialog" :close-on-click-overlay="false">
      <div class="pop-div">
        <div class="dialog-title">{{ $t('updateNote') }}</div>
        <div class="dialog-close-img" @click="closePopup">
          <theme-image alt="close" imageName="close.png" />
        </div>
      </div>
      <div class="update-box">
        <div class="update-content">{{ updateContent }}</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appSetTitle, appBack } from '@/utils/appbridge'
import { getParamsFromUserAgent, getUrlQuery, getMaxUrlQuery, debounce } from '@/utils/common'
import { getCloudUpgradeInfo, cloudUpgrade, getUpgradeResult, getChanneList } from '@/api/cloudUpgrade'
import { STATUS_COLOR_THEME } from '@/utils/options.js'
import { mapState } from 'vuex'
import ThemeImage from '@/components/ThemeImage.vue'

export default {
  name: 'upgradeList',
  components: {
    NavBar,
    ThemeImage
  },
  data() {
    return {
      pageContent: navigator.userAgent,
      devId: '',
      bindState: 0, // SN和安全码 添加的   bindState是1     ip添加的 bindState是0  IP添加的设备升级是要输入账号密码
      isDeviceNeedUpgrade: true, //设备 有没有更新的 devInfo state字段   lastest/false  newVersion/true
      isCameraSupportUpgrade: true, //摄像机是否支持云升级
      isHasCameraUpgrade: true, //是否有摄像机云升级
      showUpdateContent: false,
      updateContent: '',
      devInfo: {
        state: '',
        devName: '',
        oldVer: '',
        ver: '',
        newVersionGUID: '',
        progress: '',
        verNote: '',
        showDevStatusText: false, //设备 升级的状态展示
        devStatusColor: '',
        devStatusText: ''
      },
      cameraList: [], //摄像机 字段 _name,state,progress,version,newVersion,newVersionGUID,_id,_ip,showIpcStatusText,IpcStatusText,IpcStatusColor,verNote
      recordCamData: [], // 对比记录的是否展示的ipc
      devNewVersionInfo: [], // 版本更新内容
      timer: null,
      cloudUpgradeStateMap: {
        // latest: this.$t('hasLatestVersion'), // 当前为最新版本
        // newVersion: '', // 有新版本
        // checkingVersion: '', // 版本检测中
        waitingForUpgrade: this.$t('waitDownload'), // 待下载
        downloading: this.$t('inprogress'), // 下载中
        downloadFail: this.$t('downloadFail'), // 下载失败，网络异常
        downloadNetException: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailNodeInvalid: this.$t('downloadFail'), // 下载失败，通道被删除或通道被拔出所致
        downloadFailOSSException: this.$t('downloadFail'), // 下载失败，OSS异常所致
        downloadFailNodeDisconnect: this.$t('downloadFail'), // 下载失败，通道离线导致的失败
        downloadFailFileWritExecption: this.$t('downloadFail'), // 下载失败，文件写失败
        downloadFailFileReadExecption: this.$t('downloadFail'), // 下载失败，文件读取失败
        downloadFailFileOpenExecption: this.$t('downloadFail'), // 下载失败，文件打开失败
        downloadSuccess: this.$t('downloadFinished'), // 下载完成
        installing: this.$t('inupgrade'), // 升级中
        installSuccess: this.$t('upgradeSuccess'), // 升级成功
        installFail: this.$t('upgradeFail'), // 升级失败，转发失败的错误原因还未明确
        installFailNodeDisconnect: this.$t('upgradeFail'), // 升级失败，通道离线所致
        installFailNodeInvalid: this.$t('upgradeFail'), // 升级失败，通道被删除或POE通道被拔出所致
        offLine: this.$t('offline'), //app给的code判断的设备离线
        repeatedlyRestart: this.$t('repeatedlyRestart'), // 反复重启
        versionUnchanged: this.$t('versionUnchanged'), // 版本不变
        versionException: this.$t('versionException') // 版本异常
      },
      errorCode: null, //10000 设备离线
      bridgeType: null, // APP类型，需要区分出superlive max
      devType: null, // 设备类型 1：IPC 2：NVR 3：DVR 4：依图套装 5：TVT套装 7：依图NVR 8：NVMS 9：卡片机 10：门铃 11：太阳能IPC 13 ViewStation,
      channleObj: {}, // 设备下通道信息
      statusTextObj: {
        0: 'waitingForUpgrade', // 待下载
        1: 'downloadSuccess', // 下载成功
        2: 'installSuccess', // 升级成功
        3: 'repeatedlyRestart', // 反复重启
        4: 'offLine', // 设备离线
        5: 'downloadFail', // 下载失败
        7: 'versionUnchanged', // 版本不变
        8: 'versionException' // 版本异常
      },
      preState: null // 上一次记录的云升级状态
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
    this.timer = null
  },
  mounted() {
    appSetTitle(this.$t('upgrade'))
    const { bridgeType } = getParamsFromUserAgent()
    this.bridgeType = bridgeType
    let json = {}
    if (bridgeType === 'superMax') {
      // superlive max走正常的网址解析
      json = getMaxUrlQuery(window.location.href)
    } else {
      json = getUrlQuery(window.location.href)
    }
    this.devId = json.devId
    this.bindState = json.bindState
    this.devInfo.devName = decodeURIComponent(decodeURIComponent(json.devName))
    this.devType = json.devType
    this.recordCamData = []
    this.getChanneList()
    // // 之前superlive cloud等APP的逻辑
    this.getCloudUpgradeInfo()
    this.startTimer() //开启定时器
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    isShowUpgradeTip() {
      let stateList = [
        'waitingForUpgrade',
        'downloading',
        'downloadFail',
        'downloadNetException',
        'downloadFailNodeInvalid',
        'downloadFailOSSException',
        'downloadFailNodeDisconnect',
        'downloadFailFileWritExecption',
        'downloadFailFileReadExecption',
        'downloadFailFileOpenExecption',
        'downloadSuccess',
        'installing',
        'offLine'
      ]
      return !!stateList.includes(this.devInfo.state)
    },
    languageFlag() {
      return this.language == 'zh'
    },
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    },
    statusColor() {
      return STATUS_COLOR_THEME(this.appStyleFlag, this.uiStyleFlag)
    }
  },
  methods: {
    back() {
      clearInterval(this.timer)
      this.timer = null
      appBack()
    },
    // 查询设备通道信息
    async getChanneList() {
      try {
        const params = { sn: this.devId }
        const res = await getChanneList(params)
        this.channleObj = (res.data || []).reduce((pre, next) => {
          const { chlIndex } = next
          pre[chlIndex] = { ...next }
          return pre
        }, {})
      } catch (err) {
        console.error(err)
      }
    },
    // 查询NVR云升级信息
    async getCloudUpgradeInfo() {
      const params = {
        sn: this.devId,
        allChls: true // 是否查询所有通道升级数据，可选。true则表示所有通道
      }
      try {
        const res = await getCloudUpgradeInfo(params)
        const { device, chls, packages } = res.data
        // 遍历packages记录设备及通道的升级信息
        const packagesObj = (packages || []).reduce((pre, next) => {
          const { verId } = next
          pre[verId] = { ...next }
          return pre
        }, {})
        this.packagesObj = packagesObj
        const { oldVer, ver, verId } = device || {}
        this.devInfo = { ...this.devInfo, ...(device || {}), ...(packagesObj[verId] || {}) }
        this.isDeviceNeedUpgrade = !!oldVer && !!ver // 可升级版本号，有新版本时才有此节点
        this.cameraList = (chls || []).map(item => {
          const { verId } = item
          return {
            ...item,
            ...(packagesObj[verId] || {})
          }
        })
        console.log('res', res)
      } catch (err) {
        console.error(err)
      }
    },
    // 设备升级
    deviceUpgrade() {
      // 已经是最新版本则不响应事件
      if (!this.isDeviceNeedUpgrade) return
      // 状态是下载成功和升级成功泽不响应事件
      if ([1, 2].includes(this.devInfo._state)) return
      let that = this
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
      } else if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
      } else if (this.bindState == 0) {
        this.$refs.checkUserPwd.show = true // 用户名密码添加的设备
      } else {
        that.cloudUpgrade()
      }
    },
    // 设备升级核心调用
    cloudUpgrade() {
      const that = this
      let tips = this.diffTip(this.$t('deviceUpgradeInfo'))
      this.$dialog
        .confirm(tips)
        .then(async () => {
          that.$toast.loading({
            // superlive max显示加载中文字
            message: that.bridgeType === 'superMax' ? that.$t('loadingText') : '',
            className: 'upgrade-check-loading',
            forbidClick: true,
            duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
          })
          try {
            const params = {
              sn: this.devId
            }
            const res = await cloudUpgrade(params)
            console.log('执行云升级返回res', res)
            that.$toast.clear()
          } catch (err) {
            console.error(err)
          }
        })
        .catch(() => {})
    },
    // 检测更新 先关闭定时器 检测完后再开启轮询
    checkVersion: debounce(async function () {
      this.$toast.loading({
        // superlive max显示加载中文字
        message: this.bridgeType === 'superMax' ? this.$t('loadingText') : '',
        className: 'upgrade-check-loading',
        forbidClick: true,
        duration: 0 // 展示时长(ms)，值为 0 时，toast 不会消失 要用clear让它消失
      })
      await this.getCloudUpgradeInfo()
      this.$toast.clear()
    }, 100),
    // 查看更新内容
    viewUpdateContent(content) {
      this.showUpdateContent = true
      this.updateContent = content || this.$t('noData')
    },
    closePopup() {
      this.showUpdateContent = false
      setTimeout(() => {
        this.updateContent = ''
      }, 300) // 弹框动画api 默认是300ms 这里处理是滚动条位置初始化
    },
    // 兼容 样式的提示
    diffTip(word) {
      let tip = {
        message: word,
        cancelButtonText: this.$t('cancel'),
        confirmButtonText: this.$t('confirm')
      }
      if (this.style == 'UI1B') {
        tip['title'] = this.$t('tips')
      }
      return tip
    },
    // 摄像机升级
    upgradeCamera(item) {
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      }
      if (this.errorCode == 101001) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      }
      let tips = this.diffTip(this.$t('cameraUpgradeInfo'))
      this.$dialog
        .confirm(tips)
        .then(async () => {
          try {
            const params = {
              sn: this.devId,
              chls: [item.chlIndex]
            }
            const res = await cloudUpgrade(params)
            console.log('执行单个通道云升级返回res', res)
          } catch (err) {
            console.error(err)
          }
        })
        .catch(() => {})
    },
    // 摄像机一键升级
    upgradeAllCamera() {
      if (this.errorCode == 10000) {
        this.$toast(this.$t('deviceDisconnected'))
        return
      }
      if (this.errorCode == 604) {
        this.$toast(this.$t(`errorCode.101001`))
        return
      }
      let tips = this.diffTip(this.$t('cameraUpgradeInfo'))
      this.$dialog
        .confirm(tips)
        .then(async () => {
          try {
            const params = {
              sn: this.devId,
              chls: this.cameraList.map(item => item.chlIndex)
            }
            const res = await cloudUpgrade(params)
            console.log('执行多个通道云升级返回res', res)
          } catch (err) {
            console.error(err)
          }
        })
        .catch(() => {})
    },
    // 查询云升级结果
    async getUpgradeResult() {
      try {
        let resultIds = []
        const { resultId } = this.devInfo
        if (resultId) {
          resultIds.push(resultId)
        }
        this.cameraList.forEach(item => {
          item.resultId && resultIds.push(item.resultId)
        })
        if (resultIds.length === 0) {
          return
        }
        const params = {
          resultIds
        }
        const res = await getUpgradeResult(params)
        // 遍历云升级返回结果，把状态加入设备和通道中
        res.data.forEach(item => {
          const { resultId, state } = item
          const stateText = this.statusTextObj[state]
          if (this.devInfo.resultId === resultId) {
            // 升级后重新请求云升级信息
            if (state > 1) {
              this.getCloudUpgradeInfo()
            }
            // 说明是设备
            this.devInfo = {
              ...this.devInfo,
              _state: state,
              state: stateText,
              showDevStatusText: true,
              devStatusText: this.cloudUpgradeStateMap[stateText],
              devStatusColor: stateText
            }
          } else {
            // 说明是通道
            const idx = this.cameraList.findIndex(item => item.resultId === resultId)
            if (idx > -1) {
              // 升级后重新请求云升级信息
              if (state > 1) {
                this.getCloudUpgradeInfo()
              }
              this.cameraList[idx] = {
                ...this.cameraList[idx],
                _state: state,
                state: stateText,
                showIpcStatusText: !!this.cloudUpgradeStateMap[stateText],
                IpcStatusText: this.cloudUpgradeStateMap[stateText] || '',
                IpcStatusColor: stateText || '' //拿到颜色的key去匹配
              }
            }
          }
        })
        console.log('云升级返回结果', res)
      } catch (err) {
        console.error(err)
      }
    },
    // 查询云升级结果 设备和通道信息的定时器
    startTimer() {
      let that = this
      this.timer = setInterval(() => {
        that.getUpgradeResult()
      }, 3000)
    }
  }
}
</script>
<style lang="scss" scoped>
.upgrade-list {
  height: calc(100% - 70px);
  overflow: auto;
}
.title-button-disabled {
  color: var(--brand-bg-color-disabled, #d6e4fe);
  border: 1px solid var(--brand-bg-color-disabled, #d6e4fe);
}
</style>
