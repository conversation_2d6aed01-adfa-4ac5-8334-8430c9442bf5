<template>
  <div class="panel-devices">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- DSC 标题 -->
      <div class="dsc-title">DSC</div>
      <!-- Status 区域 -->
      <alarm-system-status class="alarm-status" :status="systemData.status" :is-connected="systemData.isConnected" />

      <div class="device-box">
        <!-- 控制面板区域 -->
        <div class="device-section alarm-box-wrapper first-device-section">
          <div class="section-title">{{ $t('controlPanel') }}</div>
        </div>

        <!-- 通讯器区域 -->
        <div class="device-section alarm-box-wrapper">
          <div class="section-title">{{ $t('communicator') }}</div>
        </div>

        <!-- 有线键盘区域 -->
        <div class="device-section alarm-box-wrapper">
          <div class="section-title">{{ $t('wiredKeypad') }}</div>
        </div>

        <!-- 区域设备区域 -->
        <div class="device-section alarm-box-wrapper zone-section">
          <div class="section-title">{{ $t('zone') }}</div>
          <div class="zone-devices">
            <div v-for="(zone, index) in zoneDevices" :key="index" class="zone-device-item alarm-bottom-box">
              <div class="device-icon">
                <theme-image imageName="alarm-system/device_zones.png" alt="zone" class="zone-icon" />
              </div>
              <div class="device-info">
                <div class="device-name">{{ zone.name }}</div>
                <div class="device-zone">
                  <span>{{ zone.zone }}</span>
                  <theme-image class="bind-icon" alt="bind" imageName="alarm-system/bind.png" />
                </div>
                <div class="alarm-sub-text device-status alarm-red-tag" v-if="zone.status">
                  {{ zone.status }}
                </div>
              </div>
              <div class="device-actions">
                <van-popover
                  placement="bottom-end"
                  :offset="[26, 0]"
                  v-model="zone.showPopover"
                  trigger="click"
                  :get-container="getContainer"
                >
                  <ul class="action-btn">
                    <li @click="handleRename(zone)" v-if="isTycoSystem">{{ $t('rename') }}</li>
                    <li @click="handleBypass(zone)" v-if="isRiscoSystem">{{ $t('bypass') }}</li>
                    <li @click="handleBind(zone)">{{ $t('bindChannel') }}</li>
                    <li @click="handleUnbind(zone)">{{ $t('unbindChannel') }}</li>
                  </ul>
                  <template #reference>
                    <div class="action-menu">
                      <theme-image imageName="alarm-system/more.png" alt="more" class="more-icon" />
                    </div>
                  </template>
                </van-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="1" />

    <!-- 编辑设备名称弹窗 -->
    <van-dialog
      v-model="showEditDialog"
      :title="$t('enterDeviceName')"
      :show-cancel-button="true"
      :cancel-button-text="$t('cancel')"
      :confirm-button-text="$t('ok')"
      :before-close="handleEditBeforeClose"
      @opened="handleEditDialogOpened"
      class="common-dialog"
    >
      <div class="common-dialog-content">
        <common-input
          v-model="editDeviceName"
          :placeholder="$t('enterPanelName')"
          :errorMessage="editDeviceNameError"
          borderType="full"
          ref="editField"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import AlarmSystemStatus from './components/AlarmSystemStatus.vue'
import CommonInput from './components/CommonInput.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'PanelDevices',
  components: {
    AlarmBottomNavigation,
    AlarmSystemStatus,
    CommonInput
  },
  data() {
    return {
      showEditDialog: false,
      editDeviceName: '',
      editDeviceNameError: '',
      hasNameEmptyError: false, // 添加错误状态标识
      currentEditDevice: null,
      systemData: {
        name: 'DSC',
        status: 'STAY',
        isConnected: true
      },
      zoneDevices: [
        {
          id: 1,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 2,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 3,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 4,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 5,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 6,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        },
        {
          id: 7,
          name: '1 DELAY | HW ZONE CONNECTED DIHU...',
          zone: 'Zone 1',
          status: 'BYPASS',
          icon: 'radio_no_check.png',
          showPopover: false
        }
      ]
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['isTycoSystem', 'isRiscoSystem', 'isPimaSystem'])
  },
  watch: {
    editDeviceName: {
      handler(newVal) {
        // 当输入内容时，清除空值错误状态
        if (newVal && this.hasNameEmptyError) {
          this.editDeviceNameError = ''
          this.hasNameEmptyError = false
        }
      }
    }
  },
  methods: {
    getContainer() {
      return document.querySelector('.panel-devices')
    },
    // 重命名
    handleRename(panel) {
      this.currentEditDevice = panel
      this.editDeviceName = panel.name
      this.editDeviceNameError = ''
      this.showEditDialog = true
    },
    // 弹窗打开完成后的聚焦处理
    handleEditDialogOpened() {
      // 弹窗完全打开后聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.editField) {
          this.$refs.editField.focus()
        }
      })
    },
    // 处理编辑弹窗关闭前的逻辑
    async handleEditBeforeClose(action, done) {
      if (action === 'confirm') {
        await this.handleConfirmEdit(done)
      } else {
        this.handleCancelEdit(done)
      }
    },
    // 处理确认编辑操作
    async handleConfirmEdit(done) {
      if (!this.validateDeviceName()) {
        done(false)
        return
      }
      try {
        await this.performUpdateDevice(this.currentEditDevice, this.editDeviceName.trim())
        done(true)
        this.clearEditDialogState()
      } catch (error) {
        // 更新失败，不关闭弹窗
        done(false)
      }
    },
    // 验证面板名称
    validateDeviceName() {
      this.editDeviceNameError = ''
      this.hasNameEmptyError = false
      if (!this.editDeviceName.trim()) {
        this.editDeviceNameError = this.$t('panelNameCannotBeEmpty')
        this.hasNameEmptyError = true
        return false
      }
      return true
    },
    // 处理取消编辑操作
    handleCancelEdit(done) {
      done(true)
      this.clearEditDialogState()
    },
    // 清理编辑弹窗状态
    clearEditDialogState() {
      this.editDeviceName = ''
      this.editDeviceNameError = ''
      this.currentEditDevice = null
    },
    // 面板更新主入口
    async performUpdateDevice(panel, newName) {
      this.$loading.show()
      try {
        // 模拟API延迟
        console.log(panel, newName)
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.$toast.success(this.$t('deviceNameUpdatedSuccessfully'))
      } catch (error) {
        this.handleApiError(error)
        this.$toast.fail(this.$t('updateFailedTryAgain'))
        throw error
      } finally {
        this.$loading.hide()
      }
    },
    // 统一的API错误处理
    handleApiError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
      // 记录详细错误信息用于调试
      console.error('API Error Details:', error)
    },
    handleBypass(zone) {
      console.log('Bypass zone:', zone)
    },
    handleBind(zone) {
      this.$router.push({
        path: '/alarmSystem/bindChannel',
        query: {
          zoneId: zone.id
        }
      })
    },
    handleUnbind(zone) {
      console.log('Unbind zone:', zone)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-devices {
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-content {
    flex: 1;
    height: 0px;
    display: flex;
    flex-direction: column;

    .dsc-title {
      font-size: 16px;
      line-height: 44px;
      font-weight: 600;
      padding: 0 16px;
    }

    .alarm-status {
      margin: 0 10px;
    }

    .device-box {
      padding: 0 10px;
      flex: 1;
      overflow-y: auto;
    }
  }

  .device-section {
    padding: 0px 16px;
    box-sizing: border-box;
    height: 46px;
    display: flex;
    align-items: center;
    margin-top: 6px;

    &.first-device-section {
      margin-top: 16px;
    }

    .section-title {
      font-size: 14px;
      font-weight: 500;
    }

    &.zone-section {
      flex-direction: column;
      align-items: stretch;
      height: auto;
      padding-bottom: 8px;

      .section-title {
        height: 46px;
        display: flex;
        align-items: center;
        margin-bottom: 0;
      }
    }
  }

  .zone-devices {
    .zone-device-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      .device-icon {
        margin-right: 12px;

        .zone-icon {
          width: 20px;
          height: 32px;
        }
      }

      .device-info {
        flex: 1;
        width: 0px;

        .device-name {
          height: 18px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 4px;
          // 文本截断
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .device-zone {
          height: 18px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 5px;
          display: flex;
          align-items: center;
          .bind-icon {
            width: 20px;
            height: 20px;
            margin-left: 4px;
          }
        }

        .device-status {
          width: fit-content;
          height: 16px;
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 10px;
          line-height: 16px;
          border-radius: 11px;
          padding: 0 6px;
        }
      }

      .device-actions {
        display: flex;
        align-items: center;

        .action-menu {
          cursor: pointer;
          padding: 4px;
        }
      }
    }
  }
  .action-btn {
    width: 190px;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    li {
      padding: 12px 16px;
    }
  }
  ::v-deep .van-popover {
    .van-popover__arrow {
      display: none;
    }
    .van-popover__content {
      border-radius: 2px;
    }
  }
}
</style>
<style lang="scss">
.common-dialog {
  .van-dialog__header {
    padding: 20px 20px 16px 20px !important;
  }
  .van-dialog__content {
    padding: 0 20px 20px 20px !important;
  }
}
</style>
