import router from '@/router'
import store from '@/store'
import { pinyin } from './pinyin.js'
export const utils = {
  chineseToPinYin: function (l1) {
    const l2 = l1.length
    let result = ''
    // eslint-disable-next-line prefer-regex-literals
    const reg = new RegExp('[a-zA-Z0-9]')
    for (let i = 0; i < l2; i++) {
      const val = l1.substr(i, 1)
      const unicode = val.charCodeAt(0)
      // unicode编码范围不在汉字范围内,返回原字符
      if (unicode > 40869 || unicode < 19968) {
        result += val
      } else {
        const name = this.arraySearch(val, pinyin)
        if (reg.test(val)) {
          result += val
        } else if (name !== false) {
          result += name
        }
      }
    }
    result = result.replace(/ /g, '-')
    while (result.indexOf('--') > 0) {
      result = result.replace('--', '-')
    }
    return result
  },
  arraySearch: function (l1) {
    for (const name in pinyin) {
      if (pinyin[name].indexOf(l1) !== -1) {
        return this.ucfirst(name)
      }
    }
    return false
  },
  ucfirst: function (l1) {
    if (l1.length > 0) {
      const first = l1.substr(0, 1)
      const spare = l1.substr(1, l1.length)
      return first + spare
    }
  },
  clearNum(value) {
    let filterValue = value.toString()
    filterValue = filterValue.replace(/^\./g, '') //  不能以“.”开头
    filterValue = filterValue.replace(/[^\d.]/g, '') // 清除“数字”和“.”以外的字符
    filterValue = filterValue.replace(/\.{2,}/g, '.') // 只保留第一个. 清除多余的
    filterValue = filterValue.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
    // filterValue = filterValue.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    if (filterValue.indexOf('.') < 0 && filterValue != '') {
      // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      filterValue = parseFloat(filterValue)
    }
    if (('' + filterValue).indexOf('.') > -1) {
      // 过滤 00.01
      filterValue = parseFloat(filterValue.split('.')[0]) + '.' + filterValue.split('.')[1]
    }
    if (parseFloat(filterValue) > 99999999) {
      filterValue = 99999999
    }
    return filterValue
  },
  intNum(value, min = 1) {
    let filterValue = value.toString()
    filterValue = filterValue.replace(/[^\d]/g, '')
    if (parseFloat(filterValue) > 99999999) {
      filterValue = 99999999
    } else if (parseFloat(filterValue) < min) {
      filterValue = ''
    }
    filterValue = filterValue ? parseFloat(filterValue) : ''
    return filterValue
  },
  base64ToFile(dataurl, fileName) {
    // 将base64转换为file文件
    const arr = dataurl.split(',')
    const mime = arr[0].match(/:(.*?);/)[1]
    const bstr = window.atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], fileName, { type: mime, lastModified: Date.now() })
  },
  zipFun(file, cb) {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = function (e) {
      const image = new Image()
      image.src = e.target.result
      image.onload = function () {
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.width = image.width * 0.92
        canvas.height = image.height * 0.92
        context.drawImage(image, 0, 0, canvas.width, canvas.height)
        const data = canvas.toDataURL(file.type, 0.8)
        const files = utils.base64ToFile(data, file.name)
        if (files.size > 1 * 1024 * 1024) {
          utils.zipFun(files, cb)
        } else {
          return cb(files)
        }
      }
    }
  },
  imgZip(file) {
    return new Promise(resolve => {
      this.zipFun(file, res => {
        resolve(res)
      })
    })
  },
  routerPush(param) {
    // 默认前进
    window._transitionName = 'van-slide-left'
    router.push(param)
  },
  routerReplace(param) {
    // 替换路由
    window._transitionName = 'van-slide-left'
    router.replace(param)
  },
  goBack() {
    // const name = router.history.current.name
    window._transitionName = 'van-slide-right'
    router.go(-1)
  },
  encryptEmail(email) {
    if (email && email.indexOf('@') > 0) {
      const list1 = email.split('@')
      let str1 = ''
      if (list1[0].length >= 3) {
        const len = Math.floor(list1[0].length / 3)
        let mark = ''
        for (let i = 0; i < list1[0].slice(len, -len).length; i++) {
          mark += '*'
        }
        str1 = list1[0].slice(0, len) + mark + list1[0].slice(-len)
      } else {
        str1 = '*'
      }
      const list2 = list1[1].split('.')
      list2[0] = '*'
      const str2 = list2.join('.')
      return str1 + '@' + str2
    } else {
      return email
    }
  },
  formatTimeStamp(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (date && !isNaN(+date)) {
      return window.moment(+date).format(format)
    } else {
      return date
    }
  },
  encryptMobile(mobile) {
    let newMobile = mobile
    if (mobile && mobile.length >= 3) {
      if (mobile.indexOf('+') > 0) {
        mobile = mobile.split('+')
        const len = Math.floor(mobile[1].length / 3)
        let mark = ''
        for (let i = 0; i < mobile[1].slice(len, -len).length; i++) {
          mark += '*'
        }
        newMobile = mobile[0] + '+' + mobile[1].slice(0, len) + mark + mobile[1].slice(-len)
      } else {
        // 无区号
        const len = Math.floor(mobile.length / 3)
        let mark = ''
        for (let i = 0; i < mobile.slice(len, -len).length; i++) {
          mark += '*'
        }
        newMobile = mobile.slice(0, len) + mark + mobile.slice(-len)
      }
    }
    return newMobile
  },
  getUrlKey(name) {
    if (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href)) {
      return (
        decodeURIComponent(
          (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [''])[1].replace(/\+/g, '%20')
        ) || null
      )
    }
  },
  // 获取store中的搜索条件,并清空store
  getSearchParam(obj) {
    let name = ''
    if (obj.searchParam && obj.searchParam.name) {
      name = obj.searchParam.name
      store.commit('SET_SEARCHPARAM', {})
    }
    return name
  },
  debounceFun(fn, delay = 200, immediate = false) {
    // 多次点击按钮，搜索框根据输入的一部分值进行联想搜索(y也可以使用节流)，短信验证码，resize
    let timer = null
    let triggerTime
    const run = wait => {
      timer = setTimeout(() => {
        const executeTime = new Date().getTime()
        const alreadyWait = executeTime - triggerTime
        if (alreadyWait < wait) {
          run(delay - alreadyWait)
        } else {
          if (!immediate) fn()
          timer = null
        }
      }, wait)
    }
    return function () {
      triggerTime = new Date().getTime()
      if (!timer) {
        if (immediate) fn.apply(this, arguments)
        run(delay)
      }
    }
  }
}
