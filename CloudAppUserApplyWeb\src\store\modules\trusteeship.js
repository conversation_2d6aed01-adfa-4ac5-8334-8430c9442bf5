export default {
  namespaced: true,
  state: () => ({
    availableList: []
  }),
  getters: {},
  mutations: {
    SET_AVAILABLE_LIST(state, data) {
      state.availableList = data
    },
    //修改 删除
    MODIFY_AVAILABLE_LIST(state, data) {
      let deleteIndex = data.deleteIndex
      if (data.data) {
        state.availableList.splice(deleteIndex, 1, data.data)
      } else {
        state.availableList.splice(deleteIndex, 1)
      }
    },
    CLEAR_AVAILABLE_LIST(state) {
      state.availableList = []
    }
  },
  actions: {}
}
