<template>
  <div class="share-managment-wrapper">
    <nav-bar @clickLeft="back"></nav-bar>
    <div class="share-managment-content">
      <van-tabs :active="activeTab" @change="changeTab">
        <van-tab :title="$t('myShare')" name="myShare">
          <my-share :activeTab="activeTab" />
        </van-tab>
        <van-tab :title="$t('otherShare')" name="otherShare" :dot="showDot">
          <other-share :activeTab="activeTab" />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script>
import NavBar from '@/components/NavBar'
import MyShare from './MyShare.vue'
import OtherShare from './OtherShare.vue'
import { appSetWebBackEnable, appClose } from '@/utils/appbridge'
import { getUserShareList } from '@/api/share'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'ShareManagment',
  components: {
    NavBar,
    MyShare,
    OtherShare
  },
  props: {},
  data() {
    return {
      activeTab: 'myShare',
      pullingStatus: 0
    }
  },
  created() {
    appSetWebBackEnable(false)
    // 从路由中获取activeTab,用作默认展示tab
    const query = this.$route.query
    if (query.activeTab) {
      this.activeTab = query.activeTab
    } else {
      this.activeTab = 'myShare'
    }
  },
  mounted() {
    this.getMyList()
    this.getOtherList()
  },
  computed: {
    ...mapState('share', ['otherShareList']),
    showDot() {
      // 过滤出他人分享未接受/拒绝的
      const arr = (this.otherShareList || []).filter(item => Number(item.status) === 0)
      return !!arr.length
    }
  },
  methods: {
    ...mapMutations('share', ['SET_MY_SHARE_LIST', 'SET_OTHER_SHARE_LIST']),
    back() {
      appClose()
    },
    changeTab(val) {
      this.activeTab = val
    },
    handleShowDot(val) {
      this.showDot = val
    },
    // 获取我分享的数据--以便展示红点
    async getMyList() {
      try {
        // 1 我分享给别人的  2 别人分享给我的
        let { data } = await getUserShareList({ queryType: 1 })
        const myData = data.map(item => {
          return {
            ...item,
            status: item.shareList[0].status
          }
        })
        // 存储请求数据
        this.SET_MY_SHARE_LIST(myData)
      } catch (err) {
        console.error(err)
      }
    },
    // 获取他人分享的数据--以便展示红点
    async getOtherList() {
      try {
        // 1 我分享给别人的  2 别人分享给我的
        let { data } = await getUserShareList({ queryType: 2 })
        const otherData = data.map(item => {
          return {
            ...item,
            status: item.shareList[0].status
          }
        })
        // 存储请求数据
        this.SET_OTHER_SHARE_LIST(otherData)
      } catch (err) {
        console.error(err)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.share-managment-wrapper {
  height: 100%;
  overflow: hidden;
  .share-managment-content {
    width: 100%;
    height: calc(100% - 44px);
    overflow: auto;
    box-sizing: border-box;
  }
  ::v-deep .van-tabs--line .van-tabs__wrap {
    height: 60px;
  }
  ::v-deep .van-tabs__nav {
    padding: 16px 0px;
    box-sizing: border-box;
    .van-tab__text {
      font-size: var(--font-size-body1-size, 16px);
      line-height: 24px;
      font-weight: 500;
    }
  }
  ::v-deep .van-tab {
    height: 28px;
  }
  ::v-deep .van-tabs__line {
    display: none;
  }
  ::v-deep .van-tab--active {
    position: relative;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
    .van-tab__text {
      position: relative;
      height: 26px;
      &::before {
        content: '';
        position: absolute;
        left: 0px;
        bottom: 0px;
        width: 100%;
        height: 2px;
        border-radius: 1px;
      }
    }
  }
}
</style>
