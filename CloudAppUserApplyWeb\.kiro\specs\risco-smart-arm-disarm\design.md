# 设计文档

## 系统概述

Risco系统智能布撤防功能通过检测面板的分区配置，自动选择合适的API接口（Arm或PartArm）来执行布撤防操作。该设计确保了系统的灵活性和可靠性，同时保持了用户体验的一致性。

## 架构设计

### 核心组件架构

```mermaid
graph TD
    A[用户操作] --> B[AlarmSystemActions组件]
    B --> C{分区检测}
    C -->|有分区| D[executeRiscoPartitionAction]
    C -->|无分区| E[executeRiscoSystemAction]
    D --> F[armPartition API]
    E --> G[armPanel API]
    F --> H[面板状态刷新]
    G --> H
    H --> I[用户反馈]
```

### 数据流设计

1. **输入数据**：用户点击布撤防按钮（home/away/disarm）
2. **分区检测**：检查`panelState.data.state.status.partitions`
3. **接口选择**：根据分区存在情况选择API
4. **数据构造**：根据选择的API构造请求数据
5. **API调用**：执行相应的布撤防操作
6. **状态更新**：刷新面板状态并提供用户反馈

## 组件和接口

### 1. 分区检测组件

#### hasPartitions 计算属性
```javascript
hasPartitions() {
  const partitions = this.panelState?.data?.state?.status?.partitions
  return Array.isArray(partitions) && partitions.length > 0
}
```

**功能**：
- 检测面板状态中是否存在有效的分区配置
- 返回布尔值用于后续的接口选择逻辑
- 处理数据异常情况（null、undefined、非数组）

### 2. 状态码映射组件

#### systemStatusMapping 计算属性
```javascript
systemStatusMapping() {
  return this.isPimaSystem ? SYSTEM_STATUS_MAPPING.PIMA : SYSTEM_STATUS_MAPPING.RISCO
}
```

**Risco系统状态码映射**：
- `home`: 2 (在家布防)
- `away`: 1 (离家布防)  
- `disarm`: 0 (撤防)

### 3. 智能接口选择器

#### executeRiscoAction 方法
```javascript
async executeRiscoAction(action) {
  if (this.hasPartitions) {
    await this.executeRiscoPartitionAction(action)
  } else {
    await this.executeRiscoSystemAction(action)
  }
}
```

**决策逻辑**：
- 有分区 → 使用PartArm接口
- 无分区 → 使用Arm接口
- 异常情况 → 降级到Arm接口

### 4. 分区操作处理器

#### executeRiscoPartitionAction 方法
```javascript
async executeRiscoPartitionAction(action) {
  const partitions = this.panelState.data.state.status.partitions
  const targetState = this.systemStatusMapping[action.type]
  
  const partitionData = {
    partitions: partitions.map(partition => ({
      id: partition.id,
      armedState: targetState,
      readyState: partition.readyState || 2,
      alarmState: partition.alarmState || 0,
      groups: partition.groups || null,
      exitDelayTO: partition.exitDelayTO || 0,
      lastArmFailReasons: partition.lastArmFailReasons || null
    })),
    exitDelay: 0,
    lastArmFailReasons: []
  }
  
  await armPartition(this.siteId, partitionData)
}
```

**数据处理原则**：
- 保持现有分区结构完整性
- 只更新`armedState`字段
- 为缺失字段提供合理默认值
- 添加必要的顶层字段

### 5. 系统操作处理器

#### executeRiscoSystemAction 方法
```javascript
async executeRiscoSystemAction(action) {
  const reqData = {
    newSystemStatus: this.systemStatusMapping[action.type]
  }
  
  await armPanel(this.siteId, reqData)
}
```

**特点**：
- 简单的状态码设置
- 直接映射操作类型到状态码
- 适用于无分区系统

## 数据模型

### 分区数据模型

#### 输入数据结构（从面板状态获取）
```javascript
{
  "data": {
    "state": {
      "status": {
        "partitions": [
          {
            "id": 0,
            "armedState": 0,
            "readyState": 2,
            "alarmState": 0,
            "groups": null,
            "exitDelayTO": 0,
            "lastArmFailReasons": null
          }
        ]
      }
    }
  }
}
```

#### PartArm接口请求数据结构
```javascript
{
  "partitions": [
    {
      "id": 0,
      "armedState": 2,        // 目标状态
      "readyState": 2,        // 保持原值
      "alarmState": 0,        // 保持原值
      "groups": null,         // 保持原值
      "exitDelayTO": 0,       // 保持原值
      "lastArmFailReasons": null  // 保持原值
    }
  ],
  "exitDelay": 0,
  "lastArmFailReasons": []
}
```

#### Arm接口请求数据结构
```javascript
{
  "newSystemStatus": 2  // 目标状态码
}
```

### 状态码定义

| 操作类型 | Risco状态码 | 含义 |
|----------|-------------|------|
| home | 2 | 在家布防 |
| away | 1 | 离家布防 |
| disarm | 0 | 撤防 |

## 错误处理

### 1. 数据异常处理

**异常情况**：
- `partitions`字段为null或undefined
- `partitions`不是数组类型
- `partitions`数组为空
- 分区对象缺少必要字段

**处理策略**：
- 降级使用Arm接口
- 提供默认值填充缺失字段
- 记录错误日志便于调试

### 2. API调用错误处理

**错误类型**：
- 网络连接错误
- 认证失败
- 服务器内部错误
- 请求参数错误

**处理策略**：
- 使用统一的错误处理函数`handleCommonError`
- 显示用户友好的错误提示
- 记录详细的错误信息用于调试

### 3. 状态同步错误处理

**场景**：
- 操作成功但状态刷新失败
- 面板状态获取超时
- 状态数据格式异常

**处理策略**：
- 操作成功提示不受状态刷新影响
- 状态刷新失败只记录日志，不影响用户体验
- 提供手动刷新机制

## 测试策略

### 1. 单元测试

**测试范围**：
- `hasPartitions`计算属性的各种数据情况
- `systemStatusMapping`的正确映射
- 数据构造函数的输出格式
- 错误处理逻辑的覆盖

**测试用例**：
```javascript
// 分区检测测试
describe('hasPartitions', () => {
  it('should return true when partitions exist', () => {
    // 测试有效分区数据
  })
  
  it('should return false when partitions is null', () => {
    // 测试null情况
  })
  
  it('should return false when partitions is empty array', () => {
    // 测试空数组情况
  })
})
```

### 2. 集成测试

**测试场景**：
- 有分区系统的完整布撤防流程
- 无分区系统的完整布撤防流程
- 数据异常时的降级处理
- API调用失败时的错误处理

### 3. 端到端测试

**测试流程**：
1. 模拟不同的面板配置（有分区/无分区）
2. 执行各种布撤防操作
3. 验证API调用的正确性
4. 检查状态更新和用户反馈

### 4. 性能测试

**测试指标**：
- 分区检测的响应时间
- API调用的响应时间
- 状态刷新的完成时间
- 内存使用情况

## 调试和监控

### 1. 日志系统

**日志级别**：
- INFO: 接口选择和数据构造信息
- WARN: 数据异常和降级处理
- ERROR: API调用失败和系统错误

**日志格式**：
```javascript
console.log('🔧 使用PartArm接口，分区数据:', partitionData)
console.log('🔧 使用Arm接口，系统数据:', reqData)
console.warn('⚠️ 分区数据异常，降级使用Arm接口')
```

### 2. 性能监控

**监控指标**：
- 接口选择决策时间
- API调用成功率
- 状态刷新成功率
- 用户操作响应时间

### 3. 错误追踪

**追踪内容**：
- 分区检测失败的原因
- API调用失败的详细信息
- 状态同步失败的上下文
- 用户操作的完整链路

## 扩展性设计

### 1. 新系统类型支持

**扩展点**：
- 状态码映射配置
- 接口选择逻辑
- 数据构造规则

**实现方式**：
- 配置驱动的系统类型识别
- 插件化的操作处理器
- 统一的接口抽象层

### 2. 新操作类型支持

**扩展点**：
- 操作按钮配置
- 状态码映射
- 用户界面布局

**实现方式**：
- 配置化的操作定义
- 动态的按钮生成
- 灵活的状态映射

### 3. 高级功能支持

**潜在功能**：
- 定时布撤防
- 条件布撤防
- 批量操作
- 操作历史记录

**设计考虑**：
- 保持核心功能的简洁性
- 通过插件机制添加高级功能
- 维护向后兼容性

这个设计确保了Risco系统智能布撤防功能的可靠性、可维护性和可扩展性，同时提供了良好的用户体验和开发者体验。