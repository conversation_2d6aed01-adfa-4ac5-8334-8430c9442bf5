<template>
  <span>
    {{ formatTime }}
  </span>
</template>

<script>
export default {
  name: 'CountDown',
  props: {
    time: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      formatTime: '',
      initialTIme: 0,
      timer: 0
    }
  },
  watch: {
    time: {
      handler(newValue) {
        if (typeof newValue === 'string') {
          clearInterval(this.timer)
          this.formatTime = newValue
        } else {
          clearInterval(this.timer)
          this.initialTIme = Date.now()

          const getFormatTime = () => {
            const now = Date.now()
            const diff = Math.max(newValue - (now - this.initialTIme), 0)

            if (diff === 0) {
              clearInterval(this.timer)
              this.$emit('timeup')
              this.formatTime = '00:00:00'
              return
            }

            // 使用系统时间计算，避免误差
            this.formatTime = window.moment(diff).utc().format('HH:mm:ss')
          }
          getFormatTime()

          this.timer = setInterval(getFormatTime, 1000)
        }
      },
      immediate: true
    },
    beforeDestroy() {
      clearInterval(this.timer)
    }
  }
}
</script>

<style></style>
