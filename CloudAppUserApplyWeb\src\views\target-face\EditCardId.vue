<template>
  <div class="target-face-edit-id">
    <nav-bar @clickLeft="back" :title="$t('cardId')" class="target-face-nav-bar"> </nav-bar>
    <div class="margin-top"></div>
    <van-swipe-cell v-for="(item, index) in cardIds" :key="index">
      <van-cell class="target-item" :title="$t('cardId')">
        <van-field
          v-model="cardIds[index]"
          type="number"
          @input="value => handleInputId(value, index)"
          class="target-face-input"
          :maxlength="personFileds.identifyNumber.maxCardLen"
        />
      </van-cell>
      <template #right>
        <van-button square type="danger" class="swipe-right-btn" @click="delCardId(index)">
          <img
            class="refuse-img"
            alt=""
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/delete.png')"
          />
        </van-button>
      </template>
    </van-swipe-cell>
    <div class="add-btn-wrapper" @click="addCardId">
      <van-icon name="plus" class="add-btn" />
    </div>
    <div class="submit-wrapper">
      <div class="submit-btn" @click.prevent="submit">{{ $t('confirm') }}</div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import NavBar from '@/components/NavBar'

export default {
  name: 'EditCardId',
  components: {
    NavBar
  },
  data() {
    return {
      cardIds: []
    }
  },
  created() {
    this.SET_QUERY_DETAIL(false)
    this.initCardIds()
  },
  computed: {
    ...mapState('app', ['style', 'appType']),
    ...mapState('targetFace', ['targetFaceDetail', 'personFileds']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('targetFace', ['SET_QUERY_DETAIL']),
    initCardIds() {
      this.cardIds = this.targetFaceDetail.cardIds.slice()

      if (this.cardIds.length === 0) {
        this.addCardId()
      }
    },
    delCardId(index) {
      this.cardIds.splice(index, 1)
    },
    addCardId() {
      if (this.cardIds.length < this.personFileds.identifyNumber.maxCount) {
        this.cardIds.push('')
      } else {
        this.$toast(this.$t('cardIdTip', { max: this.personFileds.identifyNumber.maxCount }))
      }
    },
    handleInputId(value, index) {
      this.$set(this.cardIds, index, value.replace(/[^0-9]/g, ''))
    },
    back() {
      this.$router.go(-1)
    },
    submit() {
      const items = this.cardIds.filter(item => item).slice()

      const idSet = new Set(items)

      if (idSet.size !== items.length) {
        this.$toast(this.$t('cardIdExist'))
      } else {
        this.targetFaceDetail.cardIds = items
        this.back()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.target-face-edit-id {
  .margin-top {
    margin-top: 10px;
  }
  ::v-deep .target-item {
    line-height: 30px;

    &::after {
      right: 0;
      left: 0;
    }
    .van-cell__title {
      display: flex;
      align-items: center;
    }
    .van-cell__value {
      flex-grow: 1.6;
    }
    .target-face-input {
      height: 30px;
      padding: 0;
      border: 1px solid var(--outline-color-primary, #ebebeb);
      border-radius: 1px;
    }
    .van-field__control {
      line-height: 30px;
    }
  }

  ::v-deep .van-swipe-cell__right .swipe-right-btn {
    width: 70px;
    padding: 0;
    height: 50px;
    background: var(--error-bg-color-default, #ff5656);
  }
  .add-btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 46px;
    margin-bottom: 16px;
    background-color: var(--bg-color-white, #ffffff);
    .add-btn {
      font-size: var(--font-size-h4-size, 20px);
      font-weight: 700;
      color: var(--bg-color-dialogs, #00000066);
    }
  }
  .submit-wrapper {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    width: 100%;
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 343px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: var(--font-size-body2-size, 14px);
      color: var(--bg-color-white, #ffffff);
      text-align: center;
      background: var(--text-color-brand, #00baff);
      border-radius: 4px;
      margin-bottom: 16px;
    }
  }
}
</style>
