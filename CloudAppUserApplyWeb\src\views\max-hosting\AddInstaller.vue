<template>
  <div class="add-installer">
    <nav-bar @clickLeft="clickLeft"></nav-bar>
    <div class="detail-content">
      <van-field
        class="content-input"
        v-model="emailInput"
        clearable
        clear-trigger="always"
        :placeholder="$t('enterInstallerEmail')"
      />
    </div>
    <van-button :disabled="emailInput === ''" class="footer-btn" type="primary" @click="confirmEmail">
      {{ $t('nextStep') }}
    </van-button>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { validateEmail } from '@/utils/validate'
import { appClose } from '@/utils/appbridge'
import { getInstallerInfo } from '@/api/maxHosting'

export default {
  name: 'MaxHostingAddInstaller',
  components: {
    NavBar
  },
  data() {
    return {
      emailInput: ''
    }
  },
  created() {
    const { email } = this.$route.query

    if (email !== undefined) {
      this.emailInput = email
    }
  },
  methods: {
    clickLeft() {
      // 跳转到服务页面
      appClose()
    },
    async confirmEmail() {
      if (validateEmail(this.emailInput)) {
        const { data } = await getInstallerInfo({
          loginName: this.emailInput
        })

        if (data) {
          this.$router.push({
            path: '/max/hosting/device/select',
            query: {
              email: this.emailInput,
              origin: 'addInstaller',
              installerUserId: data.id
            }
          })
        } else {
          this.$toastFail(this.$t('noFoundUser'))
        }
      } else {
        this.$toastFail(this.$t('emailNameError'))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.add-installer {
  background-color: var(--bg-color-white, #ffffff);
  height: 100%;
  overflow: hidden;
  position: relative;
  .detail-content {
    display: flex;
    justify-content: start;
    align-items: center;
    flex-direction: column;
    padding: 10px 36px;

    .content-input {
      width: 300px;
      padding: 14px 0;
      color: var(--text-color-primary, #101d34);
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: 'PingFang SC';
      font-size: var(--font-size-body1-size, 16px);
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      border-bottom: 1px solid var(--bg-color-secondary, #eeeeee);
    }
  }

  .footer-btn {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 327px;
    height: 40px;
    border-radius: 23px;
    line-height: 40px;
    text-align: center;
  }
}
</style>
<style lang="scss"></style>
