<template>
  <div ref="betterScrollWrap" class="better-scroll-wrap">
    <div class="better-scroll-box">
      <div class="better-scroll-head">{{ tipText }}</div>
      <slot></slot>
      <transition name="van-fade">
        <div
          class="better-scroll-bottom"
          v-show="[1, 3, 4].indexOf(pullingStatus) > -1 && showPullText"
          @click="$emit('pullingUp')"
        >
          {{ pullingStatusList[pullingStatus] }}
        </div>
      </transition>
      <div v-if="tipTextBottom" class="better-scroll-bottom-text">{{ tipTextBottom }}</div>
    </div>
    <van-overlay :show="show">
      <van-loading type="spinner" color="var(--text-color-placeholder, #8f8e93)" size="30px" />
    </van-overlay>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
export default {
  name: 'tvtBetterScroll',
  props: {
    pullingStatus: {
      type: Number,
      default: 2 // 0-全部请求完成 1-服务异常 2-上滑加载 3-暂无数据 4-网络连接超时
    },
    showBottomCompleteText: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pullingStatusList: ['', this.$t('serviceException'), '', this.$t('noData'), this.$t('netTimeOut')],
      show: false,
      scroll: null,
      tipText: '',
      showPullText: true,
      tipTextBottom: ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.scroll = new BScroll(this.$refs.betterScrollWrap, {
        useTransition: false,
        scrollY: true,
        click: true,
        eventPassthrough: 'horizontal',
        mouseWheel: {
          speed: 20,
          easeTime: 0,
          dampingFactor: 0.3
        },
        pullUpLoad: {
          threshold: -30 // 当上拉距离超过30px时触发 pullingUp 事件
        },
        pullDownRefresh: {
          threshold: 60, //下拉距离超过60px触发pullingDown事件
          stop: 44 // 回弹停留在距离顶部44px的位置
        }
      })
      this.scroll.on('pullingUp', () => {
        if (this.pullingStatus === 2) {
          this.show = true
          this.tipTextBottom = this.$t('loadingText')
          this.$emit('pullingUp', () => {
            if (this.showBottomCompleteText) {
              this.tipTextBottom = this.$t('loadComplete')
            } else {
              this.tipTextBottom = ''
            }
            this.$nextTick(() => {
              this.show = false
              this.scroll.refresh()
              this.scroll.finishPullUp()
            })
          })
        } else {
          this.$nextTick(() => {
            this.scroll.refresh()
            this.scroll.finishPullUp()
          })
        }
      })
      this.scroll.on('scroll', param => {
        this.showPullText = param.y < 20
        this.$emit('scroll', param)
      })
      this.scroll.on('enterThreshold', () => {
        // this.tipTextBottom = this.$t('pullingText')
      })
      this.scroll.on('leaveThreshold', () => {
        this.tipText = this.$t('loosingText')
      })
      this.scroll.on('pullingDown', () => {
        this.show = true
        this.tipText = this.$t('loosing')
        this.$emit('pullingDown', () => {
          this.tipText = this.$t('refreshComplete')
          this.$nextTick(() => {
            this.show = false
            this.scroll.refresh()
            this.scroll.finishPullDown()
          })
        })
      })
    })
  },
  methods: {
    refresh() {
      this.scroll.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.better-scroll-wrap {
  width: 100%;
  overflow: hidden;
  position: relative;
  .better-scroll-box {
    width: 100%;
    .better-scroll-head {
      position: absolute;
      width: 100%;
      transform: translateY(-100%) translateZ(0);
      color: #979797;
      text-align: center;
      height: 44px;
      line-height: 44px;
    }
    .better-scroll-bottom {
      height: 50px;
      line-height: 50px;
      text-align: center;
      color: #979797;
    }
    .better-scroll-bottom-text {
      width: 100%;
      color: #979797;
      text-align: center;
      height: 44px;
      line-height: 44px;
    }
  }
  .van-overlay {
    background-color: rgba(0, 0, 0, 0);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
