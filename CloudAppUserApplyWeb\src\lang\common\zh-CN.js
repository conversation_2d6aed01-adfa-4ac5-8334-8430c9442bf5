export default {
  upgrade: '云升级',
  cancel: '取消',
  confirm: '确定',
  deviceUpdate: '设备',
  cameraUpdate: '摄像机',
  allUpdate: '一键升级',
  updateNow: '升级',
  currentVersion: '当前版本',
  latestVersion: '最新版本',
  updateContent: '更新内容',
  hasLatestVersion: '已是最新版本',
  online: '在线',
  offline: '离线',
  waitDownload: '待下载',
  inprogress: '下载中',
  downloadFail: '下载失败',
  downloadFinished: '下载完成',
  waitingForUpgrade: '待升级',
  inupgrade: '升级中',
  upgradeFail: '升级失败',
  upgradeSuccess: '升级成功',
  deviceUpgradeInfo: '升级过程中设备将断开连接并自动重启，确定升级？',
  upgradeTip:
    '下载完成后设备会自动升级，升级过程中设备将自动重启，升级过程中请勿手动重启设备或断开设备电源直到设备自动重启完成。',
  cameraUpgradeInfo: '升级过程中摄像机将断开连接并自动重启，确定升级？',
  pwdUserNameError: '用户名或密码错误',
  permissionAuth: '超级管理员权限认证',
  pleaseEnterUser: '请输入用户名',
  pleaseEnterPwd: '请输入密码',
  noCameraUpgrade: '未检测到可升级的摄像机',
  handleCheck: '检测更新',
  // H5需给出翻译
  checkSuccess: '检测成功',
  checkFail: '检测失败',
  viewUpdateContent: '查看更新内容',
  deviceDisconnected: '连接设备失败',
  updateNote: '更新说明',
  noData: '暂无数据',
  tips: '提示',
  // 结束
  paySuccess: '支付成功',
  payFail: '支付失败',
  done: '完成',
  rePurchase: '重新购买',
  cloudStorage: '云存储',
  INSTRUMENT_DECLINED: '交易超出卡限制',
  PAYER_ACCOUNT_LOCKED_OR_CLOSED: '付款人账户不能用于此交易',
  PAYER_ACCOUNT_RESTRICTED: '付款人账户被限制',
  TRANSACTION_LIMIT_EXCEEDED: '总支付金额超过交易限额',
  TRANSACTION_RECEIVING_LIMIT_EXCEEDED: '交易超过了接收方的收款限额',
  fail: '交易失败 ',
  // 设备托管
  myInstaller: '我的安装商',
  trusteeshipDevice: '已托管设备',
  addTrusteeship: '新增托管',
  waitReceived: '待接收',
  received: '已接收',
  refuse: '拒绝',
  delete: '删除',
  operationSuccess: '操作成功', // 托管成功  取消托管成功
  operationFail: '操作失败',
  cancelTrusteeship: '取消托管',
  chooseDevice: '选择设备',
  noAvaiableDevice: '设备版本支持托管功能且通过绑定添加的设备，才能托管',
  leastChoose: '至少选择一个设备',
  finish: '完成',
  details: '详情',
  deviceDetails: '详情',
  live: '现场预览',
  rec: '回放',
  config: '配置',
  confirmTrusteeshipTip: '托管请求已发送给安装商，请等待安装商处理',
  cancelTrusteeshipTip: '取消托管后安装商无法为您提供远程维护服务，确定取消？',
  unBindTrusteeship: '解绑后将取消所有设备托管，确定解除绑定？',
  trusteeshipPermissions: '托管权限',
  trusteeshipTime: '托管时长',
  unBind: '解除绑定',
  serviceException: '服务异常',
  netTimeOut: '网络连接超时',
  pullingText: '松开立即加载',
  loosingText: '松开立即刷新',
  loosing: '正在刷新...',
  loadingText: '加载中...',
  refreshComplete: '刷新完成',
  noMore: '没有更多了...',
  noMoreDevice: '暂无可选的设备',
  invalid: '已失效',
  // 1.14.0
  password: '密码',
  ipcUpgrade: '云升级',
  pwdError: '密码错误，还可以尝试{0}次',
  pwdErrorLock: '错误次数过多已锁定，请稍后尝试！',
  noPermissions: '无权限',
  permission: '权限',
  validity: '有效期',
  permissionValidity: '授权',
  isSaveModify: '是否保存更改?',
  manyMinutes: '{0}分钟',
  manyHours: '{0}小时',
  manyDays: '{0}天',
  manyMinutesEn: '{0}分钟',
  manyHoursEn: '{0}小时',
  manyDaysEn: '{0}天',
  oneWeek: '1周',
  forever: '永久',
  expired: '已过期',
  residue: '剩余',
  // 转移请求
  transferRequest: '转移请求',
  acceptTransfer: '接受转移',
  refuseTransferConfirm: '确定拒绝转移?',
  bindInstallerText: '绑定安装商({account})后支持托管设备，立即绑定？',
  bindSuccess: '绑定成功',
  acceptSuccess: '接受成功',
  from: '来自',
  // 分享管理--除superlive plus外
  shareManage: '分享管理',
  shareDetail: '分享详情',
  acceptShare: '接受分享',
  permissionText: '您获得的权限',
  livePreview: '现场预览',
  playback: '回放',
  alarm: '报警',
  intercom: '对讲',
  gimbal: '云台',
  refuseShareConfirm: '确定拒绝分享?',
  acceptAll: '全部接受',
  exitShare: '退出分享',
  changeCapability: '设置权限',
  deviceOperation: '设备操作',
  devOperationTitle: '设置设备权限',
  deviceOperationDesc: '分享此权限后，被分享者可进行相关设备操作，如设备搜索、布防/撤防、报警输出。',
  none: '无',
  // 布防撤防--除superlive plus外
  cancelDefense: '撤防',
  homeDefense: '在家布防',
  outDefense: '外出布防',
  defenseDeployment: '布防/撤防',
  oneClickDeployment: '一键布防',
  oneClickDisarm: '一键撤防',
  oneClickRemoval: '一键消警',
  deploySuccess: '布防成功',
  disarmSuccess: '撤防成功',
  add: '添加',
  edit: '编辑',
  setting: '设置',
  all: '全部',
  name: '名称',
  cameraSensor: '关联摄像机',
  deleteDeviceConfirm: '确定删除设备?',
  onlySameDevice: '同一组内只支持添加同一设备下的摄像机',
  pleaseChooseChannel: '请选择通道',
  pleaseAddCameraSensor: '请添加摄像机/传感器',
  defensiveLinkageItem: '撤防联动项',
  defensiveDesc: '撤防状态下勾选的撤防联动项不生效',
  bypassHome: '在家布防旁路',
  bypassHomeDesc: '开启后，在家布防模式下该防区将自动旁路',
  ipcSound: 'IPC_声音',
  ipcLight: 'IPC_灯光',
  pleaseChooseLinkage: '请选择撤防联动项',
  deleteConfirm: '确定删除?',
  pleaseAddGroup: '请先添加分组',
  groupChannelEmpty: '布防组未添加通道',
  removalSuccess: '消警已发送',
  removalFail: '消警发送失败',
  reqSuccess: '成功',
  reqFail: '失败',
  groupLimit: '最多支持添加{limit}个分组',
  areaGroup: '区域分组',
  pleaseEnter: '请输入',
  groupNoDevice: '分组下未添加设备',
  // 预置点、巡航线添加--除superlive plus外
  addPoint: '添加',
  addSuccess: '添加成功',
  addFail: '添加失败',
  editSuccess: '编辑成功',
  editFail: '编辑失败',
  deleteSuccess: '删除成功',
  deleteFail: '删除失败',
  pointNameExist: '预置点名称已存在',
  noPointSelect: '没有预置点可选择，请先创建预置点',
  choosePoint: '选择',
  presetPoint: '预置点',
  presetPointName: '预置点名称',
  cruiseLineName: '巡航线名称',
  pleaseEnterPoint: '请输入预置点名称',
  pleaseEnterLine: '请输入巡航线名称',
  presetPointEmpty: '预置点列表不能为空！',
  lineNameExist: '巡航线名称已存在',
  presetPointLimit: '预置点数量不能超过{0}个！',
  manySecond: '{0}秒',
  oneMinute: '1分钟',
  speed: '速度',
  holdTime: '持续时间',
  deletePointConfirm: '确定删除预置点?',
  pleaseChoosePoint: '请选择预置点',
  pleaseChooseSpeed: '请选择速度',
  pleaseChooseHoldTime: '请选择持续时间',
  // line APP绑定解绑
  lineAuthBind: 'line授权绑定',
  bindFail: '绑定失败',
  binding: '绑定中',
  // 1.5.1 superlive plus  住户管理
  householdManagement: '楼栋与住户',
  addBuilding: '添加楼栋',
  buildingName: '楼栋名称',
  enterBuildingName: '请输入楼栋名称',
  buildingNum: '楼栋号',
  enterBuildingNum: '请输入楼栋号',
  relateDevice: '关联设备',
  roomNum: '房间数量',
  room: '房间',
  addRoom: '添加房间',
  roomName: '房间号',
  enterRoomName: '请输入房间号',
  household: '住户',
  addRoomMember: '添加住户',
  changeSuccessfully: '修改成功',
  email: '邮箱',
  enterMemberEmail: '请输入邮箱',
  mobile: '手机',
  enterMemberMobile: '请输入手机号',
  emailNameError: '邮箱格式不正确',
  mobileError: '手机号格式不正确',
  emailNameNotEmpty: '邮箱不能为空',
  mobileNotEmpty: '手机号不能为空',
  memberInMax: '此房间下住户数量达到上限',
  memberMobileRepeate: '此房间内已存在该手机号',
  emailRepeate: '此房间内已存在该邮箱',
  supportDash: '仅支持-，_和空格作为特殊字符',
  pageUpdateTitle: '更新提示',
  pageUpdateContent: '页面内容已更新，请刷新页面',
  ok: '确定',
  // 1.16.1 superlive plus  楼栋住户与人脸开门
  buildingDetail: '楼栋详情',
  deleteBuilding: '删除楼栋',
  roomDetail: '房间详情',
  deleteRoom: '删除房间',
  memberAccount: '住户账号',
  basicInfo: '基本信息',
  certificate: '凭证',
  doorCard: '门禁卡',
  doorCardNo: '门禁卡号',
  maxCardLimit: '最多添加{0}张门禁卡',
  cardExist: '此卡号已存在',
  faceEntry: '人脸开门',
  modifyResident: '修改住户',
  enterMemberName: '请输入姓名',
  enterMemberAccount: '请输入住户账号',
  uploadSuccess: '上传成功',
  uploadFail: '上传失败',
  doorCardCount: '门禁卡数量',
  facePhotoTip: '拍照/上传人脸照片',
  // 1.5.1 superlive max 分享
  chooseSharer: '选择分享人',
  shareManagment: '分享管理',
  mobileNum: '手机号',
  historyShare: '历史分享',
  nextStep: '下一步',
  choose: '选择',
  preview: '预览',
  shareAuthSetting: '设置通道权限',
  confirmShare: '确定分享',
  shareDevice: '分享设备',
  shareDesc: '您的设备将分享给该用户',
  shareDesc2: '对方接受后，将可以根据您设定的权限查看内容或控制设备。',
  remark: '备注',
  shareSuccess: '分享成功',
  shareToUser: '您的设备已成功分享给用户',
  myShare: '我的分享',
  otherShare: '他人分享',
  accept: '接受',
  reject: '拒绝',
  toAccept: '待接受',
  accepted: '已接受',
  rejected: '已拒绝',
  allAccept: '全部接受',
  acceptSuccMsg: '接受成功，您可前往App设备页查看',
  acceptFail: '接受失败',
  rejectFail: '拒绝失败',
  rejectMsg: '已拒绝',
  cancelShare: '取消分享',
  cancelShareDesc: '取消分享后，对方将无法继续针对您的设备进行内容查看或设备控制。',
  notNow: '暂不',
  stillCancel: '继续取消',
  initiateShare: '发起分享',
  settingSuccess: '设置成功',
  deviceNum: '{0}台设备',
  noShareDevice: '无分享设备',
  noFoundUser: '未找到该用户',
  notShareSelf: '不能分享给自己',
  tourist: '游客',
  andOther: '和其他',
  shareViewFail: '无法查看，该设备已被取消分享',
  messageDetail: '消息详情',
  agreeShare: '同意分享',
  agreed: '已同意',
  agree: '同意',
  rejectAll: '全部拒绝',
  shareApplyDesc: '{0}向您发起设备分享申请。若允许请进行通道和权限确认。',
  chooseChanAuth: '选择通道和权限',
  shareOverTenDesc: '该设备已分享给10个用户，不可再次进行分享',
  channelEmpty: '通道列表为空',
  msgExpired: '消息内容已失效',
  alarmNotification: '报警通知',
  // 1.5.1 superlive max 托管和转移
  service: '服务',
  serviceDetail: '服务详情',
  site: '站点',
  changeSiteName: '修改站点名称',
  cancelHosting: '取消站点托管',
  cancelHostingTip: '取消站点托管后，安装商将无法再针对托管权限进行查看或配置操作。',
  cancelHostingError: '当前站点为安装商分享交付站点，不可取消托管。如需取消请联系您的安装商。',
  cancelDeviceHostingTip: '取消托管后，您将无法再重新为安装商赋予设备权限，安装商也将无法再为您提供远程服务。',
  devicePermissionApprove: '设备托管申请',
  enterSiteName: '请输入站点名称',
  deviceHostDetail: '设备托管详情',
  addInstaller: '添加安装商',
  enterInstallerEmail: '请输入安装商邮箱地址',
  applicationSuccess: '发送成功',
  applicationInfo: '托管请求已发送给安装商{安装商账号}，请耐心等待处理。',
  remarkPlaceholder: '备注',
  hostingDevice: '托管设备',
  noDeviceHosting: '无设备托管申请',
  sitePermissionApprove: '站点托管申请',
  sitePermissionCancel: '取消站点托管',
  siteHostingTip: '安装商向您发起站点托管申请, 该站点用于安装商在为您提供服务时更方便的进行设备的管理和维护。',
  siteCancelHostingTip: '安装商已取消对您的站点托管，若存在设备需要进行运维，可在“服务”中进行申请。',
  deviceCancelHostingTip: '安装商已取消对您的设备托管，若存在设备需要进行运维，可在“服务”中进行申请。',
  stillUnbind: '继续解除',
  unBindDesc: '解除绑定后，您将无法再重新为安装商赋予设备权限，安装商也将无法再为您提供远程服务。',
  trusteeshipService: '托管服务',
  trusteeshipText: '随时随地，为您服务',
  usedNumber: '{0}人已使用',
  operateText: '一键操作，即可开启便捷的远程托管操作',
  fastText: '及时、快速的响应您的设备问题维护',
  protectText: '托管权限设置、关键行为提醒，保障您的安全',
  immediateTrusteeship: '立即托管',
  introduceText: 'Hi, 我是您的专属服务顾问',
  noTrusteeship: '您当前无设备托管',
  trustDeviceCount: '{0}台设备正在托管',
  trustDeviceNone: '当前无设备托管',
  duration: '时长',
  siteDescTitle: '什么是站点',
  siteDesc1: '“站点”是用于安装商在为您提供服务时更方便的进行设备的管理和维护。',
  siteDesc2:
    '若您与安装商建立了站点托管关系，则安装商可更便捷地为您添加设备，以及在得到设备托管授权的情况下，快速地解决您的设备健康等问题',
  trustPermission: '托管权限',
  iKnow: '我知道了',
  deviceRejectMsg: '已拒绝，后续可在{0}中继续申请设备托管',
  trustTime: '托管时长',
  permissionSetting: '权限设置',
  settingTime: '设置时长',
  trusteeshipSuccess: '发送成功',
  trusteeshipToUser: '托管请求已发送给安装商，请耐心等待处理。',
  trusteeshipDetail: '托管详情',
  unTrusteeshipDesc: '取消托管后，您将无法再重新为安装商赋予设备权限，安装商也将无法再为您提供远程服务。',
  transferApply: '设备转移申请',
  deviceTransferDesc: '安装商正向您发起设备转移，若接受则默认为您绑定安装商，后续可为您进行设备托管服务。',
  bindAcceptTransfer: '绑定并接受',
  goUnBind: '去解绑',
  bindedInstaller: '已绑定安装商',
  transferDesc: '安装商向您发起设备转移',
  bindedInstallerDesc: '您当前已存在绑定安装商，您可直接接受转移或解除之前的绑定安装商，重新进行绑定',
  viewDetail: '查看详情',
  bindInstaller: '绑定安装商',
  bindInstallerDesc: '设备托管需先于安装商建立绑定关系，请将二维码展示或分享给安装商进行扫码绑定。',
  noTrusteeshipDevice: '无托管设备',
  deviceTrusteeship: '设备托管',
  chooseTrusteeshipDevice: '选择托管设备',
  deviceTransfer: '设备转移',
  transferDeviceCount: '您当前有 {0} 台设备正在转移',
  transferDevice: '转移设备',
  transferSuccess: '转移成功',
  rejectTransfer: '拒绝',
  rejectDeviceTransfer: '拒绝设备转移',
  rejectTransfetDesc: '拒绝接受设备转移后，您无法针对设备进行内容的查看或控制等操作',
  waitTrust: '待托管',
  waitTrustReceived: '待接收托管',
  devicePermissionCancel: '取消设备托管',
  duringTrust: '正在托管',
  residueTime: '剩余时长',
  duringTrsutResidueTime: '正在托管 剩余时长',
  manyMinutesEn2: '{0}分',
  manySeconds: '{0}秒',
  manySecondsEn: '{0}秒',
  refresh: '刷新',
  netErrText: '网络异常，点击重试',
  cancelled: '已取消',
  transferAcceptedDesc: '转移成功',
  transferRejectedDesc: '已拒绝',
  applyExpiredDesc: '申请已过期，请要求对方重新发送',
  applyCancelledDesc: '申请已取消',
  noDeviceTransfer: '无设备转移申请',
  // 设备交付
  skip: '跳过',
  selectDevice: '选择设备',
  ensure: '确定',
  waitingTips: '正在添加设备，请勿离开当前页面',
  finished: '完成',
  successAdd: '成功添加{num}个设备',
  failAdd: '{num}个设备添加失败',
  nChannels: '{n}个通道',
  deviceName: '设备名称',
  open: '开',
  close: '关',
  adding: '正在添加',
  addToSite: '添加至账户',
  bindByOther: '此设备已被{name}用户添加',
  enterVerificationCode: '验证码/安全码',
  enterDevVerificationCode: '请输入验证码/安全码',
  noCode: '获取验证码/安全码失败',
  input: '输入',
  codeTips1: '您可按照以下步骤获取设备验证码/安全码',
  codeTips2: '1.打开验证码/安全码号所在界面:',
  codeTips3: '设备端:主菜单-设置-网络-NAT',
  codeTips4: '网页端:主菜单-功能面板-网络-NAT',
  codeTips5: '2.设备验证码/安全码位于设备二维码右侧，详见下图:',
  emptyDevice: '暂无设备',

  // 1.5.1 superlive plus 人脸库管理
  targetFaceManagement: '人脸库管理',
  targetName: '姓名',
  targetType: '类型',
  cardId: '卡号',
  strangerList: '访客',
  whiteList: '白名单人员',
  blackList: '黑名单人员',
  admin: '管理员',
  filter: '筛选',
  searchTargetFace: '请输入姓名',
  personType: '人员类型',
  addPerson: '添加人员',
  personFace: '人脸',
  floor: '楼层',
  verificationMethod: '开门验证方式',
  lockPermission: '门锁权限',
  startTime: '开始时间',
  endTime: '结束时间',
  gender: '性别',
  age: '年龄',
  telephone: '电话',
  termOfValidity: '有效期',
  foreverValid: '永久有效',
  custom: '自定义',
  jobNumber: '工号',
  male: '男',
  female: '女',
  Password: '密码',
  FaceMatch: '人脸',
  SwipingCard: '刷卡',
  MatchAndPass: '人脸比对 + 密码',
  MatchAndCard: '人脸比对 + 刷卡',
  MatchorCard: '人脸比对或者刷卡',
  AllType: '人脸比对或者密码或者刷卡',
  doorLock: '门锁',
  'personFace.uploadTip': '请上传人脸图像',
  'personFace.sizeTip': '图像大小不能超过200k',
  ageRangeTip: '年龄范围是{min}~{max}',
  floorRangeTip: '楼层范围是{min}~{max}',
  roomRangeTip: '房间号范围是{min}~{max}',
  cardIdTip: '卡号数量不能超过{max}个!',
  passwordRangeTip: '密码长度范围是{min}~{max}',
  passwordLengthTip: '密码长度是{length}',
  timeRangeTip: '开始时间必须小于结束时间',
  doorLockTip: '至少选中一个门锁',
  jobNumberTip: '请输入工号',
  cardIdsTip: '请输入卡号',
  confirmDeletePerson: '确定删除人员?',
  cardIdExist: '卡号已存在',
  faceMatchErrorCode: {
    '-2': '参数错误！',
    '-3': '图像不符合要求',
    '-5': '图像不符合要求.',
    '-6': '人员数量已达上限',
    '-11': ' 图片格式不支持',
    '-12': '图片数据异常.',
    '-16': '导入失败',
    '-18': '图片大小超限制',

    '-19': '更新特征库记录失败',
    '-20': '添加特征库记录失败',
    '-21': '提取图片特征失败',
    '-22': '保存底图失败',
    '-23': '保存人员信息失败',
    '-24': '保存人员信息失败',
    '-25': '已存在相同人脸',
    '-26': '未知特征库操作错误',

    '-30': '开门验证方式参数错误',
    '-31': '密码不能重复',
    '-32': '密码不能为空',
    '-33': '工号不能为空',
    '-34': '卡号不能为空',
    '-35': '没有人脸图片',
    499: '参数校验不通过'
  },
  // 1.5.1 superlive max云存储
  immediateBuy: '立即购买',
  secureAndQuick: '安全放心，方便快捷',
  purchaseRecords: '购买记录',
  cloudStorageInUse: '云存储使用中',
  serviceBought: '{0}个通道正在使用',
  // 1.5.1 superlive max布撤防
  linkCameraSensor: '关联通道或传感器',
  defenseAreaSetting: '防区设置',
  enterGroupName: '请输入组名称',
  createGroup: '创建组',
  msgPushSwitch: '推送',
  alarmOut: '报警输出',
  saveModify: '保存修改',
  addedTo: '已添加至{0}',
  pleaseEnterName: '请输入名称',
  noGroupDesc: '未添加任何布防组，可前往添加',
  createSuccess: '创建成功',
  createFail: '创建失败',
  saveSuccess: '保存成功',
  saveFail: '保存失败',
  selectAll: '全选',
  cancelSelectAll: '取消全选',
  // 云后台云升级
  repeatedlyRestart: '反复重启',
  versionUnchanged: '版本不变',
  versionException: '版本异常',
  // max新的设备转移和托管权限申请
  transferManagApply: '设备转移和托管权限申请',
  stillReject: '继续拒绝',
  trusteeshipAuthApply: '托管权限申请',
  agreeTrusteeshipDesc: '已同意，后续可在{0}中更改托管权限',
  rejectTrusteeshipDesc: '已拒绝，后续可在{0}中继续申请设备托管',
  transferApplyTip: '安装商向您发起站点（{0}）及站点下的设备托管申请。',
  // BA-4491 用户反馈 满意度收集
  userFeedback: '用户反馈',
  feedBackPlaceholder1: '请写下您对App的使用反馈，便于我们后续为您提供更好的产品体验。',
  feedBackPlaceholder2: '请写下您对App的使用反馈',
  starOne: '非常不满意，未达到预期效果',
  starTwo: '不满意，需求未被全部满足',
  starThree: '一般，服务和体验基本满足，但有改进空间',
  starFour: '满意，体验良好还可以达到完美',
  starFive: '非常满意，超出预期，体验极佳',
  feedBackSuccess: '反馈成功',
  feedBackFail: '反馈失败',
  feedBackInfo: '为了后续能够为您提供更佳的体验，我们非常希望得到您的反馈和宝贵意见。',
  submit: '提交',
  feedBackFinish: '后续您可在{me}-{userFeedback}中继续评价',
  me: '我',
  // 重置密码
  resetDevPwd: '重置设备密码',
  resetPwdTips: '安装商提醒您进行设备密码重置',
  modifyDevPwd: '修改设备密码',
  pwdTips: '密码长度8-16位，数字、字母（区分大小写）、字符至少包含两种',
  confirmNewPassword: '确认新密码',
  emptyPsw: '密码不能为空',
  emptyOldPwd: '旧密码不能为空',
  setPwdTips: '修改密码需要验证旧密码，请先输入旧密码 ',
  oldPwd: '旧密码',
  enterOldPwd: '请输入旧密码',
  newPwd: '新密码',
  enterNewPwd: '请输入新密码',
  confirmPwd: '确认密码',
  enterConfirmPwd: '请输入确认新密码',
  notMatchPsw: '两次输入的密码不匹配，请重新输入',
  updateTips: '可修改您的设备名便于后续查找',
  updateWarnTips: '不支持特殊字符{0}',
  pleaseEnterDevName: '请输入设备名称',
  updateDevName: '修改设备名',
  errRenameTips: '设备用户名或密码输入错误',
  mediumPwd: '1.长度为8-16个字符。\n2.包含数字、小写字母、大写字母、符号中两种或两种以上。',
  strongPwd: '1.长度为8-16个字符。\n2.包含数字、小写字母、大写字母、符号中三种或三种以上。',
  strongerPwd: '1.长度为9-16个字符。\n2.包含数字、小写字母、大写字母、符号。',
  mediumPwdTips: '密码长度为8-16个字符，包含数字、小写字母、大写字母、符号中两种或两种以上',
  strongPwdTips: '密码长度为8-16个字符，包含数字、小写字母、大写字母、符号中三种或三种以上',
  strongerPwdTips: '密码长度为9-16个字符，包含数字、小写字母、大写字母、符号',
  // max 设备分享交付
  deviceShareDeliveryTips: '安装商向您发起设备分享交付，接受后安装商始终拥有设备的配置权限，且可随时取消设备分享',
  rejectShareTitle: '拒绝设备分享',
  rejectShareTips: '拒绝接受设备分享后，您将无法针对设备进行内容的查看或控制等操作',
  shareDeliceryTitle: '设备交付申请',
  shareChangeSiteTitle: '设备移动申请',
  shareChangeSiteTip1: '安装商申请将以下设备由原站点',
  shareChangeSiteTip2: '移动至目标站点',
  emphasizeStr: ' “{name}” ',
  changeSite: '移动设备',
  changeSiteWaitingTip: '设备移动中，请稍后查询结果',
  moveSuccessfully: '移动成功',
  moveFailed: '移动失败',
  shareDeliceryCancelTitle: '取消分享交付',
  shareCancelTips: '安装商已取消对您的设备分享，若存在疑问可联系您的安装商',
  // superlive max 2.2  云盘卡片
  gift: '赠送',
  cloudDiskUse: '云盘使用中',
  capacity: '容量',
  totalFiles: '共{0}个文件',
  expirationTime: '到期时间',
  // superlive max 2.2  云存储卡片
  purchasedChannel: '已购买服务通道',
  // superlive plus 1.16 告警主机
  alarmSystem: '报警系统',
  chooseAlarmSystem: '选择报警系统',
  login: '登录',
  register: '注册',
  forgotPassword: '忘记密码',
  configureAccountSettings: '配置账户设置',
  username: '用户名',
  serverAddressRequired: '服务器地址不能为空',
  accountLocked: '账户已锁定或禁用',
  loginFailed: '登录失败，请稍后重试',
  invalidCredentials: '用户名或密码错误',
  loginSuccessful: '登录成功',
  passwordResetSuccess: '密码已重置，请使用新密码登录',
  restorePassword: '重置密码',
  inputEmailForRegistering: '输入您注册时使用的邮箱地址',
  verifyYourEmail: '验证您的电子邮件',
  checkEmailAndInputCode: '请查看您的邮箱并输入验证码',
  authenticationCode: '验证码',
  pleaseEnterAuthCodeL: '请输入验证码',
  setNewPassword: '设置新密码',
  pleaseSetNewPassword: '请为您的帐户设置新密码',
  newPassword: '新密码',
  notifications: '通知',
  notificationMethods: '通知方式',
  accountDetails: '账户详情',
  alarmTrouble: '故障',
  //我的新增
  logout: '退出登录',
  panelList: '面板列表',
  confirmLogout: '确认退出登录',
  confirmLogoutTips: '确认退出登录吗？',
  loggingOut: '退出登录中...',
  loggedOutSuccess: '退出登录成功',
  unlocking: '解锁',
  enterUserCode: '输入用户代码',
  proceed: '继续',
  pleaseEnterUserCode: '请输入用户代码',
  loginPanelFailed: '登录失败，请检查您的代码',
  status: '状态',
  action: '操作',
  allPartitions: '全部分区',
  activateSiren: '激活 Siren',
  muteSiren: '静音 Siren',
  loadingAlarms: '报警加载中...',
  noAlarmsFound: '暂无报警数据',
  getAlarmsFailed: '获取警报列表失败',
  sirenActivateSuccess: 'Siren 激活成功',
  sirenMuteSuccess: 'Siren 静音成功',
  noSecuritySystem: '无报警系统',
  controlPanel: '控制面板',
  communicator: '通讯器',
  wiredKeypad: '有线键盘',
  zone: '区域',
  rename: '重命名',
  bypass: '通过',
  bindChannel: '绑定通道',
  channelBind: '通道绑定',
  unbindChannel: '解绑通道',
  save: '保存',
  events: '事件',
  failures: '故障',
  activities: '活动',
  enterDeviceName: '请输入设备名称',
  deviceNameUpdatedSuccessfully: '设备名称更新成功',
  updateFailedTryAgain: '更新失败，请稍后重试',
  home: '在家布防',
  away: '外出布防',
  disarm: '撤防',
  mixed: '混合状态',
  troubles: '问题',
  latestActivity: '最新活动',
  systemDisarmed: '系统撤防',
  systemArmed: '系统布防',
  doorActivity: '门禁活动',
  motionDetected: '移动检测',
  windowActivity: '窗户活动',
  unknownEvent: '未知事件',
  unknownActivity: '未知活动',
  activity: '活动',
  unknownZone: '未知区域',
  // superlive plus 1.16 告警主机 通知类型
  alarms: '报警',
  restoreEvents: '恢复事件',
  armDisarmEvents: '布防/撤防事件',
  noticeEvents: '通知事件',
  duressEvents: '胁迫事件',
  enableAll: '启用全部',
  burglary: '入侵',
  panic: '紧急',
  fire: '火警',
  duress: '胁迫',
  medical: '医疗',
  tamper: '防拆',
  faults: '故障',
  armDisarm: '布防/撤防',
  alarmRestore: '报警恢复',
  picture: '图片',
  invalidReportCode: '无效报告代码',
  // superlive plus 1.16 告警主机 面板相关
  enterPanelName: '请输入面板名称',
  enterPinCode: '请输入PIN码',
  pinCodeDigits: '请输入PIN码（1-6位数字）',
  panelNameCannotBeEmpty: '面板名称不能为空',
  pinCodeMustBeDigits: 'PIN码必须是1-6位数字',
  deleteConfirmPanel: '确定删除面板{0}?',
  missingSiteInformation: '缺少站点信息',
  // superlive plus 1.16 告警主机 Output相关
  off: '关闭',
  on: '开启',
  enterOutputName: '请输入输出名称',
  noOutputsFound: '暂无输出设备',
  // superlive plus 1.16 告警主机 设置相关
  users: '用户',
  panelDateTime: '面板日期和时间',
  installerAccess: '安装商授权',
  confirmInstallerAccess: '您确定要批准远程安装商访问吗？',
  installerAccessApproved: '已批准远程安装商访问',
  // 活动事件类型
  errorCode: {
    536870947: '用户名不存在',
    536870948: '用户名或者密码错误',
    536871060: '操作失败，请检查设备状态', //双系统功能 新增 设备当前不允许升级,请稍后再试
    10000: '连接设备失败', //app错误码
    550: '请求超时', //app返回的超时
    12344: '网络连接失败',
    12345: '网络连接超时', // 前端axios设置超时自用code
    23024: '提供的支付卡已过期',
    23025: '交易因违规而被拒绝',
    400: '参数错误', //app错误码
    404: '请求的资源（网页等）不存在',
    500: '系统异常',
    502: '服务器请求失败',
    503: '服务器异常',
    504: '服务器请求超时',
    32018: '数据不存在',
    32022: '{0}设备和安装商不在一个国家/区域，不支持托管',

    536871039: '无效参数',
    536870943: '无效参数',
    536870945: '操作失败，请检查设备状态', //'设备忙，请稍后重试', //设备忙（互斥）关系的错误码和对应的词条
    536871017: '操作失败，请检查设备状态', //'版本不匹配', // APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令告诉了一个更新的版本。此时APP触发升级NVR的话，就会返回
    536871082: '操作失败，请检查设备状态', //'无新版本',
    536871083: '操作失败，请检查设备状态', //'云升级版本不存在', //APP上一次查询获取到了版本信息，在下一次查询前，云平台下发命令取消掉了版本。此时APP触发升级NVR的话，就会返回
    536870940: '操作失败，请检查设备状态', //'设备未开启云升级功能', //云升级未开启
    536870934: '操作失败，请检查设备状态', //统一的模糊的提示
    536870975: '操作失败，请检查设备状态',
    536871030: '无磁盘',
    1000: '参数错误',
    1005: '图片验证码错误',
    1007: '需要图片验证码',
    1008: '验证码已过期',
    1009: '验证码错误',
    1011: '未正确填写参数！',
    1012: 'API无法识别',
    1013: '验证码发送异常',
    1015: '用户已存在',
    1027: '请输入正确的设备序列号/安全码',
    1028: '该通道已经启用或禁用',
    4500: '参数错误',
    5000: '抱歉，您没有权限进行该项操作',
    5001: '当前用户无权限',
    6000: '当前业务状态不支持此操作',
    6001: '操作过于频繁',
    7000: '参数错误',
    7001: '用户不存在',
    7002: '旧密码错误！',
    7003: 'Token不合法',
    7004: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录',
    7005: '签名无效',
    7006: '手机号码已存在',
    7007: '用户被锁定,请联系管理员解锁',
    7009: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', // Token达到最大数量后被强制退出最久未访问的会话
    7010: '管理员帐号未激活',
    7011: '帐号未激活',
    7019: '用户名已存在',
    7021: '删除失败！请先清空该主机组下的所有主机',
    7023: '该邮箱已被绑定',
    7028: '模板已在项目中使用，无法删除！',
    7029: '模板名已存在！',
    7030: '该厂商编号和版本号组合已存在！',
    7032: '固件包已存在！',
    7034: '该固件包已发布任务，不能删除！',
    7042: '有其他任务处于启动状态',
    7043: '任务尚未审核！',
    7044: '操作失败，没有符合升级条件的设备!',
    7045: '任务未审核通过！',
    7056: '配套兼容管理中已含有该版本，不允许删除!',
    7057: '签发单不能为空!',
    7061: '修正失败，不能重复创建修正！',
    7066: '客户代号已存在！',
    7068: '客户代号或客户编码不存在！',
    7069: '数据量过多，请缩小范围重新搜索！',
    7081: '导入失败！',
    7082: '导出失败！',
    7084: '该客户国家代码已存在',
    7086: '系统异常，拒绝操作',
    7087: '商品已经存在！',
    7088: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', // 密码变更后被强制退出
    7090: '您好，由于长时间未操作或在其他设备登录等原因，您的账号已登出，请重新登录', //账号注销后被强制退出
    7093: '图文信息未配置！',
    7094: '服务条款信息不存在！',
    9000: '系统异常！',
    9001: '协议版本过低,老版本不再兼容，需升级',
    9002: '协议版本错误，版本字段无法识别或错误信息',
    9003: '验证码发送异常',
    9004: '数据库操作失败',
    9005: '数据不存在',
    9006: '数据已存在',
    9007: '查看失败，数据库中不存在此数据',
    9008: '数据不存在',
    9009: '数据异常',
    9500: '系统异常！',
    10001: '系统异常！',
    20021: '该Email已被使用',
    20024: '该账号已激活',
    20030: '链接已失效',
    33001: '没有权限操作该设备',
    33002: '没有权限操作该站点',
    33003: '站点不存在',
    33004: '设备名称长度必须在0~32之间',
    33010: '设备已存在', //设备推送配置已存在
    // 托管错误码
    32019: '操作失败',
    32021: '数据不存在',
    // 安装商转移
    34006: '转移记录不存在',
    34007: '只能接受来自同一用户的转移',
    7040: '设备不存在或不在线',
    7065: '设备通道已经被分享',
    // ipc 云升级错误码
    ipc: {
      499: '未知错误', //未知错误
      612: '操作失败，请检查设备状态', //直连ipc升级过程中点击检测更新
      730: '操作失败，请检查设备状态', //检查新版本信息时，无新版本信息
      731: '操作失败，请检查设备状态', //云升级功能未使能  升级guid错误
      732: '操作失败，请检查设备状态', //升级任务已存在
      735: '操作失败，请检查设备状态', //直连ipc关闭云升级开关后点击检测更新
      101001: '无法连接设备，请检查设备状态',
      99006: '网络连接不可用',
      604: '无法连接设备，请检查设备状态',
      536871049: '云升级异常',
      536870931: '连接断开',
      733: '操作失败，请检查设备状态',
      734: '操作失败，请检查设备状态',
      736: '操作失败，请检查设备状态',
      737: '操作失败，请检查设备状态',
      738: '操作失败，请检查设备状态',
      739: '操作失败，请检查设备状态',
      740: '操作失败，请检查设备状态',
      130001: '无法连接设备，请检查设备状态',
      101003: '无法连接设备，请检查设备状态'
    },
    101001: '无法连接设备，请检查设备状态',
    99006: '网络连接不可用',
    604: '无法连接设备，请检查设备状态',
    536871049: '云升级异常',
    536870931: '连接断开',
    733: '操作失败，请检查设备状态',
    734: '操作失败，请检查设备状态',
    736: '操作失败，请检查设备状态',
    737: '操作失败，请检查设备状态',
    738: '操作失败，请检查设备状态',
    739: '操作失败，请检查设备状态',
    740: '操作失败，请检查设备状态',
    130001: '无法连接设备，请检查设备状态',
    101003: '无法连接设备，请检查设备状态',
    // Line App绑定
    34003: 'state中信息不正确',
    34001: '该账号已被绑定',
    34004: '创建授权失败',
    34005: '操作失败：授权已失效，请重新获取',
    22022: '该 LINE 账号已被其他HiviewPlus 用户绑定。请更换其他LINE账号，或联系原绑定用户解绑。',
    // 1.5.1 布防撤防 -除superlive plus外
    33601: '重复设备！',
    33602: '超过最大分组数量!',
    33603: '操作失败!',
    20070: '此VMS用户与您在不同的数据中心，无法邀请',
    20071: '此VMS用户与您分属不同经销商，无法邀请',
    // 1.5.1 superlive plus  住户管理
    34021: '楼栋数量达到上限',
    34022: '楼栋名称已存在',
    34023: '楼栋号已存在',
    34024: '删除失败，请先删除楼栋下的房间和关联设备',
    34025: '关联失败，设备已被其他楼栋关联',
    34026: '此楼栋下设备数量达到上限',
    34027: '操作失败，该楼栋已被删除',
    34028: '房间号已存在',
    34029: '此楼栋下房间数量达到上限',
    34030: '此房间下住户数量达到上限',
    34031: '操作失败，该房间已被删除',
    34033: '删除失败，请先删除房间下的住户',
    34035: '此房间内已存在该手机号',
    34036: '此房间内已存在该邮箱',
    34037: '操作失败，该住户已被删除',
    // 托管
    34203: '此安装商不存在',
    34204: '设备托管状态已更新，请刷新相关页面以同步最新状态',
    34205: '申请状态已更新，请刷新相关页面以同步最新状态',
    34206: '申请状态已更新，请刷新相关页面以同步最新状态',
    34207: '站点状态已更新，请刷新相关页面以同步最新状态',
    34208: '站点状态已更新，请刷新相关页面以同步最新状态',
    34209: '设备托管状态已更新，请刷新相关页面以同步最新状态',
    // 1.5.1 superlive max 分享
    10004: '参数错误',
    21109: '该设备已退出分享',
    // 云后台-云升级
    33681: '设备不支持此种方式升级',
    33682: '通道不存在',
    33683: '通道离线',
    7046: '达到版本检测限制',
    // 绑定设备错误码
    7048: '设备不存在',
    7062: '设备不在线',
    7063: '设备已被其他人绑定',
    7072: '设备已被自己绑定',
    100008: '设备已被自己绑定',
    7085: '本用户不能绑定该设备',
    33016: '设备正在绑定中',
    7071: '设备未开启对应的云业务',
    // 重置密码错误码
    100003: '未找到指定设备',
    101007: '未找到指定设备',
    211: '用户被锁定',
    215: '系统繁忙',
    217: '连接超时',
    130000: '无法连接设备，请检查设备状态',
    499: '参数校验不通过'
  }
}
