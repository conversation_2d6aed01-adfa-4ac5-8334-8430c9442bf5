.add-defense-wrapper .nav-bar {
    z-index: 1!important;
}

.add-defense-head {
    border-bottom: 1px solid $max-border2;
    .add-defense-text {
        color: $max-gray;
    }
}

.add-defense-device {
    .device-label {
        color: $max-light-black;
        // color: red;
    }
}


.add-defense-head-block .van-field {
    border-bottom: 1px solid $max-border;
    padding: 10px 0px;
}

.defense-deployment-content {
    background-color: $max-light-white;
}

.defense-list-wrapper .defense-item-wrapper {
    background-color: $max-background;
}

.canecl-defense-status {
    color: $max-green;
}

.defense-status {
    color: $max-primary;
}

.ipc-setting-content {
    background-color: $max-light-white;
}

.ipc-setting-wrapper {
    .ipc-setting-box {
        background-color: $max-background;
        .ipc-setting-text {
            color: $max-gray;
        }
    }
    .ipc-setting-box:not(:last-child) .ipc-setting-line {
        border-bottom: 1px solid $max-border;
    }
    .ipc-setting-desc {
        color: $max-gray;
    }
}

.ipc-linkage-wrapper .ipc-configure {
    color: $max-gray;
}

.ipc-linkage-wrapper {
    .ipc-linkage-content {
        background-color: $max-light-white;
        .ipc-linkage-list {
            background-color: $max-background;
            .ipc-linkage-item {
                .ipc-linkage-item-line {
                    border-bottom: 1px solid $border-color;
                }
            }
        }
    }
}


.channel-wrapper .channel-content .channel-list {
    background-color: $max-background;
}

.channel-checkbox-content {
    color: $max-black;
}

.channel-cell-wrapper {
    border-bottom: 1px solid $border-color;
}

.channel-checkbox-content-disabled {
    color: $max-disabled;
}

.channel-checkbox-desc {
    color: $max-disabled;
}

.channel-desc-text {
    color: $max-disabled;
}

.defense-deployment-wrapper .footer2 {
    background-color: $max-light-white;
}

.defense-deployment-wrapper .icon-disabled {
    color: $max-disabled;
}