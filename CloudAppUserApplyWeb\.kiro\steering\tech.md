# Technology Stack

## Core Framework
- **Vue.js 2.7.8** - Main frontend framework
- **Vue Router 3.5.4** - Client-side routing
- **Vuex 3.6.2** - State management
- **Vant 2.12.48** - Mobile UI component library

## Build System
- **Vue CLI 5.0.8** - Build tooling and development server
- **Webpack 4** - Module bundler (configured via vue.config.js)
- **Babel** - JavaScript transpilation
- **Sass** - CSS preprocessing

## Development Tools
- **ESLint + Prettier** - Code linting and formatting
- **Husky + lint-staged** - Git hooks for code quality
- **VConsole** - Mobile debugging (development only)

## Key Libraries
- **Axios** - HTTP client
- **amfe-flexible** - Mobile viewport adaptation
- **better-scroll** - Mobile scrolling solution
- **vue-i18n** - Internationalization
- **vuedraggable** - Drag and drop functionality
- **crypto-js, js-md5, sha1** - Encryption utilities

## Common Commands

### Development
```bash
npm install          # Install dependencies
npm run dev          # Start development server (port 9020)
npm run serve        # Alternative dev command
```

### Build & Deploy
```bash
npm run build        # Production build
npm run stage        # Staging build
```

### Code Quality
```bash
npm run lint         # Run ESLint
npm run deps         # Interactive dependency updates
```

## Environment Configuration
- Development: `.env.development`
- Uses proxy configuration for API calls
- Supports multiple deployment environments (dev, staging, production)

## Mobile Optimization
- Responsive design with viewport adaptation
- Touch gesture support via alloyfinger
- Safe area handling for mobile devices
- Gzip compression for production builds